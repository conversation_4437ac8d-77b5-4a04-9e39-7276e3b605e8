package com.timevale.footstone.user.service.aop.validator;

import com.timevale.footstone.user.service.exception.UserFootstoneErrors;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

public abstract class ParamValidator {

    private static final Validator VALIDATOR =
            Validation.byProvider(HibernateValidator.class)
                    .configure()
                    .failFast(true)
                    .buildValidatorFactory()
                    .getValidator();

    public static <T> void valid(T obj) {

        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(obj);

        // 抛出检验异常
        if (null != constraintViolations && constraintViolations.size() > 0) {

            String message = constraintViolations.iterator().next().getMessage();

            throw new UserFootstoneErrors.IllegalParamWith(message);
        }
    }
}
