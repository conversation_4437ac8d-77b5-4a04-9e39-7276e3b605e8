package com.timevale.footstone.user.service.async;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.timevale.easun.service.exception.EasunErrors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.SyncTaskExecutor;

import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 不影响主要逻辑的附加类任务，添加到这里运行<br>
 * 根据重要程度，对任务失败分为warn报错和error报错
 *
 * <AUTHOR> on 2018-05-17
 */
public class TaskUtil {

    private static Logger logger = LoggerFactory.getLogger(TaskUtil.class);

    private static ExecutorService executors;


    /**
     * 耗时的线程池（执行一些比较耗时的任务）
     */
    private static ExecutorService timeConsumingExecutors;

    /**
     * 业务空间相关线程池
     *  spaceUpdate thread pool
     */
    private static ExecutorService spaceTimeConsumingExecutors;

    /**
     * debug使用，切勿生产使用
     */
    private static SyncTaskExecutor syncTaskExecutor;

    public static ExecutorService getExecutors() {
        return executors;
    }

    public static ExecutorService getTimeConsumingExecutors() {
        return timeConsumingExecutors;
    }

    public static ExecutorService getSpaceTimeConsumingExecutors() {
        return spaceTimeConsumingExecutors;
    }

    /**
     * debug使用，切勿生产使用
     */
    public synchronized static Executor getDebugExecutors() {
        return syncTaskExecutor = new SyncTaskExecutor();
    }

    static {
        ThreadFactory namedThreadFactory =
                new ThreadFactoryBuilder().setNameFormat("footstone-task-pool-%d").build();

        int cpuCores = Runtime.getRuntime().availableProcessors();

        int coreThreads = Math.max(cpuCores, 4);

        executors =
                new ThreadPoolExecutor(
                        coreThreads,
                        cpuCores * 10,
                        1L,
                        TimeUnit.MINUTES,
                        new LinkedBlockingQueue<>(256),
                        namedThreadFactory,
                        new ThreadPoolExecutor.CallerRunsPolicy());

        timeConsumingExecutors =
                new ThreadPoolExecutor(
                        coreThreads,
                        cpuCores * 10,
                        1L,
                        TimeUnit.MINUTES,
                        new LinkedBlockingQueue<>(256),
                        namedThreadFactory,
                        new ThreadPoolExecutor.CallerRunsPolicy());
        spaceTimeConsumingExecutors =
                new ThreadPoolExecutor(
                        coreThreads,
                        cpuCores * 10,
                        1L,
                        TimeUnit.MINUTES,
                        new LinkedBlockingQueue<>(256),
                        namedThreadFactory,
                        new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static void addImportantTask(Task t) {
        executors.execute(
                () -> {
                    try {
                        t.fulfil();
                    } catch (Throwable e) {
                        logger.error("An important task failed", e);
                    }
                });
    }

    public static void addCommonTask(Task t) {
        executors.execute(
                () -> {
                    try {
                        t.fulfil();
                    } catch (Throwable e) {
                        logger.warn("A common task failed", e);
                    }
                });
    }

    public static <R> Future<R> addFutureTask(Supplier<R> f) {
        return executors.submit(f::get);
    }

    public static void shutdown() {
        executors.shutdown();
        timeConsumingExecutors.shutdown();
        spaceTimeConsumingExecutors.shutdown();
        logger.info("shutdown the executor");
    }

    public static <R> R getResult(Future<R> future) {
        try {
            return future.get();
        } catch (InterruptedException | ExecutionException e) {
            logger.error("get future result fail", e);
            throw new EasunErrors.InternalServiceTaken(e.getMessage());
        }
    }
}
