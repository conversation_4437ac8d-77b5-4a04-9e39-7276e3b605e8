package com.timevale.footstone.user.service.aop;

import com.google.common.collect.Maps;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.api.RpcDeptPlusService;
import com.timevale.easun.service.model.organization.input.BizEasunDeptIdInput;
import com.timevale.easun.service.model.organization.output.BizDeptChildOutput;
import com.timevale.easun.service.model.organization.output.BizDeptDetailOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.consts.OpLogEnum;
import com.timevale.footstone.user.service.constants.audit.AuditLogConstants;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgMemberServiceAdapt;
import com.timevale.footstone.user.service.model.dept.request.UpdateDeptRequest;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.footstone.user.service.utils.conv.CommonConverter;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.timevale.footstone.user.consts.OpLogConsts.*;
import static com.timevale.footstone.user.consts.OpLogEnum.*;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_TENANT_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

/**
 * <AUTHOR>
 * @date 2023/10/19 17:01
 */
@Aspect
@Slf4j
@Component
public class OpLog4DeptUpdateAspect extends OpLogAspectBaseService {

    @Autowired
    private AuditLogService logService;
    @Autowired
    private IdAdapt idAdapt;
    @Autowired
    private RpcIcUserPlusServiceAdapt icUserPlusService;

    @Autowired
    private RpcDeptPlusService deptService;

    @Autowired
    private RpcInnerOrgMemberServiceAdapt memberServiceAdapt;

    // 一个接口，三个事件，只能切面实现
    @Pointcut("execution(* com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdapt.updateDept(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        BizDeptDetailOutput deptDetail;
        String orgId = (String) pjp.getArgs()[0];
        String deptId = (String) pjp.getArgs()[1];
        UpdateDeptRequest deptRequest = (UpdateDeptRequest) pjp.getArgs()[2];
        String organId = null;
        try {
            organId = idAdapt.orgIdToOrganId(orgId);
            BizEasunDeptIdInput deptBefore = new BizEasunDeptIdInput();
            deptBefore.setDeptId(deptId);
            deptBefore.setOrganId(organId);
            deptDetail = deptService.getDeptDetail(new RpcInput<>(deptBefore)).getData();
        } catch (Exception e) {
            log.warn("opLog log failed when updateDept before aspect,orgId:{},dpetId{},params {}", orgId, deptId, deptRequest, e);
            Pair<Object, Throwable> result = Pair.of(RESULT_FAILED, e);
            return returnResult(result);
        }
        Pair<Object, Throwable> result = getResult(pjp);
        try {
            String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
            boolean editDept = isEditDept(deptRequest,deptDetail);
            //编辑部门
            if (editDept) {
                saveEditDeptLog(orgId, deptRequest, deptDetail, resultStr);
            }
            //没有改动部门负责人
            if (CollectionUtils.isEmpty(deptDetail.getMainMembers()) && CollectionUtils.isEmpty(deptRequest.getMainMemberIds())) {
                return returnResult(result);
            }
            boolean addDeptLeader = CollectionUtils.isEmpty(deptDetail.getMainMembers())
                    && CollectionUtils.isNotEmpty(deptRequest.getMainMemberIds());
            //新增部门负责人
            if (addDeptLeader) {
                AuditLog a = deptMemberChange(orgId, new HashSet<>(), new HashSet<>(deptRequest.getMainMemberIds())
                        , deptDetail, resultStr,ADD_DEPT_LEADER);
                logService.saveAuditLog(a);
                return returnResult(result);
            }
            //编辑部门负责人
            Set<String> originMainMembers = memberServiceAdapt.getOidsByMemberIds(organId, deptDetail.getMainMembers());
            boolean changeDeptMainMember = isChangeMainMember(deptRequest, originMainMembers);
            if (changeDeptMainMember) {
                AuditLog a = deptMemberChange(orgId, originMainMembers, new HashSet<>(deptRequest.getMainMemberIds())
                        , deptDetail, resultStr,CHANGE_DEPT_LEADER);
                logService.saveAuditLog(a);
            }
        } catch (Throwable e) {
            log.warn("opLog log failed when register,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }

    private boolean isChangeMainMember(UpdateDeptRequest deptRequest, Set<String> originMainMembers) {
        Set<String> grantLeaders = new HashSet<>();
        Set<String> revokeLeaders = new HashSet<>();
        for (String grantMemberId : deptRequest.getMainMemberIds()) {
            //原来不是部门负责人，但这次有，就是新增
            if (!originMainMembers.contains(grantMemberId)) {
                grantLeaders.add(grantMemberId);
            }
        }
        for (String grantMemberId : originMainMembers) {
            //原来有这次没有，就是取消
            if (!deptRequest.getMainMemberIds().contains(grantMemberId)) {
                revokeLeaders.add(grantMemberId);
            }
        }
        return CollectionUtils.isNotEmpty(grantLeaders) || CollectionUtils.isNotEmpty(revokeLeaders);
    }

    private void saveEditDeptLog(String orgId, UpdateDeptRequest deptRequest, BizDeptDetailOutput deptDetail, String resultStr) {
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        String roleNames = (String) LogRecordContext.getVariables().get(AuditLogConstants.ROLE_NAMES);
        Map<String, String> extParams = Maps.newHashMap();
        extParams.put(DEPARTMENT1_KEY, deptRequest.getDeptName());
        BizEasunDeptIdInput deptParent = new BizEasunDeptIdInput();
        deptParent.setDeptId(deptDetail.getDeptId());
        deptParent.setOrganId(idAdapt.orgIdToOrganId(orgId));
        BizDeptChildOutput parent = deptService.getParent(new RpcInput<>(deptParent)).getData().get(0);
        String parentName = parent == null ? "根部门" : parent.getDeptName();
        extParams.put(DEPARTMENT2_KEY, parentName);
        AuditLog a = OpLogUtils.logBuilder(EDIT_DEPT, rootOrgId, resultStr, deptDetail.getDeptId(),deptDetail.getDeptName(), orgId, roleNames, extParams);
        logService.saveAuditLog(a);
    }

    private boolean isEditDept( UpdateDeptRequest deptRequest, BizDeptDetailOutput deptDetail){
        boolean changeDeptName = !(StringUtils.isBlank(deptDetail.getDeptName()) && StringUtils.isBlank(deptRequest.getDeptName()))
                && !Objects.equals(deptDetail.getDeptName(), deptRequest.getDeptName());
        boolean changeParent = !Objects.equals(deptDetail.getParentDeptId(), deptRequest.getParentDeptId());
        boolean changeDesc = !Objects.equals(deptDetail.getDesc(), deptRequest.getDesc());
        return changeDeptName || changeParent || changeDesc;
    }

    private AuditLog deptMemberChange(String orgId, Set<String> originMainMembers, Set<String> newMainMembers
            , BizDeptDetailOutput deptDetail, String resultStr, OpLogEnum opLogEnum) {
        List<String> deptMainMembers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(originMainMembers)) {
            deptMainMembers.addAll(originMainMembers);
        }
        if (CollectionUtils.isNotEmpty(newMainMembers)) {
            deptMainMembers.addAll(newMainMembers);
        }
        List<BizICUserOutput> icUsers = icUserPlusService.getICUsers(deptMainMembers);
        List<BizICUserOutput> originMainUser = new ArrayList<>();
        List<BizICUserOutput> newMainUser = new ArrayList<>();
        for (BizICUserOutput icUserOutput : icUsers) {
            if (originMainMembers.contains(icUserOutput.getId().getOuid())){
                originMainUser.add(icUserOutput);
            }
            if (newMainMembers.contains(icUserOutput.getId().getOuid())){
                newMainUser.add(icUserOutput);
            }
        }
        String newDeptLeaderNames = CommonConverter.listNames(e->
                CommonConverter.getPropertyValueByType(e.getProperties(),BuiltinProperty.INFO_NAME),newMainUser);
        String originDeptLeaderNames =   CommonConverter.listNames(e->
                CommonConverter.getPropertyValueByType(e.getProperties(),BuiltinProperty.INFO_NAME),originMainUser);
        Map<String, String> extParams = Maps.newHashMap();
        extParams.put(DEPT_NAME, deptDetail.getDeptName());
        extParams.put(DEPT_MAIN_MEMBER_1, originDeptLeaderNames);
        extParams.put(DEPT_MAIN_MEMBER_2, newDeptLeaderNames);
        String roleNames = (String) LogRecordContext.getVariables().get(AuditLogConstants.ROLE_NAMES);
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        return OpLogUtils.logBuilder(opLogEnum, rootOrgId, resultStr, deptDetail.getDeptId(),deptDetail.getDeptName(), orgId, roleNames, extParams);
    }

}
