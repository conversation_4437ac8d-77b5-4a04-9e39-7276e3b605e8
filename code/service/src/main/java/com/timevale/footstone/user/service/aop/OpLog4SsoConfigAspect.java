package com.timevale.footstone.user.service.aop;

import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_FAILED;
import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_SUCCESSED;
import static com.timevale.footstone.user.consts.OpLogEnum.CHANGE_SSO_DOMAIN;
import static com.timevale.footstone.user.consts.OpLogEnum.CREATE_SSO_CONNECTOR;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_TENANT_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

import com.google.common.collect.Maps;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.footstone.user.service.constants.audit.AuditLogConstants;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoOrgConnectorManageAdapt;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.token.service.model.input.SsoConnectorConfigSaveInput;
import com.timevale.token.service.model.output.SsoOrganizationConnectorConfigOutput;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2024/6/14 17:55
 */

@Aspect
@Slf4j
@Component
public class OpLog4SsoConfigAspect extends OpLogAspectBaseService {


    @Autowired
    private AuditLogService logService;

    @Autowired
    private RpcSsoOrgConnectorManageAdapt ssoOrgConnectorManageAdapt;

    //
    @Pointcut("execution(* com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoOrgConnectorManageAdapt.saveConnectorConfig(..))")
    public void pointCut() {
    }


    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        SsoConnectorConfigSaveInput ssoRequest = (SsoConnectorConfigSaveInput) pjp.getArgs()[0];
        Pair<Object, Throwable> result = new MutablePair<>();
        try {
            SsoOrganizationConnectorConfigOutput config = null;
            if (ssoRequest.getId() != null) {
                config = ssoOrgConnectorManageAdapt.getConnectorConfigById(ssoRequest.getId());
            }
            result = getResult(pjp);
            if (ssoRequest.getId() != null && config != null) {
                saveUpdate(ssoRequest, result, config);
            }

            if (ssoRequest.getId() == null && result.getLeft()!= null) {
                saveCreate(ssoRequest, result);
            }

            return returnResult(result);
        } catch (Exception e) {
            log.warn("save sso op log error", e);
        } finally {
            return returnResult(result);
        }
    }


    private void saveUpdate(SsoConnectorConfigSaveInput ssoRequest,
            Pair<Object, Throwable> result, SsoOrganizationConnectorConfigOutput config) {
        String roleNames = (String) LogRecordContext.getVariables()
                .get(AuditLogConstants.ROLE_NAMES);
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        Map<String, String> extParams = Maps.newHashMap();
        //修改sso域名
        if (!Objects.equals(ssoRequest.getDomain(), config.getDomain())) {
            extParams.put("oldDomain", config.getDomain());
            extParams.put("newDomain", ssoRequest.getDomain());
            AuditLog a = OpLogUtils.logBuilder(CHANGE_SSO_DOMAIN, rootOrgId, resultStr,
                    String.valueOf(ssoRequest.getId()), ssoRequest.getName(),
                    ssoRequest.getOrganOuid(), roleNames, extParams);
            logService.saveAuditLog(a);
        }
    }


    private void saveCreate(SsoConnectorConfigSaveInput ssoRequest,
            Pair<Object, Throwable> result) {
        String roleNames = (String) LogRecordContext.getVariables()
                .get(AuditLogConstants.ROLE_NAMES);
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        Map<String, String> extParams = Maps.newHashMap();
        //创建sso连接器
        AuditLog a = OpLogUtils.logBuilder(CREATE_SSO_CONNECTOR, rootOrgId, resultStr,
                String.valueOf(result.getLeft()), ssoRequest.getName(), ssoRequest.getOrganOuid(),
                roleNames, extParams);
        logService.saveAuditLog(a);
    }


}
