package com.timevale.footstone.user.service.aop;

import com.google.common.collect.Maps;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.account.input.ThirdPartyBindInput;
import com.timevale.easun.service.model.idcard.BizIdcardSetInput;
import com.timevale.easun.service.model.mods.RpcOuidInput;
import com.timevale.easun.service.model.sender.input.BizModifyIdcardExecuteInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.consts.OpLogEnum;
import com.timevale.footstone.user.model.oplog.ThirdPartyModel;
import com.timevale.footstone.user.service.inner.respdecoration.deco.decoraters.Decoration;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.mandarin.common.lang.ValidateUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.timevale.account.service.model.constants.BuiltinThirdparty.*;
import static com.timevale.footstone.user.consts.OpLogConsts.*;
import static com.timevale.footstone.user.consts.OpLogEnum.*;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

@Aspect
@Slf4j
@Component
public class OpLog4ContactUpdateAspect extends OpLogAspectBaseService {
    @Autowired
    private AuditLogService logService;
    @Autowired
    private RpcSenderAuthService authService;
    @Autowired
    private Decoration decorater;

    @Pointcut("execution(* com.timevale.easun.service.api.RpcSenderAuthService.senderIdcardModifyExecute(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        String reciever = null;
        try {
            RpcInput<BizModifyIdcardExecuteInput> arg0 = (RpcInput<BizModifyIdcardExecuteInput>) pjp.getArgs()[0];
            BizModifyIdcardExecuteInput input = arg0.getInput();
            reciever = authService.getRecieverByServiceId(new RpcInput<>(input.getServiceId())).getData();
        } catch (Throwable e) {
            log.warn("opLog log failed when get reciever,params:{}", pjp.getArgs()[0], e);
        }
        Pair<Object, Throwable> result = getResult(pjp);
        //没有操作人过滤
        String oid = RequestUtil.getHeader(HEADER_OPERATOR_ID);
        if (null == reciever || StringUtils.isBlank(RequestUtil.getHeader(HEADER_OPERATOR_ID))) {
            return returnResult(result);
        }
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        try {

            OpLogEnum logEnum = null;
            Map<String, String> extParams = Maps.newHashMap();
            if (ValidateUtils.mobileValid(reciever)) {
                logEnum = UPDATE_MOBILE;
                extParams.put(EXT_PARAMS_NEW_MOBILE, decorater.decorate(DecorationType.MOBILE, reciever));

            } else {
                logEnum = UPDATE_EMAIL;
                extParams.put(EXT_PARAMS_NEW_EMAIL, decorater.decorate(DecorationType.EMAIL, reciever));
            }
            extParams.put(EXT_PARAMS_REQUEST_URI, RequestContext.getRequest().getRequestURI());
            extParams.put(EXT_PARAMS_TARGET_OID, oid);
            AuditLog logDTO = OpLogUtils.logBuilder(logEnum, oid, resultStr,
                    reciever, extParams);
            logService.saveAuditLog(logDTO);
        } catch (Throwable e) {
            log.warn("opLog log failed when ContactUpdate,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }


}
