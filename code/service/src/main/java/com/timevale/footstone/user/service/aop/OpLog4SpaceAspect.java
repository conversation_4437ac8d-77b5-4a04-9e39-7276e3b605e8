package com.timevale.footstone.user.service.aop;

import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_SUCCESSED;
import static com.timevale.footstone.user.consts.OpLogConsts.SPACE_NAME;
import static com.timevale.footstone.user.consts.OpLogEnum.CHANGE_SPACE;
import static com.timevale.footstone.user.consts.OpLogEnum.CLOSE_SPACE;
import static com.timevale.footstone.user.consts.OpLogEnum.CREATE_SPACE;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_TENANT_ID;
import static com.timevale.footstone.user.service.constants.audit.AuditLogConstants.SelfDefiningDetails.SPACE_ADD_ADMIN;
import static com.timevale.footstone.user.service.constants.audit.AuditLogConstants.SelfDefiningDetails.SPACE_ADD_DEPT;
import static com.timevale.footstone.user.service.constants.audit.AuditLogConstants.SelfDefiningDetails.SPACE_REMOVE_ADMIN;
import static com.timevale.footstone.user.service.constants.audit.AuditLogConstants.SelfDefiningDetails.SPACE_REMOVE_DEPT;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;
import static org.apache.commons.lang.StringUtils.EMPTY;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.account.organization.service.constant.MqTagConstant;
import com.timevale.account.organization.service.model.mq.SpaceScopeMessageBody;
import com.timevale.account.organization.service.model.service.newbiz.output.entity.Dept;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.model.account.input.SpaceDetailInput;
import com.timevale.easun.service.model.account.input.SpaceDetpRequest;
import com.timevale.easun.service.model.account.input.SpaceRequestParam;
import com.timevale.easun.service.model.account.output.EasunSpaceOutput;
import com.timevale.easun.service.model.account.output.SpaceItemDetailOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.output.BizDeptDetailOutput;
import com.timevale.footstone.user.service.constants.audit.AuditLogConstants;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdaptV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrganSpaceAdapt;
import com.timevale.footstone.user.service.inner.model.SpaceModifyModel;
import com.timevale.footstone.user.service.inner.model.audit.SpaceUpdateParam;
import com.timevale.footstone.user.service.mq.SpaceScopeChangeMqClient;
import com.timevale.footstone.user.service.utils.AopUtils;
import com.timevale.footstone.user.service.utils.IcUserUtils;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.footstone.user.service.utils.conv.CommonConverter;
import com.timevale.mandarin.base.util.CollectionUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2024/12/10 15:47
 */
@Aspect
@Slf4j
@Component
public class OpLog4SpaceAspect extends OpLogAspectBaseService {


    @Autowired
    private AuditLogService logService;


    @Autowired
    private RpcDeptPlusServiceAdaptV3 deptPlusServiceAdaptV3;

    @Autowired
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;


    @Autowired
    private SpaceScopeChangeMqClient deptsChangeMqClient;


    @Autowired
    private RpcOrganSpaceAdapt spaceAdapt;

    // 一个接口，多个事件，通过切面实现
    @Pointcut("execution(*  com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrganSpaceAdapt.updateSpace(..))"
            + "|| execution(* com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrganSpaceAdapt.createSpace(..))"
            + "|| execution(* com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrganSpaceAdapt.deleteSpace(..))")
    public void pointCut() {
    }


    /**
     *
     * 业务空间切面
     * @param pjp
     * @return
     * @throws Throwable
     */
    @Around("pointCut()")
    public Object spacePointCut(ProceedingJoinPoint pjp) throws Throwable {
        Signature signature = pjp.getSignature();
        String name = signature.getName();
        switch (name) {
            case "updateSpace":
                return spaceUpdate(pjp);
            case "createSpace":
                return createSpace(pjp);
            case "deleteSpace":
               return deleteSpace(pjp);
            default:
                break;
        }
        return null;
    }

    /**
     * 空间更新切点
     * @param pjp
     * @return
     * @throws Throwable
     */
    private Object spaceUpdate(ProceedingJoinPoint pjp) throws Throwable {
        SpaceDetailInput spaceDetailInput = (SpaceDetailInput) pjp.getArgs()[0];
        if (spaceDetailInput == null || CollectionUtils.isEmpty(spaceDetailInput.getList())) {
            Pair<Object, Throwable> result = getResult(pjp);
            return returnResult(result);
        }
        Pair<Object, Throwable> result = null;
        List<String> spaceDepts = new ArrayList<>();
        List<String> spaceAdmins = new ArrayList<>();
        List<SpaceDetpRequest> changeList = spaceDetailInput.getList();
        //业务空间新添加的数据map
        Map<String, SpaceModifyModel> spaceAddModelMap = new HashMap<>();
        try {
            //获取业务空间原有的数据（部门列表，管理员列表），以便与请求参数对比，得到新添加的
            Map<String, SpaceModifyModel> spaceOriDataMap = getSpaceOriData(spaceDetailInput);
            for (SpaceDetpRequest spaceRequest : changeList) {
                //得到每个 item 添加的部门列表，管理员列表（因为移出的列表包含在请求参数里面，所以不需要处理）
                getSpaceAddModel(spaceOriDataMap, spaceRequest, spaceAddModelMap);
                if (CollectionUtils.isNotEmpty(spaceRequest.getDeptIds())) {
                    spaceDepts.addAll(spaceRequest.getDeptIds());
                }
                if (CollectionUtils.isNotEmpty(spaceRequest.getRevokeDetpList())) {
                    spaceDepts.addAll(spaceRequest.getRevokeDetpList());
                }
                if (CollectionUtils.isNotEmpty(spaceRequest.getAdminsOid())) {
                    spaceAdmins.addAll(spaceRequest.getAdminsOid());
                }
                if (CollectionUtils.isNotEmpty(spaceRequest.getRevokeAdminList())) {
                    spaceAdmins.addAll(spaceRequest.getRevokeAdminList());
                }
            }
        } catch (Exception e) {
            log.warn("log space update error ", e);
        }
        //业务方执行
        result = AopUtils.getResult(pjp);
        try {
            Map<String, Dept> deptMap = deptPlusServiceAdaptV3.batchGetDeptInfo(spaceDepts);
            Map<String, BizICUserOutput> userMap = icUserPlusServiceAdapt.getICUsersMap(
                    spaceAdmins);
            for (SpaceDetpRequest spaceRequest : changeList) {
                recordSpaceUpdateAuditLog(spaceDetailInput, spaceAddModelMap, deptMap, userMap,
                        spaceRequest);
            }
        } catch (Exception e) {
            log.warn("log space update error ", e);
        }
        return AopUtils.returnResult(result);
    }

    /**
     * 记录业务空间变更日志
     * @param spaceAddModelMap
     * @param deptMap
     * @param userMap
     * @param spaceRequest
     */
    private void recordSpaceUpdateAuditLog(SpaceDetailInput spaceDetailInput,  Map<String, SpaceModifyModel> spaceAddModelMap,
            Map<String, Dept> deptMap, Map<String, BizICUserOutput> userMap,
            SpaceDetpRequest spaceRequest) {
        SpaceUpdateParam param =getAuditParam(spaceDetailInput);
        SpaceModifyModel addModel = spaceAddModelMap.get(spaceRequest.getOrganOid());

        if (addModel != null) {
            recordAddDatas(deptMap, userMap, spaceRequest,param, addModel);
        }
        //移出的部门
        recordRemoveDatas(deptMap, userMap, spaceRequest, param);
        //业务空间部门变更消息
        sendSpaceUpdateMsg(spaceRequest, param, addModel);
    }


    /**
     * 业务空间部门变更消息
     * @param spaceRequest
     * @param param
     * @param addModel
     */
    private void sendSpaceUpdateMsg(SpaceDetpRequest spaceRequest, SpaceUpdateParam param,
            SpaceModifyModel addModel) {
        if ((addModel != null && CollectionUtils.isNotEmpty(addModel.getDeptIds()))
                || CollectionUtils.isNotEmpty(spaceRequest.getRevokeDetpList())){
            SpaceScopeMessageBody deptChangeMsgBody = new SpaceScopeMessageBody();
            deptChangeMsgBody.setRemoveIds(spaceRequest.getRevokeDetpList());
            deptChangeMsgBody.setAddIds(Lists.newArrayList(addModel.getDeptIds()));
            deptChangeMsgBody.setSpaceId(param.getResourceId());
            deptChangeMsgBody.setOrganOuid(spaceRequest.getOrganOid());
            deptsChangeMqClient.sendAsync(deptChangeMsgBody, MqTagConstant.SPACE_SCOPE_CHANGE_DETP_TAG);
        }
    }

    /**
     * 记录业务空间移出数据的审计日志
     * @param deptMap
     * @param userMap
     * @param spaceRequest
     * @param param
     */
    private void recordRemoveDatas(Map<String, Dept> deptMap, Map<String, BizICUserOutput> userMap,
            SpaceDetpRequest spaceRequest, SpaceUpdateParam param) {
        //移出的部门
        if (CollectionUtils.isNotEmpty(spaceRequest.getRevokeDetpList())) {
            String deptNames = CommonConverter.getAppentFieldInMapByIds(deptMap,
                    spaceRequest.getRevokeDetpList(), Dept::getDeptName);
            recordAuditLog(deptNames, SPACE_REMOVE_DEPT, spaceRequest.getOrganOid(), param);

        }
        //移出的管理员
        if (CollectionUtils.isNotEmpty(spaceRequest.getRevokeAdminList())) {
            String names = CommonConverter.getAppentFieldInMapByIds(userMap, spaceRequest.getRevokeAdminList(),
                    IcUserUtils::getIcUserName);
            recordAuditLog(names, SPACE_REMOVE_ADMIN, spaceRequest.getOrganOid(), param);
        }
    }

    /**
     * 记录业务空间新增数据的审计日志
     * @param deptMap
     * @param userMap
     * @param spaceRequest
     * @param param
     * @param addModel
     */
    private void recordAddDatas(Map<String, Dept> deptMap, Map<String, BizICUserOutput> userMap,
            SpaceDetpRequest spaceRequest, SpaceUpdateParam param, SpaceModifyModel addModel) {
        // 添加的部门
        if (CollectionUtils.isNotEmpty(addModel.getDeptIds())) {
            String deptNames = CommonConverter.getAppentFieldInMapByIds(deptMap, addModel.getDeptIds(),
                    Dept::getDeptName);
            recordAuditLog(deptNames, SPACE_ADD_DEPT,  spaceRequest.getOrganOid(), param);
        }
        //添加的管理员
        if (CollectionUtils.isNotEmpty(addModel.getAdminOids())) {
            String names = CommonConverter.getAppentFieldInMapByIds(userMap, addModel.getAdminOids(),
                    IcUserUtils::getIcUserName);
            recordAuditLog(names, SPACE_ADD_ADMIN,spaceRequest.getOrganOid(), param);
        }
    }


    /**
     * 空间创建切点
     * @param pjp
     * @return
     * @throws Throwable
     */
    public Object createSpace(ProceedingJoinPoint pjp) throws Throwable {
        SpaceDetailInput spaceDetailInput = (SpaceDetailInput) pjp.getArgs()[0];
        //执行业务方
        Pair<Object, Throwable> result = getResult(pjp);
        try {
            String resourceId = Objects.toString(result.getLeft(), EMPTY);;
            String resourceName = spaceDetailInput.getName();
            String roleNames = String.valueOf(
                    LogRecordContext.getVariable(AuditLogConstants.ROLE_NAMES));
            String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
            List<SpaceDetpRequest> changeList = spaceDetailInput.getList();
            for (SpaceDetpRequest spaceRequest : changeList) {
                Map<String, String> extParams = Maps.newHashMap();
                extParams.put(SPACE_NAME, spaceDetailInput.getName());
                AuditLog a = OpLogUtils.logBuilder(CREATE_SPACE, rootOrgId, RESULT_SUCCESSED,resourceId,resourceName, spaceRequest.getOrganOid(), roleNames, extParams);
                logService.saveAuditLog(a);
            }
        } catch (Exception e) {
            log.warn("log space create error ", e);
        }
        return AopUtils.returnResult(result);
    }


    /**
     * 空间删除切点
     * @param pjp
     * @return
     * @throws Throwable
     */
    public Object deleteSpace(ProceedingJoinPoint pjp) throws Throwable {
        Pair<Object, Throwable> result = null;
        try {
            SpaceRequestParam spaceParam = (SpaceRequestParam) pjp.getArgs()[0];
            String resourceId = spaceParam.getSpaceId();
            String roleNames = Objects.toString(LogRecordContext.getVariable(AuditLogConstants.ROLE_NAMES),EMPTY);
            String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
            EasunSpaceOutput easunSpaceOutput = getSpaceOriData(resourceId,rootOrgId);
            String resourceName = easunSpaceOutput.getName();
            result = getResult(pjp);
            List<SpaceItemDetailOutput> spaceList = easunSpaceOutput.getList();
            for (SpaceItemDetailOutput spaceItem : spaceList) {
                Map<String, String> extParams = Maps.newHashMap();
                extParams.put(SPACE_NAME, easunSpaceOutput.getName());
                AuditLog a = OpLogUtils.logBuilder(CLOSE_SPACE, rootOrgId, RESULT_SUCCESSED,resourceId,resourceName, spaceItem.getOrganOuid(), roleNames, extParams);
                logService.saveAuditLog(a);
            }
        } catch (Exception e) {
            log.warn("log space delete error ", e);
            //为空，说明删除方法还没执行，再调用一遍
            if (result == null) {
                result = getResult(pjp);
            }
        }

        return AopUtils.returnResult(result);
    }


    /**
     * 查询业务空间完整数据
     * @param spaceId
     * @param orgId
     * @return
     */
    private EasunSpaceOutput getSpaceOriData(String spaceId,String orgId) {
        SpaceRequestParam spaceRequestParam = new SpaceRequestParam();
        spaceRequestParam.setSpaceId(spaceId);
        spaceRequestParam.setOnlyOwner(false);
        spaceRequestParam.setOrganOuid(orgId);
        return spaceAdapt.getSpaceDetail(spaceRequestParam);
    }

    /**
     * 记录审计日志
     * @param names
     * @param detailsTemplate
     * @param param
     */
    private void recordAuditLog(String names, String detailsTemplate, String organOid,
            SpaceUpdateParam param) {
        String details = String.format(detailsTemplate, names);
        AuditLog a = OpLogUtils.logBuilder(CHANGE_SPACE, param.getRootOrgId(), "成功", details, param.getResourceId(),
                param.getResourceName(), organOid, param.getRoleNames(), new HashMap<>());
        logService.saveAuditLog(a);
    }

    //判断出入参里面的数据，哪些是新增的
    private void getSpaceAddModel(Map<String, SpaceModifyModel> oriSpaceDataMap,
            SpaceDetpRequest spaceRequest, Map<String, SpaceModifyModel> addSpaceDataMap) {
        SpaceModifyModel spaceModel = oriSpaceDataMap.get(spaceRequest.getOrganOid());
        SpaceModifyModel addModel = new SpaceModifyModel();
        //原来没有，入参有，就是新增
        if (CollectionUtils.isNotEmpty(spaceRequest.getDeptIds())) {
            Set<String> addDeptSets = new HashSet<>();
            spaceRequest.getDeptIds().forEach(e -> {
                Set<String> oriDepts =
                        spaceModel != null && CollectionUtils.isNotEmpty(spaceModel.getDeptIds())
                                ? spaceModel.getDeptIds() : new HashSet<>();
                if (!oriDepts.contains(e)) {
                    addDeptSets.add(e);
                }
            });
            addModel.setDeptIds(addDeptSets);
        }
        //原来没有的管理员id，入参有，就是新增
        if (CollectionUtils.isNotEmpty(spaceRequest.getAdminsOid())) {
            Set<String> addAdminSets = new HashSet<>();
            spaceRequest.getAdminsOid().forEach(e -> {
                Set<String> oriAdmins =
                        spaceModel != null && CollectionUtils.isNotEmpty(spaceModel.getAdminOids())
                                ? spaceModel.getAdminOids() : new HashSet<>();
                if (!oriAdmins.contains(e)) {
                    addAdminSets.add(e);
                }
            });
            addModel.setAdminOids(addAdminSets);
        }
        addSpaceDataMap.put(spaceRequest.getOrganOid(), addModel);
    }

    /**
     * 查询业务空间变更之前的数据，
     * @param input
     * @return Map<String, SpaceModifyModel>  key：企业oid，value:业务空间的部门列表，管理员列表
     */
    private Map<String, SpaceModifyModel> getSpaceOriData(SpaceDetailInput input) {
        SpaceRequestParam spaceRequestParam = new SpaceRequestParam();
        spaceRequestParam.setSpaceId(input.getSpaceId());
        spaceRequestParam.setOnlyOwner(false);
        spaceRequestParam.setOrganOuid(input.getOperatorOrganOid());
        EasunSpaceOutput spaceDetail = spaceAdapt.getSpaceDetail(spaceRequestParam);
        return getSpaceModel(spaceDetail);
    }


    /**
     * 业务空间的list 的item 的list,数据结构转为set,提高 contains 的效率
     * @param spaceDetail
     * @return Map<String, SpaceModifyModel>  key：企业oid，value:业务空间的部门列表，管理员列表
     */
    public Map<String, SpaceModifyModel> getSpaceModel(EasunSpaceOutput spaceDetail) {
        List<SpaceItemDetailOutput> spaceItems = spaceDetail.getList();
        Map<String, SpaceModifyModel> spaceDataMap = new HashMap<>();
        for (SpaceItemDetailOutput item : spaceItems) {
            SpaceModifyModel model = new SpaceModifyModel();
            model.setAdminOids(item.getMembers().stream().map(MemberDetail::getMemberOid)
                    .collect(Collectors.toSet()));
            model.setDeptIds(item.getDepts().stream().map(BizDeptDetailOutput::getDeptId)
                    .collect(Collectors.toSet()));
            spaceDataMap.put(item.getOrganOuid(), model);
        }
        return spaceDataMap;
    }


    /**
     *
     * 审计日志的参数获取
     * @param spaceDetailInput
     * @return
     */
    public SpaceUpdateParam getAuditParam(SpaceDetailInput spaceDetailInput){
        SpaceUpdateParam param = new SpaceUpdateParam();
        String resourceId = spaceDetailInput.getSpaceId();
        String resourceName = spaceDetailInput.getName();
        String roleNames = String.valueOf(
                LogRecordContext.getVariable(AuditLogConstants.ROLE_NAMES));
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        param.setRoleNames(roleNames);
        param.setResourceId(resourceId);
        param.setResourceName(resourceName);
        param.setRootOrgId(rootOrgId);
        return param;
    }

}
