package com.timevale.footstone.user.service.aop;

import com.google.common.collect.Maps;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUserUpdateInput;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.model.account.input.BizICUserDetailInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.inner.respdecoration.deco.decoraters.Decoration;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.timevale.account.service.model.constants.BuiltinContacts.EMAIL;
import static com.timevale.account.service.model.constants.BuiltinContacts.MOBILE;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_PSN_REALNAME_MOBILE;
import static com.timevale.footstone.user.consts.OpLogConsts.*;
import static com.timevale.footstone.user.consts.OpLogEnum.*;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;
import static com.timevale.footstone.user.service.utils.EasyCollectionUtils.forEachIfNotEmpty;

@Aspect
@Slf4j
@Component
public class OpLog4UserUpdateAspect extends OpLogAspectBaseService {
    @Autowired
    private AuditLogService logService;
    @Autowired
    private Decoration decorater;

    @Pointcut("execution(* com.timevale.easun.service.api.RpcIcUserPlusService.updateICUser(..))" +
            "||execution(* com.timevale.easun.service.api.RpcIcUserPlusService.updateDetail(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        Pair<Object, Throwable> result = getResult(pjp);
        //没有操作人过滤
        if (StringUtils.isBlank(RequestUtil.getHeader(HEADER_OPERATOR_ID))) {
            return returnResult(result);
        }
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        try {
            Object input = ((RpcInput<?>) pjp.getArgs()[0]).getInput();

            String oid;
            List<Property> properties = null;
            List<Property> contacts = null;
            if (input instanceof BizICUserUpdateInput) {
                BizICUserUpdateInput rInput = (BizICUserUpdateInput) input;
                oid = rInput.getIcUserId().getOuid();
                properties = rInput.getProperties();
                contacts = rInput.getContacts();
            } else if (input instanceof BizICUserDetailInput) {
                BizICUserDetailInput rInput = (BizICUserDetailInput) input;
                oid = rInput.getOuid();
                properties = rInput.getProperties();
                contacts = rInput.getContacts();
            } else {
                return returnResult(result);
            }
            Map<String, String> extParams = Maps.newHashMap();
            extParams.put(EXT_PARAMS_REQUEST_URI, RequestContext.getRequest().getRequestURI());
            forEachIfNotEmpty(properties, t -> {

                switch (t.getType()) {
                    case INFO_NAME: {
                        extParams.put(EXT_PARAMS_ACCOUNT_NAME, t.getValue());
                        logService.saveAuditLog(
                                OpLogUtils.logBuilder(UPDATE_NAME_INFO, oid, resultStr, oid, extParams));

                    }
                    break;
                    case INFO_PSN_REALNAME_MOBILE: {
                        extParams.put(EXT_PARAMS_EXACT_TYPE_REMARK, INFO_PSN_REALNAME_MOBILE);
                        extParams.put(EXT_PARAMS_NEW_MOBILE, decorater.decorate(DecorationType.MOBILE, t.getValue()));
                        logService.saveAuditLog(
                                OpLogUtils.logBuilder(UPDATE_MOBILE, oid, resultStr, oid, extParams));
                    }
                    break;
                    default:
                }
            });
            forEachIfNotEmpty(contacts, t -> {
                switch (t.getType()) {
                    case MOBILE:
                        extParams.put(EXT_PARAMS_EXACT_TYPE_REMARK, MOBILE);
                        extParams.put(EXT_PARAMS_NEW_MOBILE, decorater.decorate(DecorationType.MOBILE, t.getValue()));
                        logService.saveAuditLog(
                                OpLogUtils.logBuilder(UPDATE_MOBILE, oid, resultStr, oid, extParams));
                        break;
                    case EMAIL:
                        extParams.put(EXT_PARAMS_EXACT_TYPE_REMARK, EMAIL);
                        extParams.put(EXT_PARAMS_NEW_EMAIL, decorater.decorate(DecorationType.EMAIL, t.getValue()));
                        logService.saveAuditLog(
                                OpLogUtils.logBuilder(UPDATE_EMAIL, oid, resultStr, oid, extParams));
                        break;
                    default:
                }
            });
        } catch (Throwable e) {
            log.warn("opLog log failed when updateICUser,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }


}
