package com.timevale.footstone.user.service.aop;

import com.timevale.footstone.user.service.exception.CustomBizAuthException;
import com.timevale.footstone.user.service.model.WrapperResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

/**
 * 统一异常处理器
 */
@Slf4j(topic = "stacktrace")
@ControllerAdvice
public class CustomBizAuthExceptionHandler extends ResponseEntityExceptionHandler {



  /**
   * CustomBizAuthException异常处理
   */
  @ExceptionHandler({CustomBizAuthException.class})
  @ResponseBody
  public ResponseEntity<Object> handleCustomBizAuthException(
          CustomBizAuthException e, WebRequest request) {
    WrapperResponse result = new WrapperResponse(e.getNCode(), e.getMessage(), null);
    return handleExceptionInternal(e, result, null, HttpStatus.OK, request);
  }


}
