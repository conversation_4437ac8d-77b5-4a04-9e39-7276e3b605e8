package com.timevale.footstone.user.service.aop;

import com.alibaba.fastjson.JSONObject;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.user.service.aop.validator.ParamValidator;
import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.timevale.footstone.user.service.utils.IOUtil.GSON;

/** <AUTHOR> on 2018-05-30 */
@Aspect
@Component
@Order(3)
public class RestRecordAspect {

    private static Logger logger = LoggerFactory.getLogger(RestRecordAspect.class);

    @Pointcut("execution(* com.timevale.footstone.user.service.rest..*.*(..))")
    public void pointCut() {}

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {

        long start = System.currentTimeMillis();

        Object o = "";
        try {
            Object[] args = pjp.getArgs();
            for (Object arg : args) {
                if (arg instanceof ToString) {
                    ParamValidator.valid(arg);
                }
            }
            o = pjp.proceed();
            if (desensitive()&&BaseResult.class.isAssignableFrom(o.getClass())) {
                ObjectWalk.walkBean(o, walker);
            }
            return o;
        } catch (Throwable e) {
            o = e;
            if (e instanceof SaasCommonBizException) {
               return new WrapperResponse(((SaasCommonBizException) e).getErrCode(), e.getMessage(), null);
            }
            throw e;
        } finally {
            if(logger.isDebugEnabled()) {
                String targetMethod =
                        pjp.getSignature().getDeclaringTypeName() + "." + pjp.getSignature().getName();
                logger.debug("目标：{}", targetMethod);

                logger.debug("TraceId：{}", RequestContext.getTransactionId());

                Object[] args = pjp.getArgs();
                logger.debug("参数：{}", JSONObject.toJSONString(args));

                if (o instanceof Throwable) {
                    logger.debug("报错：{}", ((Throwable) o).getMessage());
                } else {
                    logger.debug("返回：{}", JSONObject.toJSONString(o));
                }
                long cost = System.currentTimeMillis() - start;
                logger.debug("耗时：" + cost + " ms");
            }
        }
    }

    @Autowired
    private DecorationWalker walker;

    private static final String HEADER_X_TSIGN_DESENSITIVE = "X-Tsign-desensitive";
    private boolean desensitive() {
        HttpServletRequest request = ((ServletRequestAttributes)
                RequestContextHolder.getRequestAttributes()).getRequest();
        String header = request.getHeader(HEADER_X_TSIGN_DESENSITIVE);
        if (StringUtils.isEmpty(header)) {
            return false;
        }

        try {
            return Boolean.valueOf(header);
        } catch (Exception e) {
            logger.warn("invalid head for desensitive. value:{}", header);
            return false;
        }
    }

}
