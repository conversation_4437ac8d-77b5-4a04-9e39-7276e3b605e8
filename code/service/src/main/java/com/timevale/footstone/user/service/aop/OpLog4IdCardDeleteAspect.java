package com.timevale.footstone.user.service.aop;

import com.google.common.collect.Maps;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.model.account.input.ThirdPartyBindInput;
import com.timevale.easun.service.model.account.input.ThirdPartyUnBindInput;
import com.timevale.easun.service.model.idcard.BizIdcardSetInput;
import com.timevale.easun.service.model.idcard.BizMultiIdcardDeleteInput;
import com.timevale.easun.service.model.mods.RpcOuidInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.consts.OpLogEnum;
import com.timevale.footstone.user.model.oplog.ThirdPartyModel;
import com.timevale.footstone.user.service.utils.EasyCollectionUtils;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.timevale.account.service.model.constants.BuiltinThirdparty.*;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.footstone.user.consts.OpLogConsts.*;
import static com.timevale.footstone.user.consts.OpLogEnum.*;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

@Aspect
@Slf4j
@Component
public class OpLog4IdCardDeleteAspect extends OpLogAspectBaseService {
    @Autowired
    private AuditLogService logService;

    @Pointcut("execution(* com.timevale.easun.service.api.RpcIdcardPlusService.deleteIdcard(..))" +
            "|| execution(* com.timevale.easun.service.api.RpcIcUserPlusService.thirdPartyUnbind(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        Pair<Object, Throwable> result = getResult(pjp);
        //没有操作人过滤
        if (StringUtils.isBlank(RequestUtil.getHeader(HEADER_OPERATOR_ID))) {
            return returnResult(result);
        }
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        try {
            Object arg0 = pjp.getArgs()[0];
            if (arg0 instanceof RpcOuidInput) {
                RpcOuidInput<BizMultiIdcardDeleteInput> input = ((RpcOuidInput<BizMultiIdcardDeleteInput>) arg0);
                String oid = input.getOuid();
                EasyCollectionUtils.forEachIfNotEmpty(input.getInput().getThirdparties(), t -> saveLog(oid,
                        t.getThirdpartyKey(), resultStr));
            } else {
                ThirdPartyUnBindInput input = ((RpcInput<ThirdPartyUnBindInput>) arg0).getInput();
                saveLog(input.getOuid(), input.getThirdpartyKey(), resultStr);
            }
        } catch (Throwable e) {
            log.warn("opLog log failed when deleteIdcard,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }

    private void saveLog(String oid, String thKey, String resultStr) {
        OpLogEnum logEnum = getOpLogEnum(thKey);
        if (null == logEnum) {
            return;
        }
        Map<String, String> extParams = Maps.newHashMap();
        extParams.put(EXT_PARAMS_REQUEST_URI, RequestContext.getRequest().getRequestURI());
        extParams.put(EXT_PARAMS_TARGET_OID, oid);
        logService.saveAuditLog(OpLogUtils.logBuilder(logEnum, oid, resultStr, thKey, extParams));
    }

    private OpLogEnum getOpLogEnum(String thKey) {
        OpLogEnum logEnum = null;
        switch (thKey) {
            case "WECHAT_OFFICIAL_ACCOUNT":
            case WE_CHAT:
                logEnum = WECHAT_UN_BIND;
                break;
            case ALI_PAY:
                logEnum = ALIPAY_UN_BIND;
                break;
            case DING_TALK:
                logEnum = DING_UN_BIND;
                break;
            default:
        }
        return logEnum;
    }

}
