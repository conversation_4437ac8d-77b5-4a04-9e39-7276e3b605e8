package com.timevale.footstone.user.service.aop;

import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_FAILED;
import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_SUCCESSED;
import static com.timevale.footstone.user.consts.OpLogEnum.START_SSO_CONNECTOR;
import static com.timevale.footstone.user.consts.OpLogEnum.STOP_SSO_CONNECTOR;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_TENANT_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

import com.google.common.collect.Maps;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.footstone.user.service.constants.audit.AuditLogConstants;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoOrgConnectorManageAdapt;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.token.service.model.input.BizSsoConnectorInput;
import com.timevale.token.service.model.output.SsoOrganizationConnectorConfigOutput;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2024/6/17 10:44
 */

@Aspect
@Slf4j
@Component
public class Oplog4SsoEnableAspect extends OpLogAspectBaseService {


    @Autowired
    private AuditLogService logService;

    @Autowired
    private RpcSsoOrgConnectorManageAdapt ssoOrgConnectorManageAdapt;

    //
    @Pointcut("execution(* com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoDomainServiceAdapt.enableSsoConnector(..))")
    public void pointCut() {
    }


    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        BizSsoConnectorInput ssoRequest = (BizSsoConnectorInput) pjp.getArgs()[0];
        Pair<Object, Throwable> result = new MutablePair<>();
        try {
            SsoOrganizationConnectorConfigOutput config = ssoOrgConnectorManageAdapt.getConnectorConfigById(
                    ssoRequest.getId());
            result = getResult(pjp);
            saveSsoConnectorOperator(ssoRequest, result, config);
        } catch (Exception e) {
            log.warn("save sso op log error", e);
        }
        return returnResult(result);
    }


    private void saveSsoConnectorOperator(BizSsoConnectorInput connectorInput,
            Pair<Object, Throwable> result, SsoOrganizationConnectorConfigOutput config) {
        String roleNames = (String) LogRecordContext.getVariables()
                .get(AuditLogConstants.ROLE_NAMES);
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        String resultStr = null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED;
        Map<String, String> extParams = Maps.newHashMap();
        //启用sso连接器
        AuditLog auditLog = null;
        if (connectorInput.getEnabled()) {
            auditLog = OpLogUtils.logBuilder(START_SSO_CONNECTOR, rootOrgId, resultStr,
                    String.valueOf(config.getId()), config.getName(),
                    config.getOrganOuid(), roleNames, extParams);
        } else {
            //停用sso连接器
            auditLog = OpLogUtils.logBuilder(STOP_SSO_CONNECTOR, rootOrgId, resultStr,
                    String.valueOf(config.getId()), config.getName(),
                    config.getOrganOuid(), roleNames, extParams);
        }
        logService.saveAuditLog(auditLog);
    }


}
