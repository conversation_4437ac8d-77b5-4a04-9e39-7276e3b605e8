package com.timevale.footstone.user.service.aop;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Enumeration;
import java.util.Map;

import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.footstone.user.consts.OpLogConsts.*;
import static com.timevale.footstone.user.consts.OpLogEnum.UPDATE_PASSWORD;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;
import static com.timevale.footstone.user.service.utils.RequestUtil.getHeaders;

@Aspect
@Slf4j
@Component
public class OpLog4PasswordUpdateAspect extends OpLogAspectBaseService {
    @Autowired
    private AuditLogService logService;

    @Pointcut("execution(* com.timevale.easun.service.api.RpcIdcardPlusService.setAllPassword(..)) ||" +
            "execution(* com.timevale.easun.service.api.RpcSenderAuthService.senderResetPwdExecute(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        Pair<Object, Throwable> result = getResult(pjp);
        String oid = RequestUtil.getHeader(HEADER_OPERATOR_ID);
        //没有操作人过滤
        if (StringUtils.isBlank(oid)) {
            return returnResult(result);
        }
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        try {
            Map<String, String> extParams = Maps.newHashMap();
            extParams.put(EXT_PARAMS_REQUEST_URI, RequestContext.getRequest().getRequestURI());
            logService.saveAuditLog(
                    OpLogUtils.logBuilder(UPDATE_PASSWORD, oid, resultStr, oid, extParams));
        } catch (Throwable e) {
            log.warn("opLog log failed when setAllPassword,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }

}
