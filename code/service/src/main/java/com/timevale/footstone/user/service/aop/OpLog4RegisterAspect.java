package com.timevale.footstone.user.service.aop;

import com.google.common.collect.Maps;
import com.timevale.account.service.model.service.biz.acc.output.BizAccountOutput;
import com.timevale.account.service.model.service.biz.helper.acc.BizICUserOutputHelper;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.easun.service.model.account.output.BizTokenOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.consts.OpLogEnum;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.model.account.response.CreateOrgResponse;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static com.timevale.footstone.user.consts.OpLogConsts.*;
import static com.timevale.footstone.user.consts.OpLogEnum.CREATE_ORG;
import static com.timevale.footstone.user.consts.OpLogEnum.CREATE_PERSON;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

@Aspect
@Slf4j
@Component
public class OpLog4RegisterAspect extends OpLogAspectBaseService {
    @Autowired
    private AuditLogService logService;
    @Autowired
    private IdAdapt idAdapt;
    @Autowired
    private RpcIcUserPlusServiceAdapt icUserPlusService;
    @Pointcut("execution(* com.timevale.easun.service.api.RpcSenderAuthService.senderRegistExecute(..))" +
            "||execution(* com.timevale.easun.service.api.RpcSenderAuthService.senderRegistLoginExecute(..))" +
            "||execution(* com.timevale.footstone.user.service.inner.biz.BizOrganService.createOrg(..))" +
            "||execution(* com.timevale.easun.service.api.RpcOrgPlusService.createICOrgAndCompanyWithSource(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        Pair<Object, Throwable> result = getResult(pjp);
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        HttpServletRequest req = RequestContext.getRequest();
        if (null == req) {
            return returnResult(result);
        }
        try {
            String pointName = pjp.getSignature().getName();
            String oid = null;
            OpLogEnum logEnum = CREATE_PERSON;
            switch (pointName) {
                case "senderRegistExecute": {
                    RpcOutput<BizAccountOutput> output = (RpcOutput<BizAccountOutput>) result.getLeft();
                    String accountUid = output.getData().getBase().getAccountUid();
                    oid = idAdapt.accountId(accountUid);
                }
                break;
                case "senderRegistLoginExecute": {
                    RpcOutput<BizTokenOutput> output = (RpcOutput<BizTokenOutput>) result.getLeft();
                    if (Objects.equals(Boolean.FALSE,output.getData().getCreateStatus())){
                        return returnResult(result);
                    }
                    String accountUid = output.getData().getAccountUid();
                    oid = idAdapt.accountId(accountUid);
                }
                break;
                case "createOrg": {
                    CreateOrgResponse resp = (CreateOrgResponse) result.getLeft();
                    oid = resp.getOrgId();
                    logEnum = CREATE_ORG;
                }
                break;
                case "createICOrgAndCompanyWithSource": {
                    RpcOutput<BizOrganOuid> output = (RpcOutput<BizOrganOuid>) result.getLeft();
                    oid = output.getData().getOuid();
                    logEnum = CREATE_ORG;
                }
                break;
                default:
            }
            BizICUserOutput output = icUserPlusService.getICUserByOuid(oid);
            BizICUserOutputHelper helper = new BizICUserOutputHelper(output);
            Map<String, String> extParams = Maps.newHashMap();
            extParams.put(EXT_PARAMS_REQUEST_URI, req.getRequestURI());
            extParams.put(EXT_PARAMS_ACCOUNT_NAME, helper.getPropertyValue(INFO_NAME));
            String operOid = RequestUtil.getHeader(HEADER_OPERATOR_ID);
            AuditLog a = OpLogUtils.logBuilder(logEnum, StringUtils.isBlank(operOid) ? oid : operOid,
                    resultStr, oid, extParams);
            //注册可能没有操作人
            if (StringUtils.isBlank(a.getUserUnique1())) {
                a.setEnterpriseSpaceUnique1(oid);
                a.setUserUnique1(oid);
            }
            logService.saveAuditLog(a);
        } catch (Throwable e) {
            log.warn("opLog log failed when register,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }


}
