package com.timevale.footstone.user.service.aop;

import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_FAILED;
import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_SUCCESSED;
import static com.timevale.footstone.user.consts.OpLogEnum.CHANGE_ORGAN_VISIBLE;
import static com.timevale.footstone.user.consts.OpLogEnum.RELATE_SSO_DOMAIN;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_TENANT_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

import com.google.common.collect.Maps;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.footstone.user.service.constants.audit.AuditLogConstants;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.model.organization.response.OrganBaseExtendResponse;
import com.timevale.footstone.user.service.model.sso.manage.request.SsoSaveOrgConnectorRefRequest;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.footstone.user.service.utils.conv.CommonConverter;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2024/6/17 11:36
 */

@Aspect
@Slf4j
@Component
public class OpLog4SsoLoginAspect extends OpLogAspectBaseService {


    @Autowired
    private AuditLogService logService;

    @Autowired
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;


    //
    @Pointcut("execution(* com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoConnectorManageServiceImpl.saveSsoConnectorRef(..))")
    public void pointCut() {
    }


    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        SsoSaveOrgConnectorRefRequest ssoRequest = (SsoSaveOrgConnectorRefRequest) pjp.getArgs()[0];
        Pair<Object, Throwable> result = new MutablePair<>();
        try {
            result = getResult(pjp);
            if (CollectionUtils.isEmpty(ssoRequest.getOrgIds())) {
                return returnResult(result);
            }
            Map<String, EsOrgSearhOutput> orgMap = esAccountServiceAdapt.searchByOrgIds(
                    ssoRequest.getOrgIds());
            //保存关联域名
            saveConnectDomain(ssoRequest, result, orgMap);
            return returnResult(result);
        } catch (Exception e) {
            log.warn("save sso op log error", e);
        } finally {
            return returnResult(result);
        }
    }

    private void saveConnectDomain(SsoSaveOrgConnectorRefRequest ssoRequest, Pair<Object, Throwable> result,
            Map<String, EsOrgSearhOutput> orgMap) {
        if (CollectionUtils.isNotEmpty(ssoRequest.getDomainList())) {
            for (String orgId : ssoRequest.getOrgIds()) {
                EsOrgSearhOutput targetOrgan = orgMap.get(orgId);
                if (targetOrgan == null || targetOrgan.getUser() == null
                        || targetOrgan.getUser().getId() == null) {
                    continue;
                }
                saveConnectorOrg(targetOrgan, ssoRequest, result);
                saveVisibleConfig(targetOrgan, ssoRequest, result);
            }
        }
    }


    private void saveConnectorOrg(EsOrgSearhOutput targetOrgan, SsoSaveOrgConnectorRefRequest ssoRequest,
            Pair<Object, Throwable> result) {
        String roleNames = (String) LogRecordContext.getVariables()
                .get(AuditLogConstants.ROLE_NAMES);
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        Map<String, String> extParams = Maps.newHashMap();
        String organName = CommonConverter.getPropertyValueByType(targetOrgan.getUser().getProperties(), INFO_NAME);
        //修改sso域名
        extParams.put("organName", organName);
        extParams.put("ssoDomain", StringUtils.join(ssoRequest.getDomainList()));
        AuditLog a = OpLogUtils.logBuilder(RELATE_SSO_DOMAIN, rootOrgId, resultStr,
                targetOrgan.getUser().getId().getOuid(), organName, ssoRequest.getMainOrgId(), roleNames, extParams);
        logService.saveAuditLog(a);

    }


    private void saveVisibleConfig(EsOrgSearhOutput targetOrgan, SsoSaveOrgConnectorRefRequest ssoRequest,
            Pair<Object, Throwable> result) {
        String roleNames = (String) LogRecordContext.getVariables()
                .get(AuditLogConstants.ROLE_NAMES);
        String organName  = CommonConverter.getPropertyValueByType(targetOrgan.getUser().getProperties(), INFO_NAME);
        String resourceId = targetOrgan.getUser().getId().getOuid();
        String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        Map<String, String> extParams = Maps.newHashMap();
        //修改sso域名
        extParams.put("organName", organName);
        extParams.put("organVisible", String.valueOf(ssoRequest.getOnlySsoLoginPermit()));
        AuditLog a = OpLogUtils.logBuilder(CHANGE_ORGAN_VISIBLE, rootOrgId, resultStr, resourceId
                , organName, ssoRequest.getMainOrgId(), roleNames, extParams);
        logService.saveAuditLog(a);
    }

}
