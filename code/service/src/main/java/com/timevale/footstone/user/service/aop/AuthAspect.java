package com.timevale.footstone.user.service.aop;

import com.timevale.account.organization.service.model.service.biz.input.BizThirdOrganGeneralInput;
import com.timevale.account.service.exception.Errors;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.AppIdInvalide;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.AppNotAlowedQueryOrgRealException;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.OrganNotAuthorization;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAuthenticationServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.oem.RpcOAuthRecordServiceAdapt;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.open.platform.oauth.enums.OAuthRecordTypeEnum;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

/**
 * 鉴权拦截层
 *
 * <p>用于 开放API的鉴权
 *
 * <AUTHOR>
 * @since 2021/11/18
 */
@Aspect
@Component
@Order(10)
@Slf4j
public class AuthAspect {

    private String stdAppId;
    private static final String ORG_ID = "orgId";

    private RpcOAuthRecordServiceAdapt authenticationServiceAdapt;
    private RpcIcUserPlusServiceAdapt userPlusServiceAdapt;

    @Autowired
    public void setAuthenticationServiceAdapt(
            RpcOAuthRecordServiceAdapt authenticationServiceAdapt) {
        this.authenticationServiceAdapt = authenticationServiceAdapt;
    }

    @Autowired
    public void setUserPlusServiceAdapt(RpcIcUserPlusServiceAdapt userPlusServiceAdapt) {
        this.userPlusServiceAdapt = userPlusServiceAdapt;
    }

    @Pointcut(
            ""
                    + "execution(* com.timevale.footstone.user.service.rest.MemberRest.createMembers(..)) ||"
                    + " execution(* com.timevale.footstone.user.service.rest.MemberRest.deleteMember(..)) ||"
                    + " execution(* com.timevale.footstone.user.service.rest.MemberRest.getMemberListPageable(..)) ||"
                    + " execution(* com.timevale.footstone.user.service.rest.MemberRest.administrators(..))")
    public void pointCut() {
        /*
         拦截点: 需要开放的API methods
        */
    }

    @Before("pointCut()")
    public void beforeAll(JoinPoint point) {
        String appId = RequestContext.getAppId();
        if (StringUtils.isEmpty(appId)) {
            appId = stdAppId;
        }
        ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        Map<Object, Object> map =
                (Map<Object, Object>)
                        request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
        String orgId = Objects.isNull(map) ? StringUtils.EMPTY : map.get(ORG_ID).toString();

        /*
        * 账号是否存在校验 (用于兼容: 鉴权校验时 没判断账号是否存在)
         */
        userPlusServiceAdapt.getICUserBaseByOuid(orgId);


        boolean authorized =
                authenticationServiceAdapt.checkAuthorized(orgId, appId, OAuthRecordTypeEnum.ORGAN);
        if (!authorized) {
            log.warn("Organization({}) do NOT authorize app({}).", orgId, appId);
            throw new OrganNotAuthorization();
        }
    }

    @Value("${gateway.app.id}")
    public void setStdAppId(String stdAppId) {
        this.stdAppId = stdAppId;
    }
}
