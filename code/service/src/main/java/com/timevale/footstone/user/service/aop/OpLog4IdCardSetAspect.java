package com.timevale.footstone.user.service.aop;

import com.google.common.collect.Maps;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.model.account.input.ThirdPartyBindInput;
import com.timevale.easun.service.model.idcard.BizIdcardSetInput;
import com.timevale.easun.service.model.mods.RpcOuidInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.consts.OpLogEnum;
import com.timevale.footstone.user.model.oplog.ThirdPartyModel;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.timevale.account.service.model.constants.BuiltinThirdparty.*;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.footstone.user.consts.OpLogConsts.*;
import static com.timevale.footstone.user.consts.OpLogEnum.*;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

@Aspect
@Slf4j
@Component
public class OpLog4IdCardSetAspect extends OpLogAspectBaseService {
    @Autowired
    private AuditLogService logService;

    @Pointcut("execution(* com.timevale.easun.service.api.RpcIdcardPlusService.set(..))" +
            "|| execution(* com.timevale.easun.service.api.RpcIcUserPlusService.thirdPartyBind(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        Pair<Object, Throwable> result = getResult(pjp);
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        try {

            ThirdPartyModel model = getThModel((RpcInput) pjp.getArgs()[0]);
            if (null == model || null == model.getLogEnum()) {
                return returnResult(result);
            }
            Map<String, String> extParams = Maps.newHashMap();
            extParams.put(EXT_PARAMS_REQUEST_URI, RequestContext.getRequest().getRequestURI());
            extParams.put(EXT_PARAMS_TARGET_OID, model.getOid());

            AuditLog logDTO = OpLogUtils.logBuilder(model.getLogEnum(), model.getOid(), resultStr,
                    model.getThUserId(), extParams);
            //没有操作人
            if (StringUtils.isBlank(logDTO.getUserUnique1())) {
                logDTO.setUserUnique1(model.getOid());
            }
            logService.saveAuditLog(logDTO);
        } catch (Throwable e) {
            log.warn("opLog log failed when setIdCard,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }

    private ThirdPartyModel getThModel(RpcInput rpcInput) {
        ThirdPartyModel model = null;
        if (rpcInput instanceof RpcOuidInput) {
            RpcOuidInput<BizIdcardSetInput> input = ((RpcOuidInput<BizIdcardSetInput>) rpcInput);
            IdcardContentThirdparty idcard = input.getInput().getIdcards().getThirdparty();
            if (null == idcard || null == idcard.getDetail()) {
                return null;
            }
            IdcardThirdparty idcardDetail = (IdcardThirdparty) idcard.getDetail();
            model = new ThirdPartyModel(input.getOuid(), getOpLogEnum(idcardDetail.getThirdpartyKey()),
                    idcardDetail.getThirdpartyUserId());
        } else {
            RpcInput<ThirdPartyBindInput> input = ((RpcInput<ThirdPartyBindInput>) rpcInput);
            model = new ThirdPartyModel(input.getInput().getOuid(), getOpLogEnum(input.getInput().getThirdpartyKey())
                    , input.getInput().getThirdpartyUserId());
        }
        return model;
    }

    private OpLogEnum getOpLogEnum(String thKey) {
        OpLogEnum logEnum = null;
        switch (thKey) {
            case "WECHAT_OFFICIAL_ACCOUNT":
            case WE_CHAT:
                logEnum = WECHAT_BIND;
                break;
            case ALI_PAY:
                logEnum = ALIPAY_BIND;
                break;
            case DING_TALK:
                logEnum = DING_BIND;
                break;
            default:
        }
        return logEnum;
    }
}
