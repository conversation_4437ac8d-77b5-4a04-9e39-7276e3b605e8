package com.timevale.footstone.user.service.aop;

import com.timevale.footstone.user.service.inner.biz.BizAccountService;
import com.timevale.footstone.user.service.utils.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;

@Slf4j
@Service
public class OpLogAspectBaseService {
    @Autowired
    private BizAccountService bizAccountService;

    protected String getEnterpriseSpaceUnique1() {
        String operId = RequestUtil.getHeader(HEADER_OPERATOR_ID);
        if (StringUtils.isBlank(operId)) {
            return operId;
        }
        String mainOrgId = bizAccountService.getMainOrg(operId).getOrganId();
        return StringUtils.isBlank(mainOrgId) ? operId : mainOrgId;
    }


}
