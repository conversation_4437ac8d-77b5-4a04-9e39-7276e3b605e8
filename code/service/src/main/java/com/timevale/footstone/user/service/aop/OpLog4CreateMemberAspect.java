package com.timevale.footstone.user.service.aop;

import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_FAILED;
import static com.timevale.footstone.user.consts.OpLogConsts.RESULT_SUCCESSED;
import static com.timevale.footstone.user.consts.OpLogEnum.CREATE_MEMBER;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_TENANT_ID;
import static com.timevale.footstone.user.service.utils.AopUtils.getResult;
import static com.timevale.footstone.user.service.utils.AopUtils.returnResult;

import com.google.common.collect.Maps;
import com.timevale.dayu.sdk.bean.AuditLog;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.footstone.user.consts.OpLogEnum;
import com.timevale.footstone.user.service.constants.audit.AuditLogConstants;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.utils.OpLogUtils;
import com.timevale.footstone.user.service.utils.RequestUtil;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2025/3/17 11:16
 */

@Aspect
@Slf4j
@Component
public class OpLog4CreateMemberAspect extends OpLogAspectBaseService {

    @Autowired
    private AuditLogService logService;

    @Autowired
    private RpcIcUserPlusServiceAdapt rpcIcUserPlusServiceAdapt;

    private static final String NAME_KEY = "name";


    @Pointcut("execution(* com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt.createMember(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object invokeLog(ProceedingJoinPoint pjp) throws Throwable {
        String name = null, orgId = null,accountId = null;
        try {
            orgId = (String) pjp.getArgs()[0];
            accountId = (String) pjp.getArgs()[1];
            if (StringUtils.isNotBlank(accountId)) {
                name = this.rpcIcUserPlusServiceAdapt.getNameByOid(accountId);
            }
        } catch (Throwable e) {
            log.warn("opLog log failed when get member,params:{}", pjp.getArgs()[0], e);
        }
        Pair<Object, Throwable> result = getResult(pjp);
        if (result.getRight() != null) {
            return returnResult(result);
        }
        //没有操作人过滤
        String oid = RequestUtil.getHeader(HEADER_OPERATOR_ID);
        String resultStr = (null != result.getRight() ? RESULT_FAILED : RESULT_SUCCESSED);
        try {
            OpLogEnum logEnum = CREATE_MEMBER;
            Map<String, String> extParams = Maps.newHashMap();
            extParams.put(NAME_KEY, name);
            String roleNames = (String) LogRecordContext.getVariables().get(AuditLogConstants.ROLE_NAMES);
            String resourceName = (String) LogRecordContext.getVariables().get(AuditLogConstants.RESOURCE_NAME);
            String rootOrgId = RequestUtil.getHeader(HEADER_TENANT_ID);
            AuditLog logDTO = OpLogUtils.logBuilder(logEnum, rootOrgId, resultStr, accountId,resourceName, orgId, roleNames, extParams);
            logService.saveAuditLog(logDTO);
        } catch (Throwable e) {
            log.warn("opLog log failed when ContactUpdate,params:{}", pjp.getArgs()[0], e);
        }
        return returnResult(result);
    }
}
