<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.timevale.footstone.user</groupId>
        <artifactId>footstone-user-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>footstone-user-deploy</artifactId>
    <name>footstone-user/deploy</name>
    <packaging>jar</packaging>

    <properties>
        <capuslue.maven.plugin.version>1.5.1</capuslue.maven.plugin.version>
        <cat.agent.version>1.2.7-SNAPSHOT</cat.agent.version>
        <aspectj.version>1.8.13</aspectj.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>28.1-jre</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>footstone-user-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.footstone</groupId>
            <artifactId>footstone-identity-common-service-facade</artifactId>
            <version>2.7.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale</groupId>
                    <artifactId>open-identity-account</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.10.0</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.10.0</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.14.5</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.14.5</version>
        </dependency>

        <!-- 部署工具 -->
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>capsule-linux-daemon</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>footstone-user-api-capsule</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.10.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <mainClass>com.timevale.footstone.user.deploy.Application</mainClass>
                    <jvmArguments>
                        -server -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8888
                    </jvmArguments>
                </configuration>
            </plugin>



            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M3</version>
                <configuration>
                    <argLine>
                        -javaagent:"${settings.localRepository}/org/aspectj/aspectjweaver/${aspectj.version}/aspectjweaver-${aspectj.version}.jar"
                    </argLine>
                    <argLine>${surefireArgLine}</argLine>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <propertyName>surefireArgLine</propertyName>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/env/**</exclude>
                    <exclude>application.yml</exclude>
                    <exclude>systemconfig.properties</exclude>
                </excludes>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources/env/dev</directory>
                    </resource>
                </resources>
            </build>

        </profile>
        <profile>
            <id>test</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources/env/test</directory>
                    </resource>
                </resources>
            </build>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>pre</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources/env/pre</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources/env/prod</directory>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
</project>