package com.timevale.unittest.footstone.tc;

import org.bouncycastle.asn1.ASN1Object;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.crypto.AsymmetricBlockCipher;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.encodings.PKCS1Encoding;
import org.bouncycastle.crypto.engines.RSAEngine;
import org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import org.bouncycastle.crypto.util.PublicKeyFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/** <AUTHOR> on 2020-04-21 */
public class LocalRsaUtil {

    /**
     * BASE64加密
     *
     * @param data 要加密的数据
     * @return 加密后的字符串
     */
    private static String encryptBASE64(byte[] data) {
        return Base64.getEncoder().encodeToString(data);
    }

    /**
     * BASE64解密
     *
     * @param data 已加密的数据
     * @return 加密前的数据
     */
    private static byte[] decryptBASE64(String data) {
        return Base64.getDecoder().decode(data);
    }

    /**
     * 加密
     *
     * @param str 加密字符串
     * @param key 秘钥
     * @return 密文
     * @throws IOException
     * @throws InvalidCipherTextException
     */
    public static String encryptByKey(String str, String key)
            throws IOException, InvalidCipherTextException {
        AsymmetricBlockCipher engine = new RSAEngine();
        PKCS1Encoding cipher = new PKCS1Encoding(engine);
        byte[] publicInfoBytes = decryptBASE64(key);
        ASN1Object pubKeyObj = ASN1Primitive.fromByteArray(publicInfoBytes);
        AsymmetricKeyParameter pubKey =
                PublicKeyFactory.createKey(SubjectPublicKeyInfo.getInstance(pubKeyObj));

        cipher.init(true, pubKey);
        byte[] data = str.getBytes(StandardCharsets.UTF_8);
        byte[] encryptDataBytes = cipher.processBlock(data, 0, data.length);
        String encryptData = encryptBASE64(encryptDataBytes);
        return encryptData;
    }

    public static void main(String[] args) throws Exception {
        String data = "{\"startTime\":1587475427,\"timeout\":60,\"userId\":\"1290713341631961\"}";
        String key =
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkg4Qy7eb1bom4b9+rKAx4MRF2HNr9CyINglA8SvKzB48YhU2G"
                        + "/iCcQXH1xNCVgtpD/VfMro4gt0daxQ49oQ11EeJdzrQXXO+TsJXlXG/50Ogmw/1cWx5Nn16fs71KLAeq/XCTk/NgW2QuJuOxsDuKwak1C+NMvqdbSZeo9AeceAw6QaKx/IdDN6pSKTdMXFS0+zAP66UF1NDBT+cmrn/WuqeAouHQeD2Bai2nRY8pI6Kh61eyvuDnZIOiIuGeToU9cWE0OSqmhQyeL8drRFGeB388Lw+vYaA5aF1771KmEm4UYfuYqZ/7sdDdqw6UUrWBNifU6Tcgub4g7UMeDhUtQIDAQAB";
        String eData = encryptByKey(data, key);
        System.out.println(eData);
    }
}
