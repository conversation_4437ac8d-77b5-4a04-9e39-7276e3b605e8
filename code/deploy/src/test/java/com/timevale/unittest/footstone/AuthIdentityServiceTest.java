//package com.timevale.unittest.footstone;
//
//import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
//import com.timevale.footstone.user.service.api.AuthIdentityService;
//import com.timevale.footstone.user.service.model.WrapperResponse;
//import com.timevale.footstone.user.service.model.auth.AuthRedirectModel;
//import com.timevale.footstone.user.service.model.auth.AuthRedirectResponse;
//import com.timevale.footstone.user.service.model.enums.AuthIdentityScene;
//import com.timevale.unittest.footstone.configuration.Application;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
///**
// * <AUTHOR>
// * @since 2022-03-16 19:30
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class AuthIdentityServiceTest {
//
//
//    @Autowired private AuthIdentityService authIdentityService;
//
//    @Test
//    public void   authRedirect(){
//        AuthRedirectModel model = new AuthRedirectModel();
//        model.setAuthType(AuthIdentityScene.PSN_WILL.name());
//        model.setOid("df5ae91f86554b25806c494eb030bd98");
//        WrapperResponse<AuthRedirectResponse> wrapperResponse = authIdentityService.authRedirect(model);
//        Assert.assertTrue(wrapperResponse.ifSuccess());
//        Assert.assertTrue(wrapperResponse.getData().getReturnUrl().length() > 0);
//
//
//        AuthRedirectModel modelFail = new AuthRedirectModel();
//        modelFail.setAuthType(AuthIdentityScene.PSN_WILL.name());
//
//        try{
//            WrapperResponse<AuthRedirectResponse> wrapperResponseFail = authIdentityService.authRedirect(modelFail);
//            Assert.assertFalse(wrapperResponseFail.ifSuccess());
//        }catch (ErrorsBase.MissingArgumentsWith e){
//            Assert.assertTrue(true);
//            return;
//        }
//        Assert.assertTrue(false);
//
//    }
//}
