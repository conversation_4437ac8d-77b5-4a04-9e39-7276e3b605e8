package com.timevale.unittest.footstone;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.sender.output.BizApplyWillAssembleAuthOutput;
import com.timevale.easun.service.model.sender.output.BizApplyWillAuthOutput;
import com.timevale.easun.service.model.sender.output.BizSenderWillAssembleServiceOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.identity.common.service.api.common.RpcIndividualAuthService;
import com.timevale.footstone.identity.common.service.response.scene.webpage.IdentityAuthUrlResponse;
import com.timevale.footstone.user.service.component.SenderControlComponent;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizSenderAuthService;
import com.timevale.footstone.user.service.inner.geetest.GeetestRobotAuthService;
import com.timevale.footstone.user.service.inner.impl.biz.BizAccountServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.ShortLinkServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcSenderAuthServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.model.OperatorTimesLimit;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.senderauth.*;
import com.timevale.footstone.user.service.model.account.senderauth.request.AdminTransferRequest;
import com.timevale.footstone.user.service.model.account.senderauth.request.WillAssembleAuthApplyRequest;
import com.timevale.footstone.user.service.model.account.senderauth.request.WillAssembleAuthVerifyRequest;
import com.timevale.footstone.user.service.model.account.senderauth.request.WillAuthApplyRequest;
import com.timevale.footstone.user.service.model.account.senderauth.response.SenderWillAssembleCheckResponse;
import com.timevale.footstone.user.service.model.account.senderauth.response.WillAssembleAuthApplyResponse;
import com.timevale.footstone.user.service.model.account.senderauth.response.WillAuthApplyResponse;
import com.timevale.footstone.user.service.rest.SenderAuthRest;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.footstone.user.service.utils.UUIDUtils;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/10/24 18:32
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class SendAuthMockTest {

    @InjectMocks
    SenderAuthRest rest;


    @Mock
    private IdAdapt idAdapt;

    @Mock
    private ShortLinkServiceAdapt shortLinkService;

    @Mock
    private HttpServletRequest servletRequest;

    @Mock
    private GeetestRobotAuthService geetestRobotAuthService;

    @Mock
    private BizSenderAuthService bizSenderAuthService;

    @Mock
    SenderControlComponent senderControlComponent;

    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

    @Mock
    private RpcSenderAuthServiceAdapt senderAuthServiceAdapt;

    @Mock
    private AuditLogCommon auditLogCommon;

    @Mock
    private RpcIndividualAuthService rpcIndividualAuthService;



    @Mock
    private BizAccountServiceImpl bizAccountService;

    @Mock
    private RpcSenderAuthService svc;

    @Mock
    private Tedis tedis;

    @Before
    public void setUp(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR,"operator");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,UUID.randomUUID().toString().replace("-",""));
        servletRequest.setAttribute(HeaderConstants.HEADER_APP_ID,UUID.randomUUID().toString().replace("-",""));
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR,"operator");
        ReflectionTestUtils.setField(rest,"servletRequest",servletRequest);
        ReflectionTestUtils.setField(rest,"ipOrIdNoChekck",true);
        when(geetestRobotAuthService.validRobotResult(any(),any())).thenReturn(BaseResult.success(true));
        when(geetestRobotAuthService.validResult(any(),any())).thenReturn(BaseResult.success(true));
        StringOperations<String, Map<String,Object>> stringOperations = new MyDefaultStringOperations();
        Map<String,Object> organMap = new HashMap<>();
        stringOperations.set("legal_check_result_memberOid",organMap);
        when(tedis.string()).thenReturn(stringOperations);
        ReflectionTestUtils.setField(tedis,"valueOps",stringOperations);
        TedisUtil.setTedis(tedis);
        IdentityAuthUrlResponse response = new IdentityAuthUrlResponse();
        when(rpcIndividualAuthService.createIndividualIdentityAuthUrl(any())).thenReturn(BaseResult.success(response));
        BizApplyWillAuthOutput output = new BizApplyWillAuthOutput();
        output.setFaceUrl("wwww.baidu.com");
        output.setFaceToken(UUIDUtils.getUUID());
        when(svc.willAuthApply(any())).thenReturn(RpcOutput.with(output));
        BizApplyWillAssembleAuthOutput output1 = new BizApplyWillAssembleAuthOutput();
        output1.setAuthUrl("www.baidu.com");
        when(svc.willAssembleAuthApply(any())).thenReturn(RpcOutput.with(output1));
        BizSenderWillAssembleServiceOutput assembleServiceOutput = new BizSenderWillAssembleServiceOutput("APPLY","servcieId");
        assembleServiceOutput.setServiceId("serviceID");
        when(svc.willAssembleAuthCommit(any())).thenReturn(RpcOutput.with(assembleServiceOutput));
        OperatorTimesLimit limit = new OperatorTimesLimit();
        limit.setIpKey("ipKey");
        limit.setIdNoKey("idNokey");
        when(bizAccountService.getLastTimes(any(),any(),anyBoolean(),any(),any())).thenReturn(limit);
    }


    @Test
    public void testAdminTransfer() {
        String liyinAccountUid = "c632f31cde0e4763a38bb4e191f018c7";
        String liyinoid = "7882bc9368014ca6a8ab49bd1ad9ce42";
        String xiaozhaoAccountUid = "592784afad344c4fb7fa5cc39f79dcab";
        String xiaozhaoOid = "3bc25a2489a440999e0589248ea55182";
        String organId = "939d98d082134a918e5694b04d05fd02";
        AdminTransferRequest transferRequest = new AdminTransferRequest();
//        transferRequest.setNewAdminAccountId(xiaozhaoAccountUid);
        transferRequest.setOrgId(organId);
//        transferRequest.setSwitchedAccountId(liyinoid);
        transferRequest.setServiceId(UUID.randomUUID().toString().replace("-", ""));
        rest.adminTransfer(transferRequest);

    }


    @Test
    public void getPersonIdentityUrl() {
        PersonIdentityUrlRequest request = new PersonIdentityUrlRequest();
        FindAccountRequest findAccountRequest = new FindAccountRequest();
        FindAccountContextInfo findAccountContextInfo = new FindAccountContextInfo();
        findAccountContextInfo.setRedirectUrl("www.baidu.com");
        findAccountRequest.setContextInfo(findAccountContextInfo);
        FindAccountPsnModel psnModel = new FindAccountPsnModel();
        psnModel.setName("name");
        psnModel.setCertNo("371***********3434");
        findAccountRequest.setIndivInfo(psnModel);
        request.setIndividualIdentityAuthUrlRequest(findAccountRequest);
        GeetestRobotAuthVerifyModel authVerifyModel = new GeetestRobotAuthVerifyModel();
        authVerifyModel.setGeetest_validate(UUID.randomUUID().toString().replace("-",""));
        authVerifyModel.setGeetest_challenge(UUID.randomUUID().toString().replace("-",""));
        authVerifyModel.setGeetest_seccode(UUID.randomUUID().toString().replace("-",""));
        request.setRobotModel(authVerifyModel);
        WrapperResponse<IdentityAuthUrlResponse> response = rest.getPersonIdentityUrl(request);
//        Assert.assertTrue(response.getData() != null);
    }


    @Test
    public void testWillAuthApply(){
        WillAuthApplyRequest willAuthApplyRequest = new WillAuthApplyRequest();
        willAuthApplyRequest.setBizType("ADMIN_TRANSFER");
        willAuthApplyRequest.setCallBackUrl("https://etreaty.oss-cn-hangzhou.aliyuncs.com/apps/web-treaty-front_2.0/enterprise/willFaceCallback.html");
        willAuthApplyRequest.setFaceAuthMode(12);

        WrapperResponse<WillAuthApplyResponse> wrapperResponse = rest.willAuthApply("64fb6dc480124cd19ff34b5389e7c133", willAuthApplyRequest);
        Assert.assertNotNull(wrapperResponse.getData().getFaceToken());
    }


    @Test
    public void willAssembleAuthApply(){
        String oid = "3bb66d0174b14365ac5a7bd7c380d2b5";
        WillAssembleAuthApplyRequest willAssembleAuthApply = new WillAssembleAuthApplyRequest();
        willAssembleAuthApply.setBizType("RESET_SIGN_PWD");
        willAssembleAuthApply.setClientType("PC");
        willAssembleAuthApply.setCallBackUrl("https://esign.cn");
        WillAssembleAuthApplyResponse authApplyResponse = rest.willAssembleAuthApply(oid, willAssembleAuthApply).getData();
        Assert.assertNotNull(authApplyResponse.getAuthUrl());
        String serviceId = authApplyResponse.getServiceId();
        WillAssembleAuthVerifyRequest authVerifyRequest = new WillAssembleAuthVerifyRequest();
        authVerifyRequest.setServiceId(serviceId);
        SenderWillAssembleCheckResponse checkResponse = rest.willAssembleAuthVerify(authVerifyRequest).getData();
        Assert.assertNotNull(checkResponse);
        Assert.assertEquals(checkResponse.getResult(), "APPLY");
    }
}
