package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.api.RpcQrLoginService;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.response.QrUidApplyResponse;
import com.timevale.footstone.user.service.model.account.response.QrUidResultResponse;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @since 2021-03-09 13:40
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RpcQrLoginServiceTest{

    @Autowired
    private RpcQrLoginService rpcQrLoginService;

    @Test
    public void applyTest() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        WrapperResponse<QrUidApplyResponse> apply = rpcQrLoginService.apply();
        Assert.assertTrue(apply!=null);

        String quUid = apply.getData().getQruid();
        WrapperResponse<QrUidResultResponse> result = rpcQrLoginService.result(quUid);
        Assert.assertTrue(result != null);
    }
}
