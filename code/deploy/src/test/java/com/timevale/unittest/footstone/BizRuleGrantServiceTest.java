package com.timevale.unittest.footstone;

import com.alibaba.fastjson.JSONObject;
import com.timevale.footstone.user.service.inner.biz.BizRuleGrantService;
import com.timevale.footstone.user.service.model.rules.request.BindRuleRequest;
import com.timevale.footstone.user.service.model.rules.request.ChangeNotifySettingRequest;
import com.timevale.footstone.user.service.model.rules.request.GrantedDetailRequest;
import com.timevale.footstone.user.service.model.rules.response.BindRuleResponse;
import com.timevale.footstone.user.service.model.rules.response.GetRuleGrantByGrantIdResponse;
import com.timevale.footstone.user.service.model.rules.response.GetRuleGrantByResourceIdResponse;
import com.timevale.privilege.service.enums.rules.RoleTypeRegister;
import com.timevale.privilege.service.enums.rules.RuleGrantStatus;
import com.timevale.privilege.service.enums.rules.TemplateKeyRegister;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/12/11
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizRuleGrantServiceTest {

    String orgId = "f8fe268afa114aa2ab824af3a9c1f4b6";
    String granter = "7c4d19de0d094ab28a5fca7362b4c839";
    String grantedUser = "c42053b11e2b45c995874ca2a48a9198";
    String resourceId = "b0a5c102-0912-43af-9d3e-f952551ff4c4";
    String resourceType = "SEAL";
    String ruleGrantId = null;
    @Autowired
    private BizRuleGrantService bizRuleGrantService;

    @Test
    public void test() {
        try {
            // saveRuleGrant
            BindRuleRequest request = saveRuleGrant();
            updateRuleGrant();
            GetRuleGrantByGrantIdResponse response = getRuleGrantByGrantId();
            Assert.assertNotNull(response);
            Assert.assertTrue(response.getNotifySetting());

            // getRuleGrantByResourceId
            GetRuleGrantByResourceIdResponse getRuleGrantByResourceIdResponse =
                    getRuleGrantByResourceId();
            Assert.assertNotNull(getRuleGrantByResourceIdResponse);

            // changeNotifySetting
            changeNotifySetting(false);
            response = getRuleGrantByGrantId();
            Assert.assertNotNull(response);
            Assert.assertFalse(response.getNotifySetting());

            // deleteRuleGrant
            deleteRoleGrant(orgId, ruleGrantId);
            response = getRuleGrantByGrantId();
//            Assert.assertNotNull(response);

            // clear data
            clear();
        } catch (Exception e) {
            // ignore
        }
    }

    private BindRuleRequest saveRuleGrant() {
        BindRuleRequest request = new BindRuleRequest();
        request.setResourceId(resourceId);
        request.setResourceType(resourceType);
        request.setTemplateKey(TemplateKeyRegister.SEAL_AUTH.name());
        request.setRoleKey(RoleTypeRegister.SEAL_USER.name());
        request.setAutoFall(false);
        request.setScope("ALL");
        request.setNotifySetting(true);
        request.setGranter(granter);
        request.setGrantedUser(grantedUser);
        request.setEffectiveTime(System.currentTimeMillis());
        request.setExpireTime(0L);
        BindRuleResponse response = bizRuleGrantService.bindRuleGrant(orgId, request);
        ruleGrantId = response.getRuleGrantId();
        return request;
    }

    private BindRuleRequest updateRuleGrant() {
        BindRuleRequest request = new BindRuleRequest();
        request.setResourceId(resourceId);
        request.setResourceType(resourceType);
        request.setTemplateKey(TemplateKeyRegister.SEAL_AUTH.name());
        request.setRoleKey(RoleTypeRegister.SEAL_USER.name());
        request.setAutoFall(false);
        request.setScope("ALL");
        request.setNotifySetting(true);
        request.setGranter(granter);
        request.setGrantedUser(grantedUser);
        request.setEffectiveTime(System.currentTimeMillis());
        request.setExpireTime(0L);
        BindRuleResponse response =
                bizRuleGrantService.updateRuleGrant(orgId, ruleGrantId, request);
        ruleGrantId = response.getRuleGrantId();
        return request;
    }

    private void changeNotifySetting(Boolean shouldNotify) {
        ChangeNotifySettingRequest request = new ChangeNotifySettingRequest();
        request.setShouldNotify(shouldNotify);
        bizRuleGrantService.changeNotifySetting(orgId, granter, ruleGrantId, request);
    }

    private GetRuleGrantByResourceIdResponse getRuleGrantByResourceId() {
        GrantedDetailRequest grantedDetailRequest = GrantedDetailRequest.builder()
                .resourceId(resourceId)
                .ruleGrantStatus(RuleGrantStatus.VALID.getDesc())
                .build();
        GetRuleGrantByResourceIdResponse resourceIdResponse =
                bizRuleGrantService.getRuleGrantByResourceId(
                        orgId, granter, grantedDetailRequest);
        return resourceIdResponse;
    }

    private GetRuleGrantByGrantIdResponse getRuleGrantByGrantId() {
        return bizRuleGrantService.getRuleGrantByGrantId(orgId, ruleGrantId);
    }

    private void deleteRoleGrant(String orgId, String grantId) {
        bizRuleGrantService.deleteRoleGrant(orgId, grantId);
    }

    private void clear() {
    }

//    @Test
//    public void getRuleGrantByResourceIdTest() {
//        GrantedDetailRequest request = GrantedDetailRequest
//                .builder()
//                .offset(0)
//                .size(20)
//                .resourceId("a8148cab-f935-45e6-b385-58de130090e5")
//                .ruleGrantStatus("ALL")
//                .type(2)
//                .build();
//        GetRuleGrantByResourceIdResponse ruleGrants = bizRuleGrantService.getRuleGrantByResourceId(
//                "448515a6563545758c95d55d5cdf28b9",
//                "7882bc9368014ca6a8ab49bd1ad9ce42"
//                , request);
//        List<GetRuleGrantByGrantIdResponse> items = ruleGrants.getItems();
//        System.out.println(JSONObject.toJSONString(items));
//    }
//
//
//    //@Test
//    public void updateRuleGrantTest() {
//        String orgId = "448515a6563545758c95d55d5cdf28b9";
//        String operator = "7882bc9368014ca6a8ab49bd1ad9ce42";
//        String grantedID = "cee23082-a586-402c-a6fc-ade3ca8b42fd";
//        String request = "{\"autoFall\":null,\"roleKey\":\"SEAL_USER\",\"flowId\":\"3bfeb5a4fc0e43e3b47771fea2b71f72\",\"ruleGrantId\":\"7ba86828-6933-4ed8-96ef-006845970df2\",\"orgId\":\"448515a6563545758c95d55d5cdf28b9\",\"resourceId\":\"7475be63-ebf2-4763-a4a5-408f58883a34\",\"resourceType\":\"SEAL\",\"contractTemplateId\":\"10abb2d211dd450f9a8586cfa36b573a\",\"contractTemplateName\":\"企业模版3\",\"templateKey\":\"SEAL_AUTH\",\"templateName\":\"SEAL_AUTH\",\"roleName\":\"印章使用员\",\"scope\":\"10abb2d211dd450f9a8586cfa36b573a\",\"scopeName\":\"企业模版3\",\"notifySetting\":true,\"granter\":\"7882bc9368014ca6a8ab49bd1ad9ce42\",\"grantedUser\":\"25f0b1a0247f4ce9bf2c7ea0391f0ba9\",\"grantedUserName\":\"王剑阳\",\"effectiveTime\":1608393600000,\"expireTime\":1640015999000,\"expireDesc\":\"未失效\",\"status\":\"VALID\",\"statusDesc\":\"正常\",\"createTime\":1608451296000,\"level\":2000,\"grantedUserCode\":null,\"grantType\":1}";
//
////        System.out.println(request);
//        BindRuleRequest bindRuleRequest = JSONObject.parseObject(request, BindRuleRequest.class);
//        bindRuleRequest.setGranter(operator);
//        BindRuleResponse response = bizRuleGrantService.updateRuleGrant(orgId,grantedID,bindRuleRequest);
//        Assert.assertNotNull(response);
//        Integer deleteCount = bizRuleGrantService.deleteRuleGrantForTest(null,response.getRuleGrantId());
//        System.out.println("deleteCount ==  "+ deleteCount);
//    }
}
