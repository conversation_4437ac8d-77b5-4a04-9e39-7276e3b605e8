package com.timevale.unittest.footstone.v2.rest;

import com.timevale.footstone.user.service.model.account.mods.*;
import com.timevale.footstone.user.service.model.account.request.OrgCreateRequest;
import com.timevale.footstone.user.service.model.account.request.OrgUpdateRequest;
import com.timevale.footstone.user.service.model.account.response.AccountBaseResponse;
import com.timevale.footstone.user.service.rest.v2.OrganizationRestV2;
import com.timevale.footstone.user.service.utils.ResponseUtil;
import com.timevale.unittest.footstone.assist.AccountAssist;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version $ Id: OrganizationV2Test.java, v0.1 2021年03月24日 11:37 WangYuWu $
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OrganizationV2Test {

    @Autowired
    private AccountAssist assist;

    @Autowired
    private OrganizationRestV2 restV2;

    @Test
    public void testCreateOrganV2() {
        String creator = assist.createEmpty();
        OrgProperties prop = new OrgProperties();
        prop.setName("杭州天谷信息科技有限公司");
        prop.setLegalIdno("110108196710262291");
        prop.setLegalName("何一兵");

        OrgGlobalCredentials credentials = new OrgGlobalCredentials();
        credentials.setUsccCode("913301087458306077");

        OrgCreateRequest request = new OrgCreateRequest();
        request.setCreater(creator);
        request.setCredentials(credentials);
        request.setProperties(prop);

        AccountBaseResponse resp;
        try {
            resp = ResponseUtil.get(restV2.createOrgV2(request));
            Assert.assertNotNull(resp.getAccountId());
        } catch (Exception e) {

        }
    }

    @Test
    public void test2CreateOrganV2() {
        String creator = assist.createEmpty();
        OrgProperties prop = new OrgProperties();
        prop.setName("");
        prop.setLegalIdno("110108196710262291");
        prop.setLegalName("何一兵");

        OrgGlobalCredentials credentials = new OrgGlobalCredentials();
        credentials.setUsccCode("913301087458306077");

        OrgCreateRequest request = new OrgCreateRequest();
        request.setCreater(creator);
        request.setCredentials(credentials);
        request.setProperties(prop);

        AccountBaseResponse resp;
        try {
            resp = ResponseUtil.get(restV2.createOrgV2(request));
        } catch (Exception e) {

        }
    }

    @Test
    public void testUpdateOrganV2() {
        OrgProperties prop = new OrgProperties();
        //随便写一个错误的值
        prop.setLegalIdno("110108196710262291q2wq2");
        prop.setLegalName("asdwqw");

        OrgGlobalCredentials credentials = new OrgGlobalCredentials();
        credentials.setUsccCode("");

        OrgUpdateRequest request = new OrgUpdateRequest();
        request.setProperties(prop);
        request.setCredentials(credentials);

        try {
            String creator = assist.createEmpty();
            restV2.updateOrganV2(assist.createOrgan(creator), request);
        } catch (Exception e) {

        }

    }

}
