package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestEmailBean;
import org.junit.Assert;

public enum EmailTestcaseCheckForU220 implements SimpleTestcaseChecker{
    T_1("<EMAIL>", "12*<EMAIL>"),

    T_2("<EMAIL>", "1**@test.com"),

    T_3("<EMAIL>", "1**@test.com"),

    T_4("@test.com", "@test.com"),

    T_5("<EMAIL>", "12**<EMAIL>"),

    T_6("<EMAIL>", "12***<EMAIL>"),
    ;

    private String input;

    private String anwser;

    EmailTestcaseCheckForU220(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestEmailBean t = new TestEmailBean();
        t.setEmailu(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getEmailu());
    }
}
