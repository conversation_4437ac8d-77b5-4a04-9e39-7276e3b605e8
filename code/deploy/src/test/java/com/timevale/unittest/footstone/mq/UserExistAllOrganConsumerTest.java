package com.timevale.unittest.footstone.mq;

import static com.timevale.footstone.user.service.utils.IOUtil.GSON;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.dayu.sdk.service.custom.AuditLogService;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.ICOrg.input.MemberExistOrganInput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.mq.consumer.UserExistAllOrganConsumer;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.framework.mq.client.producer.Msg;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version 2025/2/25 10:03
 */
@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@Slf4j
public class UserExistAllOrganConsumerTest {

    @InjectMocks
    private UserExistAllOrganConsumer existAllOrganConsumer;

    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Mock
    private AuditLogService logService;
    @Mock
    private IdAdapt idAdapt;
    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Before
    public void setUp(){
        List<MemberDetail> memberDetails = Lists.newArrayList(MockUntils.getMemberDetail());
        PagerResult<MemberDetail> pagerResult =  MockUntils.mockPageResultEsSearchAccount(memberDetails);
        when( esAccountServiceAdapt.getMemberListByEs(any())).thenReturn(pagerResult);
        when(icUserPlusServiceAdapt.getFatICUserByOuid(any())).thenReturn(MockUntils.mockBizFatICUserOutput());
    }


    @Test
    public void consumerTest(){
        MemberExistOrganInput input = new MemberExistOrganInput();
        input.setOrganId("organId");
        input.setMemberOid("memberOid");
        Msg msg = new Msg();
        msg.setBody(JSONObject.toJSONBytes(input));
        existAllOrganConsumer.receive(Lists.newArrayList(msg));
    }
}
