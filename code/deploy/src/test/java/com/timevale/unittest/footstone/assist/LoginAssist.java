package com.timevale.unittest.footstone.assist;

import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.login.request.PwdLoginRequest;
import com.timevale.footstone.user.service.model.account.login.response.LoginSuccessResponse;
import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import com.timevale.footstone.user.service.rest.LoginRest;
import com.timevale.footstone.user.service.utils.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** <AUTHOR> on 2019-09-02 */
@Component
public class LoginAssist {

    @Autowired private LoginRest rest;

    public String getTokenByPwd(String principal, String pwd) {
        LoginParams params = new LoginParams();
        params.setEndpoint("PC");

        PwdLoginRequest req = new PwdLoginRequest();
        req.setPrincipal(principal);
        req.setPassword(pwd);
        req.setLoginParams(params);

        WrapperResponse<LoginSuccessResponse> resp = rest.pwdLogin(req);
        return ResponseUtil.get(resp).getToken();
    }
}
