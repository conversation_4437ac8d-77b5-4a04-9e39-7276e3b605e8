package com.timevale.unittest.footstone;

import com.alibaba.fastjson.JSONObject;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.sender.input.BizSenderApplyInput;
import com.timevale.easun.service.model.sender.input.BizSenderCommitInput;
import com.timevale.easun.service.model.sender.model.SenderType;
import com.timevale.easun.service.model.sender.output.BizCodeAuthOutput;
import com.timevale.easun.service.model.sender.output.BizSenderServiceOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.model.account.idcard.request.SetThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.idcard.request.UnbindThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.senderauth.request.ResetPwdRequest;
import com.timevale.footstone.user.service.model.account.senderauth.request.ServiceIdRequest;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.UUID;

import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_APP_ID;
import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class OpLogTest {


    @Autowired
    private WebApplicationContext wac;
    @Autowired
    private RpcSenderAuthService svc;
    private MockMvc mvc;

    @Before
    public void setUp() {
        //初始化MockMvc对象
        mvc = MockMvcBuilders.webAppContextSetup(wac).build();
    }

    //@Test
    public void opLogQueryTest() throws Exception {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 24 * 3600 * 1000;
        mvc.perform(
                MockMvcRequestBuilders.get(
                        "/v1/opLog/queryOpLogs")
                        .param("firstModule", "user_personal_center")
                        .param("startTime", String.valueOf(1664553600000L))
                        .param("endTime", String.valueOf(1667145600000L))
                        .param("page", "1")
                        .param("pageSize", "1")
                        .header(HEADER_OPERATOR_ID, "a84d9fff9b2e4c22af6bb85171b8df43")
//                        .header(HEADER_OPERATOR_ID,"3bc25a2489a440999e0589248ea55182")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test
    public void pwdTest() {
        try {
            BizSenderApplyInput applyInput = new BizSenderApplyInput();
            applyInput.setPrincipal("15988186993");
            applyInput.setBizType(SenderType.RESET_PWD.getType());
            applyInput.setProjectApp(new ProductApp("**********"));
            BizSenderServiceOutput output = svc.senderMobileApply(new RpcInput<>(applyInput)).getData();
            String serviceId2 = output.getServiceId();
            BizSenderCommitInput checkInput = new BizSenderCommitInput();
            checkInput.setServiceId(serviceId2);
            checkInput.setCredentials("123456");
            BizCodeAuthOutput checkOutput = svc.codeAuth(new RpcInput<>(checkInput)).getData();
            ResetPwdRequest request = new ResetPwdRequest();
            request.setServiceId(serviceId2);
            request.setNewPassword(UUID.randomUUID().toString());
            mvc.perform(
                    MockMvcRequestBuilders.put(
                            "/v1/sender/pwdReset")
                            .content(JSONObject.toJSONString(request))
                            .header(HEADER_APP_ID.toLowerCase(), "**********")
                            .header(HEADER_OPERATOR_ID.toLowerCase(), "a84d9fff9b2e4c22af6bb85171b8df43")
//                        .header(HEADER_OPERATOR_ID,"3bc25a2489a440999e0589248ea55182")
                            .contentType(MediaType.APPLICATION_JSON_UTF8))
                    .andExpect(
                            mvcResult -> {
                                System.out.print(mvcResult.getResponse().getContentAsString());
                            })
                    .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
        }catch (Exception e){

        }
    }

    @Test
    public void contactTest() throws Exception {
        BizSenderApplyInput applyInput = new BizSenderApplyInput();
        applyInput.setPrincipal("15988186993");
        applyInput.setBizType(SenderType.RESET_PWD.getType());
        applyInput.setProjectApp(new ProductApp("**********"));
        BizSenderServiceOutput output = svc.senderMobileApply(new RpcInput<>(applyInput)).getData();
        ServiceIdRequest request=new ServiceIdRequest();
        request.setServiceId(output.getServiceId());
        mvc.perform(
                MockMvcRequestBuilders.put(
                        "/v1/sender/idcardModify")
                        .content(JSONObject.toJSONString(request))
                        .header(HEADER_APP_ID.toLowerCase(), "**********")
                        .header(HEADER_OPERATOR_ID.toLowerCase(), "a84d9fff9b2e4c22af6bb85171b8df43")
//                        .header(HEADER_OPERATOR_ID,"3bc25a2489a440999e0589248ea55182")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        });
    }

    @Test
    public void thTest() throws Exception {
        SetThirdPartyRequest request1 = new SetThirdPartyRequest();
        request1.setKey("DING_TALK");
        request1.setUserId("3bc25a2489a440999e0589248ea55182");
        mvc.perform(
                MockMvcRequestBuilders.post(
                        "/v1/accounts/ce152eebdadb4e4aa350b69c10cb7861 /setThirdParty")
                        .content(JSONObject.toJSONString(request1))
                        .header(HEADER_APP_ID, "**********")
                        .header(HEADER_OPERATOR_ID, "ce152eebdadb4e4aa350b69c10cb7861 ")
//                        .header(HEADER_OPERATOR_ID,"3bc25a2489a440999e0589248ea55182")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
        UnbindThirdPartyRequest request = new UnbindThirdPartyRequest();
        request.setKey("DING_TALK");
        mvc.perform(
                MockMvcRequestBuilders.post(
                        "/v1/accounts/ce152eebdadb4e4aa350b69c10cb7861 /unbindThirdParty")
                        .content(JSONObject.toJSONString(request))
                        .header(HEADER_APP_ID, "**********")
                        .header(HEADER_OPERATOR_ID, "ce152eebdadb4e4aa350b69c10cb7861 ")
//                        .header(HEADER_OPERATOR_ID,"3bc25a2489a440999e0589248ea55182")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

    }

}
