package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.PrefWillTypeSetRequest;
import com.timevale.footstone.user.service.model.account.response.PrefWillTypeResponse;
import com.timevale.footstone.user.service.rest.AccountExtendRest;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.unittest.footstone.assist.AccountAssist;
import com.timevale.unittest.footstone.configuration.Application;
import groovy.json.StringEscapeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年08月30日 10:31:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@Slf4j
public class AccountExtendTest {

//    @Autowired
//    AccountExtendRest rest;
//
//    @Autowired
//    AccountAssist accountAssist;

    @Test
    public void testWillTypeTest(){
        return;
//        String accountId  = accountAssist.createEmpty();
//        WrapperResponse<PrefWillTypeResponse> getRes =  rest.getPreWillType(accountId);
//        Assert.isTrue(StringUtils.isEmpty(getRes.getData().getPrefWillType()));
//
//        PrefWillTypeSetRequest setRequest = new PrefWillTypeSetRequest();
//        setRequest.setPrefWillType("FACE");
//        rest.setPrefWillType(accountId,setRequest);
//        getRes =  rest.getPreWillType(accountId);
//        Assert.isTrue(StringUtils.equals(getRes.getData().getPrefWillType(),"FACE"));

    }

}
