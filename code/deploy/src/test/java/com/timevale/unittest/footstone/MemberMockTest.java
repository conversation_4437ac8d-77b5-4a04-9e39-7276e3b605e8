package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.impl.BizMemberServiceImpl;
import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.rest.MemberRest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @since 2021/11/30
 **/
@RunWith(MockitoJUnitRunner.class)
@ContextConfiguration(classes = Application.class)
public class MemberMockTest {

  @InjectMocks
  private MemberRest memberRest;
  @Mock
  private BizMemberService bizMemberService;
  @Mock
  private DecorationWalker walker;

  @Autowired
  private BizMemberServiceImpl memberService;

    @Test
    public void test_administrators() {
      bizMemberService = memberService;
      memberRest.administrators("e8316ad4578940febb4d68788f21276d", 0, 100);
    }
}
