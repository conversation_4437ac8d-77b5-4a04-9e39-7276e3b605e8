package com.timevale.unittest.footstone;

import com.timevale.easun.service.api.RpcThirdAuthorizeService;
import com.timevale.easun.service.model.mods.BizWechatApplyAuthorizeOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.model.mods.request.WeChatAuthorizeRequest;
import com.timevale.footstone.user.service.rest.AuthorizeRest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年05月25日 11:16:00
 */
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AuthorizeTest {

    @InjectMocks
    AuthorizeRest rest;

    @Mock
    RpcThirdAuthorizeService authorizeService;

    @Test
    public void wechatAuthorize2Test(){

        BizWechatApplyAuthorizeOutput authorizeOutput = new BizWechatApplyAuthorizeOutput();
        authorizeOutput.setOpenId("aaaa");
        authorizeOutput.setUserId("sss");
        RpcOutput<BizWechatApplyAuthorizeOutput> output = RpcOutput.with(authorizeOutput);
        when(authorizeService.wechatAuth(any())).thenReturn(output);

        WeChatAuthorizeRequest request = new WeChatAuthorizeRequest();
        request.setCode("a");
        request.setEncryptedData("s");
        request.setIv("ss");
        request.setSignature("ss");
        request.setRawData("sd");
        rest.authorize2(request);

    }
}
