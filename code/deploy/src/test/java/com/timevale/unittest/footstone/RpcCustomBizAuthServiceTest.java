package com.timevale.unittest.footstone;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.easun.service.model.organization.ICOrg.input.MemberExistOrganInput;
import com.timevale.footstone.identity.common.service.response.scene.webpage.IdentityAuthUrlResponse;
import com.timevale.footstone.user.service.api.RpcCustomBizAuthService;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.exception.CustomBizAuthException;
import com.timevale.footstone.user.service.exception.CustomBizAuthResultEnum;
import com.timevale.footstone.user.service.inn.bizauth.BizAuthFlowComponent;
import com.timevale.footstone.user.service.inn.bizauth.RealNameClient;
import com.timevale.footstone.user.service.inn.bizauth.WillingnessAuthClient;
import com.timevale.footstone.user.service.inn.bizauth.callback.MQManager;
import com.timevale.footstone.user.service.inn.bizauth.callback.RealNameFinishNoticeMessageHandler;
import com.timevale.footstone.user.service.inn.bizauth.callback.RealNameFinishNoticeResult;
import com.timevale.footstone.user.service.inn.bizauth.callback.WillAuthFinishNoticeMessageHandler;
import com.timevale.footstone.user.service.inn.bizauth.tool.BizAuthFlowIdTool;
import com.timevale.footstone.user.service.model.account.request.v3.*;
import com.timevale.footstone.user.service.model.account.response.v3.RpcApplyBizAuthResponse;
import com.timevale.footstone.user.service.model.account.response.v3.RpcQueryOrgBizAuthResponse;
import com.timevale.footstone.user.service.model.account.response.v3.RpcQueryPersonBizAuthResponse;
import com.timevale.footstone.user.service.model.bizauth.CustomBizAuthApplyRequest;
import com.timevale.footstone.user.service.model.bizauth.CustomBizAuthInfoRequest;
import com.timevale.footstone.user.service.model.bizauth.CustomBizAuthInfoResponse;
import com.timevale.footstone.user.service.rest.inner.CustomBizAuthRest;
import com.timevale.footstone.will.common.service.enums.IdentityObjectTypeEnum;
import com.timevale.framework.mq.client.producer.Msg;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import com.timevale.willauth.service.result.WillFinishNoticeResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @since 2021-03-09 13:40
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RpcCustomBizAuthServiceTest {
    @Autowired private BizAuthFlowComponent bizAuthFlowComponent;

    @Autowired
    private MQManager mqManager;

    @Autowired
    private RpcCustomBizAuthService customBizAuthService;
    @Autowired
    private CustomBizAuthRest customBizAuthRest;
    @Autowired
    private WillingnessAuthClient willingnessAuthClient;
    @Autowired
    private RealNameClient realNameClient;


    /** 意愿完成通知 */
    private final String WILL_FINISH_NOTICE = "will_finish_notice";

    /**
     * 实名完成通知
     */
    private final String REALNAME_FINISH_NOTICE = "realname_finish_notice";

    @Test
    public void applyOrgBizAuthTest() {
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "fc9a22239f1441a4bcb77437ff618d58");
        RequestContext.put("httpServletRequest",servletRequest);

        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");

        RpcApplyOrgBizAuthRequest request = new RpcApplyOrgBizAuthRequest();
        request.setAppId("7876698694");
        request.setCustomBizNum("xxxxx");
        request.setCustomBizScene("CERT_APPLY_ESHIELD");
        request.setRepeatableRealName(false);


        ApplyBizOrgSubject orgSubject = new ApplyBizOrgSubject();
        orgSubject.setOrgId("1a88aedf47f34f9899a7e535d05d30b4");
        ApplyBizOrgAgentInfo agentInfo = new ApplyBizOrgAgentInfo();
        agentInfo.setAgentId("fc9a22239f1441a4bcb77437ff618d58");
        orgSubject.setAgentInfo(agentInfo);
        request.setOrgSubject(orgSubject);


        ApplyBizContext bizContext = new ApplyBizContext();
        ApplyBizContextCertParticipator certParticipatorEntity = new ApplyBizContextCertParticipator();
        certParticipatorEntity.setUserCertOperationType("APPLY");
        certParticipatorEntity.setUserCertAgreementType("caStandardCert");
        certParticipatorEntity.setUserCertCaRoute("eSignCA");
        bizContext.setCertParticipator(certParticipatorEntity);
        request.setBizContext(bizContext);

        ApplyInteractiveConfig interactiveConfig = new ApplyInteractiveConfig();
        interactiveConfig.setRedirectUrl("https://www.baidu.com");
        request.setInteractiveConfig(interactiveConfig);


        RpcApplyBizAuthResponse bizAuthResponse =
                customBizAuthService.applyOrgBizAuth(request).getData();
        Assert.assertNotNull(bizAuthResponse);
        String bizAuthFlowId = bizAuthResponse.getBizAuthFlowId();
        messageWillMQ(bizAuthFlowId);

        CustomBizAuthInfoResponse infoResponse =
                customBizAuthRest.bizFlowInfo(bizAuthFlowId, new CustomBizAuthInfoRequest()).getData();

        CustomBizAuthApplyRequest authApplyRequest = new CustomBizAuthApplyRequest();
        authApplyRequest.setFlowStep("AGENT_WILL");
        authApplyRequest.setCallbackUrl("httsp://baidu.com");
        customBizAuthRest.bizFlowApply(bizAuthFlowId,authApplyRequest).getData();



        request.setRepeatableRealName(true);
        bizAuthResponse =
                customBizAuthService.applyOrgBizAuth(request).getData();
        Assert.assertNotNull(bizAuthResponse);

        RpcBizAuthQueryRequest queryRequest =  new RpcBizAuthQueryRequest();
        queryRequest.setBizAuthFlowId(bizAuthResponse.getBizAuthFlowId());
        RpcQueryOrgBizAuthResponse response = customBizAuthService.queryOrgBizAuth(queryRequest).getData();
        Assert.assertNotNull(response);
        Assert.assertEquals(response.getBizAuthFlowStatus(), "INIT");
        messageIdentityMQ(bizAuthResponse.getBizAuthFlowId(), IdentityObjectTypeEnum.ORGANIZATION);

    }

    @Test
    public void applyPersonBizAuthTest() {
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);

        RequestContext.put("X-Tsign-Open-App-Id", "7876698694");
        RpcApplyPersonBizAuthRequest request = new RpcApplyPersonBizAuthRequest();
        request.setAppId("7876698694");
        request.setCustomBizNum("xxxxx");
        request.setCustomBizScene("CERT_APPLY_ESHIELD");
        request.setRepeatableRealName(false);

        ApplyBizPersonSubject personSubject = new ApplyBizPersonSubject();
        personSubject.setPersonId("5121f65e93484712ae04f869f77ded6e");
        request.setPersonSubject(personSubject);

        ApplyBizContext bizContext = new ApplyBizContext();
        ApplyBizContextCertParticipator certParticipatorEntity = new ApplyBizContextCertParticipator();
        certParticipatorEntity.setUserCertOperationType("APPLY");
        certParticipatorEntity.setUserCertAgreementType("caStandardCert");
        certParticipatorEntity.setUserCertCaRoute("eSignCA");
        bizContext.setCertParticipator(certParticipatorEntity);
        request.setBizContext(bizContext);

        ApplyInteractiveConfig interactiveConfig = new ApplyInteractiveConfig();
        interactiveConfig.setRedirectUrl("https://www.baidu.com");
        request.setInteractiveConfig(interactiveConfig);


        RpcApplyBizAuthResponse bizAuthResponse =
                customBizAuthService.applyPersonBizAuth(request).getData();
        Assert.assertNotNull(bizAuthResponse);

        String bizAuthFlowId = bizAuthResponse.getBizAuthFlowId();
        messageWillMQ(bizAuthFlowId);


        request.setRepeatableRealName(true);
        bizAuthResponse =
                customBizAuthService.applyPersonBizAuth(request).getData();
        Assert.assertNotNull(bizAuthResponse);
        messageIdentityMQ(bizAuthResponse.getBizAuthFlowId(), IdentityObjectTypeEnum.INDIVIDUAL);

//        RpcBizAuthQueryRequest queryRequest =  new RpcBizAuthQueryRequest();
//        queryRequest.setBizAuthFlowId("BAF-PSN-3619d374e4080023");
//        RpcQueryPersonBizAuthResponse response = customBizAuthService.queryPersonBizAuth(queryRequest).getData();
//        Assert.assertNotNull(response);
//        Assert.assertEquals(response.getBizAuthFlowStatus(), "ING");

        IdentityAuthUrlResponse authUrlResponse = realNameClient.authFlowPsnIdentityAuthUrl(request,bizAuthFlowId);
        Assert.assertNotNull(authUrlResponse.getFlowId());


    }

    @Test
    public void sendMessage(){
        String bizAuthFlowId = BizAuthFlowIdTool.buildBizAuthFlowId(false);
        RpcApplyBizAuthResponse response = new RpcApplyBizAuthResponse();
        response.setBizAuthFlowId(bizAuthFlowId);

        RpcApplyOrgBizAuthRequest request = new RpcApplyOrgBizAuthRequest();
        request.setAppId("7876698694");
        request.setCustomBizNum("xxxxx");
        request.setCustomBizScene("CERT_APPLY_ESHIELD");
        request.setRepeatableRealName(false);


        ApplyBizOrgSubject orgSubject = new ApplyBizOrgSubject();
        orgSubject.setOrgId("1a88aedf47f34f9899a7e535d05d30b4");
        ApplyBizOrgAgentInfo agentInfo = new ApplyBizOrgAgentInfo();
        agentInfo.setAgentId("fc9a22239f1441a4bcb77437ff618d58");
        orgSubject.setAgentInfo(agentInfo);
        request.setOrgSubject(orgSubject);


        ApplyBizContext bizContext = new ApplyBizContext();
        ApplyBizContextCertParticipator certParticipatorEntity = new ApplyBizContextCertParticipator();
        certParticipatorEntity.setUserCertOperationType("APPLY");
        certParticipatorEntity.setUserCertAgreementType("caStandardCert");
        certParticipatorEntity.setUserCertCaRoute("eSignCA");
        bizContext.setCertParticipator(certParticipatorEntity);
        request.setBizContext(bizContext);

        ApplyInteractiveConfig interactiveConfig = new ApplyInteractiveConfig();
        interactiveConfig.setRedirectUrl("https://www.baidu.com");
        request.setInteractiveConfig(interactiveConfig);

        willingnessAuthClient.sendMessage(response, request);
    }


    public void messageWillMQ(String bizAuthFlowId){
        WillAuthFinishNoticeMessageHandler messageHandler  = new  WillAuthFinishNoticeMessageHandler(WILL_FINISH_NOTICE, mqManager, bizAuthFlowComponent);

        WillFinishNoticeResult noticeResult = new WillFinishNoticeResult();
        noticeResult.setContextId(bizAuthFlowId);
        noticeResult.setBizType("SIGN");
        noticeResult.setBizId("SIGN");
        noticeResult.setWillAuthId("SIGN");
        noticeResult.setPassed(Boolean.TRUE);
        Msg msg = new Msg();
        msg.setBody(JSONObject.toJSONBytes(noticeResult));
        messageHandler.receive(Lists.newArrayList(msg));
    }

    public void messageIdentityMQ(String bizAuthFlowId, IdentityObjectTypeEnum objectTypeEnum){
        RealNameFinishNoticeMessageHandler messageHandler  = new RealNameFinishNoticeMessageHandler(REALNAME_FINISH_NOTICE, mqManager, bizAuthFlowComponent);

        RealNameFinishNoticeResult noticeResult = new RealNameFinishNoticeResult();
        noticeResult.setContextId(bizAuthFlowId);
        noticeResult.setObjectType(objectTypeEnum.name());
        noticeResult.setPassed(Boolean.TRUE);
        noticeResult.setFlowId("SIGN");
        noticeResult.setPassed(Boolean.TRUE);
        Msg msg = new Msg();
        msg.setBody(JSONObject.toJSONBytes(noticeResult));
        messageHandler.receive(Lists.newArrayList(msg));
    }

    @Test
    public void CustomBizAuthExceptionTest(){
        Assert.assertNotNull(new CustomBizAuthException("异常"));
        Assert.assertNotNull(new CustomBizAuthException(CustomBizAuthResultEnum.CALL_IDENTITY_FAIL.getCode(),"异常"));
        Assert.assertNotNull(new CustomBizAuthException(CustomBizAuthResultEnum.CALL_IDENTITY_FAIL.getCode(),"异常","异常"));
        Assert.assertNotNull(new CustomBizAuthException(CustomBizAuthResultEnum.CALL_IDENTITY_FAIL));
        Assert.assertTrue(new CustomBizAuthException(CustomBizAuthResultEnum.CALL_IDENTITY_FAIL).getNCode()> 0);
    }
}
