package com.timevale.unittest.footstone;

import com.google.common.collect.Lists;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.role.model.CreateRoleModel;
import com.timevale.footstone.user.service.model.role.request.CreateRoleRequest;
import com.timevale.footstone.user.service.model.role.request.FreezeLegalRequest;
import com.timevale.footstone.user.service.model.role.request.RoleBatchGrantedMemeber;
import com.timevale.footstone.user.service.model.role.request.UpdateRoleRequest;
import com.timevale.footstone.user.service.model.role.response.CreateRoleResponse;
import com.timevale.footstone.user.service.model.role.response.GetRolesByOrgResponse;
import com.timevale.footstone.user.service.rest.RoleRest;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.privilege.service.enums.RoleType;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/13
 */
@RunWith(MockitoJUnitRunner.Silent.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RoleTest {

    @InjectMocks
    private RoleRest rest;


    @Mock
    private BizRoleService bizRoleService;

    @Mock
    private AuditLogCommon auditLogCommon;

    @Mock
    private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;


    @Mock
    private BizRoleService mockBizRoleService;


    @Mock
    private HttpServletRequest mockHttpServletRequest;

    private static final String INPUT_HEADER_OPERATOR_ID = "operator123";
    private static final String INPUT_HEADER_TENANT_ID = "tenant123";
    private static final String EXPECTED_ROLE_ID = "adminRoleId";


    @Before
    public void init() {
        HttpServletRequest httpServletRequest = new MyTestHttpServletRequest();
        httpServletRequest.setAttribute(WeaverConstants.APP_ID_LABEL,"3438757422");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_REAL_IP,"127.0.0.1");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"d6a34fcf82774b8ba34549a3fe103113");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"cd7ee89e84bc458e99dd13c1e1745345");
        RequestContext.put(WeaverConstants.REQUEST_KEY,httpServletRequest);
        AuditLogCommon.initAuditLogContext();
    }

    @Test
    public void testRoleList(){
        String orgId = "e8316ad4578940febb4d68788f21276d";
        WrapperResponse<ApiPageResult<GetRolesByOrgResponse>> res = rest
            .getRolesByOrg(orgId, "ALL", 0, 100,null);
        WrapperResponse<ApiPageResult<GetRolesByOrgResponse>> res2 = rest
            .getRolesByOrg(orgId, RoleType.BUILTLIN.name(), 0, 100,null);
        WrapperResponse<ApiPageResult<GetRolesByOrgResponse>> res3 = rest
            .getRolesByOrg(orgId, null, 0, 100,null);
    }

    @Test
    public void testCreateAndUpdateRoles(){
        try {
            CreateRoleRequest request = new CreateRoleRequest();
            CreateRoleModel model = new CreateRoleModel();
            model.setName("权衡测试0120");
            model.setDisc("12121212");
            List<CreateRoleModel> roleModels = Lists.newArrayList(model);
            request.setRoleModels(roleModels);
            // 创建角色
            WrapperResponse<CreateRoleResponse> rst =
                    rest.createRoles("70c430ac94924bcca3e928f7126f39be", request);
            ;
            UpdateRoleRequest updateRoleRequest = new UpdateRoleRequest();
            updateRoleRequest.setName("create-role特色提出");
            updateRoleRequest.setDisc("权衡测试-修改角色啊飒飒");
            // 更新角色
            rest.updateRole("70c430ac94924bcca3e928f7126f39be",
                    rst.getData().getRoleId().get(0),
                    updateRoleRequest);
            // 删除角色
            rest.deleteRole("70c430ac94924bcca3e928f7126f39be",
                    rst.getData().getRoleId().get(0));
        }catch (Exception e){

        }
    }

    @Test
    public void testGrantUsers(){
        try {

            String orgId = "d6a34fcf82774b8ba34549a3fe103113";
            RoleBatchGrantedMemeber grantedMemeber = new RoleBatchGrantedMemeber();
            grantedMemeber.setRoleIds(Arrays.asList("79b6fd9fc939464c9a753145c02664e6"));
            grantedMemeber.setAccountUids(Arrays.asList("04a544d1814346f985a5e4e531a0f02c","592784afad344c4fb7fa5cc39f79dcab","ae5b01e6f34543e0980c502b483e1f13"));
            rest.grantedRoles(orgId,grantedMemeber);
        }catch (Exception e){}
    }


    @Test
    public void freezeLegalTest(){
        FreezeLegalRequest request = new FreezeLegalRequest();
        request.setOrgId("orgId");
        BizRoleDetail roleDetail = new BizRoleDetail();
        when(bizRoleService.getRoleByRoleKey(anyString(),anyString())).thenReturn(roleDetail);
        when(bizRoleService.checkIfIsAdminOrLegal(anyString(),anyString())).thenReturn(true);
        when(rolePrivilegePlusServiceAdapt.freezeLegal(anyString())).thenReturn(true);
        rest.freezeLegal(request);
        rest.freezeOrgLegal(request.getOrgId());
    }


    @Test
    public void unFreezeLegalTest(){
        FreezeLegalRequest request = new FreezeLegalRequest();
        request.setOrgId("orgId");
        BizRoleDetail roleDetail = new BizRoleDetail();
        when(bizRoleService.getRoleByRoleKey(anyString(),anyString())).thenReturn(roleDetail);
        when(bizRoleService.checkIfIsAdminOrLegal(anyString(),anyString())).thenReturn(true);
        when(rolePrivilegePlusServiceAdapt.unFreezeLegal(anyString())).thenReturn(true);
        rest.unFreezeLegal(request);
        rest.unFreezeOrgLegal("orgId");
    }

}
