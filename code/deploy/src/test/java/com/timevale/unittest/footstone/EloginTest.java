//package com.timevale.unittest.footstone;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Maps;
//import com.timevale.account.service.enums.RealnameStatus;
//import com.timevale.account.service.exception.Errors;
//import com.timevale.account.service.model.constants.BuiltinCredentials;
//import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
//import com.timevale.footstone.user.service.inner.biz.BizEloginService;
//import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAuthenticationServiceAdapt;
//import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
//import com.timevale.footstone.user.service.model.WrapperResponse;
//import com.timevale.footstone.user.service.model.account.mods.EloginInfoModel;
//import com.timevale.footstone.user.service.model.account.request.*;
//import com.timevale.footstone.user.service.model.account.response.AccountEloginKeyResponse;
//import com.timevale.footstone.user.service.model.elogin.EloginCreateInfoPersistModel;
//import com.timevale.footstone.user.service.rest.EloginRest;
//import com.timevale.footstone.user.service.utils.UrlUtil;
//import com.timevale.framework.tedis.util.TedisUtil;
//import com.timevale.mandarin.base.security.Base64Utils;
//import com.timevale.mandarin.weaver.utils.RequestContext;
//import com.timevale.open.platform.service.service.api.AppService;
//import com.timevale.open.platform.service.service.api.RSAService;
//import com.timevale.open.platform.service.service.model.request.RSAEncryptRequest;
//import com.timevale.unittest.footstone.configuration.Application;
//import com.timevale.unittest.footstone.tc.UTCGenerator;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import java.util.HashMap;
//
///** <AUTHOR> on 2020-02-27 */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class EloginTest {
//
//    private static final String APP_ID = "3876545303";
//
//    private static final String SIGN_SHORT_URL = "https://testt.tsign.cn/YHB7fZbfmoYA";
//
//    private static final String THIRD_USER_INFO =
//            "{\"userId\":\"%s\",\"startTime\":%s,\"timeout\":86400}";
//
//    @Autowired private EloginRest rest;
//
//    @Autowired private BizEloginService eloginSvc;
//
//    @Autowired private RpcAuthenticationServiceAdapt authSvc;
//
//    @Autowired private RpcIcUserPlusServiceAdapt icuserSvc;
//
//    @Autowired private AppService appSvc;
//
//    @Autowired private RSAService rsaSvc;
//
//    private String encryptContent;
//
//    @Before
//    public void init() {
//        this.encryptContent = encrypt(UTCGenerator.utcThirdAppUser());
//    }
//
//    private String encrypt(String userId) {
//        String content = String.format(THIRD_USER_INFO, userId, System.currentTimeMillis() / 1000);
//        String publicKey = appSvc.getAppInfo(APP_ID).getPublicKey();
//
//        RSAEncryptRequest request = new RSAEncryptRequest();
//        request.setPublicKey(publicKey);
//        request.setEncryptContent(content);
//        return rsaSvc.encryptByPublicKey(request).getData();
//    }
//
//    @Test
//    public void testEloginExec() {
//        setContextAppId();
//
//        AccountEloginExecRequest request = new AccountEloginExecRequest();
//        request.setEncryptContent(encrypt("*********"));
//        request.setType("alipay");
//        rest.eloginExec(request);
//    }
//
//    @Test
//    public void testEloginSign() {
//        setContextAppId();
//
//        AccountEloginSignRequest request = new AccountEloginSignRequest();
//        request.setShortLinkUrl(SIGN_SHORT_URL);
//        request.setEncryptContent(encryptContent);
//        rest.authorizeSignElogin(request);
//    }
//
//    @Test
//    public void testEloginBind() {
//        setContextAppId();
//
//        AccountEloginBindRequest request = new AccountEloginBindRequest();
//        request.setEncryptContent(encryptContent);
//        try {
//            rest.authorizeBindElogin(request);
//        } catch (FootstoneUserErrors.ThirdIdAlreadyBind ignored) {
//
//        }
//    }
//
//    @Test
//    public void testEloginUnBind() {
//        setContextAppId();
//
//        AccountEloginUnbindRequest request = new AccountEloginUnbindRequest();
//        request.setEncryptContent(encryptContent);
//        try {
//            rest.authorizeUnbindElogin(request);
//        } catch (Errors.AccIdcardNotExist ignored) {
//
//        }
//    }
//
//
//    public void testCreatePerson() {
//        setContextAppId();
//
//        String mobile = UTCGenerator.utcMobile();
//        AccountEloginPCreateRequest request = new AccountEloginPCreateRequest();
//        request.setMobile(mobile);
//        request.setUserId(UTCGenerator.utcThirdAppUser());
//        String key = eloginSvc.create(request).getKey();
//        EloginCreateInfoPersistModel info = TedisUtil.get(key);
//
//        // 重复绑定
//        AccountEloginPCreateRequest requestDup = new AccountEloginPCreateRequest();
//        requestDup.setMobile(mobile);
//        requestDup.setUserId(UTCGenerator.utcThirdAppUser());
//        try {
//            eloginSvc.create(requestDup);
//        } catch (FootstoneUserErrors.EloginEsignAccountBinded ignored) {
//
//        }
//
//        AccountEloginUndoBindRequest undoReq = new AccountEloginUndoBindRequest();
//        undoReq.setUndoKey(key);
//        undoReq.setCode(info.getCode());
//        eloginSvc.undoBind(undoReq);
//    }
//
//    @Test
//    public void testCreateOrgan() {
//        setContextAppId();
//
//        String userId = UTCGenerator.utcThirdAppUser();
//        String mobile = UTCGenerator.utcMobile();
//        AccountEloginPCreateRequest pRequest = new AccountEloginPCreateRequest();
//        pRequest.setMobile(mobile);
//        pRequest.setUserId(userId);
//        eloginSvc.create(pRequest);
//
//        String accountUid = authSvc.checkMobile(mobile, null).getAccountUid();
//        icuserSvc.addGlobalUser(
//                accountUid,
//                BuiltinCredentials.CRED_PSN_CH_IDCARD,
//                UTCGenerator.utcPsnIdcard(),
//                RealnameStatus.ACCEPT);
//
//        AccountEloginOCreateRequest request = new AccountEloginOCreateRequest();
//        request.setName(UTCGenerator.utcName());
//        request.setUserId(userId);
//        rest.eloginCreateOrgan(request);
//
//        // 同名的实名企业
//        AccountEloginOCreateRequest request2 = new AccountEloginOCreateRequest();
//        request2.setName("杭州天谷信息科技有限公司");
//        request2.setUserId(userId);
//        rest.eloginCreateOrgan(request2);
//    }
//
//    @Test
//    public void testUrlUtil() {
//        String origin = "https://www.baidu.com";
//        String k1 = "foo";
//        String v1 = "bar";
//
//        String appendUrl = UrlUtil.appendParam(origin, k1, v1);
//        Assert.assertEquals(origin + "?" + k1 + "=" + v1, appendUrl);
//
//        String getParam = UrlUtil.getParam(appendUrl, k1);
//        Assert.assertEquals(v1, getParam);
//
//        String removeUrl = UrlUtil.removeParam(appendUrl, k1, v1);
//        Assert.assertEquals(origin, removeUrl);
//    }
//
////    @Test
////    public void testeloginEsign() {
////        AccountEloginExecRequest request = new AccountEloginExecRequest();
////        String appID = "**********";
////        EloginInfoModel model = new EloginInfoModel();
////        model.setAppId(appID);
////        model.setStartTime(System.currentTimeMillis());
////        model.setMaxTimeout(5 * 3600 * 1000L);
////        model.setTimeout(2*3600L);
////        model.setUserId("4501308d254a434b90fd9d96f63dc161");
////        String content = JSONObject.toJSONString(model);
////        String encrypt = Base64Utils.encode(content.getBytes());
////        request.setEncryptContent(encrypt);
////        request.setType("JIN_HUA_SOCIAL");
////        RequestContext.put("X-Tsign-Open-App-Id",appID);
////        WrapperResponse<AccountEloginKeyResponse> result = rest.eloginEsign(request);
////    }
//
//    @Test
//    public void testEloginOrganRealnameUrl() {
//        setContextAppId();
//        String redirectUrl = "https://www.baidu.com";
//        String organKey = "test_elogin_organ_key";
//
//        EloginCreateInfoPersistModel info = new EloginCreateInfoPersistModel();
//        info.setCreate(true);
//        info.setOuid("4501308d254a434b90fd9d96f63dc161");
//        info.setOrgId("f32e53e400474217a18d1c432a5ab2eb");
//        info.setAppId(APP_ID);
//        info.setUserId("2222");
//        info.setName("浙江优创信息技术有限公司");
//        info.setRedirectUrl(redirectUrl);
//
//        TedisUtil.set(organKey, info);
//
//        rest.eloginOrganRealnameUrl(organKey, "");
//    }
//
//    private void setContextAppId() {
//        RequestContext.clear();
//
//        HashMap<String, Object> ctx = Maps.newHashMap();
//        ctx.put("X-Tsign-Open-App-Id", APP_ID);
//        RequestContext.setContextMap(ctx);
//    }
//}
