package com.timevale.unittest.footstone.v2.rest;

import com.timevale.cert.common.service.enums.CertTimeEnum;
import com.timevale.footstone.user.service.inner.biz.BizCertService;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.cert.model.CertApplyConfigModel;
import com.timevale.footstone.user.service.model.cert.model.CertApplyUserModel;
import com.timevale.footstone.user.service.model.cert.request.*;
import com.timevale.footstone.user.service.model.cert.response.*;
import com.timevale.footstone.user.service.rest.v2.CertRestV2;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.util.Assert;

/**
 * <AUTHOR> fusu
 * @version V1.0
 * @title: CertApplyTest
 * @date 2021年02月03日 17:29
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CertV2Test {

    private final static String csr = "MIICVjCCAT4CAQAwEzERMA8GA1UEAwwI6YGu5aSpV1UwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCnQHB0NZt1aAcMuXSgxD2S9CNNBxuKBdyyxlvmnNt7zTrtAX0LVmlg03q61BgHI0Tx9kyD1L6HRz6oJTi3RWPm6nhndvZ03QxbU1EThSOsqKvUucbfbFp2J6INqJ4mSQGr8J7SLOlRjD3uQGiimXh5T/GjEqYDCNvsG39xoI2V3gyU66PpMQ2wfYDxAw6q3Q3ha//aPgcq7dHrK5odytCoyV0yEaXvbnQrJ5B3KO3vAIQ1kh3RRQF0yRc2cAqLCHi6QfHMyk38cqexjVimYifRYQDBwoBIZ+vm1cXW99IWnPVnPiJApHBMSK8OEJgbmVuy6GUzjOf98Al6w117UkEjAgMBAAEwDQYJKoZIhvcNAQEEBQADggEBAClhLoyszpQEVNGlLJ//xxFOcVQVJyWG6SfnVlCLCxg5/ifN6l4AxjokBXv7xA7QDDCfgFjk8KoA2ZKaBCckb+KOZ53uxCfMaCLpmi5FkSb0YPhCRPFG7OyLth2GDE6xSDFPJqMhAt2NTEJLi4PFrX4EOMyLDSWmQiovJ1oqZDcVVM7npHr+aSLGJh2fCdFTLkQffQ" +
            "iU3LuPJsla+cr31CM9ykqEp4X7hDf5xqfBvMHwUS6/QF1a09C8VE/Iukz1CjYaRipME3MOZdHsUCJg7gaX0kloLMOdpiSsyCkj8+7udfmDb1clHuLDvDAcN5s8CwqSKCT8pzqom7VyTW60ZIU=";


    @Autowired
    private BizCertService bizCertService;

    @Autowired
    private CertRestV2 certRestV2;
    @Before
    public void testBefore() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
    }


    @Test
    public void testApply() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        CertApplyWithoutAccountRequest request = new CertApplyWithoutAccountRequest();
        CertApplyUserModel user = new CertApplyUserModel();
        user.setCertName("遮天大公司");
        user.setLicenseType(19);
        user.setLicenseNumber("432427188510297707");
        request.setUserModel(user);
        CertApplyConfigModel configModel = new CertApplyConfigModel();
        configModel.setAlgorithm("SM2");
        request.setConfigModel(configModel);
        request.setCsr("MIIBGjCBwAIBADBeMRgwFgYDVQQDDA/pga7lpKnlpKflhazlj7gxGzAZBgNVBAsMEjQzMjQyNzE4ODUxMDI5NzcwNzELMAkGA1UEBwwCaHoxCzAJBgNVBAgMAnpqMQswCQYDVQQGEwJDTjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABPspIpYwGZgfq9bn8WnWdQeonThZxQQbjUgf+rk7Ci1GQVzgXT6VY0q5WKFVZ1BEVrAF2KFwP+44VlbSFwppf9ugADAMBggqgRzPVQGDdQUAA0cAMEQCIBeyNR63G2O7lnO6P8qHuCZTpZvZgsNqTkSXHHHUxyutAiB34fj1eQSM7Pott871oemWPNYH3R5d+vU5xbFKQJSldg==\\n");
        try {
            bizCertService.certApplyWithoutAccount(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void testDelay() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        String certId = "a0c0c5af21b744b3913143fc3eebe260";
        PostponeRequestV2 request = new PostponeRequestV2();
        request.setValidDuration("1Y");
         try {
            bizCertService.certDelayV2(certId, request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDelay01() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        String certId = "5376cf7469b54e85833f791d7927c9b9";
        PostponeRequestV2 request = new PostponeRequestV2();
        request.setValidDuration("1Y");
        try {
            bizCertService.certDelayV2(certId, request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDelayException() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        String certId = "e52b3051d69447ef87d9783207def2a7";
        PostponeRequestV2 request = new PostponeRequestV2();
        request.setValidDuration("1Y");
        request.setCsr("MIIBGjCBwAIBADBeMRgwFgYDVQQDDA/pga7lpKnlpKflhazlj7gxGzAZBgNVBAsMEjQzMjQyNzE4ODUxMDI5NzcwNzELMAkGA1UEBwwCaHoxCzAJBgNVBAgMAnpqMQswCQYDVQQGEwJDTjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABPspIpYwGZgfq9bn8WnWdQeonThZxQQbjUgf+rk7Ci1GQVzgXT6VY0q5WKFVZ1BEVrAF2KFwP+44VlbSFwppf9ugADAMBggqgRzPVQGDdQUAA0cAMEQCIBeyNR63G2O7lnO6P8qHuCZTpZvZgsNqTkSXHHHUxyutAiB34fj1eQSM7Pott871oemWPNYH3R5d+vU5xbFKQJSldg==\\n");
        try {
            bizCertService.certDelayV2(certId, request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDelayV1() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        String certId = "a0c0c5af21b744b3913143fc3eebe260";
         try {
            bizCertService.certDelay(certId, CertTimeEnum.ONEYEAR);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDelayV1Exception() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        String certId = "e52b3051d69447ef87d9783207def2a7";
        try {
            bizCertService.certDelay(certId, CertTimeEnum.ONEYEAR);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDelayV1ManyYear() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        CertApplyWithoutAccountRequest request = new CertApplyWithoutAccountRequest();
        CertApplyUserModel user = new CertApplyUserModel();
        user.setCertName("遮天大公司");
        user.setLicenseType(19);
        user.setLicenseNumber("432427188510297707");
        request.setUserModel(user);
        CertApplyConfigModel configModel = new CertApplyConfigModel();
        configModel.setAlgorithm("RSA");
        request.setConfigModel(configModel);
        request.setCsr("MIICoTCCAYkCAQAwXjEYMBYGA1UEAwwP6YGu5aSp5aSn5YWs5Y+4MRswGQYDVQQLDBI0MzI0MjcxODg1MTAyOTc3MDcxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCGqSGjUqCzQRPRhtHHexYbnr4ZYtW/e0KJqzfjaDOF0f24dZuCOLAL14BSxERZdhFuzGjZ0nBqG6I7y51sx60IbS9/J5Pd2D5U1c+L29uXj5+mxW2l4qbsGq/B++heaOMhKZO2lDk8RoI1R6BegFnRO12q80y17O0wUHmryPdWUUmIJWf8Ko/jfdeLZrLbcpwFGjKz5RYF+W0ETD15MmYpgL1ZmTkYC1PGgHjKSenUsYVz7O6EE7O2AJMAtlTZg2YPOdFP13gjiKJ/ub6GuQh8ntUw+IlKmc4Ppx/jNha84dZoI+Uc9DGcLsRRkU7qbc9QcbdANyOeE//AjNGH2U91AgMBAAEwDQYJKoZIhvcNAQEEBQADggEBAAaESAp/BgC1G2zWm6dH0FJ/BdzSPBPW5EtisxIky9cUayngLz97vNa4of+DXd91JKhe47n5LbqbkM5IOABkRLPCQ3CEXG+BE2cGEQFzaoFkvp21GYTvKCkz1IE/9rA4heS4s0wbuaxhPnK+kdcsd9QyN6/cy9rJ+lIcFEjKlX6WpMKV6GlC7gJdWrQbnubvJ6h7FmgHNupwM9sWpzOTqF0LjPnJwECzt6rKQCUnRPWR6goQXoBvWl9Fj99H3ldrimZGvFEPF6ro3iQ0UlcY4dUm8nb36eijmDAuVMnrxVpolsvw7MuZwfYVVE2pL+BT1TSghY9Q8OWFMERFij8x0Ro=");
        try {
            CertApplyWithoutAccountResponse certApplyWithoutAccountResponse = bizCertService.certApplyWithoutAccount(request);
            String certId2 = certApplyWithoutAccountResponse.getCertId();
            PostponeRequestV2 delayRequest = new PostponeRequestV2();
            delayRequest.setValidDuration("2Y");
            bizCertService.certDelayV2(certId2, delayRequest);

            certApplyWithoutAccountResponse = bizCertService.certApplyWithoutAccount(request);
            String certId3 = certApplyWithoutAccountResponse.getCertId();
            PostponeRequestV2 delayRequest3 = new PostponeRequestV2();
            delayRequest3.setValidDuration("3Y");
            bizCertService.certDelayV2(certId3, delayRequest3);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testUpdate() {

        CertUpdateRequest certUpdateRequest = new CertUpdateRequest();
        certUpdateRequest.setCertName("测试证书-变更");
        certUpdateRequest.setCsr(csr);

        try {
            CertUpdateResponse response = bizCertService.updateCert("c09866ab952745ce8c593dedb01a31a5", certUpdateRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void testDownload() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        String certId = "c09866ab952745ce8c593dedb01a31a5";
        try {
            CertDownloadResponse certDownloadResponse = bizCertService.certDownload(certId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGenerateAgreement() {

        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        try {
            GenerateAgreementTemplateRequest request = new GenerateAgreementTemplateRequest();
            List<String> oidList = new ArrayList<>();
            oidList.add("a00504ad17624b5598022b006cbaa05b");
            request.setOidList(oidList);
            request.setAppId("**********");
            request.setEnSaved(true);
            AgreementTemplateResponse response = bizCertService.generateAgreement(request);
            Assert.notNull(response);

            for(String oid:oidList){
                List<AgreementOid> agreementOids = response.getAgreementsList();
                agreementOids = agreementOids.stream().filter(agreementOid -> agreementOid.getOid().equals(oid)).collect(Collectors.toList());
                Assert.isTrue(CollectionUtils.isNotEmpty(agreementOids));
                AgreementOid agreementOid = agreementOids.get(0);
                ApplyByAgreementRequest applyByAgreementRequest = new ApplyByAgreementRequest();
                applyByAgreementRequest.setCertAgreementId(agreementOid.getCertAgreementId());
                ApplyByAgreementResponse applyByAgreementResponse = bizCertService.applyByAgreement(applyByAgreementRequest);
                Assert.notNull(applyByAgreementResponse);
            }

            GenerateAgreementTemplateRequest request1 = new GenerateAgreementTemplateRequest();
            List<String> oidList1 = new ArrayList<>();
            oidList1.add("a00504ad17624b5598022b006cbaa05b");
            request1.setOidList(oidList);
            request1.setEnSaved(false);
            WrapperResponse<AgreementTemplateResponse> response1 = certRestV2.generateAgreement(request1);
            Assert.notNull(response1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGenerateAgreementException() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        try{
            // 测试异常场景
            ApplyByAgreementRequest applyByAgreementRequest = new ApplyByAgreementRequest();
            applyByAgreementRequest.setCertAgreementId("1111");
            WrapperResponse<ApplyByAgreementResponse> wrapperResponse = certRestV2.applyByAgreement(applyByAgreementRequest);
        }catch (Exception e){

        }

        try{
            GenerateAgreementTemplateRequest request1 = new GenerateAgreementTemplateRequest();
            List<String> oidList = new ArrayList<>();
            oidList.add("a00504ad17624b5598022b006cbaa05b");
            request1.setOidList(oidList);
            request1.setEnSaved(true);
            WrapperResponse<AgreementTemplateResponse> response1 = certRestV2.generateAgreement(request1);
        }catch (Exception e){

        }
    }

    @Test
    public void addWhiteLicenseNumber() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        CreateCertBillBlackRequest request = new CreateCertBillBlackRequest();
        request.setIDCardNum("******************");
        try {
            certRestV2.addWhiteList(request);
        } catch (Exception e) {

        }
    }

    @Test
    public void addWhiteLicenseNumberError() {
        CreateCertBillBlackRequest request = new CreateCertBillBlackRequest();
        request.setIDCardNum("******************");
        try {
            certRestV2.addWhiteList(request);
        } catch (Exception e) {

        }
    }
}
