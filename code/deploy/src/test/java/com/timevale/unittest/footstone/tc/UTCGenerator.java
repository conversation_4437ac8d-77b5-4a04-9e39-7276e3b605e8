package com.timevale.unittest.footstone.tc;

import java.util.Random;

public class UTCGenerator {

    private static final Random R = new Random();

    static {
        R.setSeed(System.currentTimeMillis());
    }

    public static String utcMobile() {
        return String.format(UTCC.UT_FMT_MOBILE, R.nextInt(********));
    }

    public static String utcEmail() {
        return String.format(UTCC.UT_FMT_EMAIL, R.nextInt(9999));
    }

    public static String utcThirdKeyUser() {
        return String.format(UTCC.UT_FMT_THIRDKEY_USER, R.nextInt(999999));
    }

    public static String utcThirdAppUser() {
        return String.format(UTCC.UT_FMT_THIRDAPP_USER, R.nextInt(999999));
    }

    public static String utcPsnIdcard() {
        return String.format(UTCC.UT_FMT_CRED_PSN_CH_IDCARD, R.nextInt(9999));
    }

    public static String utcName() {
        return String.format(UTCC.UT_FMT_PROPERTY_INFO_NAME, R.nextInt(9999));
    }

    public static String utcOrgCode() {
        return String.format(UTCC.UT_FMT_CRED_ORG_CODE, R.nextInt(999999));
    }

    public static String utcUsccCode() {
        return String.format(UTCC.UT_FMT_CRED_USCC_CODE, R.nextInt(999999));
    }

    public static String utcString() {
        return String.format(UTCC.UT_FMT_STRING, R.nextInt(********));
    }

    public static String utcAccountUid() {
        return String.format(UTCC.UT_FMT_ACCOUNT_UID, R.nextInt(99999));
    }

    public static String utcAccountGuid(String accountUid) {
        return String.format(UTCC.UT_FMT_ACCOUNT_GUID, accountUid);
    }
}
