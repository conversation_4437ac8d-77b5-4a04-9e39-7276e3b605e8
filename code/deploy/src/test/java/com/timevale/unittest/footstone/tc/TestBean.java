package com.timevale.unittest.footstone.tc;

import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import lombok.Data;

@Data
public class TestBean {
    @ModelFieldDecoration("decoration.person.name")
    private String name223;

    @ModelFieldDecoration("decoration.ut.222")
    private String name222;

    @ModelFieldDecoration("decoration.ut.202")
    private String name202;

    @ModelFieldDecoration("decoration.ut.022")
    private String name022;

    @ModelFieldDecoration("decoration.ut.220")
    private String name220;

    @ModelFieldDecoration("decoration.ut.200")
    private String name200;

    @ModelFieldDecoration("decoration.ut.020")
    private String name020;

    @ModelFieldDecoration("decoration.mobile")
    private String mobile;
}
