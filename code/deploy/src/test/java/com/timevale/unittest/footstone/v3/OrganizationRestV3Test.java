package com.timevale.unittest.footstone.v3;

import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.easun.service.model.organization.input.BatchCheckOrganLegalInput;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.biz.v3.BizOrganizationServiceV3;
import com.timevale.footstone.user.service.model.dept.request.v3.BatchCheckLegalRequest;
import com.timevale.footstone.user.service.rest.v3.OrganizationRestV3;
import com.timevale.mandarin.weaver.utils.RequestContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/9/7 17:06
 */

@RunWith(MockitoJUnitRunner.Silent.class)
public class OrganizationRestV3Test {

    @InjectMocks
    private OrganizationRestV3 organizationRestV3;

    @Mock
    private BizOrganizationServiceV3 bizOrganizationServiceV3;

    @Mock
    private BizRoleService bizRoleService;

    @Before
    public void setUp(){
         HttpServletRequest servletRequest = new MyTestHttpServletRequest();
         servletRequest.setAttribute("X-Tsign-Open-Operator-Id","accountId");
        ReflectionTestUtils.setField(organizationRestV3,"servletRequest",servletRequest);
        RequestContext.put("httpServletRequest",servletRequest);
        when(bizRoleService.checkIfIsAdminOrLegal(any(),any())).thenReturn(true);
    }

    @Test
    public void batchCheckOrganLegalTest(){
        BatchCheckLegalRequest input = new BatchCheckLegalRequest();
        input.setMemberOid("accountId");
        input.setOrganOids(Lists.newArrayList("orgId1"));
        organizationRestV3.batchCheckOrganLegal(input);
    }


    @Test
    public void dataSyncOrganTest(){
        organizationRestV3.organHasDataSync("orgId");
    }


    @Test
    public void organLegalSummaryTest(){
        organizationRestV3.organLegalSummary("orgId");
    }
}
