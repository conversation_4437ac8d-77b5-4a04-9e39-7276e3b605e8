package com.timevale.unittest.footstone;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.enums.AccountDeleteStatusEnum;
import com.timevale.footstone.user.service.enums.LocalConfigLang;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.RpcAuthServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.doc.DocAdapter;
import com.timevale.footstone.user.service.inner.model.IdCardInfo;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.idcard.request.CheckMobileEmailRequest;
import com.timevale.footstone.user.service.model.account.idcard.request.SetThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.idcard.request.UnbindThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.login.request.TokenRequest;
import com.timevale.footstone.user.service.model.account.mods.*;
import com.timevale.footstone.user.service.model.account.request.*;
import com.timevale.footstone.user.service.model.account.response.*;
import com.timevale.footstone.user.service.model.organization.request.SetMemberMainOrganRequest;
import com.timevale.footstone.user.service.model.organization.response.BatchOrgByNamesRequest;
import com.timevale.footstone.user.service.model.realname.request.RealnameCallBackRequest;
import com.timevale.footstone.user.service.model.willauth.request.WillAuthCBRequest;
import com.timevale.footstone.user.service.rest.AccountRest;
import com.timevale.footstone.user.service.rest.EloginRest;
import com.timevale.footstone.user.service.rest.OrganizationRest;
import com.timevale.footstone.user.service.rest.RealnameRest;
import com.timevale.footstone.user.service.utils.*;
import com.timevale.footstone.user.service.utils.conv.AccountConverter;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.platform.util.CollectionUtils;
import com.timevale.token.service.enums.LoginType;
import com.timevale.token.service.model.input.AuthBasicLoginInput;
import com.timevale.token.service.model.output.AuthBasicLoginOutput;
import com.timevale.unittest.footstone.assist.AccountAssist;
import com.timevale.unittest.footstone.assist.RsaUtil;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.timevale.footstone.user.service.constants.DeleteConstants.ACCOUNT_DELETE_PRFIX;

/**
 * <AUTHOR> on 2019-08-12
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class AccountTest {

    @Autowired
    private AccountRest rest;

    @Autowired
    private RealnameRest realnameRest;

    @Autowired
    private EloginRest eloginRest;

    @Autowired
    private OrganizationRest orgRest;

    @Autowired
    private RpcOrgPlusService rpcOrgPlusService;

    @Autowired
    private AccountAssist assist;

    @Autowired
    private RsaUtil rsaUtil;

    @Autowired
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Autowired
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Autowired
    private DocAdapter docAdapter;

    @Autowired
    private RpcAuthServiceAdapt authServiceAdapt;
    private final Set<String> persons = new HashSet<>();

    private final Set<String> organs = new HashSet<>();

    private final String realnameCtx = ",RN,";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mvc;

    @Before
    public void setUp() {
        //初始化MockMvc对象
        mvc = MockMvcBuilders.webAppContextSetup(wac).build();
    }

    @Test
    public void getNotifyConfig(){

        String oid = "7db05015688048e89658b5a838e40411";
        GetNotifyConfigRequest configRequest = new GetNotifyConfigRequest();
        configRequest.setCategory(Sets.newHashSet("MOBILE_ALL", "EMAIL_ALL", "PRIORITY_NOTIFICATIONS"));

        NotifyConfigGetResponse getResponse = rest.getNotifyConfig(oid, configRequest).getData();
        Assert.assertEquals(getResponse.getConfigs().size(), 3);
        Assert.assertEquals(getResponse.getDefaultConfigs().size(), 2);
        Assert.assertTrue(CollectionUtils.isNotEmpty(getResponse.getConfigs().get(0).getScenes()));
    }

    @Test
    public void getLocaleConfig(){
        String oid = "3bb66d0174b14365ac5a7bd7c380d2b5";
        LocalConfigGetResponse getResponse = rest.getLocaleConfig(oid, new  GetLocalConfigRequest()).getData();
        Assert.assertEquals(getResponse.getLang(), LocalConfigLang.ZH_CN.getCode());

        String oid1 = "974a2665419d4d3c817780fb299c681f";
        UpdateLocalConfigRequest updateLocalConfigRequest = new UpdateLocalConfigRequest();
    updateLocalConfigRequest.setLang(LocalConfigLang.EN_US.getCode());
        WrapperResponse wrapperResponse = rest.updateLocaleConfig(oid1, updateLocalConfigRequest);
        Assert.assertEquals(wrapperResponse.ifSuccess(), true);

        getResponse = rest.getLocaleConfig(oid1, new  GetLocalConfigRequest()).getData();
        Assert.assertEquals(getResponse.getLang(), LocalConfigLang.EN_US.getCode());
    }


    @Test
    public void testGetSpecificInfo() {
        String oid = "3bb66d0174b14365ac5a7bd7c380d2b5";
        Map<String, String> resp =
                rest.specificInfo(oid, "INFO_NAME,CRED_PSN_CH_IDCARD,MOBILE").getData();
        Assert.assertNotNull(resp.get("INFO_NAME"));
        Assert.assertNotNull(resp.get("CRED_PSN_CH_IDCARD"));
        Assert.assertNotNull(resp.get("MOBILE"));
    }

    @Test
    public void testCreatePerson() {
        try {
            PersonProperties prop = new PersonProperties();
            prop.setRealnameContext(realnameCtx);

            PersonCreateRequest request = new PersonCreateRequest();
            request.setProperties(prop);

            // AccountBaseResponse resp = ResponseUtil.get(rest.createOverall(request));
            // 里面有HttpRequest，不能直接调用
            ICUserId icuserId =
                    icUserPlusServiceAdapt.createICUser(AccountConverter.conv(request), null);
            String accountId = icuserId.getOuid();
            Assert.assertNotNull(accountId);
            persons.add(accountId);

            UserAllInfoResponse getResp = ResponseUtil.get(rest.allInfo(accountId, null));

            Assert.assertEquals(realnameCtx, getResp.getProp().getProperties().getRealnameContext());
        }catch (Exception e){

        }
    }

    @Test
    public void testCreateOrgan() {
        try {
            String creator = assist.createEmpty();
            persons.add(creator);

            OrgProperties prop = new OrgProperties();
            prop.setRealnameContext(realnameCtx);

            OrgCreateRequest request = new OrgCreateRequest();
            request.setCreater(creator);
            request.setProperties(prop);

            String orgId =
                    rpcOrgPlusService
                            .createICOrgAndCompanyWithSource(
                                    AccountConverter.conv(creator, request, null))
                            .getData()
                            .getOuid();
            organs.add(orgId);

            OrgAllInfoResponse getResp = ResponseUtil.get(orgRest.getInfo(orgId));
            OrgCreateRequest createRequest=new OrgCreateRequest();
            OrgIdcardCollection tOrgIdCard=new OrgIdcardCollection();
            IdcardThirdpartyDetail i=new IdcardThirdpartyDetail();
            i.setThirdpartyKey("WE_CHAT");
            i.setThirdpartyUserId("**************");
            tOrgIdCard.setThirdparty(i);
            createRequest.setCreater("974a2665419d4d3c817780fb299c681f");
            createRequest.setIdcards(tOrgIdCard);
            MockHttpServletRequest servletRequest = new MockHttpServletRequest();
            servletRequest.addHeader("X-Tsign-Open-Operator-Id",creator);
            RequestContext.put("httpServletRequest",servletRequest);
            RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
            organs.add(orgRest.createOrg(createRequest).getData().getAccountId());
            Assert.assertEquals(realnameCtx, getResp.getProp().getProperties().getRealnameContext());
        }catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }

    @Test
    public void testPrivilegeDesensitization() {
        try {
            String orgId = "d6a34fcf82774b8ba34549a3fe103113";
            String operationPermit = "admin";
            String bizType = "1";
            rest.privilegeDesensitization(orgId,0,bizType,operationPermit,10);
        }catch (Exception e) {

        }

    }

//    @Test
//    public void testGetMultiIdcard() {
//        // 创建账号，设置多登录凭证
//        String oid = assist.createEmpty();
//        persons.add(oid);
//
//        String key1 = BuiltinThirdparty.ALI_PAY;
//        String userId1 = UTCGenerator.utcThirdKeyUser();
//        assist.bindThirdparty(oid, key1, userId1);
//
//        TestUtil.sleep(1000);
//
//        String key2 = BuiltinThirdparty.DING_TALK;
//        String userId2 = UTCGenerator.utcThirdKeyUser();
//        assist.bindThirdparty(oid, key2, userId2);
//
//        // 查询多登录凭证
//        WrapperResponse<UserAllFatInfoResponse> infosResp = rest.allInfos(oid, null);
//        UserAllFatInfoResponse infosData = ResponseUtil.get(infosResp);
//        List<IdcardThirdpartyDetail> infosTps =
//                infosData.getLoginInfo().getIdcard().getThirdparties();
//
//        Assert.assertEquals(2, infosTps.size());
//
//        Assert.assertEquals(key1, infosTps.get(0).getThirdpartyKey());
//        Assert.assertEquals(userId1, infosTps.get(0).getThirdpartyUserId());
//        Assert.assertEquals(key2, infosTps.get(1).getThirdpartyKey());
//        Assert.assertEquals(userId2, infosTps.get(1).getThirdpartyUserId());
//
//        // 老接口查询单个
//        WrapperResponse<UserAllInfoResponse> infoResp = rest.allInfo(oid, null);
//        UserAllInfoResponse infoData = ResponseUtil.get(infoResp);
//        IdcardThirdpartyDetail infoTp = infoData.getLoginInfo().getIdcard().getThirdparty();
//
//        Assert.assertNotNull(infoTp);
//        Assert.assertEquals(key1, infoTp.getThirdpartyKey());
//        Assert.assertEquals(userId1, infoTp.getThirdpartyUserId());
//
//        // 解绑一个
//        assist.unbindThirdParty(oid, key1);
//
//        WrapperResponse<UserAllFatInfoResponse> laterResp = rest.allInfos(oid, null);
//        UserAllFatInfoResponse laterData = ResponseUtil.get(laterResp);
//        List<IdcardThirdpartyDetail> laterTps =
//                laterData.getLoginInfo().getIdcard().getThirdparties();
//
//        Assert.assertEquals(1, laterTps.size());
//
//        Assert.assertEquals(key2, laterTps.get(0).getThirdpartyKey());
//        Assert.assertEquals(userId2, laterTps.get(0).getThirdpartyUserId());
//    }

    @Test
    public void testDeleteWrongType() {
        try{
            String accountId = assist.createEmpty();
            String orgId = assist.createOrgan(accountId);
            persons.add(accountId);
            organs.add(orgId);

            try {
                rest.deleteAccount(orgId);
                Assert.fail();
            } catch (FootstoneUserErrors.FsAccountTypeNotMatch
                    | FootstoneUserErrors.FsNotSupportOrgan ignored) {

            }
        }catch (Exception e){

        }

    }
//    @Test
//    public void testGetSaasOrgRoleOem() throws Exception{
//        mvc.perform(
//                MockMvcRequestBuilders.get(
//                        "/v1/accounts/4501308d254a434b90fd9d96f63dc161/organizations/saas-org-roles",
//                        "4501308d254a434b90fd9d96f63dc161")
//                        .contentType(MediaType.APPLICATION_JSON_UTF8)
//                        .header("X-Tsign-Open-App-Id", "**********")
//                        .header(
//                                HeaderConstants.HEADER_AUTHORIZER_TOKEN,
//                                "64a17e2cf7894891a7c961d44a5969c9"))
//                .andExpect(
//                        mvcResult -> {
//                            System.out.print(mvcResult.getResponse().getContentAsString());
//                        })
//                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
//    }

    //    @Test
    //    public void testAuthorizeElogin() {
    //        AccountEloginSignRequest request = new AccountEloginSignRequest();
    //        request.setEncryptContent(
    //
    // "gQv1gsqW9GVNUC8c+b34/DOZ4lw/IrxlVkALYZUQr88xkgygHs0IFGGYC7RO3sy/Dj/WZG4iXZFprZGRlUGjvfyLrh3+vz" +
    //
    // "/X7OBwuhfPS1hYZsUJmQQCl4w75A3x/KbYElQth+xaYS3PKOtZ5uPv1SHYOtdROOzKtIoDuqbot8fIO9x1dmok6BV" +
    //
    // "/2seFTgq9J2hAXnl3009FTzSWmuBtIqjQEy2EHaSCeYEbjh2aaZ8F4fLdrGLEN+Iy8/ogtE8OR9IR" +
    //
    // "+OtJlniomEu9IDSrRJ3UHtW/isJgfK3DMcCtDfCoTlwgDq0ZzSRGcyGBHcm62hAPE7G13xQBIc9U6+v+WA==");
    //        request.setShortLinkUrl("https://testt.tsign.cn/xqHOs2UiZQXJ");
    //        HashMap<String, Object> ctx = Maps.newHashMap();
    //        ctx.put("X-Tsign-Open-App-Id", "**********");
    //        RequestContext.setContextMap(ctx);
    //        try {
    //            eloginRest.authorizeSignElogin(request);
    //        } catch (FootstoneUserErrors.ValidTimeNotEnough ignored) {
    //
    //        }
    //
    //        RequestContext.clear();
    //    }

    //    @Test
    //    public void testUpdateEloginConfig() {
    //        EloginConfigRequest request = new EloginConfigRequest();
    //        request.setThirdKey(String.valueOf(Math.random()));
    //        request.setContent("a1b2c3");
    //        eloginRest.updateEloginConfig("056bf4b9ea954da8b396bfa94a83536f", request);
    //    }

    @Test
    public void testGetEloginConfig() {
        try {
            eloginRest.getEloginConfig("056bf4b9ea954da8b396bfa94a83536f");
        } catch (Exception e) {

        }
    }

    @After
    public void clean() {
        persons.forEach(rest::deleteAccount);
        organs.forEach(orgRest::deleteOrg);
    }

    @Test
    public void testGetOrgsByEs() {
        BatchOrgByNamesRequest in = new BatchOrgByNamesRequest();
        in.setNames("天谷,杭州天谷信息科技");
        in.setProjectId(null);
        try {
            orgRest.getOrgsByNames(in);

        } catch (Exception e) {

        }
    }

    @Test
    public void testGetOrgsByEsNew() {
        BatchOrgByNamesRequest in = new BatchOrgByNamesRequest();
        in.setNames("天谷,杭州天谷信息科技");
        in.setProjectId(null);
        try {
            orgRest.searchOrgNew("天谷信息", null, null, null, null, null, null, null, null);

        } catch (Exception e) {

        }
    }

    @Test
    public void testCheckMobile() {
        try {
            CheckMobileEmailRequest checkMobileEmailRequest = new CheckMobileEmailRequest();
            String redisKey = "71e0dc92a1424f90bf0f2520464af7e2";
            checkMobileEmailRequest.setPrincipal("150****8223");
            HttpServletRequest servletRequest = new MyTestHttpServletRequest();
            servletRequest.setAttribute(RequestUtil.PRINCIPAL_KEY, redisKey);
            TedisUtil.set(redisKey, "15067108223", 60l, TimeUnit.SECONDS);
            RequestContext.put(RequestUtil.PRINCIPAL_KEY, "71e0dc92a1424f90bf0f2520464af7e2");
            checkMobileEmailRequest.setPrincipal(PrincipalUtil.getRealPrincipal(checkMobileEmailRequest.getPrincipal()
                    , servletRequest));

            checkMobileEmailRequest.setPrincipal("15067108223");
//            HttpServletRequest servletRequest = new MyTestHttpServletRequest();
            RequestContext.put(RequestUtil.PRINCIPAL_KEY, "71e0dc92a1424f90bf0f2520464af7e2");
            rest.checkMobileOrEmail(checkMobileEmailRequest);

        } catch (Exception e) {

        }
    }

    @Test
    public void testAllInfos() {
        rest.getByIdcard("15088608331", "MOBILE");
        String oid = "88e44a1ebef441d69306d2abcc0eb276";
        WrapperResponse<UserAllFatInfoResponse> infos = rest.allInfos(oid, null);
    }

    //    @Test
    //    public void testGenerateEloginKey() {
    //        String content = rsaUtil.encryptByPublic("dbca4321");
    //
    //        String appId = "**********";
    //        HashMap<String, Object> ctx = Maps.newHashMap();
    //        ctx.put("X-Tsign-Open-App-Id", appId);
    //        RequestContext.setContextMap(ctx);
    //
    //        AccountEloginBindRequest bindRequest = new AccountEloginBindRequest();
    //        bindRequest.setEncryptContent(content);
    //        bindRequest.setRedirectUrl("https://www.baiduc.om");
    //        String eloginKey =
    // eloginRest.authorizeBindElogin(bindRequest).getData().getEloginKey();
    //
    //        RequestContext.clear();
    //
    //        Assert.assertNotNull(eloginKey);
    //    }

    //    @Test
    //    public void testUnbindElogin() {
    //        String oid = "056bf4b9ea954da8b396bfa94a83536f";
    //        String appId = "**********";
    //        String userId = "abcde54321";
    //
    //        assist.bindThirdparty(oid, appId, userId);
    //
    //        String content = rsaUtil.encryptByPublic(userId);
    //
    //        HashMap<String, Object> ctx = Maps.newHashMap();
    //        ctx.put("X-Tsign-Open-App-Id", appId);
    //        RequestContext.setContextMap(ctx);
    //
    //        AccountEloginUnbindRequest unbindRequest = new AccountEloginUnbindRequest();
    //        unbindRequest.setEncryptContent(content);
    //        eloginRest.authorizeUnbindElogin(unbindRequest);
    //
    //        RequestContext.clear();
    //    }

    @Test
    public void thirdPartyBindTest() {

        String accountId = "974a2665419d4d3c817780fb299c681f";
        String key = "tgtest";
        UnbindThirdPartyRequest unbindThirdPartyRequest = new UnbindThirdPartyRequest();
        unbindThirdPartyRequest.setKey(key);
        rest.unbindThirdParty(accountId, unbindThirdPartyRequest);
        SetThirdPartyRequest request = new SetThirdPartyRequest();
        request.setKey(key);
        request.setUserId("tgtest-test1");
        rest.setThirdParty(accountId, request);
    }

    @Test
    public void thirdPartyUnBindTest() {
        UnbindThirdPartyRequest request = new UnbindThirdPartyRequest();
        request.setKey("tgtest");
        rest.unbindThirdParty("974a2665419d4d3c817780fb299c681f", request);
    }

    @Test
    public void testPreDelete() {
        try {
            String accountId = "459de21fc4b448f19ab5380bed2a92f4";
            String accoutUid = "24e89ff8f841470ca25ae0cd38c602f5";
            String loginUser = "***********";
            String pwd = "e10adc3949ba59abbe56e057f20f883e";
            String token = login(accoutUid, loginUser, pwd);
            AccountPreDeleteReuqest request = new AccountPreDeleteReuqest();
            request.setRemark("注销");
            request.setToken(token);
            request.setClientType("PC");
            rest.preDelete(accountId, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void testPreDeleteWithMail() {
        try {
            String accountId = "7198b089471a4d709aef3dd4cf121fc6";
            AccountPreDeleteReuqest request = new AccountPreDeleteReuqest();
            IdCardInfo idCardInfo = new IdCardInfo();
            idCardInfo.setMobile("");
            idCardInfo.setEmail("");
            idCardInfo.getCodeAuthSendTypeEnum();
            idCardInfo.getEmail();
            idCardInfo.getLoginUser();
            rest.preDelete(accountId, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void testGetWillAuthResult() {
        try {
            String serviceId = "ef6119b61ad4405189dd0809278a52ae";
            AccountDeleteModel model = new AccountDeleteModel();
            model.setBizId("60f0bc84477747a5a990b3cb658c12e2");
            model.setDeleteStatus(AccountDeleteStatusEnum.PRE);
            model.setRemark("123");
            TedisUtil.set(ACCOUNT_DELETE_PRFIX + serviceId, model);
            rest.getWillAuthResult(serviceId);
        } catch (Exception e) {

        }
    }

    @Test
    public void testWillAuthCBRequest() {
        try {
            String accountId = "9ac6a3400744441e85b6dd24a2d2699b";
            String serviceId = UUID.randomUUID().toString().replaceAll("-", "");
            String bizId = "60f0bc84477747a5a990b3cb658c12e2";
            AccountDeleteModel model = new AccountDeleteModel();
            model.setBizId(bizId);
            model.setDeleteStatus(AccountDeleteStatusEnum.PRE);
            model.setRemark("123");
            model.setOuid(accountId);
            TedisUtil.set(ACCOUNT_DELETE_PRFIX + serviceId, model);

            WillAuthCBRequest request = new WillAuthCBRequest();
            request.setSuccess(true);
            request.setBizId(bizId);
            request.setAccountId(accountId);
            rest.deleteByAuth(serviceId, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void testGetDeleteResult() {
        try {
            String serviceId = "ef6119b61ad4405189dd0809278a52ae";
            AccountDeleteModel model = new AccountDeleteModel();
            model.setBizId("60f0bc84477747a5a990b3cb658c12e2");
            model.setDeleteStatus(AccountDeleteStatusEnum.PRE);
            model.setRemark("123");
            TedisUtil.set(ACCOUNT_DELETE_PRFIX + serviceId, model);
            rest.getDeleteResult(serviceId);
        } catch (Exception e) {

        }
    }

    @Test
    public void testDeleteAuthCode() {

        try {
            String accountId = "fd4babe1d2af41faa8bd464396fe42a4";
            String accoutUid = "acb61409b1c34306993ff1567239fb96";
            String loginUser = "***********";
            String pwd = "e10adc3949ba59abbe56e057f20f883e";
            String token = login(accoutUid, loginUser, pwd);
            TokenRequest request = new TokenRequest();
            request.setToken(token);
            rest.authCode(accountId, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void testOidWithMail() {

        try {
            String accountId = "7198b089471a4d709aef3dd4cf121fc6";
            TokenRequest request = new TokenRequest();
            rest.authCode(accountId, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void deleteUnRealNameAccount() {
        String accountId = "fd4babe1d2af41faa8bd464396fe42a4";
        try {
            String serviceId = "1a349322d9bf4b0cac8a7c14cb231e84";
            AccountDeleteByAuthCodeRequest request = new AccountDeleteByAuthCodeRequest();
            request.setAuthCode("1234");
            request.setServiceId(serviceId);
            rest.deleteUnRealNameAccount(accountId, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void checkDelete() {
        try {
            String accountId = "459de21fc4b448f19ab5380bed2a92f4";
            rest.checkDelete(accountId);
            accountId = "88e44a1ebef441d69306d2abcc0eb276";
            rest.checkDelete(accountId);
        } catch (Exception e) {

        }
    }

    private String login(String accountUid, String loginUser, String pwd) {
        AuthBasicLoginInput input = new AuthBasicLoginInput();
        input.setEquipId(UUID.randomUUID().toString());
        input.setExpireTime(null);
        input.setLoginType(LoginType.PASSWORD_LOGIN);
        input.setSourceCode(UUID.randomUUID().toString());
        input.setAccountUid(accountUid);
        input.setLoginUser(loginUser);
        input.setPassword(pwd);
        AuthBasicLoginOutput login = authServiceAdapt.login(input);
        return login.getToken();
    }

    @Test
    public void testSetMainOrg() {
        try {
            SetMemberMainOrganRequest request = new SetMemberMainOrganRequest();
            request.setOrgId("23a91d7eb9744b689d8e9373119e1a76");
            rest.setMainOrg("974a2665419d4d3c817780fb299c681f", request);

        } catch (Exception e) {

        }
    }

    @Test
    public void getRealAccountByCode() {
        try {
            rest.getRealAccountByCode("***************", null);
            rest.getRealAccountByCode("330327199402152001", null);
        } catch (Exception e) {

        }
    }


    @Test
    public void testDesensitize() {
        try {
            String idCard = "123456789012345678";
            String bankCard = "62220812345678901234";
            String name = "张三";
            String businessLicense = "12345678901234567890";
            String phone = "***********";
            String email = "<EMAIL>";

            System.out.println("脱敏后的身份证号: " + DesensitizeUtil.desensitize(idCard));
            System.out.println("脱敏后的银行卡号: " + DesensitizeUtil.desensitize(bankCard));
            System.out.println("脱敏后的姓名: " + DesensitizeUtil.desensitize(name));
            System.out.println("脱敏后的营业执照号: " + DesensitizeUtil.desensitize(businessLicense));
            System.out.println("脱敏后的手机号: " + DesensitizeUtil.desensitize(phone));
            System.out.println("脱敏后的邮箱: " + DesensitizeUtil.desensitize(email));
        } catch (Exception e) {

        }
    }

    @Test
    public void safeConfig() {
        String accountId = "a84d9fff9b2e4c22af6bb85171b8df43";
        rest.getSafeConfig(accountId);

        AccountSafeConfigRequest accountSafeConfigRequest = new AccountSafeConfigRequest();
        accountSafeConfigRequest.setCategory(0);
        accountSafeConfigRequest.setIsOpen(true);
        rest.updateSafeConfig(accountId, accountSafeConfigRequest);
    }

    @Test
    public void safeWarns() {
        String accountId = "a84d9fff9b2e4c22af6bb85171b8df43";
        rest.safeWarnsHistory(accountId, 1, 10);

        AccountWarnRuleRequest request = new AccountWarnRuleRequest();
        request.setIsOpen(false);
        request.setCategory(1);
        request.setContent("中国，浙江，杭州");
        rest.updateWarnRule(accountId, request);
    }

    @Test
    public void testGetSaasOrgRoles() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get(
                        "/v1/accounts/755f196b22bb4d47b50aee51c4a561fa/organizations/saas-org-roles",
                        "755f196b22bb4d47b50aee51c4a561fa")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));


    }

    @Test
    public void testUserInfoExport() throws Exception {
        String key="footstone_user_info_export_count_709d2fe7007146a1b0aca5713790731f";
        TedisUtil.delete(key);
        mvc.perform(
                MockMvcRequestBuilders.get(
                        "/v1/accounts/709d2fe7007146a1b0aca5713790731f/exportUserInfo")
                        .param("recEmail","<EMAIL>")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));


    }
    @Test
    public void testgetDecorationAllInfos() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get(
                        "/v1/accounts/05687447c3a14a7aaf4156786ff76d35/getDecorationAllInfos")
                        .param("needExt","true")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));


    }

    @Test
    public void testGetLoginHistory() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get(
                        "/v1/accounts/755f196b22bb4d47b50aee51c4a561fa/getLoginHistory")
                        .contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test
    public void testGetSpecification() {
        rest.specificInfo("974a2665419d4d3c817780fb299c681f", "LOGIN_MOBILE");
        rest.specificInfo("974a2665419d4d3c817780fb299c681f", "LOGIN_EMAIL");
    }


    @Test
    public void testRealNameCallback() {
        RealnameCallBackRequest request = new RealnameCallBackRequest();
        request.setAccountId("4f949122749546dd8d43c2d67d681c09");
        request.setStatus(true);
        request.setContext(Maps.newHashMap());
        WrapperResponse<?> response = realnameRest.realNameCallback(request);
        Assert.assertTrue(response.getMessage(), response.getCode() == 0);
    }

    @Test
    public void testGetAccountBaseInfo() {

        PersonCreateRequest request = new PersonCreateRequest();

        // AccountBaseResponse resp = ResponseUtil.get(rest.createOverall(request));
        // 里面有HttpRequest，不能直接调用
        ICUserId icuserId =
                icUserPlusServiceAdapt.createICUser(AccountConverter.conv(request), null);
        String accountId = icuserId.getOuid();
        Assert.assertEquals(rest.getBaseInfo(accountId).getData().getOuid(), accountId);
    }

    @Test
    public void freemarkerTest() throws IOException {
        Map<String, Object> params = Maps.newHashMap();
        params.put("headerImg", "");
        params.put("name", "1");
        params.put("certType", "2");
        params.put("certNum", "3");
        params.put("mobile", "4");
        params.put("email", "5");
        params.put("address", "6");
        String pwd = IOUtil.genUzipPwd(8);
        byte[] resultFile = IOUtil.zipWithPwd(FreeMarkerUtils.createHtml("exportUserInfo.ftl", params), "index.html",
                pwd);
        FileUtils.writeByteArrayToFile(new File("aaa.zip"), resultFile);
        System.out.println(pwd);
    }

    @Test
    public void testGetSortedInfos() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get(
                        "/v1/accounts/getSortedInfos")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HeaderConstants.HEADER_OPERATOR_ID,"cd7ee89e84bc458e99dd13c1e1745345"))
                .andExpect(
                        mvcResult -> {
                            System.out.print(mvcResult.getResponse().getContentAsString());
                        })
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }
}
