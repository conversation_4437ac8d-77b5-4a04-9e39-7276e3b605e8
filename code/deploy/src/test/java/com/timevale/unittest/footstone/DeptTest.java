package com.timevale.unittest.footstone;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizDeptService;
import com.timevale.footstone.user.service.model.dept.request.CreateDeptRequest;
import com.timevale.footstone.user.service.model.dept.request.UpdateDeptRequest;
import com.timevale.footstone.user.service.model.member.request.AddMemberDeptRequest;
import com.timevale.footstone.user.service.model.member.request.DeleteMemberDeptRequest;
import com.timevale.footstone.user.service.rest.MemberRest;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import javax.servlet.http.HttpServletRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.text.SimpleDateFormat;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DeptTest {

    String orgId = "5ccb1c1b0cc84eb9abced907cf392717";

    @Autowired
    private BizDeptService svc;
    @Autowired
    private MemberRest memberRest;

    @Before
    public void init() {
        // 审计日志初始化请求头参数
        HttpServletRequest httpServletRequest = new MyTestHttpServletRequest();
        httpServletRequest.setAttribute(WeaverConstants.APP_ID_LABEL,"3438757422");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_REAL_IP,"127.0.0.1");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"d6a34fcf82774b8ba34549a3fe103113");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"cd7ee89e84bc458e99dd13c1e1745345");
        RequestContext.put(WeaverConstants.REQUEST_KEY,httpServletRequest);
        AuditLogCommon.initAuditLogContext();
    }

    @Test
    public void testCreateAndDelete(){
        String memberId = "88e44a1ebef441d69306d2abcc0eb276";
        CreateDeptRequest request = new CreateDeptRequest();
        request.setDeptName("测试"+ DateUtils.getDateString(DateUtils.now(), new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        request.setParentDeptId("ALL");
        try {
            String deptId = svc.createDept(request,orgId, memberId);
            svc.deptDetail(orgId,deptId);
            UpdateDeptRequest update = new UpdateDeptRequest();
            update.setDeptName("开发"+ DateUtils.getDateString(DateUtils.now(), new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS)));
            svc.updateDept(orgId,deptId,update);

            AddMemberDeptRequest addMemberDeptRequest = new AddMemberDeptRequest();
            addMemberDeptRequest.setDeptId(deptId);
            addMemberDeptRequest.setAccountId(memberId);
            memberRest.addDept(orgId,addMemberDeptRequest);
            DeleteMemberDeptRequest deleteMemberDeptRequest = new DeleteMemberDeptRequest();
            deleteMemberDeptRequest.setDeleteRelation(false);
            memberRest.removeDept(orgId,deptId,memberId,deleteMemberDeptRequest);
            svc.deleteDept(orgId,deptId);
        } catch (Exception e) {
        }
    }
//    @Test
//    public void testBatch() {
//
//        String fileKey = "$bc78e723f6a1485fbdfb5c844639622f$**********$H";
//        String taskId = svc.batchCreate(orgId, "A",fileKey);
//        LockSupport.parkNanos(TimeUnit.SECONDS.toNanos(1));
//        svc.getBatchCreateResult(orgId, taskId);
//        svc.deptTree(orgId,true);
//    }



}
