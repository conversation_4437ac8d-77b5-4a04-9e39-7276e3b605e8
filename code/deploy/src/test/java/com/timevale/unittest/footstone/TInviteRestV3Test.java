package com.timevale.unittest.footstone;

import com.google.common.collect.Lists;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.easun.service.api.RpcDeptPlusService;
import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import com.timevale.easun.service.model.organization.ICOrg.output.BizICCreateMemberOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.biz.v3.BizOrganizationServiceV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizTInviteServiceV3Impl;
import com.timevale.footstone.user.service.inner.model.v3.ThirdSyncModel;
import com.timevale.footstone.user.service.model.dept.response.v3.OrganizationDataSyncResponseV3;
import com.timevale.footstone.user.service.model.invite.request.v3.AdminBatchOperate4InvitationReqV3;
import com.timevale.footstone.user.service.model.invite.request.v3.InviteJoinMemberReqV3;
import com.timevale.footstone.user.service.rest.v3.TInviteRestV3;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.footstone.user.service.utils.UUIDUtils;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.notice.service.api.RpcInviteJoinService;
import com.timevale.notice.service.api.RpcInviteJoinServiceV3;
import com.timevale.notice.service.api.RpcTInviteService;
import com.timevale.notice.service.model.service.biz.mods.TInviteContextData;
import com.timevale.notice.service.model.service.biz.output.InviteJoinMember;
import com.timevale.notice.service.model.service.biz.output.InviteJoinMembersOutput;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@RunWith(MockitoJUnitRunner.Silent.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TInviteRestV3Test {

  @InjectMocks
  private TInviteRestV3 tInviteRestV3;

  @InjectMocks
  private BizTInviteServiceV3Impl bizTInviteServiceV3;

  @Mock
  private RpcTInviteService inviteService;

  @Mock
  private RpcInviteJoinService inviteJoinService;

  @Mock
  private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

  @Mock
  private RpcDeptPlusServiceAdapt rpcDeptPlusServiceAdapt;

  @Mock
  private BizRoleService bizRoleService;

  @Mock
  private BizOrganizationServiceV3 bizOrganizationServiceV3;


  @Mock
  private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;

  @Mock
  private RpcSaaSVipServiceAdapt vipServiceAdapt;

  @Mock
  private RpcDeptPlusService deptPlusService;

  @Mock
  private IdAdapt idAdapt;

  @Mock
  private Tedis tedis;

  @Mock
  private AuditLogCommon auditLogCommon;

  @Mock
  private RpcAccountPlusServiceAdapt accountPlusServiceAdapt;

  @Mock
  private RpcInviteJoinServiceV3 inviteJoinServiceV3;

  private static final String INVITE_ORG_ID = "04a47793479a4b3c9e69f07c0680b573";

  private static final String HEAD_X_TSIGN_OPERATOR = "f430bb5ea857493b96e35abe0fa49d4a";

  private static final String ADMIN_INVITE_INIT_INVITE_ID = "15026ca9a51b47bfa12fe05b524ed15a";

  private static final String ADMIN_INVITE_INIT_LOGIN_ACCOUNT = "***********";

  private static final String MEMBER_SCAN_INIT_INVITE_ID = "d70d39fca4a4405cbca05cd721284f38";

  @Before
  public void setUp() {
    StringOperations<String,String> tedisString = new MyDefaultStringOperations();
    when(tedis.string()).thenReturn(tedisString);
    TedisUtil.setTedis(tedis);
    when(idAdapt.orgIdToOrganId(any())).thenReturn("organId");
    when(deptPlusService.getChildren(any())).thenReturn(RpcOutput.with(MockUntils.getDeptChildren()));
    ReflectionTestUtils.setField(tInviteRestV3,"tInviteServiceV3",bizTInviteServiceV3);
    ReflectionTestUtils.setField(bizTInviteServiceV3,"bizOrganizationServiceV3",bizOrganizationServiceV3);
    ReflectionTestUtils.setField(bizTInviteServiceV3,"inviteService",inviteService);
    ReflectionTestUtils.setField(bizTInviteServiceV3,"inviteJoinService",inviteJoinService);
    ReflectionTestUtils.setField(bizTInviteServiceV3,"icOrgPlusServiceAdapt",icOrgPlusServiceAdapt);
    ReflectionTestUtils.setField(bizTInviteServiceV3,"rpcDeptPlusServiceAdapt",rpcDeptPlusServiceAdapt);
//    ReflectionTestUtils.setField(bizTInviteServiceV3,"bizRoleService",bizRoleService);
    ReflectionTestUtils.setField(bizTInviteServiceV3,"inviteJoinServiceV3",inviteJoinServiceV3);
    when(inviteJoinService.queryInviteJoinMembers(any())).thenReturn(RpcOutput.with(new InviteJoinMembersOutput()));
    String orgId = "orgId";
    Map<String, ThirdSyncModel> thirdSyncModelMap = new HashMap<>();
    ThirdSyncModel syncModel = new ThirdSyncModel();
    syncModel.setPlatformName(ThirdPartyPlatFormEnum.FEI_SHU.getPlatformName());
    syncModel.setOpenThirdSync(false);
    syncModel.setPlatform(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform());
    thirdSyncModelMap.put(orgId,syncModel);
    OrganizationDataSyncResponseV3 responseV3 = new OrganizationDataSyncResponseV3();
    responseV3.setIsOpenThirdSync(false);
    when(bizOrganizationServiceV3.getOrganHasDataSync(anyString())).thenReturn(responseV3);
    when(icOrgPlusServiceAdapt.getOrgSummary(anyString())).thenReturn(MockUntils.mockBizOrgSummaryOutputV3());
    when(vipServiceAdapt.getVipInfo(anyString())).thenReturn(MockUntils.mockAccountVipQueryOutput());
    when(vipServiceAdapt.queryVipFunctionInfo(anyString())).thenReturn(MockUntils.mockVipFunctionQueryOutput());
    when(accountPlusServiceAdapt.getEsAccountByIdCardCaseInsensitive(anyString())).thenReturn(Lists.newArrayList(MockUntils.getAccountOutPut()));
  }

//  @Test
//  public void testBachInviteJoinAsync() {
//    clearBatchJoinTask(HEAD_X_TSIGN_OPERATOR, INVITE_ORG_ID);
//    initRequestContext();
//
//    BatchInviteJoinRequest request = new BatchInviteJoinRequest();
//    List<InviteJoinPerson> invitedPersons = new ArrayList<>();
//    InviteJoinPerson inviteJoinPerson = new InviteJoinPerson();
//    invitedPersons.add(inviteJoinPerson);
//    Map<String, String> memberInfo = new HashMap<>();
//    inviteJoinPerson.setEmail(generateEmail());
//    inviteJoinPerson.setMemberInfo(memberInfo);
//    memberInfo.put("memberName", "测试邀请");
//    memberInfo.put("employeeId", "工号23");
//    memberInfo.put("roleIds", "6ff05699efa340ddb34fcb2dec6d148c");
//    memberInfo.put("deptIds", "3e74d1b85a0c44289cf0b0c75d43f61e");
//    memberInfo.put("ownerOrgId", INVITE_ORG_ID);
//    request.setInvitedPersons(invitedPersons);
//    Assert.assertNotNull(tInviteRestV3.bachInviteJoinAsync(INVITE_ORG_ID, request).getData());
//  }

  @Test
  public void testGetInviteJoinList() {
    Assert.assertNotNull(
        tInviteRestV3.getInviteJoinList(INVITE_ORG_ID, null, null, null, 0, 10).getData());
  }

  @Test
  public void testBatchPassedJoin() {
    initRequestContext();
    whenAdminInviteInit();
    whenCreatedMember();

    tInviteRestV3.batchPassedJoin(INVITE_ORG_ID, mockAdminBatchOperateParam());
  }

  @Test
  public void testBatchRejectJoin() {
    initRequestContext();
    whenAdminInviteInit();

    when(inviteJoinServiceV3.batchRejectJoin(any())).thenReturn(RpcOutput.with(null));
    tInviteRestV3.batchRejectJoin(INVITE_ORG_ID, mockAdminBatchOperateParam());
  }

  @Test
  public void testBatchUrgingJoin() {
    initRequestContext();
    whenAdminInviteInit();

    when(inviteJoinServiceV3.urgingInviteJoin(any())).thenReturn(RpcOutput.with(null));
    tInviteRestV3.batchUrgingJoin(INVITE_ORG_ID, mockAdminBatchOperateParam());
  }

  @Test
  public void testBatchDelete() {
    initRequestContext();
    whenAdminInviteInit();

    when(inviteJoinServiceV3.batchDelete(any())).thenReturn(RpcOutput.with(null));
    tInviteRestV3.batchDelete(INVITE_ORG_ID, mockAdminBatchOperateParam());
  }

  @Test
  public void testBatchReInvite() {
    initRequestContext();
    whenAdminInviteInit();

    when(inviteJoinServiceV3.batchReInviteV3(any())).thenReturn(RpcOutput.with(null));
    tInviteRestV3.batchReInvite(INVITE_ORG_ID, mockAdminBatchOperateParam());
  }

  @Test
  public void testGetAdminContextData() {
    initRequestContext();
    whenMemberScanInit();

//    tInviteRestV3.getAdminContextData();
  }

  @Test
  public void testAdminAcceptJoin() {
    initRequestContext();
    whenMemberScanInit();

//    tInviteRestV3.adminAcceptJoin();
  }

  @Test
  public void testAdminRejectJoin() {
    initRequestContext();
    whenMemberScanInit();

//    tInviteRestV3.adminRejectJoin();
  }

  @Test
  public void testGetMemberContextData() {
    initRequestContext();
    whenAdminInviteInit();

//    tInviteRestV3.getMemberContextData();
  }

  @Test
  public void testMemberAcceptJoin() {
    initRequestContext();
    whenAdminInviteInit();

//    tInviteRestV3.memberAcceptJoin();
  }

  @Test
  public void testMemberRejectJoin() {
    initRequestContext();
    whenAdminInviteInit();

//    tInviteRestV3.memberRejectJoin();
  }

  @Test
  public void testGetScanContextData() {
    initRequestContext();
    whenMemberScanInit();

    try {
      tInviteRestV3.getScanContextData(MEMBER_SCAN_INIT_INVITE_ID);
    } catch (Exception e) {

    }

  }

  @Test
  public void testScanApplyJoin() {
    initRequestContext();
    whenMemberScanInit();

    try {
      tInviteRestV3.scanApplyJoin(MEMBER_SCAN_INIT_INVITE_ID);
    } catch (Exception e) {

    }
  }

  @Test
  public void testApplyJoin() {
    initRequestContext();

//    tInviteRestV3.applyJoin();
  }

  @Test
  public void testGetInviteJoinUrl() {
    initRequestContext();

//    tInviteRestV3.getInviteJoinUrl();
  }

  @Test
  public void testCheckInvitelink() {
    initRequestContext();

//    tInviteRestV3.checkInvitelink();
  }

  private void whenCreatedMember() {
    BizICCreateMemberOutput createdMember = new BizICCreateMemberOutput();
    createdMember.setMemberId("1");
    when(icOrgPlusServiceAdapt.createMemberWithResult(any(), any(), any())).thenReturn(
        createdMember);
    Mockito.doNothing().when(rpcDeptPlusServiceAdapt).addMembersToDepts(any(), any(), any());
    Mockito.doNothing().when(bizRoleService).roleBatchGrant(any(), any(), any());
    when(inviteJoinServiceV3.passedInviteJoinV3(any())).thenReturn(RpcOutput.with(null));
  }

  private AdminBatchOperate4InvitationReqV3 mockAdminBatchOperateParam() {
    AdminBatchOperate4InvitationReqV3 request = new AdminBatchOperate4InvitationReqV3();
    List<InviteJoinMemberReqV3> inviteJoinMembers = new ArrayList<>();
    request.setInviteJoinMembers(inviteJoinMembers);
    InviteJoinMemberReqV3 inviteJoinMemberReqV3 = new InviteJoinMemberReqV3();
    inviteJoinMembers.add(inviteJoinMemberReqV3);
    inviteJoinMemberReqV3.setInviteId(ADMIN_INVITE_INIT_INVITE_ID);
    inviteJoinMemberReqV3.setInviteeAccount(ADMIN_INVITE_INIT_LOGIN_ACCOUNT);
    return request;
  }

  private void whenAdminInviteInit() {
    TInviteContextData inviteContextData = new TInviteContextData();
    inviteContextData.setInitiator("esigntest审计日志玩玩");
    inviteContextData.setInviteId(ADMIN_INVITE_INIT_INVITE_ID);
    Map<String, String> bizData = new HashMap<>();
    inviteContextData.setBizData(bizData);
    bizData.put("orgId", INVITE_ORG_ID);
    bizData.put("mobile", "***********");
    when(inviteService.getInviteContextData(any())).thenReturn(RpcOutput.with(inviteContextData));

    InviteJoinMember inviteJoinMember = new InviteJoinMember();
    inviteJoinMember.setLoginAccount(ADMIN_INVITE_INIT_LOGIN_ACCOUNT);
    inviteJoinMember.setOrgId(INVITE_ORG_ID);
    Map<String, String> memberInfo = new HashMap<>();
    inviteJoinMember.setMemberInfo(memberInfo);
    memberInfo.put("memberName", "测试邀请");
    memberInfo.put("ownerOrgId", INVITE_ORG_ID);
    memberInfo.put("employeeId", "工号01");
    memberInfo.put("deptIds", "a8b40cbb25584bb6bc0b57ed87498539,c9b2b01fecbd4b74a3a57de3a1a940e8");
    memberInfo.put("roleIds", "6ff05699efa340ddb34fcb2dec6d148c,f92c5301485f47bab1cffa73cd03b99e");
    inviteJoinMember.setStatus(10);
    inviteJoinMember.setType(0);
    inviteJoinMember.setDeptId("UN_ALLOCATE");
    when(inviteJoinService.queryTargetInviteMember(any())).thenReturn(
        RpcOutput.with(inviteJoinMember));
  }

  private void whenMemberScanInit() {
    TInviteContextData inviteContextData = new TInviteContextData();
    inviteContextData.setInitiator("esigntest审计日志玩玩");
    inviteContextData.setInviteId(MEMBER_SCAN_INIT_INVITE_ID);
    Map<String, String> bizData = new HashMap<>();
    inviteContextData.setBizData(bizData);
    bizData.put("orgId", INVITE_ORG_ID);
    bizData.put("deptId", "UN_ALLOCATE");
    when(inviteService.getInviteContextData(any())).thenReturn(RpcOutput.with(inviteContextData));

    InviteJoinMember inviteJoinMember = new InviteJoinMember();
    inviteJoinMember.setStatus(0);
    inviteJoinMember.setType(0);
    when(inviteJoinService.queryTargetInviteMember(any())).thenReturn(
        RpcOutput.with(inviteJoinMember));
  }

  private void clearBatchJoinTask(String operatorId, String orgId) {
    String redisKey = "ASYNC_BATCH_TASK_" + operatorId + "_" + orgId;
    TedisUtil.set(redisKey, null);
  }

  private void initRequestContext() {
    HttpServletRequest httpServletRequest = new MyTestHttpServletRequest();
    httpServletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR, HEAD_X_TSIGN_OPERATOR);
    RequestContext.put(WeaverConstants.REQUEST_KEY, httpServletRequest);
  }

  private String generateEmail() {
    return UUIDUtils.getUUID() + "@tsign.cn";
  }
}
