package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.pricheck.PriInstanceCheck;
import com.timevale.footstone.user.service.inner.pricheck.PriInstanceCheckInterceptor;
import com.timevale.footstone.user.service.inner.pricheck.PriInstanceCheckServiceImpl;
import com.timevale.footstone.user.service.rest.PrivilegeRest;
import com.timevale.unittest.footstone.configuration.Application;
import org.apache.catalina.connector.RequestFacade;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年04月07日 10:51:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PriCheckTest {
    @Autowired PriInstanceCheckInterceptor checkInterceptor;

    @Autowired PriInstanceCheckServiceImpl checkService;

    @Autowired PrivilegeRest privilegeRest;

    @Autowired HttpServletRequest servletRequest;

    @Test
    public void testPricheck() {
        // HttpServletRequest request = new
        // checkInterceptor.preHandle()
        try {
            Method s =
                    PrivilegeRest.class.getMethod(
                            "getPrivilegeListByUser", String.class, String.class);
            s.getDeclaredAnnotations();
            HandlerMethod m = new HandlerMethod(privilegeRest, s);

            MockHttpServletRequest request = new MockHttpServletRequest();
            request.addHeader(HeaderConstants.HEADER_OPERATOR, "974a2665419d4d3c817780fb299c681f");
            request.setParameter("orgId", "dace182f80f54a56afb2812e0c15ec99");
            request.setRequestURI("/v1/accounts/974a2665419d4d3c817780fb299c681f/privileges");

            checkInterceptor.preHandle(request, null, m);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCheckService() {
        checkService.check(
                "dace182f80f54a56afb2812e0c15ec99", "974a2665419d4d3c817780fb299c681f", "ADMIN");
    }

    @Test
    public void testGetStr() {
        PriInstanceCheckInterceptor.getTargetStr("/v1/aaaa/getData", "/v1/{*}/getData");
        PriInstanceCheckInterceptor.getTargetVale(
                "/organizations/{orgId}/rules-grant/{ruleGrantedId}/updateRuleGrant",
                "organizations\\/(.*?)\\/rules");
    }
}
