/*
 *  __  .__                            .__
 *_/  |_|__| _____   _______  _______  |  |   ____
 *\   __\  |/     \_/ __ \  \/ /\__  \ |  | _/ __ \
 * |  | |  |  Y Y  \  ___/\   /  / __ \|  |_\  ___/
 * |__| |__|__|_|  /\___  >\_/  (____  /____/\___  >
 *               \/     \/           \/          \/
 *
 *                   Copyright 2017-2017 Timevale.
 */
package com.timevale.unittest.footstone.configuration;

import com.timevale.footstone.user.service.aop.RestRecordAspect;
import com.timevale.footstone.user.service.configuration.FootstoneUserServiceConfiguration;
import com.timevale.framework.puppeteer.spring.annotation.EnablePuppeteerConfig;
import com.timevale.mandarin.microservice.NoDBService;
import com.timevale.privilege.authenticate.configuration.EnablePrivilegeAuthenticate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

/**
 * 服务启动入口
 *
 * <AUTHOR> Kunpeng
 * @version $Id: Application.java, v 0.1 2017年11月13日 下午2:23:03 LIU Kunpeng Exp $
 */
@NoDBService

@EnableFeignClients(
        basePackages = { // <br/>
                "com.timevale.easun.service", // <br/>
                "com.timevale.account.service", // <br/>
                "com.timevale.account.organization.service",
                "com.timevale.privilege.service",
                "com.timevale.realnameauth.service.api",
                "com.timevale.filesystem.common.service.api",
                "com.timevale.user.resource.service.api",
                "com.timevale.account.organization.service.api",
                "com.timevale.easun.service.api",
                "com.timevale.token.service.api",
                "com.timevale.sealmanager.common.service.api",
                "com.timevale.email.service.api",
                "com.timevale.mobile.common.service.api",
                "com.timevale.cert.api",
                "com.timevale.cert.common.service.api",
                "com.timevale.open.platform.service.service",
                "com.timevale.account.ashman.service.api",
                "com.timevale.account.ashman2.facade.api",
                "com.timevale.billing.facade.api",
                "com.timevale.notice.service.api",
                "com.timevale.docmanager.service.api",
                "com.timevale.tactivity.service.api",
                "com.timevale.notificationmanager.service.api",
                "com.timevale.open.platform.service",
                "com.timevale.shortlink.common.service.api",
                "com.timevale.footstone.rpc.api",
                "com.timevale.doccooperation.service.api",
                "com.timevale.open.platform.oauth.service.api",
                "com.timevale.footstone.will.common.service.api",
                "com.timevale.signflow.search.docSearchService.api",
                "com.timevale.footstone.identity.common.service.api",
                "com.timevale.signflow.search.docSearchService.api",
                "com.timevale.risktensor.service.api",
                "com.timevale.open.platform.oauth.service.rpc",
                "com.timevale.account.flow.service.api",
                "com.timevale.billing.manager.sdk.api",
                "com.timevale.saas.common.manage.common.service.api",
                "com.timevale.saas.integration.service",
                "com.timevale.billing.manager.sdk",
                "com.timevale.footstone.seal.facade",
                "com.timevale.dayu.facade.api",
                "com.timevale.contractmanager.common.service.api",
                "com.timevale.infoauth.service.api",
                "com.timevale.gray.config.manage.service.api",
                "com.timevale.contractapproval.facade.api",
                "com.timevale.lowcode.eflow.facade.rpc",
                "com.timevale.lowcode.emap.facade.rpc",
                "com.timevale.authcode.service.api",
                "com.timevale.besp.lowcode.integration.third",
                "com.timevale.saas.auth.api.facade.api",
                "com.timevale.besp.lowcode.integration.dingtalk",
                "com.timevale.bridge.service.api"
        })
@MapperScan("com.timevale.footstone.user.dal")
@ComponentScan(
        basePackages = {
                "com.timevale.footstone.user.service",
                "com.timevale.open.platform.service.service",
                "com.timevalue.account.framwork.buried",
                "com.timevale.mc",
                "com.timevale.framework.mq",
                "com.timevale.unittest.footstone.assist"
        },
        excludeFilters =
                @ComponentScan.Filter(
                        type = FilterType.ASSIGNABLE_TYPE,
                        classes = {RestRecordAspect.class}))
@EnablePuppeteerConfig({
        "application",
        "customBizAuthFlow",
        "JSBZ.SOA_PUBLIC",
        "JCYW.BURIED_POINT_CONFIG",
        "JSBZ.messagecenter",
        "JCYW.PROP_REAL_NAME_ORGAN"
})
@EnablePrivilegeAuthenticate
@Import(FootstoneUserServiceConfiguration.class)
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
