package com.timevale.unittest.footstone.tc;

import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import lombok.Data;

@Data
public class ParentBean {

    @ModelFieldDecoration("decoration.name")
    private String name;

    @ModelFieldDecoration("decoration.email")
    private String email;

    @ModelFieldDecoration("decoration.mobile")
    private String mobile;

    @ModelFieldDecoration("decoration.idno")
    private String idno;
}
