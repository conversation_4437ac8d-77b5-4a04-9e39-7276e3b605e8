package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.rest.AccountRest;
import com.timevale.privilege.service.enums.BuiltinPrivilege;
import com.timevale.unittest.footstone.assist.OpenApiAssist;
import com.timevale.unittest.footstone.configuration.Application;
import com.timevale.unittest.footstone.tc.UTCGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RolePrivilegeTest {

    @Autowired
    private IdAdapt adapt;
    @Autowired
    private OpenApiAssist assist;

    @Autowired
    AccountRest accountRest;
    private Set<String> persons = new HashSet<>();

    private Set<String> organs = new HashSet<>();

    @Test
    public void testAdapt() {
//        try {
//            adapt.getMemberByGuid(UUID.randomUUID().toString(), UUID.randomUUID().toString());
//        } catch (Exception e) {
//            //todo nothing
//        }
//        String person = assist.createEmptyUser();
//        String organ = assist.createOrgWithThirdId(person, UTCGenerator.utcThirdAppUser());
//        adapt.trueMemberId(person, organ);
//
//        try {
//            accountRest.privilegeOwnerOrgs(person, BuiltinPrivilege.GOD, "ALL");
//
//        } catch (Exception e) {
//            //todo nothing
//        }
    }

}
