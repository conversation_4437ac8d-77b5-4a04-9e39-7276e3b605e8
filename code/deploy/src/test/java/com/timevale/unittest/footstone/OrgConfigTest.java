package com.timevale.unittest.footstone;

import com.google.common.collect.Sets;
import com.timevale.account.service.api.RpcICUserService;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUseGetOrganConfigInput;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUserOrganConfigInput;
import com.timevale.account.service.model.service.biz.icuser.output.BizOrganConfigGetOutput;
import com.timevale.account.service.model.service.mods.prop.OrganConfig;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.model.account.mods.OrganCategoryModel;
import com.timevale.footstone.user.service.model.account.request.GetOrganConfigRequest;
import com.timevale.footstone.user.service.model.account.request.UpdateOrganConfigRequest;
import com.timevale.footstone.user.service.rest.OrganizationRest;
import com.timevale.footstone.user.service.utils.conv.AccountConverter;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OrgConfigTest {

    @Autowired
    private OrganizationRest organizationRest;

    String orgId = "c5fd0420e25c4ec5b8e5116190df7992";

    @Test
    public void updateOrgConfig() {
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);

        RequestContext.put("X-Tsign-Open-App-Id", "7876698694");

        UpdateOrganConfigRequest request = new UpdateOrganConfigRequest();
        request.setConfigs(Arrays.asList(new OrganCategoryModel("CONTRACT_FILING", true, "FINISH")));
        organizationRest.updateOrganConfig(orgId, request);
    }

    @Test
    public void getOrgConfig() {
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);

        RequestContext.put("X-Tsign-Open-App-Id", "7876698694");

        GetOrganConfigRequest request = new GetOrganConfigRequest();
        request.setCategory(Sets.newHashSet("CONTRACT_FILING"));
        organizationRest.getOrganConfig(orgId, request);
    }
}
