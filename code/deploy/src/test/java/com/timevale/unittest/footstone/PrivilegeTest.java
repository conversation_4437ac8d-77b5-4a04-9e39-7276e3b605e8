package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizPrivilegeService;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: sicheng
 * @since: 2020-08-24 14:16
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PrivilegeTest {
    @Autowired
    BizPrivilegeService bizPrivilegeService;

    @Test
    public void getAllPrivilegesTest(){
        bizPrivilegeService.getAllPrivileges();

    }
}
