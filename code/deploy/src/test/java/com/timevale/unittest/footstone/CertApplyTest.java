package com.timevale.unittest.footstone;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.footstone.user.service.inner.biz.BizCertService;
import com.timevale.footstone.user.service.model.cert.model.CertApplyConfigModel;
import com.timevale.footstone.user.service.model.cert.model.CertApplyUserModel;
import com.timevale.footstone.user.service.model.cert.request.BillCertApplyRequest;
import com.timevale.footstone.user.service.model.cert.request.CertApplyWithoutAccountRequest;
import com.timevale.footstone.user.service.model.cert.request.OrgStandardCertApplyAuthenticationRequest;
import com.timevale.footstone.user.service.model.cert.request.OrgStandardCertApplyInitRequest;
import com.timevale.footstone.user.service.model.cert.response.OrgStandardCertApplyAuthenticationResponse;
import com.timevale.footstone.user.service.model.cert.response.OrgStandardCertApplyInitResponse;
import com.timevale.footstone.user.service.rest.CertRest;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> fusu
 * @version V1.0
 * @title: CertApplyTest
 * @date 2021年02月03日 17:29
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CertApplyTest {


    @Autowired
    private BizCertService bizCertService;

    @Autowired
    private CertRest certRest;

    @Test
    public void orgStandardCertApplyInit1(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute("X-Tsign-Open-App-Id","3438757422");
        servletRequest.setAttribute("X-Tsign-Open-Tenant-Id","35eab803ffde4af5a5c8d53ff893c905");
        servletRequest.setAttribute("X-Tsign-Open-Operator-Id","5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute("x-tsign-open-language","zh-cn");
        ReflectionTestUtils.setField(certRest,"servletRequest",servletRequest);

        OrgStandardCertApplyInitRequest request = new OrgStandardCertApplyInitRequest();
        OrgStandardCertApplyInitResponse response = certRest.orgStandardCertApplyInit(request).getData();
        Assert.assertNotNull(response);
        Assert.assertTrue(response.isCertExist());
    }

    @Test
    public void orgStandardCertApplyInit2(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute("X-Tsign-Open-App-Id","3438757422");
        servletRequest.setAttribute("X-Tsign-Open-Tenant-Id","db4ea03a40534d38b6b1611141551b74");
        servletRequest.setAttribute("X-Tsign-Open-Operator-Id","70ca3669f6544de5aba14291dae917c2");
        servletRequest.setAttribute("x-tsign-open-language","zh-cn");
        ReflectionTestUtils.setField(certRest,"servletRequest",servletRequest);

        OrgStandardCertApplyInitRequest request = new OrgStandardCertApplyInitRequest();
        OrgStandardCertApplyInitResponse response = certRest.orgStandardCertApplyInit(request).getData();
        Assert.assertNotNull(response);
        Assert.assertFalse(response.isCertExist());
    }

    @Test
    public void orgStandardCertApplyAuthenticationWill(){

        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute("X-Tsign-Open-App-Id","3438757422");
        servletRequest.setAttribute("X-Tsign-Open-Tenant-Id","35eab803ffde4af5a5c8d53ff893c905");
        servletRequest.setAttribute("X-Tsign-Open-Operator-Id","5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute("x-tsign-open-language","zh-cn");
        ReflectionTestUtils.setField(certRest,"servletRequest",servletRequest);
        RequestContext.put(WeaverConstants.REQUEST_KEY,servletRequest);
        OrgStandardCertApplyAuthenticationRequest request = new OrgStandardCertApplyAuthenticationRequest();
        request.setFrom("brower");
        request.setRedirectUrl("http://esgn.cn");
        OrgStandardCertApplyAuthenticationResponse response = certRest.orgStandardCertApplyAuthentication(request).getData();

        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getAuthUrl());
        Assert.assertEquals(response.getAuthSourceType(), "will");
    }
    @Test
    public void orgStandardCertApplyAuthenticationIdentity(){

        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute("X-Tsign-Open-App-Id","3438757422");
        servletRequest.setAttribute("X-Tsign-Open-Tenant-Id","db4ea03a40534d38b6b1611141551b74");
        servletRequest.setAttribute("X-Tsign-Open-Operator-Id","70ca3669f6544de5aba14291dae917c2");
        servletRequest.setAttribute("x-tsign-open-language","zh-cn");
        ReflectionTestUtils.setField(certRest,"servletRequest",servletRequest);
        RequestContext.put(WeaverConstants.REQUEST_KEY,servletRequest);
        OrgStandardCertApplyAuthenticationRequest request = new OrgStandardCertApplyAuthenticationRequest();
        request.setFrom("brower");
        request.setRedirectUrl("http://esgn.cn");
        OrgStandardCertApplyAuthenticationResponse response = certRest.orgStandardCertApplyAuthentication(request).getData();

        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getAuthUrl());
        Assert.assertEquals(response.getAuthSourceType(), "identity");
    }



    @Test
    public void easyCertMakeTest(){

        BillCertApplyRequest request = new BillCertApplyRequest();
        request.setAccountId("123");

        try{
            certRest.billcertApply(request);
        }catch (Exception e){
            e.printStackTrace();
        }


    }


    @Test
    public void easyCertMakeTest2(){

        BillCertApplyRequest request = new BillCertApplyRequest();
        RequestContext.put("X-Tsign-Open-App-Id","**********");
        request.setAccountId("123");

        try{
            certRest.billcertApply(request);
        }catch (Exception e){
            e.printStackTrace();
        }


    }

    @Test
    public void easyCertMakeTest3(){

        RequestContext.put("X-Tsign-Open-App-Id","**********");
        try{
            bizCertService.easyBillCertApply("5f74e4342602448abab37df12cae454a");
        }catch (Exception e){
            e.printStackTrace();
        }


    }


}
