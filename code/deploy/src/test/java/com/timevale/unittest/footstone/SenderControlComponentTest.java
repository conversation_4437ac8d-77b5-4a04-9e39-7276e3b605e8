package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.component.SenderControlComponent;
import com.timevale.footstone.user.service.component.SenderRequest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * Author: liujiaxing
 * Date: 2022/12/30 11:42 上午
 * Description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SenderControlComponentTest {

    @Resource
    SenderControlComponent senderControlComponent;

    @Test
    public void checkControl() {
        try {
            for (int i=0;i<5;i++){
                SenderRequest senderRequest = new SenderRequest("15557182290","RESET_PWD");
                senderControlComponent.checkControl(senderRequest);
                System.out.println("发送成功：" + i);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
