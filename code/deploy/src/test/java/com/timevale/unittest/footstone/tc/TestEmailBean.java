package com.timevale.unittest.footstone.tc;

import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import lombok.Data;

@Data
public class TestEmailBean {

    @ModelFieldDecoration("decoration.ut.email.u220")
    private String emailu;

    @ModelFieldDecoration("decoration.ut.email.d220")
    private String emaild;

    @ModelFieldDecoration("decoration.ut.email.ud222")
    private String emailud;

    @ModelFieldDecoration("decoration.ut.email.ud223")
    private String emailud223;

}
