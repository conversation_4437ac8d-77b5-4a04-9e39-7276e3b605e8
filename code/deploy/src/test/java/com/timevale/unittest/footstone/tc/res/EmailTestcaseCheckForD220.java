package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestEmailBean;
import org.junit.Assert;

public enum EmailTestcaseCheckForD220 implements SimpleTestcaseChecker {
    T_1("12345@123456", "12345@12**56"),

    T_2("1@12345", "1@12*45"),

    T_3("1@1234", "1@1**"),

    T_4("1@1", "1@1**"),

    T_5("1@", "1@"),
    ;

    private String input;

    private String anwser;

    EmailTestcaseCheckForD220(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestEmailBean t = new TestEmailBean();
        t.setEmaild(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getEmaild());
    }
}
