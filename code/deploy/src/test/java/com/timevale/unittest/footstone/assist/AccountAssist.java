package com.timevale.unittest.footstone.assist;

import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.idcard.request.SetThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.idcard.request.UnbindThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.mods.PersonGlobalCredentials;
import com.timevale.footstone.user.service.model.account.request.OrgCreateRequest;
import com.timevale.footstone.user.service.model.account.request.PersonCreateRequest;
import com.timevale.footstone.user.service.rest.AccountRest;
import com.timevale.footstone.user.service.utils.ResponseUtil;
import com.timevale.footstone.user.service.utils.conv.AccountConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** <AUTHOR> on 2019-08-12 */
@Component
public class AccountAssist {

    @Autowired private AccountRest rest;

    @Autowired private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Autowired private RpcOrgPlusService rpcOrgPlusService;

    public String createEmpty() {
        // AccountBaseResponse resp = ResponseUtil.get(rest.createOverall(request));
        // 里面有HttpRequest，不能直接调用
        ICUserId icuserId =
                icUserPlusServiceAdapt.createICUser(
                        AccountConverter.conv(new PersonCreateRequest()), null);
        return icuserId.getOuid();
    }

    public String createOrgan(String accountId) {
        OrgCreateRequest request = new OrgCreateRequest();
        request.setCreater(accountId);
        return rpcOrgPlusService
                .createICOrgAndCompanyWithSource(AccountConverter.conv(accountId, request, null))
                .getData()
                .getOuid();
    }

    public void bindThirdparty(String oid, String key, String userId) {
        SetThirdPartyRequest req = new SetThirdPartyRequest();
        req.setKey(key);
        req.setUserId(userId);
        WrapperResponse<?> resp = rest.setThirdParty(oid, req);
        ResponseUtil.get(resp);
    }

    public void unbindThirdParty(String oid, String key) {
        UnbindThirdPartyRequest req = new UnbindThirdPartyRequest();
        req.setKey(key);
        WrapperResponse<?> resp = rest.unbindThirdParty(oid, req);
        ResponseUtil.get(resp);
    }
}
