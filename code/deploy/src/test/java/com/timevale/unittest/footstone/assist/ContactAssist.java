package com.timevale.unittest.footstone.assist;

import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.contact.model.ContactModel;
import com.timevale.footstone.user.service.model.contact.request.ContactAddRequest;
import com.timevale.footstone.user.service.model.contact.request.ContactUpdateRequest;
import com.timevale.footstone.user.service.model.contact.response.ContactAddResponse;
import com.timevale.footstone.user.service.model.contact.response.ContactListResponse;
import com.timevale.footstone.user.service.rest.ContactRest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/** <AUTHOR> on 2019-08-01 */
@Component
public class ContactAssist {

    @Autowired private ContactRest rest;

    @Autowired private  OpenApiAssist openApiAssist;
    private String getUniqueId()
    {
        return UUID.randomUUID().toString().replace("-","").substring(0,10)+"@tsign.cn";
    }

    public String addContact(String accountId) {
        ContactAddRequest request = new ContactAddRequest();
        request.setUniqueId(getUniqueId());
        request.setName("测试一");
        request.setAffliation("测试公司");
        WrapperResponse<ContactAddResponse> resp =rest.add(accountId,request);
        return resp.getData().getContactId();
    }

    public boolean updateContact(String accountId,String contactId) {
        ContactUpdateRequest request = new ContactUpdateRequest();
        request.setName("xxx");
        request.setAffliation("测试公司1");
        request.setUniqueId(getUniqueId());
        WrapperResponse resp =rest.update(accountId,contactId,request);
        return resp.ifSuccess();
    }

    public List<ContactModel> getContact(String accountId) {
        WrapperResponse<ContactListResponse> resp =rest.list(accountId,0,5,"","","",true);
        return resp.getData().getContactList();
    }
    public boolean deleteContact(String accountId,String contactId) {
        WrapperResponse resp =rest.delete(accountId,contactId);
        return resp.ifSuccess();
    }

}
