package com.timevale.unittest.footstone;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.service.model.service.biz.helper.acc.BizICUserOutputHelper;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.mods.OrgPropCollection;
import com.timevale.footstone.user.service.model.account.mods.OrgProperties;
import com.timevale.footstone.user.service.model.account.response.BatchResponse;
import com.timevale.footstone.user.service.model.account.response.OrgAllInfoResponse;
import com.timevale.footstone.user.service.model.member.mods.MemberDetailModel;
import com.timevale.footstone.user.service.model.member.request.ChangeMemberDeptRequest;
import com.timevale.footstone.user.service.model.member.request.SearchMemberRequest;
import com.timevale.footstone.user.service.model.member.response.BatchChangeDeptMemberResponse;
import com.timevale.footstone.user.service.model.member.response.ErrorBatchChangeDeptMemberResponse;
import com.timevale.footstone.user.service.model.organization.request.AddLegalRequest;
import com.timevale.footstone.user.service.rest.MemberRest;
import com.timevale.framework.puppeteer.enums.PropertyChangeType;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import javax.servlet.http.HttpServletRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;

import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MemberTest {

    @Mock
    MemberRest memberRest;


    String orgId = "5ccb1c1b0cc84eb9abced907cf392717";

    @Mock
    BizOrganService organService;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;

    @Before
    public void init() {
        // 审计日志初始化请求头参数
        HttpServletRequest httpServletRequest = new MyTestHttpServletRequest();
        httpServletRequest.setAttribute(WeaverConstants.APP_ID_LABEL,"**********");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_REAL_IP,"127.0.0.1");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"d6a34fcf82774b8ba34549a3fe103113");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"cd7ee89e84bc458e99dd13c1e1745345");
        RequestContext.put(WeaverConstants.REQUEST_KEY,httpServletRequest);
        AuditLogCommon.initAuditLogContext();
    }

    @InjectMocks
    private CommonConfig config;


    @Test
    public void testMembers() {
        memberRest.listMembers(orgId, 0, 20, null, null, null, null, null, null, null, null,null);
    }

//    @Test
//    public void testSendDeleteMsg() {
//        String accountId = "88e44a1ebef441d69306d2abcc0eb276";
//        String loginName = "净渊";
//        String loginAccount = "***********";
//        List<String> organs = esAccountServiceAdapt.getOrganByAccountId(accountId);
//
//        memberService.sendDeleteAccountMsg(accountId, loginName, loginAccount, organs);
//    }

    @Test
    public void testOrgAdmins() {
        String orgId = "5ccb1c1b0cc84eb9abced907cf392717";
        WrapperResponse<ApiPageResult<MemberDetailModel>> admins = memberRest.admins(orgId, 0, 20);
    }

    @Test
    public void testSearch() {
        SearchMemberRequest request = new SearchMemberRequest();
        request.setKeyword("南");
        request.setOffset(0);
        request.setSize(20);
        memberRest.listMembers(orgId, request);
    }

    @Test
    public void testChangeDept() {
        ChangeMemberDeptRequest request = new ChangeMemberDeptRequest();
        request.setDeptIds(Arrays.asList("14b473528a0b42ca83dab4b3dd24cd84"));
        request.setMemberIds(Arrays.asList("88841636a2c842b2a6896db0c4f33a25"));
        WrapperResponse<
                        BatchResponse<
                                List<BatchChangeDeptMemberResponse>,
                                List<ErrorBatchChangeDeptMemberResponse>>>
                response = memberRest.changeDept("386346ed0d14468a898c5c4b09d54827", request);
    }

    @Test
    public void queryMemberWithDept() {
        SearchMemberRequest request = new SearchMemberRequest();
        request.setDeptId("ALL");
        request.setSize(10);
        request.setOffset(0);
        mockListMembers();
        WrapperResponse<ApiPageResult<MemberDetailModel>> resultWrapperResponse =
                memberRest.listMembers("dace182f80f54a56afb2812e0c15ec99", request);
        resultWrapperResponse.getData();
        request.setDeptId("109ea5ab82434f76b762d9f93921f8ef");
        try {
            resultWrapperResponse =
                    memberRest.listMembers("dace182f80f54a56afb2812e0c15ec99", request);

        } catch (Exception e) {
        }
    }

    private void mockListMembers(){
        List<MemberDetailModel> models = new ArrayList<>();
        ApiPageResult<MemberDetailModel> pageResult = new ApiPageResult<>();
        pageResult.setResult(models);
        WrapperResponse<ApiPageResult<MemberDetailModel>> response = new WrapperResponse<>();
        response.setData(pageResult);
        when(memberRest.listMembers(anyString(),any())).thenReturn(response);
    }

    @Test
    public void configInitTest(){
        Map<String, ConfigChange> changes = new HashMap<>();
        ConfigChange configChange = new ConfigChange("Application"
                ,"org.sync.support.platform"
                ,"[\"\"]"
                ,"[{\"clientId\":\"FEI_SHU\",\"bindSceneInfos\":[\"\"]}]"
                , PropertyChangeType.MODIFIED);
        changes.put("org.sync.support.platform",configChange);
        ConfigChangeEvent changeEvent = new ConfigChangeEvent("Application",changes);
        config.commonConfigChangeListener(changeEvent);
    }

    @Test
    public void addLegalTest(){
        String orgId = "7a6c26e570c54f258690ddeaa82fde0b";

        String leagal1 = "da05aae12c194dc88a860e2ed4800e76";

        String legal2 = "974a2665419d4d3c817780fb299c681f";
        mockGetRealNameByOuid();
        BizAccountRealNameOutput legal2Info = icUserPlusService.getRealNameByOuid(legal2);

        RequestContext.put("X-Tsign-Open-App-Id","hhh");
        mockGetInfo();
        OrgAllInfoResponse orgInfo = organService.getInfo(orgId);
        Map<String, ConfigChange> changes = new HashMap<>();
        ConfigChange configChange = new ConfigChange("Application"
                ,"isolation.white.list.legacy"
                ,"[\"1\"]"
                ,"[\"2\"]"
                , PropertyChangeType.MODIFIED);
        changes.put("isolation.white.list.legacy",configChange);
        ConfigChangeEvent changeEvent = new ConfigChangeEvent("Application",changes);
        config.commonConfigChangeListener(changeEvent);
        ConfigChange ipConfigEmpty = new ConfigChange("Application"
                ,"ip.isolation.organ.list"
                ,"[{\"oid\":\"\",\"ipList\":[\"*************\"]}]"
                ,""
                , PropertyChangeType.MODIFIED);
        changes.put("ip.isolation.organ.list",ipConfigEmpty);
        ConfigChangeEvent ipConfigEnentEmpty = new ConfigChangeEvent("Application",changes);
        config.commonConfigChangeListener(ipConfigEnentEmpty);
        ConfigChange ipConfig = new ConfigChange("Application"
                ,"ip.isolation.organ.list"
                ,"[{\"oid\":\"\",\"ipList\":[\"*************\"]}]"
                ,"[{\"oid\":\"d6a34fcf82774b8ba34549a3fe103113\",\"ipList\":[\"*************\"]}]"
                , PropertyChangeType.MODIFIED);
        changes.put("ip.isolation.organ.list",ipConfig);
        ConfigChangeEvent ipConfigEnent = new ConfigChangeEvent("Application",changes);
        config.commonConfigChangeListener(ipConfigEnent);
        BizICUserOutputHelper helper = new BizICUserOutputHelper(legal2Info.getAccount());

        RequestContext.put("X-Tsign-Open-App-Id","**********");
        AddLegalRequest request = new AddLegalRequest();
        if(StringUtils.equals(orgInfo.getProp().getProperties().getLegalName(),helper.getPropertyValue(INFO_NAME))){
            request.setAccountId(leagal1);
            organService.addLegalMember(orgId,request);
        } else {
            request.setAccountId(legal2);
            organService.addLegalMember(orgId,request);
        }
    }

    private void mockGetInfo(){
        OrgAllInfoResponse response = new OrgAllInfoResponse();
        OrgPropCollection props = new OrgPropCollection();
        OrgProperties properties =  new OrgProperties();
        properties.setLegalName("name");
        props.setProperties(properties);
        response.setProp(props);
        when(organService.getInfo(anyString())).thenReturn(response);
    }


    private void mockGetRealNameByOuid(){
        BizICUserOutput icUserOutput = new BizICUserOutput();
        List<Property> properties = new ArrayList<>();
        Property name = new Property(INFO_NAME,"name");
        properties.add(name);
        icUserOutput.setProperties(properties);
        BizAccountRealNameOutput realNameOutput = new BizAccountRealNameOutput();
        realNameOutput.setAccount(icUserOutput);
        when(icUserPlusService.getRealNameByOuid(anyString())).thenReturn(realNameOutput);
    }
}
