package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.rest.OemRest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: sicheng
 * @since: 2020-07-28 09:10
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OemTest {

    @Autowired
    private OemRest         oemRest;


    private final String    orgId = "a4b7c0417ab44e43b54c2263a5ab2bf4";


    @Test
    public void authOrgAppList() {
        oemRest.authOrgAppList(orgId);
    }

}
