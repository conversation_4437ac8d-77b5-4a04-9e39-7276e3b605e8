package com.timevale.unittest.footstone;

import static com.timevale.footstone.user.service.enums.ItsmConstant.NEW_VERSION_CREATE_CERT;
import static com.timevale.footstone.user.service.enums.ItsmConstant.REALNAME_EVIDENCE_POINT;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.account.organization.service.model.service.newbiz.output.BizGetOrganOutput;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.constants.BuiltinContacts;
import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.idcard.IdcardEmail;
import com.timevale.account.service.model.service.mods.idcard.IdcardMobile;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.bridge.service.api.EviPointFacade;
import com.timevale.bridge.service.request.Result;
import com.timevale.bridge.service.response.RealNamePointInfoResponse;
import com.timevale.easun.service.api.RpcIdcardPlusService;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.easun.service.model.idcard.BizMultiIdcardDeleteInput;
import com.timevale.easun.service.model.mods.RpcOuidInput;
import com.timevale.easun.service.model.sender.input.BizSenderApplyInput;
import com.timevale.easun.service.model.sender.model.SenderType;
import com.timevale.easun.service.model.sender.output.BizSenderServiceOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.inner.biz.ItsmService;
import com.timevale.footstone.user.service.inner.client.CertClient;
import com.timevale.footstone.user.service.inner.client.EviClient;
import com.timevale.footstone.user.service.inner.client.impl.EviClientImpl;
import com.timevale.footstone.user.service.inner.impl.biz.BizOpenApiServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.BizTinviteServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAuthenticationServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIdentityOpenServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgServiceAdapt;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.*;
import com.timevale.footstone.user.service.model.account.response.*;
import com.timevale.footstone.user.service.model.invite.mods.InvitationSearchRequest;
import com.timevale.footstone.user.service.model.invite.request.ResetInvitaionRequest;
import com.timevale.footstone.user.service.model.organization.request.CreateOrgAndLegalRequest;
import com.timevale.footstone.user.service.model.organization.response.CreateOrgAndLegalResponse;
import com.timevale.footstone.user.service.rest.OpenRest;
import com.timevale.footstone.user.service.utils.FormatCheckUtil;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.ResponseUtil;
import com.timevale.mandarin.common.result.BusinessResult;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.msg.push.center.PushService;
import com.timevale.msg.push.center.service.HttpMethod;
import com.timevale.msg.push.center.service.PushObject;
import com.timevale.notice.service.api.RpcTInviteScheduleService;
import com.timevale.open.platform.service.service.impl.RemoteAppLicenseService;
import com.timevale.open.platform.service.service.utils.AuthUtils;
import com.timevale.unittest.footstone.assist.OpenApiAssist;
import com.timevale.unittest.footstone.tc.UTCGenerator;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

/**
 * <AUTHOR> on 2019-08-01
 */
@RunWith(MockitoJUnitRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OpenApiTest {

    @Mock
    private RpcIdcardPlusService idcardPlusService;

    @InjectMocks
    private OpenRest rest;

    @Mock
    private OpenApiAssist assist;

    @Mock
    private PushService pushService;
    @Mock
    private RpcSenderAuthService svc;
    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Mock
    private IdAdapt idAdapt;

    @InjectMocks
    private OpenRest mockRest;

    @Mock
    RpcTInviteScheduleService rpcTInviteScheduleService;

    @Mock
    private RpcAuthenticationServiceAdapt authenticationService;

    @InjectMocks
    private BizOpenApiServiceImpl openApiService;

    @Mock
    private BizOpenApiServiceImpl mockOpenApiService;


    @InjectMocks
    private BizTinviteServiceImpl bizTInviteService;

    @Mock
    private RemoteAppLicenseService remoteAppLicenseService;

    @Mock
    private  RpcIdentityOpenServiceAdapt identityOpenServiceAdapt;

    @Mock
    private ItsmService itsmService;
    @InjectMocks
    private EviClientImpl eviClient;
    @Mock
    private EviPointFacade eviPointFacade;
    @Mock
    private CertClient certClient;

    private final Set<String> persons = new HashSet<>();

    private final Set<String> organs = new HashSet<>();

    private final Map<String, Object> contextMap = new HashMap<>();

    {
        contextMap.put("X-Tsign-Open-App-Id", "**********");
        RequestContext.setContextMap(contextMap);
    }

    @Before
    public void setUp(){
        ReflectionTestUtils.setField(mockRest, "openApiService", openApiService);
        ReflectionTestUtils.setField(openApiService,"bizTInviteService",bizTInviteService);
        ReflectionTestUtils.setField(bizTInviteService,"scheduleService",rpcTInviteScheduleService);
        ReflectionTestUtils.setField(rest, "openApiService", mockOpenApiService);
        ReflectionTestUtils.setField(rest, "eviClient", eviClient);


    }

//    @Test
    public void accGetBackTest() {
        rest.getIdCardByVerifyInfo("2320182244809380003", "CRED_PSN_CH_IDCARD", "330902198803040677");
        AccGetBackIdcardUpdateRquest req = new AccGetBackIdcardUpdateRquest();
        req.setCertNum("******************");
        req.setCertType("CRED_PSN_CH_IDCARD");
        req.setOid("77d3ae753b4b4e0287595e2ca73bcbaa");
        req.setIdCard("<EMAIL>");
        req.setIdCardType(BuiltinContacts.EMAIL);
        BizMultiIdcardDeleteInput input = new BizMultiIdcardDeleteInput();
        input.setEmails(Lists.newArrayList(new IdcardEmail("<EMAIL>")));
        input.setMobiles(Lists.newArrayList(new IdcardMobile("***********")));

        BizSenderApplyInput applyInput = new BizSenderApplyInput();
        applyInput.setPrincipal("<EMAIL>");
        applyInput.setBizType(SenderType.DELETE_MODIFY_IDCARD.getType());
        applyInput.setProjectApp(new ProductApp("**********"));
        BizSenderServiceOutput output = svc.senderEmailApply(new RpcInput<>(applyInput)).getData();
        String serviceId1=output.getServiceId();
        applyInput.setPrincipal("***********");
        output = svc.senderMobileApply(new RpcInput<>(applyInput)).getData();
        String serviceId2=output.getServiceId();
        try {
            idcardPlusService.deleteIdcard(new RpcOuidInput<>(input, req.getOid()));
        } finally {

            req.setServiceId(serviceId1);
            req.setAuthCode("123456");
            rest.updateIdCardByOidAndCertInfo(req);
            req.setServiceId(serviceId2);
            req.setIdCard("***********");
            req.setIdCardType(BuiltinContacts.MOBILE);
            rest.updateIdCardByOidAndCertInfo(req);
        }

    }

    @Test
    public void testAdmin() {
        try {
            CheckAdminRequest request = new CheckAdminRequest();
            request.setOrgId("23a91d7eb9744b689d8e9373119e1a76");
            request.setAccountId("88e44a1ebef441d69306d2abcc0eb276");
            rest.checkAdmin(request);

        } catch (Exception ignored) {

        }
    }

    @Test
    public void testTransferAdmin() {
        try {
            RequestContext.setContextMap(contextMap);
            String orgId = "dbf857f62af449d683779ad995909d7f";
            String accountId = "ab71f7f55afe42b7898c3f197c22abdf";

            OpenTransferAdminRequest request = new OpenTransferAdminRequest();
            request.setAccountId(accountId);
            rest.transferAdmin(request, orgId);
        } catch (Exception e) {
        }
    }

    @Test
    public void testSetCredentials() {
        try {
            String ouid = assist.createEmptyUser();
            persons.add(ouid);

            PersonGetResponse resp = assist.getUser(ouid);
            Assert.assertNull(resp.getIdType());
            Assert.assertNull(resp.getIdNumber());

            PersonRenewV1Request request = new PersonRenewV1Request();
            request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
            request.setIdNumber(UTCGenerator.utcPsnIdcard());
            request.setName("yyy");

            icUserPlusServiceAdapt.addGlobalUser(
                    idAdapt.accountUid(ouid),
                    request.getIdType(),
                    request.getIdNumber(),
                    RealnameStatus.ACCEPT);

            WrapperResponse<PersonGetResponse> updateResp = rest.updateUserByAccountId(ouid, request);
            PersonGetResponse data = ResponseUtil.get(updateResp);

            rest.getUserByAccountId(ouid);

            Assert.assertEquals(request.getIdType(), data.getIdType());
            Assert.assertEquals(request.getIdNumber(), data.getIdNumber());
        } catch (Exception e) {

        }
    }

    @Test
    public void testExistCredentials() {
        try {

            RequestContext.setContextMap(contextMap);
            String ouid = assist.createUserWithCred();
            persons.add(ouid);

            PersonGetResponse resp = assist.getUser(ouid);
            Assert.assertNotNull(resp.getIdType());
            Assert.assertNotNull(resp.getIdNumber());

            PersonRenewV1Request request = new PersonRenewV1Request();
            request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
            request.setIdNumber(UTCGenerator.utcPsnIdcard());

            rest.updateUserByAccountId(ouid, request);
            Assert.fail();
        } catch (Exception e) {

        }
    }

    @Test
    public void testThirdIdExistForPerson() {
        try {
            String userId = UTCGenerator.utcThirdAppUser();
            String personId = assist.createUserWithThirdId(userId);
            persons.add(personId);
            String orgId = assist.createOrgWithThirdId(personId, userId);
            organs.add(orgId);
//            Assert.fail();
        } catch (Exception ignored) {
        }
    }

//    @Test
//    public void testThirdIdExistForOrgan() {
//        try {
//            String userId = UTCGenerator.utcThirdAppUser();
//
//            String personId = assist.createEmptyUser();
//            persons.add(personId);
//
//            String orgId = assist.createOrgWithThirdId(personId, userId);
//            organs.add(orgId);
//            String pId = assist.createUserWithThirdId(userId);
//            persons.add(pId);
//            Assert.fail();
//        } catch (Exception ignored) {
//        }
//    }

    public void testBatchCreatePersonByUserId() {
        WrapperResponse<AccountBaseResponse> resp = rest.createUserByUserId(buildPerson());
        AccountBaseResponse data = ResponseUtil.get(resp);
        persons.add(data.getAccountId());
    }

    @Test
    public void testBatchCreatePersonByCred() {
        try {
            WrapperResponse<AccountBaseResponse> resp =
                    rest.createUserByCredentials(buildPersonByCred());
            AccountBaseResponse data = ResponseUtil.get(resp);
            persons.add(data.getAccountId());

        } catch (Exception e) {

        }
    }

    // @Test
    public void testBatchCreateOrg() {
        try {
            BatchOrgAddByUserIdRequest request = new BatchOrgAddByUserIdRequest();
            CreatorModel<OrgAddByUserIdRequest, PersonAddByIdRequest> creatorModel =
                    new CreatorModel<>();
            creatorModel.setOrgInfo(buildOrg());
            creatorModel.setCreatorInfo(buildPerson());
            request.setOrgInfoList(Collections.singletonList(creatorModel));
            WrapperResponse<
                    BatchResponse<
                            List<BatchOrgBaseResponse<OrgAddByUserIdRequest>>,
                            List<ErrorBatchOrgBaseResponse>>>
                    response = rest.batchCreateOrgByThirdPartyUserId(request);
            WrapperResponse<OrgGetResponse> result =
                    rest.getOrgByThirdId(
                            request.getOrgInfoList().get(0).getOrgInfo().getThirdPartyUserId(),
                            request.getOrgInfoList().get(0).getOrgInfo().getThirdPartyUserType());
            Assert.assertEquals(
                    request.getOrgInfoList().get(0).getOrgInfo().getOrgLegalIdNumber(),
                    result.getData().getOrgLegalIdNumber());
            Assert.assertEquals(
                    request.getOrgInfoList().get(0).getOrgInfo().getOrgLegalName(),
                    result.getData().getOrgLegalName());
            rest.batchCreateOrgByThirdPartyUserId(request);
            response.getData()
                    .getSuccessList()
                    .forEach(
                            v -> {
                                persons.add(v.getCreator());
                                organs.add(v.getOrgId());
                            });
        } catch (Exception e) {

        }
    }

    @Test
    public void testBatchCreateOrgByCred() {
        try {

            BatchOrgAddByIdNoRequest request = new BatchOrgAddByIdNoRequest();
            CreatorModel<OrgAddByIdNoRequest, PersonAddByCredRequest> creatorModel =
                    new CreatorModel<>();
            creatorModel.setOrgInfo(buildOrgByCred());
            creatorModel.setCreatorInfo(buildPersonByCred());
            request.setOrgInfoList(Collections.singletonList(creatorModel));
            WrapperResponse<
                    BatchResponse<
                            List<BatchOrgBaseResponse<OrgAddByIdNoRequest>>,
                            List<ErrorBatchOrgBaseResponse>>>
                    response = rest.batchCreateOrgByIdNo(request);
            WrapperResponse<OrgGetResponse> result =
                    rest.getOrgByAccountId(response.getData().getSuccessList().get(0).getOrgId());
            Assert.assertEquals(
                    result.getData().getIdNumber(),
                    request.getOrgInfoList().get(0).getOrgInfo().getIdNumber());
            rest.batchCreateOrgByIdNo(request);
            response.getData()
                    .getSuccessList()
                    .forEach(
                            v -> {
                                persons.add(v.getCreator());
                                organs.add(v.getOrgId());
                            });
        } catch (Exception e) {
        }
    }

    //    @Test
    public void testPush() {
        Map<String, Object> pushMap = new HashMap<>();
        pushMap.put("invitationId", "xxx");
        pushMap.put("bizId", "aaa");
        pushMap.put("status", "d");
        String pushBody = JSON.toJSONString(pushMap);

        PushObject pushObject = new PushObject();

        Map<String, String> headMap = new HashMap<>();
        headMap.put("X-Tsign-Open-App-Id", "3876543996");
        pushObject.setHeader(headMap);
        pushObject.setUrl("http://118.178.93.198:8880/testnotify/msgRecive");
        pushObject.setHttpMethod(HttpMethod.POST);
        pushObject.setBody(pushBody);
        pushService.push(pushObject);
    }

    @Test
    public void testCreateInviteWithData() {
        CreateInvitationsWithDataRequest request = new CreateInvitationsWithDataRequest();
        request.setEmail("<EMAIL>");
        request.setMobile("15000000000");
        request.setInviteType("PERSON");
        request.setNotifyType("sms");
        try {
            rest.createInvitationsWithData(request);
        } catch (Exception ignored) {

        }
    }

    @Test
    public void testCreateInviteWithDataEmail() {
        CreateInvitationsWithDataRequest request = new CreateInvitationsWithDataRequest();
        request.setEmail("<EMAIL>");
        request.setMobile("150000000000");
        request.setInviteType("PERSON");
        request.setNotifyType("email");
        try {
            rest.createInvitationsWithData(request);
        } catch (Exception ignored) {

        }
    }

    @Test
    public void testCreateInvitation() {
        CreateInvitationsRequest request = new CreateInvitationsRequest();
        request.setInviteeOid("d3d4c7867d674ca39d3fc9d06dfafs");
        try {
            rest.createInvitations(request);
        } catch (Exception ignored) {

        }
    }

    @Test
    public void searchInvitation() {
        InvitationSearchRequest request = new InvitationSearchRequest();
        request.setInvitationId("8ca824f4c32948a09d3375f543eec0b5");
        try {
            rest.invitationSearch(request);
        } catch (Exception e) {

        }
    }

    @Test
    public void setSigPwd() {
        SetSignPwdRequest request = new SetSignPwdRequest();
        request.setPassword(UUID.randomUUID().toString());
        String ouid = assist.createEmptyUser();
        persons.add(ouid);
        try {
            rest.setSignPwd(request, ouid);
        } catch (Exception e) {

        }
    }

    @Test
    public void testQueryOrg() {
        String ouid = assist.createEmptyUser();
        Map<String, Object> params = new HashMap<>();
        params.put("X-Tsign-Open-App-Id", "**********");
        RequestContext.setContextMap(params);
        try {
            rest.getUserByAccountId(ouid);

        } catch (Exception e) {

        } finally {
            RequestContext.clear();
        }
    }

//    @Test
    public void testUpdateOrgByAccountId() {
        String idType = BuiltinCredentials.CRED_ORG_USCC;
        String idNumber = UTCGenerator.utcUsccCode();
        String userId = UTCGenerator.utcThirdAppUser();
        String personId = assist.createUserWithThirdId(userId);
        String ouid = assist.createOrgWithCred(personId, idType, idNumber);

        OrgRenewRequest request = new OrgRenewRequest();
        request.setIdType(idType);
        request.setIdNumber(idNumber);
        request.setName("yyy");
        request.setOrgLegalName("zzz");

        icUserPlusServiceAdapt.addGlobalUser(
                idAdapt.accountUid(ouid), idType, idNumber, RealnameStatus.ACCEPT);

        rest.getOrgByAccountId(ouid);
        rest.updateOrgByAccountId(ouid, request);
        rest.getOrgByAccountId(ouid);
    }

    @Test
    public void testUpdateByIdNumber() {
        String idType = BuiltinCredentials.CRED_ORG_USCC;
        String idNumber = UTCGenerator.utcUsccCode();
        String userId = UTCGenerator.utcThirdAppUser();
        String personId = assist.createUserWithThirdId(userId);
        String ouid = assist.createOrgWithCred(personId, idType, idNumber);

        OrgRenewTinyRequest request = new OrgRenewTinyRequest();
        request.setName("yyy");
        request.setOrgLegalName("zzz");

        icUserPlusServiceAdapt.addGlobalUser(
                idAdapt.accountUid(ouid), idType, idNumber, RealnameStatus.ACCEPT);

        rest.updateOrgByIdNumber(idType, idNumber, request);
    }

    @Test
    public void resetInvite() {
        ResetInvitaionRequest request = new ResetInvitaionRequest();
        request.setInviteeId("tttt");
        request.setInviteId("aaa");

        mockRest.resetInvitation(request);
    }

//    @Test
//    public void testCreateOrgAndLeal(){
//        RequestContext.put("X-Tsign-Open-App-Id", "**********");
//        CreateOrgAndLegalRequest request = new CreateOrgAndLegalRequest();
//        request.setLegalName("赵三钱");
//        request.setLegalCertType("CRED_PSN_CH_IDCARD");
//        request.setLegalCertNo("110101199003077440");
//        request.setOrgName("esign测试企业推送9933");
//        request.setOrgCertNo("***************");
//        request.setOrgCertType("CRED_ORG_REGCODE");
//        rest.createOrgAndLegal(request);
//    }

    //    @Test
//    public void testCheckLegal(){
//        RequestContext.put("X-Tsign-Open-App-Id", "**********");
//        CheckLegalRequest request = new CheckLegalRequest();
//        request.setAccountId("2b8731936e424a45afc5ae2f9c01289c");
//        request.setOrgId("87cdf5f0c1624e3fa6434ca91926c93b");
//        rest.checkLegal(request);
//    }
//
    String imgData = "iVBORw0KGgoAAAANSUhEUgAAAfAAAAHwCAMAAABucs3UAAAADFBMVEX" +
            "/////AAAAAP8AAABvxgj3AAAAAXRSTlMAQObYZgAAFxxJREFUeNrtne2CpKwOhC3a+7" +
            "/lOued2eluFZWPgAnEP7sz062QJ5UERFkWP6Y6MHmP6cCn7x7dIlN2jG6XGXtEN8+UfaEbacJe0E01Xw" +
            "/o5pqv9XSTzdd0utXmazbdcvM1mW696ZpLt" +
            "+B0baUbcbqW0s2ouJls0nc6cA1tpNprOXDZBtLcVR14YfM4yOVnAw4lpsZIzDFKw6isQXTg7ZpFlc2iA2" +
            "/RKCpuHB24bJOo32h04EINohXD0YFXN4e2jEcHXtMYuocOAhzmaFt0UzjuuRoPM81QPkltpQdwW83VDRhogp31JNCPHI57rv68HjYP" +
            "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";

//    @Test
//    public void testPhaseSeal(){
//        String orgId = "da2b9e93961d4303be15329d3924ec67";
//        /*创建印章*/
//        RequestContext.put("X-Tsign-Open-App-Id", "**********");
//        PhaseSealRequest request = new PhaseSealRequest();
//        request.setSealData(imgData);
//        request.setDataType(ImageType.BASE64.name());
//        request.setChainSealBizType(ChainSealBizTypeEnum.BILL.getChainSealBizType());
//        request.setAlias("bill");
//        request.setDefaultSeal(false);
//        request.setHeight(159);
//        request.setWidth(159);
//        WrapperResponse<PhaseSealResponse> phaseSeal = rest.phaseSeal(request, orgId);
//
//        sealRest.checkCanReceive(orgId, "4501308d254a434b90fd9d96f63dc161");
//
//        String sealId = phaseSeal.getData().getSealId();
//
//        RecordedSealRequest recordRequest = new RecordedSealRequest();
//        Set<String> sealIds = Sets.newHashSet();
//        sealIds.add(sealId);
//        recordRequest.setSealIds(sealIds);
//        recordRequest.setProvince("310000");
//        recordRequest.setCity("310000");
//        rest.recordedSeal(recordRequest, orgId);
//
//        /*更新印章*/
//        request.setAlias("aaa");
//        request.setHeight(160);
//        request.setSealId(sealId);
//        rest.phaseSeal(request, orgId);
//
//        /*删除印章*/
//        request.setDeleted(true);
//        rest.phaseSeal(request, orgId);
//    }




    @Test
    public void testCreateThirdParty() {
        try {
            PersonAddByIdV1Request request = new PersonAddByIdV1Request();
            request.setIdNumber("330327199902043333");
            request.setThirdPartyUserId("208850262214428811");
            request.setThirdPartyUserType("ALI_PAY");
            request.setCardNo("6222081203009064490");
            request.setRealnameMobile("18368044053");
            rest.createUserByUserId(request);
        } catch (Exception e) {
        }
    }

    @Test
    public void testCreateThirdPartyEvidencePointId() {
        String cardNo = "511503200007039615";
        String name = "测试梁丽丽";

        when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString())).thenReturn("1");
        Result<RealNamePointInfoResponse> result = new Result<>();
        RealNamePointInfoResponse pointInfo = new RealNamePointInfoResponse();
        pointInfo.setCardNo(cardNo);
        pointInfo.setName(name);
        result.setData(pointInfo);
        when(eviPointFacade.realNameQuery(any())).thenReturn(result);
        when(certClient.applyAllTypeCertCAAgreement(any())).thenReturn(new BusinessResult<>());

        try (MockedStatic<FormatCheckUtil> mockedStatic = Mockito.mockStatic(FormatCheckUtil.class))  {
            // Mock 静态方法
            mockedStatic.when(() -> FormatCheckUtil.bankCard(any())).thenReturn(true);
            mockedStatic.when(() -> FormatCheckUtil.mobile(any())).thenReturn(true);
            PersonAddByIdV1Request request = new PersonAddByIdV1Request();
            request.setIdNumber(cardNo);
            request.setName(name);
            request.setThirdPartyUserId("**********");
            request.setRealnameMobile("***********");
            request.setRealnameEvidencePointId("*********");

            when(itsmService.getProjectPropConfig(eq(REALNAME_EVIDENCE_POINT), anyString())).thenReturn("1");
            WrapperResponse<AccountBaseResponse> response = rest.createUserByUserId(request);

            when(itsmService.getProjectPropConfig(eq(REALNAME_EVIDENCE_POINT), anyString())).thenReturn("");

            try {
                rest.createUserByUserId(request);
            } catch (FootstoneUserErrors.InvalidFormat e) {
                Assert.assertEquals(e.getMessage(), "数据格式异常: 当前应用无法使用证据点，请联系交付经理确认");
            }
            System.out.println(response.getData());
        }

    }
    @Test
    public void testUpdateUserByAccountId_EvidencePointId() {
        String cardNo = "511503200007039615";
        String name = "测试梁丽";
        when(itsmService.getProjectPropConfig(eq(REALNAME_EVIDENCE_POINT), anyString())).thenReturn("1");
        when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString())).thenReturn("1");
        Result<RealNamePointInfoResponse> result = new Result<>();
        RealNamePointInfoResponse pointInfo = new RealNamePointInfoResponse();
        pointInfo.setCardNo(cardNo);
        pointInfo.setName(name);
        result.setData(pointInfo);
        Mockito.lenient().when(eviPointFacade.realNameQuery(any())).thenReturn(result);
        when(certClient.applyAllTypeCertCAAgreement(any())).thenReturn(new BusinessResult<>());
        when(mockOpenApiService.updateUser(any(), any())).thenReturn(new PersonGetV1Response());
        try (MockedStatic<FormatCheckUtil> mockedStatic =
                     Mockito.mockStatic(FormatCheckUtil.class);
             MockedStatic<AuthUtils> mockedAuthUtils = Mockito.mockStatic(AuthUtils.class)) {
            // Mock 静态方法
            mockedStatic.when(() -> FormatCheckUtil.bankCard(any())).thenReturn(true);
            mockedStatic.when(() -> FormatCheckUtil.mobile(any())).thenReturn(true);
            mockedAuthUtils.when(() -> AuthUtils.checkPermission(any(), any())).thenReturn(true);

            PersonRenewV1Request renewRequest = new PersonRenewV1Request();
            renewRequest.setName(name);
            WrapperResponse<PersonGetResponse> response = rest.updateUserByAccountId("*********", renewRequest);
            System.out.println(response.getData());
        }
    }

    @Test
    public void testUpdateUserByThirdId_EvidencePointId() {
        String cardNo = "511503200007039615";
        String name = "测试梁丽";
        when(itsmService.getProjectPropConfig(eq(REALNAME_EVIDENCE_POINT), anyString()))
                .thenReturn("1");
        when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString()))
                .thenReturn("1");
        Result<RealNamePointInfoResponse> result = new Result<>();
        RealNamePointInfoResponse pointInfo = new RealNamePointInfoResponse();
        pointInfo.setCardNo(cardNo);
        pointInfo.setName(name);
        result.setData(pointInfo);
        Mockito.lenient().when(eviPointFacade.realNameQuery(any())).thenReturn(result);
        Mockito.lenient().when(certClient.applyAllTypeCertCAAgreement(any())).thenReturn(new BusinessResult<>());
        Mockito.lenient().when(mockOpenApiService.updateUser(any(), any())).thenReturn(new PersonGetV1Response());
        try (MockedStatic<FormatCheckUtil> mockedStatic =
                        Mockito.mockStatic(FormatCheckUtil.class)) {
            // Mock 静态方法
            mockedStatic.when(() -> FormatCheckUtil.bankCard(any())).thenReturn(true);
            mockedStatic.when(() -> FormatCheckUtil.mobile(any())).thenReturn(true);

            PersonRenewV1Request renewRequest = new PersonRenewV1Request();
            renewRequest.setName(name);
            WrapperResponse<PersonGetResponse> response =
                    rest.updateUserByThirdId("**********", null, renewRequest);
            System.out.println(response.getData());
        }
    }

    @Test
    public void testCreateOrgByThirdPartyUserId_evidencePointId() {
        String cardNo = "53652199IPZCQU8HGY";
        String name = "esigntest测试合联电子网络有限公司";

        when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString())).thenReturn("1");
        Result<RealNamePointInfoResponse> result = new Result<>();
        RealNamePointInfoResponse pointInfo = new RealNamePointInfoResponse();
        pointInfo.setCardNo(cardNo);
        pointInfo.setName(name);
        result.setData(pointInfo);
        when(eviPointFacade.realNameQuery(any())).thenReturn(result);
        when(certClient.applyAllTypeCertCAAgreement(any())).thenReturn(new BusinessResult<>());
        try (MockedStatic<FormatCheckUtil> mockedStatic = Mockito.mockStatic(FormatCheckUtil.class))  {
            // Mock 静态方法
            mockedStatic.when(() -> FormatCheckUtil.bankCard(any())).thenReturn(true);
            mockedStatic.when(() -> FormatCheckUtil.mobile(any())).thenReturn(true);
            OrgCreateByThirdPartyUserId request = new OrgCreateByThirdPartyUserId();
            request.setIdNumber(cardNo);
            request.setName(name);
            request.setThirdPartyUserId("**********");
            request.setRealnameEvidencePointId("P1900747817287348229");
            request.setOrgLegalIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
            request.setOrgLegalIdNumber("******************");

            when(itsmService.getProjectPropConfig(eq(REALNAME_EVIDENCE_POINT), anyString())).thenReturn("1");
            WrapperResponse<OrgBaseResponse> response = rest.createOrgByThirdPartyUserId(request);
            System.out.println(response.getData());

            try {
                rest.createOrgByThirdPartyUserId(request);
            } catch (FootstoneUserErrors.InvalidFormat e) {
                Assert.assertEquals(e.getMessage(), "数据格式异常: 当前应用无法使用证据点，请联系交付经理确认");
            }
        }
    }

    @Test
    public void testUpdateOrgByAccountId_evidencePointId() {
        String cardNo = "53652199IPZCQU8HGY";
        String name = "esigntest测试合联电子网络有限公司";

        Mockito.lenient().when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString())).thenReturn("1");
        Result<RealNamePointInfoResponse> result = new Result<>();
        RealNamePointInfoResponse pointInfo = new RealNamePointInfoResponse();
        pointInfo.setCardNo(cardNo);
        pointInfo.setName(name);
        result.setData(pointInfo);
        Mockito.lenient().when(eviPointFacade.realNameQuery(any())).thenReturn(result);
        Mockito.lenient().when(certClient.applyAllTypeCertCAAgreement(any())).thenReturn(new BusinessResult<>());
        when(mockOpenApiService.updateOrg(any(), any(OrgRenewRequest.class))).thenReturn(new OrgGetResponse());

        try (MockedStatic<FormatCheckUtil> mockedStatic =
                     Mockito.mockStatic(FormatCheckUtil.class);
             MockedStatic<AuthUtils> mockedAuthUtils = Mockito.mockStatic(AuthUtils.class)) {
            // Mock 静态方法
            mockedStatic.when(() -> FormatCheckUtil.bankCard(any())).thenReturn(true);
            mockedStatic.when(() -> FormatCheckUtil.mobile(any())).thenReturn(true);
            mockedAuthUtils.when(() -> AuthUtils.checkPermission(any(), any())).thenReturn(true);

            OrgRenewRequest renewRequest = new OrgRenewRequest();
            renewRequest.setName(name);
            WrapperResponse<OrgGetResponse> response = rest.updateOrgByAccountId("*********", renewRequest);
            System.out.println(response.getData());
        }
    }

    @Test
    public void testUpdateOrgByThirdId_evidencePointId() {
        String cardNo = "53652199IPZCQU8HGY";
        String name = "esigntest测试合联电子网络有限公司";

        Mockito.lenient().when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString())).thenReturn("1");
        Result<RealNamePointInfoResponse> result = new Result<>();
        RealNamePointInfoResponse pointInfo = new RealNamePointInfoResponse();
        pointInfo.setCardNo(cardNo);
        pointInfo.setName(name);
        result.setData(pointInfo);
        Mockito.lenient().when(eviPointFacade.realNameQuery(any())).thenReturn(result);
        Mockito.lenient().when(certClient.applyAllTypeCertCAAgreement(any())).thenReturn(new BusinessResult<>());
        when(mockOpenApiService.updateOrg(any(), any(OrgRenewRequest.class))).thenReturn(new OrgGetResponse());

        try (MockedStatic<FormatCheckUtil> mockedStatic =
                     Mockito.mockStatic(FormatCheckUtil.class);
             MockedStatic<AuthUtils> mockedAuthUtils = Mockito.mockStatic(AuthUtils.class)) {
            // Mock 静态方法
            mockedStatic.when(() -> FormatCheckUtil.bankCard(any())).thenReturn(true);
            mockedStatic.when(() -> FormatCheckUtil.mobile(any())).thenReturn(true);
            mockedAuthUtils.when(() -> AuthUtils.checkPermission(any(), any())).thenReturn(true);

            OrgRenewRequest renewRequest = new OrgRenewRequest();
            renewRequest.setName(name);
            WrapperResponse<OrgGetResponse> response = rest.updateOrgByThirdId("*********",null, renewRequest);
            System.out.println(response.getData());
        }
    }
    @Test
    public void testUpdateUser_evidencePointId(){

        BizOpenApiServiceImpl bizOpenApiService = new BizOpenApiServiceImpl();
        RpcIcUserPlusServiceAdapt icUserPlusService = Mockito.mock(RpcIcUserPlusServiceAdapt.class);
        ItsmService itsmService = Mockito.mock(ItsmService.class);
        ReflectionTestUtils.setField(bizOpenApiService, "icUserPlusService", icUserPlusService);
        ReflectionTestUtils.setField(bizOpenApiService, "itsmService", itsmService);
        when(itsmService.getProjectPropConfig(eq(REALNAME_EVIDENCE_POINT), anyString()))
                .thenReturn("1");
        Mockito.lenient().when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString()))
                .thenReturn("1");
        BizICUserOutput bizICUserOutput = new BizICUserOutput();
        List<Property> properties = new ArrayList<>();
        properties.add(new Property("INFO_NAME", "测试张三"));
        bizICUserOutput.setProperties(Lists.newArrayList(properties));
        List<Property> credentials = new ArrayList<>();
        credentials.add(new Property(BuiltinCredentials.CRED_PSN_CH_IDCARD, "53652199IPZCQU8HGY"));
        bizICUserOutput.setCredentials(credentials);
        when(icUserPlusService.getICUserByOuid(any())).thenReturn(bizICUserOutput);
        BizAccountRealNameOutput bizAccountRealNameOutput = new BizAccountRealNameOutput();
        bizAccountRealNameOutput.setStatus(RealnameStatus.INIT);
        BizICUserOutput bizICUser = new BizICUserOutput();
        bizICUser.setId(new ICUserId());
        bizAccountRealNameOutput.setAccount(bizICUser);
        when(icUserPlusService.getRealNameByOuid(any())).thenReturn(bizAccountRealNameOutput);
        PersonRenewV1Request renewRequest = new PersonRenewV1Request();
        renewRequest.setName("测试张三");
        bizOpenApiService.updateUser(() -> "123456", renewRequest);
    }

    @Test
    public void testUpdateOrg_evidencePointId(){

        BizOpenApiServiceImpl bizOpenApiService = new BizOpenApiServiceImpl();
        RpcIcUserPlusServiceAdapt icUserPlusService = Mockito.mock(RpcIcUserPlusServiceAdapt.class);
        ItsmService itsmService = Mockito.mock(ItsmService.class);
        EviClient eviClient = Mockito.mock(EviClient.class);
        IdAdapt idAdapt = Mockito.mock(IdAdapt.class);
        RpcInnerOrgServiceAdapt innerOrgService = Mockito.mock(RpcInnerOrgServiceAdapt.class);
        ReflectionTestUtils.setField(bizOpenApiService, "icUserPlusService", icUserPlusService);
        ReflectionTestUtils.setField(bizOpenApiService, "itsmService", itsmService);
        ReflectionTestUtils.setField(bizOpenApiService, "eviClient", eviClient);
        ReflectionTestUtils.setField(bizOpenApiService, "innerOrgService", innerOrgService);
        ReflectionTestUtils.setField(bizOpenApiService, "idAdapt", idAdapt);
        Mockito.lenient().when(itsmService.getProjectPropConfig(eq(REALNAME_EVIDENCE_POINT), anyString()))
                .thenReturn("1");
        Mockito.lenient().when(itsmService.getProjectPropConfig(eq(NEW_VERSION_CREATE_CERT), anyString()))
                .thenReturn("1");
        RealNamePointInfoResponse realNamePointInfoResponse = new RealNamePointInfoResponse();
        realNamePointInfoResponse.setCardNo("53652199IPZCQU8HGY");
        realNamePointInfoResponse.setName("esigntest测试合联电子网络有限公司");
        when(eviClient.fetchEvidencePoint(any())).thenReturn(realNamePointInfoResponse);
        BizICUserOutput bizICUserOutput = new BizICUserOutput();
        List<Property> properties = new ArrayList<>();
        properties.add(new Property("INFO_NAME", "测试张三"));
        bizICUserOutput.setProperties(Lists.newArrayList(properties));
        List<Property> credentials = new ArrayList<>();
        credentials.add(new Property(BuiltinCredentials.CRED_PSN_CH_IDCARD, "53652199IPZCQU8HGY"));
        bizICUserOutput.setCredentials(credentials);
        when(icUserPlusService.getICUserByOuid(any())).thenReturn(bizICUserOutput);
        BizAccountRealNameOutput bizAccountRealNameOutput = new BizAccountRealNameOutput();
        bizAccountRealNameOutput.setStatus(RealnameStatus.INIT);
        BizICUserOutput bizICUser = new BizICUserOutput();
        bizICUser.setId(new ICUserId());
        bizICUser.setBase(new AccountBaseDetail());
        bizAccountRealNameOutput.setAccount(bizICUser);
        when(icUserPlusService.getRealNameByOuid(any())).thenReturn(bizAccountRealNameOutput);

        when(innerOrgService.getOrganByAccount(any())).thenReturn(new BizGetOrganOutput());
        OrgRenewRequest renewRequest = new OrgRenewRequest();
        renewRequest.setName("esigntest测试合联电子网络有限公司");
        renewRequest.setRealnameEvidencePointId("123456");
        bizOpenApiService.updateOrg(() -> "123456", renewRequest);
    }
    @Test
    public void getByIdNumTest(){
        when(mockOpenApiService.getByIdNumber(any(),any(),any(),any(),any(),any()))
                .thenReturn(MockUntils.mockPageResultEsSearchAccount(
                        Lists.newArrayList(MockUntils.mockEsOrgSearhOutput())
                ));
        when(identityOpenServiceAdapt.getIdentiy(any(),any())).thenReturn(new HashMap<>());
        rest.getOrganizationsByIdNumber("idType","idNum",1,10);
        when(mockOpenApiService.getByIdNumber(any(),any(),any(),any(),any(),any()))
                .thenReturn(MockUntils.mockPageResultEsSearchAccount(
                        Lists.newArrayList(MockUntils.getAccountOutPut())
                ));
        rest.getAccountsByIdNumber("idType","idNum",1,10);
    }

    @Test
    public void testCancelOrg() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        CreateOrgAndLegalRequest request = new CreateOrgAndLegalRequest();
        request.setLegalName("赤阦");
        request.setLegalCertType("CRED_PSN_CH_IDCARD");
        request.setLegalCertNo("110101199003071380");
        request.setOrgName("赤阦科技有限公司");
        request.setOrgCertNo("***************");
        request.setOrgCertType("CRED_ORG_REGCODE");
        WrapperResponse<CreateOrgAndLegalResponse> orgAndLegal = null;
        try {
            orgAndLegal = rest.createOrgAndLegal(request);
        } catch (Throwable e) {
        }
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        try {
            if (orgAndLegal != null) {
                rest.cancelOrg(orgAndLegal.getData().getOrgId());
            }
        } catch (Throwable e) {

        }
    }

//    @Test
//    public void testGrantedSealsList() {
//        Map<String, Object> params = new HashMap<>();
//        params.put("X-Tsign-Open-App-Id", "**********");
//        RequestContext.setContextMap(params);
//        rest.grantedSealsList("053b8e1f506f48c8a6d9c51963c0a865", 1, 10, false);
//    }

    @Test
    public void testUpdateUser() {
        try {
            RequestContext.setContextMap(contextMap);
            PersonRenewV1Request renewRequest = new PersonRenewV1Request();
            renewRequest.setMobile("***********");
            WrapperResponse<PersonGetResponse> res = rest.updateUserByAccountId("********************************",
                    renewRequest);
        } catch (Throwable exception) {
        }
    }

    @Test
    public void testGetUserByThirdId() {
        WrapperResponse<PersonGetV1Response> res = rest.getUserByThirdId(null, null);
        Assert.assertNotNull(res);
    }

    @Test
    public void testgetUserByIdNumber() {
         WrapperResponse<PersonGetV1Response> res = rest.getUserByIdNumber(null, null);
        Assert.assertNotNull(res);
    }


    @After
    public void clear() {
        // persons.forEach(p -> rest.deleteUserByAccountId(p));
        // organs.forEach(o -> rest.deleteOrgByAccountId(o));
    }

    private OrgAddByUserIdRequest buildOrg() {
        OrgAddByUserIdRequest request = new OrgAddByUserIdRequest();
        request.setIdNumber(UTCGenerator.utcPsnIdcard());
        request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setName(UTCGenerator.utcName());
        request.setThirdPartyUserId(UTCGenerator.utcThirdAppUser());
        request.setOrgLegalIdNumber(UTCGenerator.utcPsnIdcard());
        request.setOrgLegalName(UTCGenerator.utcName());
        return request;
    }

    private OrgAddByIdNoRequest buildOrgByCred() {
        OrgAddByIdNoRequest request = new OrgAddByIdNoRequest();
        request.setIdNumber(UTCGenerator.utcPsnIdcard());
        request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setName(UTCGenerator.utcName());
        request.setOrgLegalIdNumber(UTCGenerator.utcPsnIdcard());
        request.setOrgLegalName(UTCGenerator.utcName());
        return request;
    }

    private PersonAddByIdV1Request buildPerson() {
        PersonAddByIdV1Request request = new PersonAddByIdV1Request();
        request.setThirdPartyUserId(UTCGenerator.utcThirdAppUser());
        request.setIdNumber(UTCGenerator.utcPsnIdcard());
        request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setEmail(UTCGenerator.utcEmail());
        request.setMobile(UTCGenerator.utcMobile());
        request.setName(UTCGenerator.utcName());
        return request;
    }

    private PersonAddByCredRequest buildPersonByCred() {
        PersonAddByCredRequest request = new PersonAddByCredRequest();
        request.setIdNumber(UTCGenerator.utcPsnIdcard());
        request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setEmail(UTCGenerator.utcEmail());
        request.setMobile(UTCGenerator.utcMobile());
        request.setName(UTCGenerator.utcName());
        return request;
    }
}
