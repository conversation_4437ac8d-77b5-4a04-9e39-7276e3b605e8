package com.timevale.unittest.footstone.v2.rest;

import com.google.common.collect.Maps;
import com.timevale.easun.service.model.account.output.BizOrganOidOutput;
import com.timevale.easun.service.model.account.output.register.BizOidOutput;
import com.timevale.footstone.user.service.enums.RealNameAuthTypeEnum;
import com.timevale.footstone.user.service.inner.biz.BizRealNameService;
import com.timevale.footstone.user.service.inner.biz.BizWillAuthService;
import com.timevale.footstone.user.service.inner.impl.biz.v2.BizAccountServiceImplV2;
import com.timevale.footstone.user.service.inner.impl.request.CreateWillAuthRequest;
import com.timevale.footstone.user.service.inner.model.v2.AuthIdentityContext;
import com.timevale.footstone.user.service.model.account.mods.CommonContacts;
import com.timevale.footstone.user.service.model.account.mods.PersonGlobalCredentials;
import com.timevale.footstone.user.service.model.account.mods.PersonProperties;
import com.timevale.footstone.user.service.model.account.request.PersonCreateRequest;
import com.timevale.footstone.user.service.model.account.request.PersonUpdateRequest;
import com.timevale.footstone.user.service.model.account.request.v2.*;
import com.timevale.footstone.user.service.model.account.response.AccountBaseResponse;
import com.timevale.footstone.user.service.model.realname.request.OrgRealnameRequest;
import com.timevale.footstone.user.service.model.realname.request.PersonRealnameRequest;
import com.timevale.footstone.user.service.rest.v2.AccountRestV2;
import com.timevale.footstone.user.service.utils.CacheUtil;
import com.timevale.footstone.user.service.utils.ResponseUtil;
import com.timevale.footstone.user.service.utils.UrlUtil;
import com.timevale.footstone.user.service.utils.conv.v2.AccountConverterV2;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.assist.AccountAssist;
import com.timevale.unittest.footstone.configuration.Application;
import com.timevale.unittest.footstone.tc.UTCGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.timevale.footstone.user.service.inner.model.v2.SilentRegConstants.SILENT_AUTH;

/**
 * <AUTHOR>
 * @version $ Id: AccountTest.java, v0.1 2021年03月24日 10:09 WangYuWu $
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AccountV2Test {

    @Autowired
    private AccountAssist assist;

    @Autowired
    private AccountRestV2 restV2;

    @Autowired
    private BizRealNameService bizRealNameService;

    @Autowired
    private BizWillAuthService bizWillAuthService;

    @Autowired
    private BizAccountServiceImplV2 bizAccountServiceImplV2;

    private final String realnameCtx = ",RN,";

    private final Set<String> persons = new HashSet<>();

    @Test
    public void testCreatePersonV2() {
        PersonProperties prop = new PersonProperties();
        prop.setRealnameContext(realnameCtx);
        prop.setName("王玉梧");

        CommonContacts contacts = new CommonContacts();
        contacts.setMobile("***********");

        PersonGlobalCredentials credentials = new PersonGlobalCredentials();
        credentials.setIdno("411527199007151676");

        PersonCreateRequest request = new PersonCreateRequest();
        request.setProperties(prop);
        request.setContacts(contacts);
        request.setCredentials(credentials);

        AccountBaseResponse resp;
        try {
            resp = ResponseUtil.get(restV2.createOverallV2(request));
            Assert.assertNotNull(resp.getAccountId());
        } catch (Exception e) {

        }
    }

    @Test
    public void test2CreatePersonV2() {
        PersonProperties prop = new PersonProperties();
        prop.setRealnameContext(realnameCtx);
        prop.setName("");

        CommonContacts contacts = new CommonContacts();
        contacts.setMobile("***********");

        PersonGlobalCredentials credentials = new PersonGlobalCredentials();
        credentials.setIdno("411527199007151676");

        PersonCreateRequest request = new PersonCreateRequest();
        request.setProperties(prop);
        request.setContacts(contacts);
        request.setCredentials(credentials);

        AccountBaseResponse resp;
        try {
            resp = ResponseUtil.get(restV2.createOverallV2(request));
        } catch (Exception e) {

        }
    }

    @Test
    public void testUpdatePersonV2() {
        PersonProperties prop = new PersonProperties();
        prop.setRealnameContext(realnameCtx);
        prop.setName("王玉梧");
        prop.setBankNum("****************");
        CommonContacts contacts = new CommonContacts();
        contacts.setMobile("***********");

        PersonGlobalCredentials credentials = new PersonGlobalCredentials();
        credentials.setIdno("411527199007151676");

        PersonUpdateRequest request = new PersonUpdateRequest ();
        request.setProperties(prop);
        request.setContacts(contacts);
        request.setCredentials(credentials);

        try {
            restV2.updateAccountV2("eb01c4ee5fe84709a90c517cec12040f", request);
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize() {
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName(UTCGenerator.utcName());
        request.setAuthSubject(authSub);
        request.setAccount("***********");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("authentication");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        try {
            restV2.combinationAuthCode(request, "123456");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize2() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthConfigParam authConfigParam = new AuthConfigParam();
        authConfigParam.setAuthType(RealNameAuthTypeEnum.PSN_BANK4_AUTHCODE.getAuthType());
        request.setAccount("***********");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("authentication");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        request.setAuthConfigParam(authConfigParam);
        try {
            restV2.combinationAuthCode(request, "123456");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize3() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthConfigParam authConfigParam = new AuthConfigParam();
        authConfigParam.setAuthType(RealNameAuthTypeEnum.PSN_BANK4_AUTHCODE.getAuthType());
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName("esigntest深圳天谷信息科技有限公司");
        authSub.setCertNo("91440300MA5ERJGK30");
        request.setParamsKey(UUID.randomUUID().toString());
        request.setAuthSubject(authSub);
        request.setAccount("***********");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        request.setAuthConfigParam(authConfigParam);
        try {
            restV2.combinationAuthCode(request, "123456");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize4() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName("esigntest深圳天谷信息科技有限公司");
        authSub.setCertNo("91440300MA5ERJGK30");
        request.setAuthSubject(authSub);
        request.setAccount("<EMAIL>");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        try {
            restV2.combinationAuthCode(request, "123456");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize5() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        request.setAccount("***********");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        try {
            restV2.combinationAuthCode(request, "123456");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize6() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        request.setAccount(UTCGenerator.utcEmail());
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        AuthIndividualInfo individualInfo = new AuthIndividualInfo();
        individualInfo.setName("Monk");
        request.setIndividualInfo(individualInfo);
        try {
            restV2.combinationAuthCode(request, "123456");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize7() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName(UTCGenerator.utcName());
        authSub.setCertNo("91440300MA5ERJGK30");
        request.setAuthSubject(authSub);
        request.setAccount("<EMAIL>");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        try {
            restV2.combinationAuthCode(request, "123456");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize8() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName(UTCGenerator.utcName());
        authSub.setCertNo("91440300MA5ERJGK30");
        request.setAuthSubject(authSub);
        request.setAccount("<EMAIL>");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        try {
            restV2.combinationAuthCode(request, "be5c27e7-62bc-435e-b83c-30fff6eeaaff");
        } catch (Exception e) {

        }

    }

    @Test
    public void testOauth2Authorize9() {
        HttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContext.put("httpServletRequest", servletRequest);
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName(UTCGenerator.utcName());
        authSub.setCertNo("91440300MA5ERJGK30");
        request.setAuthSubject(authSub);
        request.setAccount("***********");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        try {
            restV2.combinationAuthCode(request, "be5c27e7-62bc-435e-b83c-30fff6eeaaff");
        } catch (Exception e) {

        }

    }

    @Test
    public void testRealNameCallback() {
        String orgOid = "a7d841602cfa4f8b87f8b2d99d6c01cc";
        String oid = "b8576bddced04ad98f8d971838b8b9cc";
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName(UTCGenerator.utcName());
        request.setAuthSubject(authSub);
        request.setAccount("***********");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        BizOidOutput oidOutput = new BizOidOutput();
        BizOrganOidOutput organOidOutput = new BizOrganOidOutput();
        organOidOutput.setOrgOid(orgOid);
        oidOutput.setOid(oid);
        oidOutput.setOrgan(organOidOutput);
        AuthIdentityContext authOrgContext = AuthIdentityContext
                .builder()
                .appId(request.getAppId())
                .organOid(orgOid)
                .agentOid(oid)
                .build();
        CacheUtil.set2Redis(orgOid, AccountConverterV2.convert(
                authOrgContext,
                request,
                CacheUtil.REAL_NAME,
                "",
                "http://baidu.com"));
        try {
            restV2.realNameRedirect(orgOid);
        } catch (Exception e) {

        }
        CacheUtil.deleteFromRedis(orgOid);
    }

    @Test
    public void testWillAuthCallback() {
        String oid = "b8576bddced04ad98f8d971838b8b9cc";
        SilentRegisterRequest request = new SilentRegisterRequest();
        AuthorizedSubject authSub = new AuthorizedSubject();
        authSub.setName(UTCGenerator.utcName());
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("combination");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template");
        BizOidOutput oidOutput = new BizOidOutput();
        oidOutput.setOid(oid);

        AuthIdentityContext authOrgContext = AuthIdentityContext
                .builder()
                .appId(request.getAppId())
                .agentOid(oid)
                .build();
        CacheUtil.set2Redis(oid, AccountConverterV2.convert(
                authOrgContext,
                request,
                CacheUtil.WILL_AUTH,
                "",
                ""));
        try {
            restV2.willAuthRedirect(oid);
        } catch (Exception e) {

        }
        CacheUtil.deleteFromRedis(oid);
    }

    @Test
    public void testAccountV2() {
        AccountConverterV2.convert("**********", "***********");
        AccountConverterV2.convert("**********", "<EMAIL>");
    }

    @Test
    public void testRealNameUrl() {
        // 故意写一个错的appid，测试异常场景
        Map<String, Object> map = Maps.newHashMap();
        map.put("X-Tsign-Dns-App-Id", "*********");
        RequestContext.setContextMap(map);
        String accountId = "737510a48b5d410098ad5c7f8f56a998";
        PersonRealnameRequest request = new PersonRealnameRequest();
        request.setBizAppId("*********");
        request.setRedirectUrl("https://www.baidu.com");
        Map<String, String> map2 = Maps.newHashMap();
        map2.put("dnsAppId", "*********");
        request.setContext(map2);

        try {
            bizRealNameService.getAccountRealNameUrl(accountId, request);
        } catch (Exception e) { }
    }


    @Test
    public void testRealNameUrl2() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("X-Tsign-Dns-App-Id", "xyx");
        RequestContext.setContextMap(map);
        String orgId = "02f39665e34c4bd98232da6de302e29d";
        OrgRealnameRequest request = new OrgRealnameRequest();
        request.setBizAppId("xyx");
        request.setRedirectUrl("https://www.baidu.com");
        Map<String, String> map2 = Maps.newHashMap();
        map2.put("dnsAppId", "xyx");
        request.setContext(map2);
        request.setAgentAccountId("737510a48b5d410098ad5c7f8f56a998");
        try {
            bizRealNameService.getOrganRealNameUrl(orgId, request);
        } catch (Exception e) { }

    }

    @Test
    public void testRealNameUrl3() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("X-Tsign-Dns-App-Id", "**********");
        RequestContext.setContextMap(map);
        OrgRealnameRequest model = new OrgRealnameRequest();
        model.setRedirectUrl("https://www.baidu.com");
        model.setAgentAccountId("737510a48b5d410098ad5c7f8f56a998");
        model.setBizAppId(RequestContext.getAppId());

        String accountId = "d338586decff4b21b2a1a28e2c9c052a";
        PersonRealnameRequest request = new PersonRealnameRequest();
        request.setBizAppId("**********");
        request.setRedirectUrl("https://www.baidu.com");
        try {
            bizRealNameService.getOrganRealNameNewUrl(accountId, model);
        } catch (Exception e) {

        }
    }

    @Test
    public void testRealNameUrl4() {
        OrgRealnameRequest model = new OrgRealnameRequest();
        Map<String, Object> map = Maps.newHashMap();
        map.put("X-Tsign-Dns-App-Id", "**********");
        RequestContext.setContextMap(map);
        model.setRedirectUrl("https://www.baidu.com");
        model.setAgentAccountId("737510a48b5d410098ad5c7f8f56a998");
        model.setBizAppId(RequestContext.getAppId());

        String accountId = "d338586decff4b21b2a1a28e2c9c052a";
        PersonRealnameRequest request = new PersonRealnameRequest();
        request.setBizAppId("**********");
        request.setRedirectUrl("https://www.baidu.com");
        try {
            bizRealNameService.getOrganRealNameNewUrl(accountId, model);
        } catch (Exception e) {

        }
    }

    @Test
    public void testRealNameNotification() {
//        Map<String, Object> map = Maps.newHashMap();
//        map.put("X-Tsign-Dns-App-Id", "**********");
//        MockHttpServletRequest request = new MockHttpServletRequest();
//        request.setParameter("code","********,无法确认为同一人");
//        RequestContext.setContextMap(map);
        RealNameNotifyOrgRequest request = new RealNameNotifyOrgRequest();
        request.setAgentAccountId("9aa536049ad34c86af0ce44ee42f96a8");
        request.setAgentFlowId("2020069250873951023");
        request.setAccountId("27e5dec466a44eb68df184a6a6573132");
        request.setFlowId("2020069250521629508");
        request.setContextId("123");
        request.setSuccess(true);
        request.setVerifycode("1235656");
        try {
            restV2.realNameNotification(request);
        } catch(Exception e) {

        }

    }

    @Test
    public void testWillAuthNotify() {
//        Map<String, Object> map = Maps.newHashMap();
//        map.put("X-Tsign-Dns-App-Id", "**********");
//        MockHttpServletRequest request = new MockHttpServletRequest();
//        request.setParameter("code","********,无法确认为同一人");
//        RequestContext.setContextMap(map);
//        WillAuthNotifyRequest request = new WillAuthNotifyRequest();
//        request.setAction("WILL_FINISH");
//        request.setBizId("aqsadada");
//        request.setAccountId("ea9cf8dc1a884589959ebc51a9c064e9");
//        request.setSuccess(true);
//        try {
//            restV2.willAuthNotification(request);
//        } catch(Exception e) {
//
//        }

    }

    @Test
    public void testRealNameNotify() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("X-Tsign-Dns-App-Id", "**********");
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setParameter("code","********,无法确认为同一人");
        RequestContext.setContextMap(map);
        CreateWillAuthRequest willAuthRequest = new CreateWillAuthRequest();
        willAuthRequest.setOuid("737510a48b5d410098ad5c7f8f56a998");
        willAuthRequest.setPcRedirect(Boolean.TRUE);
        willAuthRequest.setBizType(SILENT_AUTH);
        willAuthRequest.setRedirectUrl("https://www.baidu.com" + "/737510a48b5d410098ad5c7f8f56a998");
        try {
            bizWillAuthService.createWillAuth(willAuthRequest, SILENT_AUTH);
        } catch(Exception e) {

        }
    }

    @Test
    public void testNotifyOrgan() {
        SilentRegisterRequest request = new SilentRegisterRequest();
        String orgOid = "a7d841602cfa4f8b87f8b2d99d6c01cc";
        request.setAccount("***********");
        request.setAppId("**********");
        request.setResponseType("code");
        request.setBizType("authentication");
        request.setRedirectUrl("http://baidu.com");
        request.setScope("get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin");
        String flowId = UUID.randomUUID().toString().replace("-","");
        String agentOid = UUID.randomUUID().toString().replace("-","");
        try {
            bizAccountServiceImplV2.notifyOrgan(request,orgOid,flowId,flowId,agentOid,request.getAppId());
        } catch (Exception e) {

        }
    }

    @Test
    public void testUrlUtilDecode() {
        UrlUtil.decode("https://www.baidu.com");
    }
}
