package com.timevale.unittest.footstone;

import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.inner.biz.BizCertService;
import com.timevale.footstone.user.service.inner.biz.BizNotificationService;
import com.timevale.footstone.user.service.model.cert.request.BillCertApplyRequest;
import com.timevale.footstone.user.service.model.notification.mods.NotificationModel;
import com.timevale.footstone.user.service.rest.CertRest;
import com.timevale.footstone.user.service.rest.NotificationRest;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR;

/**
 *
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class NotificationRestTest {

    @Autowired
    private BizNotificationService bizNotificationService;

    @Test
    public void historyListTest(){
        String testOid ="173d461caa8944dbbe76985e4d00db15";
        PagerResult<NotificationModel> inmail = bizNotificationService.historyList(testOid, "INMAIL", null, "202007", 1, 10);
        System.out.println("historyList total:"+inmail.getTotal());

    }


}
