package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizTInviteService;
import com.timevale.footstone.user.service.inner.impl.BizMemberServiceImpl;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.AccountCertRequest;
import com.timevale.footstone.user.service.model.invite.mods.InvitePassedJoinMember;
import com.timevale.footstone.user.service.model.invite.request.BatchPassedJoinReq;
import com.timevale.footstone.user.service.model.invite.response.InviteJoinListRes;
import com.timevale.footstone.user.service.model.member.request.CreateMemberRequest;
import com.timevale.footstone.user.service.mq.MqConfig;
import com.timevale.footstone.user.service.mq.NotificationRequestConv;
import com.timevale.footstone.user.service.mq.RocketMqClient;
import com.timevale.footstone.user.service.rest.AccountRest;
import com.timevale.footstone.user.service.rest.TInviteRest;
import com.timevale.notificationmanager.service.model.SendMessageRequest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2019年11月13日 16:09:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class InviteTest {

    @Autowired BizMemberServiceImpl bizMemberService;

    @Autowired RocketMqClient<SendMessageRequest> rocketMqClient;

    @Autowired AccountRest accountRest;

    @Autowired TInviteRest inviteRest;

    @Autowired MqConfig mqConfig;

    @Autowired BizTInviteService tInviteService;

    @Test
    public void testNotificationSend() {
        CreateMemberRequest createMemberRequest = new CreateMemberRequest();
        createMemberRequest.setAccountId("3b7fb3cf576945c6baa7bac624490995");
        try {
            bizMemberService.createMember(
                    "45fa7094122349d9967e67c5550b5131",
                    createMemberRequest,
                    "45fa7094122349d9967e67c5550b5131");

        } catch (Exception e) {

        }

        CreateMemberRequest createMemberRequest1 = new CreateMemberRequest();
        createMemberRequest.setAccountId("1f2c8fba4f174d52b81098aa14d6f99b");
        try {
            bizMemberService.createMember(
                    "45fa7094122349d9967e67c5550b5131",
                    createMemberRequest1,
                    "45fa7094122349d9967e67c5550b5131");

        } catch (Exception e) {

        }
    }

    @Test
    public void testNotificationConv() {
        SendMessageRequest request =
                NotificationRequestConv.convertEmailRequest(
                        "<EMAIL>", new HashMap<>(), "invite_join");
        NotificationRequestConv.convertMobileRequest("***********", new HashMap<>(), "invite_join");
        rocketMqClient.send(null, "notification_center", "standard-in", request);
    }

    @Test
    public void updateOrgCertTest() {
        try {
            AccountCertRequest accountCertRequest = new AccountCertRequest();
            accountCertRequest.setType("CRED_ORG_USCC");
            accountCertRequest.setValue("91330106571455000W");
            accountRest.updateCert("19f0a73332c0443fb2bf2f4a524cb9b2", null);
        } catch (Exception e) {

        }
    }

    @Test
    public void inviteListTest() {
        try {
            WrapperResponse<InviteJoinListRes> res =
                    inviteRest.getInviteJoinList(
                            "749c82084e144f0ea7f41a93a531cfbb",
                            null,
                            0,
                            20,
                            null,
                            null,
                            "***********",
                            "aa",
                            null,
                            null);
            res.getData().getTotal();
        } catch (Exception e) {

        }
    }

    @Test
    public void bachtAcceptTest() {
        try {
            BatchPassedJoinReq req = new BatchPassedJoinReq();
            List<InvitePassedJoinMember> passedJoinMembers = new ArrayList<>();
            InvitePassedJoinMember member = new InvitePassedJoinMember();
            member.setInviteeAccount("tttttt");
            member.setInviteId("tInviteId");
            passedJoinMembers.add(member);
            req.setInvitedPersons(passedJoinMembers);
            inviteRest.batchPassedJoin("", req);
        } catch (Exception e) {

        }
    }

    @Test
    public void testEnc() {
        try {
            mqConfig.encryptByPrivateKey("test", "**********");
        } catch (Exception e) {

        }
    }

    @Test
    public void inviteConsume() {
        try {
            String body =
                    "{\"inviteId\":\"444af28e58754bf58b8a8dd6781927aa\",\"inviteeId\":\"974a2665419d4d3c817780fb299c681f\",\"contextId\":\"e047f00ad4bf40df8fb8e17e4bed96c8\",\"currentStatus\":\"invite.end\",\"data\":{\"orgId\":\"dace182f80f54a56afb2812e0c15ec99\"},\"pushUrl\":\"http://118.178.93.198:8880/testnotify/msgRecive\"}";
            mqConfig.pushDate(body);
        } catch (Exception e) {

        }
    }

    @Test
    public void inviteIdentityAuthUrlTest() {
        String operating = "&operating=eyJpbnZpdGVJZCI6ImJiNTNjYzdmMWZjMzQxMDRhN2E1NmVjYmQ1MjAzNjM2IiwiY29udGV4dElkIjoiZjQwOWQwMTAxMjEwNDEzMmFmYzY0NmJiY2ZiNTEyNjYifQ%3D%3D";
        try {
            String nextPage = "https://testh5.tsign.cn/account-webserver/redirect/organizations/realname?inviteId=d9aeb0c977774f72bc0669b7a25155e4&logId=caea95b8814340e4ba8538a52d4bf1df&contextId=001df6bcff2a439c80f414de918009e8"+operating;
            String inviteId = "d9aeb0c977774f72bc0669b7a25155e4";
            String accountId = "79e1b9b0e905483c946fd9606268402e";
            String contextId = "001df6bcff2a439c80f414de918009e8";
            tInviteService.inviteIdentityAuthUrl(nextPage, inviteId, accountId, contextId);
        } catch (Exception e) {

        }

        try {
            String nextPage = "https://testh5.tsign.cn/account-webserver/redirect/organizations/realname?inviteId=d9aeb0c977774f72bc0669b7a25155e4&logId=caea95b8814340e4ba8538a52d4bf1df&contextId=001df6bcff2a439c80f414de918009e8&orgId=a78aa12faafa461b978258116af8eab3"+operating;
            String inviteId = "d9aeb0c977774f72bc0669b7a25155e4";
            String accountId = "79e1b9b0e905483c946fd9606268402e";
            String contextId = "001df6bcff2a439c80f414de918009e8";
            tInviteService.inviteIdentityAuthUrl(nextPage, inviteId, accountId, contextId);
        } catch (Exception e) {

        }

        try {
            String nextPage = "https://testh5.tsign.cn/account-webserver/redirect/accounts/realname?inviteId=d9aeb0c977774f72bc0669b7a25155e4&logId=caea95b8814340e4ba8538a52d4bf1df&contextId=001df6bcff2a439c80f414de918009e8"+operating;
            String inviteId = "d9aeb0c977774f72bc0669b7a25155e4";
            String accountId = "79e1b9b0e905483c946fd9606268402e";
            String contextId = "001df6bcff2a439c80f414de918009e8";
            tInviteService.inviteIdentityAuthUrl(nextPage, inviteId, accountId, contextId);
        } catch (Exception e) {

        }
    }
}
