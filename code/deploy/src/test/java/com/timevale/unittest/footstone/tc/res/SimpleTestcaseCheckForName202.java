package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestBean;
import org.junit.Assert;

public enum SimpleTestcaseCheckForName202 implements SimpleTestcaseChecker{
    T_1("12345", "12**"),

    T_2("123", "12*"),

    T_3("12", "1*"),

    T_4("1", "*"),

    T_5("123456", "12**"),

    T_6("1234567", "12**"),

    T_7("", ""),
    ;

    private String input;

    private String anwser;

    SimpleTestcaseCheckForName202(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestBean t = new TestBean();
        t.setName202(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getName202());
    }
}
