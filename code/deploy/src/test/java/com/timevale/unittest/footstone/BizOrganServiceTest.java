package com.timevale.unittest.footstone;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.constants.BuiltinThirdparty;
import com.timevale.account.service.model.service.biz.acc.output.BizAccountBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.model.account.AdminInfo;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.identity.common.service.api.scene.webpage.RpcWebIdentityAuthService;
import com.timevale.footstone.identity.common.service.model.config.AuthConfigEntity;
import com.timevale.footstone.identity.common.service.response.scene.webpage.IdentityAuthUrlResponse;
import com.timevale.footstone.user.service.configuration.IsolationConfig;
import com.timevale.footstone.user.service.inner.biz.BizThirdPlatformIntegrationService;
import com.timevale.footstone.user.service.inner.biz.UserOrganListService;
import com.timevale.footstone.user.service.inner.impl.biz.BizOrganServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcThirdPartyPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import com.timevale.footstone.user.service.inner.model.OrganSortModel;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.inner.respdecoration.deco.decoraters.Decoration;
import com.timevale.footstone.user.service.model.account.mods.OrgProperties;
import com.timevale.footstone.user.service.model.account.request.OrgUpdateRequest;
import com.timevale.footstone.user.service.model.organization.request.CheckOrganExistRequest;
import com.timevale.footstone.user.service.model.organization.request.OrgAdminJoinAuthIdentityRequest;
import com.timevale.footstone.user.service.model.organization.request.OrgAuthIdentityContextRequest;
import com.timevale.footstone.user.service.model.third.response.CanBindOrgThirdPartyResponse;
import com.timevale.footstone.user.service.model.third.response.GetTenantAndUserInfoResponse;
import com.timevale.footstone.user.service.model.third.response.QueryCanBindThirdIdOrgListResponse;
import com.timevale.footstone.user.service.mq.RocketMqClient;
import com.timevale.notice.service.api.RpcBizProcessService;
import com.timevale.footstone.user.service.model.organization.request.OrganSortRequest;
import com.timevale.footstone.user.service.model.organization.request.OrganInput;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.notificationmanager.service.model.SendMessageRequest;
import com.timevale.privilege.service.enums.BuiltinRoleType;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import com.timevale.saas.auth.api.facade.result.user.GetUserDingInfoRsp;
import feign.Client;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

import static com.timevale.footstone.user.service.model.third.response.CanBindOrgThirdPartyResponse.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年07月19日 16:25:00
 */
@RunWith(MockitoJUnitRunner.class)
public class BizOrganServiceTest {

    @InjectMocks
    private BizOrganServiceImpl organService;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;
    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Mock
    private UserOrganListService userOrganListService;

    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

    @Mock
    private SystemProperty systemProperty;
    @Mock
    private IsolationConfig isolationConfig;

    @Mock
    private RpcWebIdentityAuthService webIdentityAuthService;

    @Mock
    private RpcBizProcessService rpcBizProcessService;
    @Mock private RpcOrgPlusServiceAdapt rpcOrgPlusServiceAdapt;
    private static String testOrgId = "testOrgId";

    private static final BizAccountRealNameOutput realnameOut;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;

    @Mock
    private RocketMqClient<SendMessageRequest> rocketMqClient;
    @Mock
    private BizThirdPlatformIntegrationService bizThirdPlatformIntegrationService;

    @Mock
    private RpcThirdPartyPlusServiceAdapt rpcThirdPartyPlusServiceAdapt;

    @Mock
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;

    @Mock
    private Decoration decorater;

    static {
        realnameOut = new BizAccountRealNameOutput();
        realnameOut.setStatus(RealnameStatus.ACCEPT);

        BizICUserOutput icUserOutput = new BizICUserOutput();
        ICUserId icUserId = new ICUserId();
        icUserId.setOuid(testOrgId);
        icUserOutput.setId(icUserId);
        icUserOutput.setProperties(new ArrayList<>());
        icUserOutput.getProperties().add(new Property(BuiltinProperty.INFO_NAME, "testName"));
        icUserOutput.getProperties().add(new Property(BuiltinProperty.INFO_ORG_LEGAL_NAME, "testOrgLegalName"));
        icUserOutput.getProperties().add(new Property(BuiltinProperty.INFO_ORG_LEGAL_IDNO, "testOrgLegalIdNo"));

        realnameOut.setAccount(icUserOutput);
    }

    @Before
    public void setUp() {
        //when(icUserPlusServiceAdapt.getRealNameByOuid(testOrgId)).thenReturn(realnameOut);
        when(userOrganListService.convSortList(any())).thenReturn(Lists.newArrayList(new OrganSortModel()));
    }

//    @Test
    public void deprecateRealnameCheckTest(){
        OrgUpdateRequest updateRequest = new OrgUpdateRequest();
        OrgProperties nameUpdateProperties = new OrgProperties();
        nameUpdateProperties.setName("newName");
        updateRequest.setProperties(nameUpdateProperties);
        Assert.isTrue(organService.deprecateRealnameCheck(testOrgId, updateRequest));

        nameUpdateProperties.setName("testName");
        Assert.isTrue(!organService.deprecateRealnameCheck(testOrgId, updateRequest));

        OrgProperties legalNameUpdateProperties = new OrgProperties();
        legalNameUpdateProperties.setLegalName("newLegalName");
        updateRequest.setProperties(legalNameUpdateProperties);
        Assert.isTrue(organService.deprecateRealnameCheck(testOrgId, updateRequest));
        legalNameUpdateProperties.setLegalName("testOrgLegalName");
        Assert.isTrue(!organService.deprecateRealnameCheck(testOrgId, updateRequest));


        OrgProperties legalIdNoUpdateProperties = new OrgProperties();
        legalIdNoUpdateProperties.setLegalIdno("newLegalIdNo");
        updateRequest.setProperties(legalIdNoUpdateProperties);
        Assert.isTrue(organService.deprecateRealnameCheck(testOrgId, updateRequest));
        legalIdNoUpdateProperties.setLegalIdno("testOrgLegalIdNo");
        Assert.isTrue(!organService.deprecateRealnameCheck(testOrgId, updateRequest));

    }

    @Test
    public void testGetAdminJoinOrgIdentityAuthUrl() {


        OrgAdminJoinAuthIdentityRequest request = new OrgAdminJoinAuthIdentityRequest();
        request.setAgentId("agentId");
        request.setOrgId("orgId");
        AuthConfigEntity authConfig = new AuthConfigEntity();
        authConfig.setOrgEditableInfo(Lists.newArrayList("legalRepName"));
        request.setConfigParams(authConfig);
        OrgAuthIdentityContextRequest contextInfo = new OrgAuthIdentityContextRequest();
        contextInfo.setRedirectUrl("redirectUrl");
        request.setContextInfo(contextInfo);
        IdentityAuthUrlResponse identityAuthUrlResponse = new IdentityAuthUrlResponse();
        identityAuthUrlResponse.setUrl("url");
        identityAuthUrlResponse.setFlowId("flowId");
        BaseResult<IdentityAuthUrlResponse> baseResult = BaseResult.success(identityAuthUrlResponse);
        when(webIdentityAuthService.createOuidOrganizationIdentityAuthUrl(any(), any())).thenReturn(baseResult);
        when(rpcBizProcessService.createBizProcessRecord(any())).thenReturn(RpcOutput.with(true));
        organService.getAdminJoinOrgIdentityAuthUrl(request);
    }


    @Test
    public void getOrganByNameTest() {
        CheckOrganExistRequest request = new CheckOrganExistRequest();
        OrganInput input = new OrganInput();
        input.setOrgName("organName");
        input.setOrgCode("organCode");
        request.setOrgans(Lists.newArrayList(input));
        when(esAccountServiceAdapt.getOrgsByEs(any()))
                .thenReturn(MockUntils.mockPageResultEsSearchAccount(Lists.newArrayList(MockUntils.mockEsOrgSearhOutput())));
        organService.batchGetOrgans(request);
    }


    @Test
    public void getByNameTest() {
        organService.getOrgByName("*", "accept", 100, 100, true, "7488"
                , "accountId", "accountId");
    }


    @Test
    public void organSortTest() {
        List<OrganSortRequest> orgsList = new ArrayList<>();
        OrganSortRequest sortRequest = new OrganSortRequest();
        sortRequest.setTop(1);
        sortRequest.setOrgId("orgId");
        orgsList.add(sortRequest);
        organService.organSort(orgsList, "accountId");
    }

    @Test
    public void thirdPartyBindNoticeParamsTest() {
        BizICUserOutput bizICUserOutput = new BizICUserOutput();
        when(icUserPlusService.getICUserByOuid(any())).thenReturn(bizICUserOutput);
        doNothing().when(rocketMqClient).send(any(), any(), any(), any());
        organService.orgThirdPartyBindNotice("FEI_SHU", "tk", "operatorOid", "orgOuid");
    }
    @Test
    public void testCreateOrgAndBindTenantKey() {
        organService.createOrgAndBindTenantKey("psn11111", "企业A", "tenantKey2222", BuiltinThirdparty.DING_TALK, "appId444444");
        Mockito.verify(rpcOrgPlusServiceAdapt, times(1)).createICOrgAndCompanyV2(any());
    }

    /**
     * 实名组织已存在，且未绑定三方id，且是管理员
     */
    @Test
    public void testCanBindOrgThirdParty() {
        ReflectionTestUtils.setField(organService,"needCheckIsAdminClientIds",  Sets.newHashSet("DING_TALK"));
        ReflectionTestUtils.setField(organService,"defaultAppId","7488");
        when(bizThirdPlatformIntegrationService.queryPersonThirdUserIdByOid(any(), any(), any())).thenReturn("thirdUserId1111");
        when(rpcTripartiteServiceAdapt.isAdmin(any(), any(), any())).thenReturn(true);

        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
        EsId esId = new EsId();
        esId.setOuid("ouid11111");
        user.setId(esId);
        BizAccountBaseOutput base = new BizAccountBaseOutput();
        ProductApp projectApp = new ProductApp();
        projectApp.setProjectId("7488");
        base.setProjectApp(projectApp);
        user.setBase(base);
        esOrgSearhOutput.setUser(user);
        when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(new PagerResult<>(Lists.newArrayList(esOrgSearhOutput),1));
        when(bizThirdPlatformIntegrationService.isBindTenantKey(any(), any())).thenReturn(false);
        when(icOrgPlusServiceAdapt.checkAdmin(any(), any())).thenReturn(true);

        CanBindOrgThirdPartyResponse response = organService.canBindOrgThirdParty(ClientEnum.DING_TALK.getClientId(), "tenantKey2222", "企业A", "psnOid11111");
        org.junit.Assert.assertEquals(CAN_BIND, response.getAction());
    }

    /**
     * 实名组织已存在，且未绑定三方id,但不是管理员
     */
    @Test
    public void testCanBindOrgThirdParty2() {
        ReflectionTestUtils.setField(organService,"needCheckIsAdminClientIds",  Sets.newHashSet("DING_TALK"));
        ReflectionTestUtils.setField(organService,"defaultAppId","7488");
        when(bizThirdPlatformIntegrationService.queryPersonThirdUserIdByOid(any(), any(), any())).thenReturn("thirdUserId1111");
        when(rpcTripartiteServiceAdapt.isAdmin(any(), any(), any())).thenReturn(true);

        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
        EsId esId = new EsId();
        esId.setOuid("ouid11111");
        user.setId(esId);
        BizAccountBaseOutput base = new BizAccountBaseOutput();
        ProductApp projectApp = new ProductApp();
        projectApp.setProjectId("7488");
        base.setProjectApp(projectApp);
        user.setBase(base);
        esOrgSearhOutput.setUser(user);
        when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(new PagerResult<>(Lists.newArrayList(esOrgSearhOutput),1));
        when(bizThirdPlatformIntegrationService.isBindTenantKey(any(), any())).thenReturn(false);
        when(icOrgPlusServiceAdapt.checkAdmin(any(), any())).thenReturn(false);

        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setName("赵三");
        AdminInfo adminInfo2 = new AdminInfo();
        adminInfo2.setName("赵本山");
        when(icOrgPlusServiceAdapt.getOrganAdmins(any())).thenReturn(Lists.newArrayList(adminInfo,adminInfo2));
        when(decorater.decorate(any(),any())).thenReturn("赵*","赵*山");

        CanBindOrgThirdPartyResponse response = organService.canBindOrgThirdParty(ClientEnum.DING_TALK.getClientId(), "tenantKey2222", "企业A", "psnOid11111");
        org.junit.Assert.assertEquals(INVITE_ADMIN, response.getAction());
        org.junit.Assert.assertEquals("赵*,赵*山", response.getAdminName());
    }

    /**
     * 实名组织已存在，且已绑定三方id
     */
    @Test
    public void testCanBindOrgThirdParty3() {
        ReflectionTestUtils.setField(organService,"needCheckIsAdminClientIds",  Sets.newHashSet("DING_TALK"));
        ReflectionTestUtils.setField(organService,"defaultAppId","7488");
        when(bizThirdPlatformIntegrationService.queryPersonThirdUserIdByOid(any(), any(), any())).thenReturn("thirdUserId1111");
        when(rpcTripartiteServiceAdapt.isAdmin(any(), any(), any())).thenReturn(true);

        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
        EsId esId = new EsId();
        esId.setOuid("ouid11111");
        user.setId(esId);
        BizAccountBaseOutput base = new BizAccountBaseOutput();
        ProductApp projectApp = new ProductApp();
        projectApp.setProjectId("7488");
        base.setProjectApp(projectApp);
        user.setBase(base);
        esOrgSearhOutput.setUser(user);
        when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(new PagerResult<>(Lists.newArrayList(esOrgSearhOutput),1));
        when(bizThirdPlatformIntegrationService.isBindTenantKey(any(), any())).thenReturn(true);

        CanBindOrgThirdPartyResponse response = organService.canBindOrgThirdParty(ClientEnum.DING_TALK.getClientId(), "tenantKey2222", "企业A", "psnOid11111");
        org.junit.Assert.assertEquals(ORG_HAS_OTHER_TENANT_KEY, response.getAction());
    }

    /**
     * 实名组织不存在
     */
    @Test
    public void testCanBindOrgThirdParty4() {
        ReflectionTestUtils.setField(organService,"needCheckIsAdminClientIds",  Sets.newHashSet("DING_TALK"));
        ReflectionTestUtils.setField(organService,"defaultAppId","7488");
        when(bizThirdPlatformIntegrationService.queryPersonThirdUserIdByOid(any(), any(), any())).thenReturn("thirdUserId1111");
        when(rpcTripartiteServiceAdapt.isAdmin(any(), any(), any())).thenReturn(true);

        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
        EsId esId = new EsId();
        esId.setOuid("ouid11111");
        user.setId(esId);
        BizAccountBaseOutput base = new BizAccountBaseOutput();
        ProductApp projectApp = new ProductApp();
        //不是7488
        projectApp.setProjectId("22222");
        base.setProjectApp(projectApp);
        user.setBase(base);
        esOrgSearhOutput.setUser(user);
        when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(new PagerResult<>(Lists.newArrayList(esOrgSearhOutput),1));

        CanBindOrgThirdPartyResponse response = organService.canBindOrgThirdParty(ClientEnum.DING_TALK.getClientId(), "tenantKey2222", "企业A", "psnOid11111");
        org.junit.Assert.assertEquals(DO_REAL_NAME, response.getAction());
    }

    @Test
    public void testQueryCanBindThirdIdOrgList() {
        ReflectionTestUtils.setField(organService,"defaultAppId","7488");

        BizOrgBaseInfoOutput bizOrgBaseInfoOutput = new BizOrgBaseInfoOutput();
        bizOrgBaseInfoOutput.setRealnameStatus(RealnameStatus.ACCEPT);
        BizRoleDetail bizRoleDetail = new BizRoleDetail();
        bizRoleDetail.setRoleKey(BuiltinRoleType.ADMIN);
        bizOrgBaseInfoOutput.setRoleDetailList(Lists.newArrayList(bizRoleDetail));
        BizICUserOutput account = new BizICUserOutput();
        AccountBaseDetail base = new AccountBaseDetail();
        ProductApp projectApp = new ProductApp();
        projectApp.setProjectId("7488");
        base.setProjectApp(projectApp);
        account.setBase(base);
        ICUserId id = new ICUserId();
        id.setOuid("ouid111111");
        id.setGuid("guid1111111");
        account.setId(id);
        bizOrgBaseInfoOutput.setAccount(account);
        bizOrgBaseInfoOutput.setOrganGuid(id.getGuid());
        when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(),any())).thenReturn(new PagerResult<>(Lists.newArrayList(bizOrgBaseInfoOutput),1));
        when(bizThirdPlatformIntegrationService.queryIsBindTenantKeyOrgGuids(any(), any(), any())).thenReturn(Lists.newArrayList());


        QueryCanBindThirdIdOrgListResponse response = organService.queryCanBindThirdIdOrgList(ClientEnum.DING_TALK.getClientId(), "psnId11111");
        QueryCanBindThirdIdOrgListResponse.CanBindOrgInfo canBindOrgInfo = response.getOrgList().get(0);
        org.junit.Assert.assertEquals(id.getOuid(), canBindOrgInfo.getOrgOid());
        org.junit.Assert.assertEquals(id.getGuid(), canBindOrgInfo.getOrgGid());
    }


    @Test
    public void testGetTenantAndUserInfo() {
        String userId = "userId11111";
        when( bizThirdPlatformIntegrationService.queryPersonThirdUserIdByOid(any(), any(), any())).thenReturn(userId);
        GetUserDingInfoRsp getUserDingInfoRsp = new GetUserDingInfoRsp();
        getUserDingInfoRsp.setUserId(userId);
        getUserDingInfoRsp.setDingName("墨渊");
        getUserDingInfoRsp.setAvatar("http://xxxx.com/a.png");
        when(rpcTripartiteServiceAdapt.getUserDingInfo( any(), any())).thenReturn(getUserDingInfoRsp);

        GetTenantAndUserInfoResponse response = organService.getTenantAndUserInfo(ClientEnum.DING_TALK.getClientId(), "psnId1111111", "tenantKey222222");
        org.junit.Assert.assertEquals(userId, response.getUserId());
        org.junit.Assert.assertEquals(getUserDingInfoRsp.getDingName(), response.getUserNickName());
        org.junit.Assert.assertEquals(getUserDingInfoRsp.getAvatar(), response.getUserAvatar());

    }


}
