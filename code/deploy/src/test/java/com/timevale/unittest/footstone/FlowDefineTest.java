package com.timevale.unittest.footstone;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.timevale.footstone.user.service.inner.biz.BizFlowModelService;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.flow.model.FlowNode;
import com.timevale.footstone.user.service.model.flow.model.FlowSeq;
import com.timevale.footstone.user.service.model.flow.model.FlowUser;
import com.timevale.footstone.user.service.model.flow.request.CreateFlowModelRequest;
import com.timevale.footstone.user.service.model.flow.request.ModelTypeRequest;
import com.timevale.footstone.user.service.model.flow.request.UpdateEnableStatusRequest;
import com.timevale.footstone.user.service.model.flow.request.UpdateFlowModelRequest;
import com.timevale.footstone.user.service.model.flow.response.*;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/23
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FlowDefineTest {

    @Autowired
    private BizFlowModelService bizFlowModelService;

    @Autowired
    private HttpServletRequest servletRequest;

    //盛
    //String orgId="2765cfbd878049c1bc314f68136ab26e";
    //String memberId="388d1adca73c468b8402f05573260c8f";

    //南冕
   /* String orgId="5ccb1c1b0cc84eb9abced907cf392717";
    String memberId="88e44a1ebef441d69306d2abcc0eb276";
    String modelId=null;*/

    //子烨
    String orgId="2765cfbd878049c1bc314f68136ab26e";
    String memberId="73b3e22d7c624120af28a17b6cf9e617";
    String modelId=null;


    //长歌
    //String orgId="79fc3ff6a15b406dbe31f6701670763a";
    //String memberId="36dc74167add40a28e96cf9c5995eabc";
    //String modelId=null;

    /**
     * 模板创建
     */
    @Test
    public void crateTest(){
        try {
            CreateFlowModelRequest createFlowModelRequest =new CreateFlowModelRequest();
            createFlowModelRequest.setName("ziye-合同审批新增");
            createFlowModelRequest.setType("CONTRACT");
            //组装节点
            List<FlowNode> flowNodes =new ArrayList<>();
            FlowNode node1 =new FlowNode();
            node1.setNodeId("flow_1");
            node1.setNodeName("开始");
            node1.setNodeType("START");
            node1.setNodeUsers(Sets.newHashSet(new FlowUser("388d1adca73c468b8402f05573260c8f","",2),new FlowUser("73b3e22d7c624120af28a17b6cf9e617","",2)));

            FlowNode node2 =new FlowNode();
            node2.setApprovalType("SIGNAL");
            node2.setNodeId("flow_2");
            node2.setNodeName("审批");
            node2.setNodeType("USERTASK");
            node2.setNodeUsers(Sets.newHashSet(new FlowUser("388d1adca73c468b8402f05573260c8f","",2)));

            FlowNode node3 =new FlowNode();
            node3.setNodeId("flow_3");
            node3.setNodeName("结束");
            node3.setNodeType("END");

            flowNodes.add(node1);
            flowNodes.add(node2);
            flowNodes.add(node3);
            createFlowModelRequest.setFlowNodes(flowNodes);

            //组装flowSeqs
            List<FlowSeq> flowSeqs=new ArrayList<FlowSeq>();
            FlowSeq seq1=new FlowSeq();
            seq1.setSeqId("seq_1");
            seq1.setSeqName("连接1");
            seq1.setSourceNodeId("flow_1");
            seq1.setTargetNodeId("flow_2");

            FlowSeq seq2=new FlowSeq();
            seq2.setSeqId("seq_2");
            seq2.setSeqName("连接2");
            seq2.setSourceNodeId("flow_2");
            seq2.setTargetNodeId("flow_3");

            flowSeqs.add(seq1);
            flowSeqs.add(seq2);
            createFlowModelRequest.setFlowSeqs(flowSeqs);
            CreateFlowModelResponse createFlowModelResponse= bizFlowModelService.createFlowModel(orgId, createFlowModelRequest);
            modelId=createFlowModelResponse.getModelId();
            System.out.println("创建审批模板 单测成功，审批ID="+modelId);
        } catch (Exception e) {
            System.out.println("创建审批模板 单测失败，审批ID="+modelId);
            e.printStackTrace();
        }
    }

    /**
     * 更新审批模板状态是否启用
     */
    @Test
    public void updateEnableStatusTest(){
        try {
            crateTest();
            //更新审批模板状态是否启用
            UpdateEnableStatusRequest updateEnableStatusRequest=new UpdateEnableStatusRequest();
            //设置模板状态为开启
            updateEnableStatusRequest.setUseStauts(1);
            bizFlowModelService.updateFlowModelEnableStatus(orgId,modelId, updateEnableStatusRequest);
            System.out.println("更新模板id="+modelId+"状态为启用 单测成功");
        } catch (Exception e) {
            System.out.println("更新模板id="+modelId+"状态为启用 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 编辑模板
     */
    @Test
    public void updateTest(){
        try {
            crateTest();

            //编辑
            UpdateFlowModelRequest updateRequest =new UpdateFlowModelRequest();
            updateRequest.setName("ziye-合同审批编辑");
            updateRequest.setType("CONTRACT");

            //组装节点
            List<FlowNode> flowNodes =new ArrayList<>();
            FlowNode node1 =new FlowNode();
            node1.setNodeId("flow_11");
            node1.setNodeName("开始");
            node1.setNodeType("START");
            node1.setNodeUsers(Sets.newHashSet(new FlowUser("388d1adca73c468b8402f05573260c8f","",2),new FlowUser("73b3e22d7c624120af28a17b6cf9e617","",2)));


            FlowNode node2 =new FlowNode();
            node2.setApprovalType("SIGNAL");
            node2.setNodeId("flow_22");
            node2.setNodeName("审批");
            node2.setNodeType("USERTASK");
            node2.setNodeUsers(Sets.newHashSet(new FlowUser("388d1adca73c468b8402f05573260c8f","",2)));

            FlowNode node3 =new FlowNode();
            node3.setNodeId("flow_33");
            node3.setNodeName("结束");
            node3.setNodeType("END");

            flowNodes.add(node1);
            flowNodes.add(node2);
            flowNodes.add(node3);
            updateRequest.setFlowNodes(flowNodes);

            //组装flowSeqs
            List<FlowSeq> flowSeqs=new ArrayList<FlowSeq>();
            FlowSeq seq1=new FlowSeq();
            seq1.setSeqId("seq_11");
            seq1.setSeqName("连接11");
            seq1.setSourceNodeId("flow_11");
            seq1.setTargetNodeId("flow_22");

            FlowSeq seq2=new FlowSeq();
            seq2.setSeqId("seq_22");
            seq2.setSeqName("连接22");
            seq2.setSourceNodeId("flow_22");
            seq2.setTargetNodeId("flow_33");

            flowSeqs.add(seq1);
            flowSeqs.add(seq2);
            updateRequest.setFlowSeqs(flowSeqs);

            CreateFlowModelResponse createFlowModelResponse=bizFlowModelService.updateFlowModel(orgId, modelId, updateRequest);
            System.out.println("编辑模板单测成功，模板id="+createFlowModelResponse.toString());
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("编辑模板单测失败");
        }
    }

    /**
     * 查看审批模板列表
     */
  @Test
  public void modelListTest() {
      try {
          ApiPageResult<ListModelResponse> apiPageResult = bizFlowModelService.queryFlowModelList(orgId, 0, 10);
          apiPageResult
              .getResult()
              .forEach(
                  e -> {
                    System.out.println(e.toString());
                  });
      System.out.println("查询当前组织下模板列表 单测成功");
      } catch (Exception e) {
          System.out.println("查询当前组织下模板列表 单测失败");
          e.printStackTrace();
      }
  }

    /**
     * 查询审批流支持的审批类型
     */
  @Test
  public void approvalTyeTest()  {
      try {
          List<ListFlowTypeResponse> list = bizFlowModelService.getFlowTypeList();
          System.out.println("审批流支持的类型：");
          list.forEach(
              e -> {
                System.out.println(e.toString());
              });
          System.out.println("查询审批流支持的审批类型 单测成功");
      } catch (Exception e) {
          System.out.println("查询审批流支持的审批类型 单测失败");
          e.printStackTrace();
      }
  }

    /**
     * 查询流程模板定义详情
     */
    @Test
    public void modelDefineDetailTest(){
        try {
            crateTest();
            GetModelDetailResponse response= bizFlowModelService.queryModelDefineDetail(orgId,modelId, "");
            System.out.println("查询流程模板定义详情 单测成功");
            System.out.println("查询模板Id="+modelId +"模板定义详情："+response.toString());
        } catch (Exception e) {
            System.out.println("查询流程模板定义详情 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 查询当前用户可以发起模板列表
     */
    @Test
    public void cuurrentUserModelListTest(){
        try {
            crateTest();
            ModelTypeRequest modelTypeRequest=new ModelTypeRequest();
            modelTypeRequest.setType("CONTRACT");
            List<ListCurrentUserModelResponse> list = bizFlowModelService.currentUserModelList(orgId, memberId, modelTypeRequest);
            System.out.println("查询当前用户可以发起模板列表 单测成功");
            list.forEach(e->{
                System.out.println(e.toString());
            });
        } catch (Exception e) {
            System.out.println("查询当前用户可以发起模板列表 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 删除审批模板
     */
    @Test
    public void deleteModelTest(){
        try {
            crateTest();
            bizFlowModelService.deleteFlowModel(orgId, modelId);
            System.out.println("删除模板,模板id="+ modelId + "单测成功");
        } catch (Exception e) {
            System.out.println("删除模板 单测失败");
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        CreateFlowModelRequest createFlowModelRequest =new CreateFlowModelRequest();
        createFlowModelRequest.setName("ziye-合同审批新增");
        createFlowModelRequest.setType("CONTRACT");
        //组装节点
        List<FlowNode> flowNodes =new ArrayList<>();
        FlowNode node1 =new FlowNode();
        node1.setNodeId("flow_1");
        node1.setNodeName("开始");
        node1.setNodeType("START");
        node1.setNodeUsers(Sets.newHashSet(new FlowUser("388d1adca73c468b8402f05573260c8f","",2),new FlowUser("73b3e22d7c624120af28a17b6cf9e617","",2)));

        FlowNode node2 =new FlowNode();
        node2.setApprovalType("SIGNAL");
        node2.setNodeId("flow_2");
        node2.setNodeName("审批");
        node2.setNodeType("USERTASK");
        node2.setNodeUsers(Sets.newHashSet(new FlowUser("388d1adca73c468b8402f05573260c8f","",2)));

        FlowNode node3 =new FlowNode();
        node3.setNodeId("flow_3");
        node3.setNodeName("结束");
        node3.setNodeType("END");

        flowNodes.add(node1);
        flowNodes.add(node2);
        flowNodes.add(node3);
        createFlowModelRequest.setFlowNodes(flowNodes);

        //组装flowSeqs
        List<FlowSeq> flowSeqs=new ArrayList<FlowSeq>();
        FlowSeq seq1=new FlowSeq();
        seq1.setSeqId("seq_1");
        seq1.setSeqName("连接1");
        seq1.setSourceNodeId("flow_1");
        seq1.setTargetNodeId("flow_2");

        FlowSeq seq2=new FlowSeq();
        seq2.setSeqId("seq_2");
        seq2.setSeqName("连接2");
        seq2.setSourceNodeId("flow_2");
        seq2.setTargetNodeId("flow_3");

        flowSeqs.add(seq1);
        flowSeqs.add(seq2);
        createFlowModelRequest.setFlowSeqs(flowSeqs);
        System.out.println(JSON.toJSONString(createFlowModelRequest));
    }
}
