package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestEmailBean;
import org.junit.Assert;

public enum EmailTestcaseCheckForUD223 implements SimpleTestcaseChecker {
    T_1("12345@123456", "123**@123456"),

    T_2("1234@12345", "123*@12345"),

    T_3("1@1234", "*@1234"),

    T_4("@123456", "@123456"),

    T_5("123456@", "123***@"),

    T_6("@", "@"),

    T_7("134@1234", "1**@1234"),

    T_8("14@1234", "1*@1234");

    private String input;

    private String anwser;

    EmailTestcaseCheckForUD223(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestEmailBean t = new TestEmailBean();
        t.setEmailud223(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getEmailud223());
    }
}
