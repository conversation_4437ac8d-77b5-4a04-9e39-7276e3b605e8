/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：SealRestTest.java <br/>
 * 包：com.timevale.unittest.footstone <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1722:38]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.enums.VipFunctionSealCreateCountEnum;
import com.timevale.footstone.user.service.inner.impl.biz.BizSealServiceImpl;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.seal.model.PreviewSealTemplatePersonalRequest;
import com.timevale.footstone.user.service.model.seal.request.*;
import com.timevale.footstone.user.service.model.seal.response.*;
import com.timevale.footstone.user.service.rest.SealRest;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.sealmanager.common.service.enums.*;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 类名：SealRestTest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1722:38]创建类 by flh
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SealRestTest {

    @Resource
    private BizSealServiceImpl bizSealService;

    @Autowired
    private SealRest rest;

    @Autowired private HttpServletRequest servletRequest;

    private String orgId = "159b9a7a54424b498784069a7a26ea5a";

    private String accountId = "fb177ec39c334fd7b615a5bf97428d47";

    private String sealId = "d12e5c6b-5bc4-4ecf-8a35-31dbadb313ba";

    public static final String LEGAL_AUTH_ORG_ID = "1ad1ec5207f04c04afd7fc31d47f4542";
    public static final String LEGAL_AUTH_AUTH_ID = "0ba00c86d5f54e3693f7247f0211aa93";

    @Test
    public void testList(){
        try {
            SealListResponse response = bizSealService.list(orgId,0,20,false,true,1,accountId,false);
        }catch (Exception e){

        }
        //Assert.assertNotNull(response.getSeals());
        //Assert.assertNotEquals(response.getSeals().size(),0);
       // Assert.assertEquals(response.getSeals().get(0).isSealUserFlag(),true);
    }

    @Test
    public void testListStatusFlagTrue(){
        try {
            SealListResponse response = bizSealService.list(orgId,0,20,true,true,1,accountId,false);
            Assert.assertNotNull(response.getSeals());
            Assert.assertNotEquals(response.getSeals().size(),0);
            Assert.assertEquals(response.getSeals().get(0).isSealUserFlag(),true);
        }catch (Exception e){

        }

    }

    @Test
    public void testUidList(){
        try {
            SealListResponse response = bizSealService.uidSealList("224c10d6c43f47e489b97135f201224a",0,20,false,true,1);
            Assert.assertNotNull(response.getSeals());
            Assert.assertNotEquals(response.getSeals().size(),0);
        }catch (Exception e){

        }

    }

    @Test
    public void platformSealList(){
        try {
            RequestContext.put("X-Tsign-Open-App-Id","**********");
            SealListResponse response = bizSealService.platformList(0,20,false,true,1);
            Assert.assertNotNull(response.getSeals());
            Assert.assertNotEquals(response.getSeals().size(),0);
        }catch (Exception e){

        }

    }

    @Test
    public void testSealId(){
        try {
            SealCommonInfoResponse response = bizSealService.queryBySealIds(orgId, Arrays.asList(sealId),true,accountId);
            Assert.assertNotNull(response.getSeals());
            Assert.assertNotEquals(response.getSeals().size(),0);
            Assert.assertEquals(response.getSeals().get(0).isSealUserFlag(),true);
        }catch (Exception e){

        }

    }

    @Test
    public void testListWithRole(){
        try {
            SealListWithRoleResponse response = bizSealService.listWithRole(orgId,0,3,"SEAL","USE.LIMIT",true,true,1,true);
            Assert.assertNotNull(response.getSeals());
        }catch (Exception e){

        }

    }

    //@Test
    public void testGetOfficialTemplateSealDetail(){
        try {
            SealOfficialDetailResponse response = bizSealService.getOfficialTemplateSealDetail(orgId,sealId,true);
            Assert.assertNotNull(response.getUrl());
        }catch (Exception e){

        }

    }

//    @Test
//    public void testOffLineLegalAuthApply(){
//        LegalAuthContentFile content = new LegalAuthContentFile();
//        content.setAuthIDReverseFilekey("$e3c8bc96-3250-4347-8bb6-43da9326289c$2626875897");
//        content.setAuthIDFrontFilekey("$6c483669-65f5-4d4a-8a14-045421e35e82$1375275728");
//        content.setAuthDocFilekey("$68d88f56-ca4b-4ba5-ad42-47b8b9bf7c19$373643099");
//        String authId = bizSealService.offLineLegalAuthApply(
//                LEGAL_AUTH_ORG_ID, LEGAL_AUTH_AUTH_ID,
//                LegalAuthTypeEnum.AUTH_TYPE_FILE, content);
//        Assert.assertTrue(StringUtils.isNotEmpty(authId));
//    }

    @Test
    public void testQueryLegalAuthInfo(){
        try {
            LegalSealAuthInfoResponse response = bizSealService.queryLegalAuthInfo(LEGAL_AUTH_ORG_ID, LEGAL_AUTH_AUTH_ID);
            Assert.assertTrue(response.getLegalAuthInfo() != null);
        }catch (Exception e){

        }

    }

    @Test
    public void testAddLegalRepImage(){
        try {
            String sealBase64 = "iVBORw0KGgoAAAANSUhEUgAAAX4AAAF+CAMAAACyBIHOAAAADFBMVEX/////AAAAAP8AAABvxgj3AAAAAXRSTlMAQObYZgAADBNJREFUeNrtne2a46gOhFNu3/8t19mdMzvT6WBbEhLgpN4/u8+0Y6AQQnwYHg8hhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgjxEUClnZjK/5/k9VNMzRznqc/6PML/JK8fYqpt0PQcK6ySWVnMk/8sMVMdBZJkjljpKfcWOCS/Rf8MMawvS03U98bOpLdI5ib4Y0xLDZVZ3MvEz6wjzjOMn28EDyXHa+pguvzLtpAC18PXv6CdCtvvwXmetjuoz57n8A/1jpCHz2GG/GsNn5BU7/ihP67Vz+p6eV/1nfrjQv3o61KsnzdUP/1l8Bg/3sb5OP04IvVwJCpcFmmLXffbuZLgJAuY2nT+fZ3dFx0mfi/5ee7KkaH/0AHezZwPF2uKvdzL+v/af1nNPU3fmSbZ7U/Okz9z6p6nkuWbdNttPSfK3+MLZxa2gQ21q7X+F/OQQxwQv9cn7LaAcfLbo0CE8taKeQb5cz7/F4dP8mc1YKT122rgb4SGLlkwWv1Lo+br/7oy2eP7eeJSkBd+p/060rn8CXWbToa9mdwKXU4gNr9sPqUZPVu4xaX6DGRyi7mcqii8PTuMSNfm7y28pWL/iACWB9h49G+0S4P3tK1II1jmVqL+YPRA/VbWD7ZW+NfdTb6/5c1+hbhu8+fFX/Gwm4Oxt2ey7aduq/B3vX/GnahYWUVPk0VHT+hUnzlD8r1HorJAhCeG5i6iJ5e8+hP623hv18u4FNfv/kVO2Tw2CZftp5V8ewRae6X++fVp7yrOtjDwZwh2uODumRX5sisPixVEZuK9Ph5HcRWjKZ7bPtqe5xMnnGuGG+dvZV1TD8rP99rsz0BbTDGCLdb+py07IdWNBV/BjpwkTDpMA4gpiX6xUOKMtpr2WtM4uqarkag+s/SPWz9HOyEE11MTFntebD9L/z1ub4Pdf3iWNWGQ/rrl+Xfs0Tvuv4vvR7/6nc7rR/LsCgVs1r9MeGkb7OPq32LG2t7uf7QO9obDLiyRNttjn56Z382UMpdQn1xJ/djqosP6FxvZ8rDbz1nt8qr/bXko3AD2XPVRqz6n2f5Byr1rT5vX7law/fEzHLzOFqo+LGW6RfEe6lsS51NUxjz5i2Y1EQjWFutzny2JiJvWflmxrCgVmFP+uab/25L4cyWKKfKX7KMyD4Cmxl2O41v+LQkRdK97LHMJx2VwXfF9A55f+v/MsPGMmz2WueipSTDqjxuI/zyV19oidv0i/weXTBPHnCle2FTqsOtZs2M7axywA3dRQ/In6W/OU8/GFnZZ/vE8K5qpwJmH9abcThWuXuJ/+brF+F0HeOwazgzgK6YLTm2CHYerYWI81FpUQCDbMDe/PdVvINX0afWlDT+PeNLsbm80d9/7sn6HQ8e/SE7WGKAUyo+uzbCcUfHMrHDLD2vWeqNZNi6opJ9P5d8bm8RqkQ8NS+qwj+snnsE3Xn70ep+HZ54PttMWUsvHN7d+X6xxrEeFNZ8eTBiLtfdKCYN7MJj3aP6M+dVUlS+1iq53mB+lRf28kdrZ113BcxO23MY5cozaPYIueisw1/pHtQAadQrVQiu4zS/UVqsKJto0enJxdiQBLjwip8iPweZPe3aC89RHk5d5+76rA8/CI2BAu1kGs3H0M1gOfhhr/Rhm9mOiqmaFZn/kuFX7hsLgZ0RchVCh5zkfVrsfDAq3zr9fAVMMYatT5U28kCei4DT5Z/iIWu9z7udz0t6SlWBhP7XCQDo56thGZAAy/xnOh3UycYZ1XyTMWfIfGX++/ljQFWGE9aPLJJFt/Bygv896ht1aZ9c2+9wrjBzWDeiAtsT0OUD/SUMKFOVp68+JNexCsgqTzZ8ZFrGV2v7zP6ftzuGz/hglcX5UsZXnNHH1pezo6ILi2+4q3JKM3xgO51oqp6lLk/FfF/bLq3PEIKMLT7hMq9rxw/jEwcoP+t//8iT97sA+a/i9NJYvTYpXOC/Ld6r+w7Ui6pff7IxhdBsnH+ccpXV9fSviFXR5fln7gchd4275PRN91k9ujsU8rGqPAzoX8uBzHT7Cxv9IuLHgQH7nNKt9PQYe4/cUwvIpK/t9E/yp+uV3R4Ge9bCX8RXcbcYbKE0ZPUQjn8jRdoiJQVNdo1P9OcPn4FJ7aADE0CkZNvWvP6Zacuk5ZP39pzr6YiVHcr1npc45nNSlXNfY3/oZAl6sml0FW3Pfhdf5sLc49BloOL3nfTirbnpBwGzHFAaPw8vBPhEANzASIYQQQgghhBBCCCHEJ4N3Kwxv9frt3Sobtd/xYbQgyKvy6J2X7sLwLu827iFlamKsb12FJxlwvPw5idoPqQ3W+XMCeYuiZRt5MdJm7TYUdFNPCaT1lK4j4fM7w9YB4Sf7A5lg/cE2991BZ7Xa6x3U1bFIc3ct/W7D0311be5hWqttWH5m/IlHRP9jjU9V810IFFDv+Tf+N/w4pLZ+q5ZT/qeC0SuwT4+A/mhYieMVz3aNuPbmYN027Grn6ezY1MTRD4a+As0C/Dgd8mLPE8wCuA/FpPlX7LVn/0gHjT7K3d5ouEbj1P87iuneYG4Optk9S0J408xvbTR3FK+/R96tdQwf6ZcxaRHVv/Oeqd4PMq6zvXdlibZ2F60B4jHF/tOinMts78Na9JQ9ygwNnxNHtUySv/e+OE5wHnXxuim+LrixNHhh5rTt+TO/CzCkvQ1Rv7sEnSpO+TYh996ufwYe5OI2eDLXtOaq6naLhtyXsvF0kRnssbJkhJWjqnzO4C3f+qf61nAKXXeILGf9R8VDrXNCdAYDCYPnOfLDdHrOt6FppRNyOfDb7F7aipr8opMAt/P9rQoYeVFi6stv5Xz+NHjjUbQLXJW28FGT/q73P7e+Rpf1jucKbKYS41PlWSTux/fAQzqPkl9KLzLqnRKzRUfTWCcrffI3th2NaxELzVQWZWV7rGv+C81UVmXlcs6HI6/CRCvcwvweqGzSdA9kojC0b7fx2eMOlOXEP+HMqeUf2C02U8do6+fIDvdoawOuDkLG2E9+Jlj/2D7waVMrr4+hxhoNscb3c5TvYaujNZ3BjjsOEXeP9EOs/vifcH56f57+40q8ctzf7mRxlkO+n/Uvw+WC4/38z7Zsu/wItjtllua/32Wt3ex8Vltzx1s0x/3xTub/7s6HHyTNSvLrCHd1vZ8uvxzMHPlv43v4ztYvJP/nyY/btWx8tPUrTh0lv5SW739r+e/k+inrF5ny33J3/z26pP1m5jJ1PTd/x9Pea0cYXv45K7qYYP1YzvSvrY8o1Z7D5F/NffraPri69j7f79l9VneA/sTL1AuSPv2u98L++ThY8WZBXdjVz/M+xdpfWv9ZBdD4B86yfTBN+9BB0hnOp21Kra2WbP6Bj1nqt97AKu2jDe5y2MWeSucU9ftPbYWzDGF3tz/SWmjdN1hB28+IfWh+CjXyO3qyogERPE8yMx36KsrfXewpwvCB4gbw8JyoPOv0VX+6W7I2mKR+c98/XC0o4a4Ken+9ZSlTrT9N2v/uJzOcPrtyyQHW3/R1NR950mL3L5IhnETngbHWn2950vBR3ACuPD47fSGzAmb7z/cs43+k3zbhG/KnHPWWMK/k++2W6RfK7J9ue4t1RWk9xwD5z0yl+LYh/BiX8ioUw8A2lDvsgmeSIe+2j0PX440P3TnhUvJfjrhq9Gfr8lfPuJShk1G4mvV71D94LuPiEP/NlWsdg1kx6dCYYH42/2+e1z0iv4pDaH0B1t17tXUZPx3Vg2Ddsh3bWGLrOSMRD1/2LhDRoCde+CP/w9DvSwZ7E5yPc3TCjpW7sxNOLD9efLPbV6CeL9RHmblEdplhaev3+370jsw77tAM/JzvZf0G8ZtGiml6oKApDLN++t0+u8LFdFa2/z1U6wwXeIYWC3fAcGnes8MaUy0x+fyZrB2PX/5kGc4yVze04e/Sd4mjImDJn6q/jhcRQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCHE2/I/Bslyrp99eOMAAAAASUVORK5CYII=";

            SealLegalRepImageAddRequest request = new SealLegalRepImageAddRequest();
            request.setAlias("test legal rep");
            request.setData(sealBase64);
            request.setType("BASE64");
            request.setHeight(159);
            request.setWidth(159);

            SealAddResponse response = rest.createOrgLegalRepImage("76930d301c69456ab4414ee7cda7f068", request).getData();
            if(response != null){
                System.out.println(response.getSealId());
            }
        }catch (Exception e){

        }

    }

    @Test
    public void testCustomImage(){
        try {
            String fileKey = "$804538cd00624343ad4ca8ade61f6438$2670722040$H";
            SealCustomImageAddRequest request = new SealCustomImageAddRequest();
            request.setData(fileKey);
            request.setType(ImageType.FILEKEY.name());
            request.setColor(ImageColor.RED.name());
            request.setAlpha(61);
            request.setTransparentFlag(true);
            request.setCvalue(100);
            request.setAlias(UUID.randomUUID().toString());
            request.setAuditFlag(false);
            request.setDownloadFlag(true);
            request.setHandleFlag(false);
            request.setHeight(132);
            request.setWidth(132);
            WrapperResponse<SealAddResponse> orgCustomImage = rest.createOrgCustomImage("76930d301c69456ab4414ee7cda7f068", request);
            String sealId = orgCustomImage.getData().getSealId();

            SealCustomImageUpdateRequest update = new SealCustomImageUpdateRequest();
            update.setAlpha(50);
            update.setData(fileKey);
            update.setType(ImageType.FILEKEY.name());
            update.setColor(ImageColor.RED.name());
            update.setAlpha(50);
            update.setTransparentFlag(true);
            update.setCvalue(80);
            update.setAlias(UUID.randomUUID().toString());
            update.setAuditFlag(false);
            update.setHandleFlag(false);
            update.setHeight(132);
            update.setWidth(132);
            rest.updateOrgCustomImage("76930d301c69456ab4414ee7cda7f068",sealId,update);
        }catch (Exception e){

        }

    }
    @Test
    public void testLegalAuthCancel(){
        try {
            rest.revoke(LEGAL_AUTH_ORG_ID);

        }catch (Exception e){

        }
    }

    @Test
    public void testLegalAuthRecover(){
        try {
            rest.recover(LEGAL_AUTH_ORG_ID);

        } catch (Exception e) {
            
        }
    }


    @Test
    public void testCheckIfTheSameProject(){
        try{
            bizSealService.checkIfIsSameProjectWithConfig("**********", accountId);
            bizSealService.checkIfIsSameProjectWithConfig("*********", accountId);
        } catch (Exception e){

        }
    }

    @Test
    public void testCheckIfHaveTheOperatingRight(){
        try {
            bizSealService.checkIfHaveTheOperatingRight(accountId, sealId);
        }catch (Exception e){

        }
    }




    @Test
    public void testCreateOfficialSeal(){
        try {
            SealTemplateOfficialAddRequest request = new SealTemplateOfficialAddRequest();
            request.setColor("RED");
            request.setAlias("**********");
            request.setHeight(159);
            request.setWidth(159);
            request.setCentral(TemplateCentral.STAR.name());
            request.setStyle(TemplateStyle.OLD.name());
            request.setType(TemplateOfficialType.TEMPLATE_ROUND.name());
            SealAddResponse response = bizSealService.createByOfficialTemplate(orgId,"", request);
            System.out.println(response.getSealId());
        }catch (Exception e){

        }

    }

    @Test
    public void testCreateOfficialCancellationSeal(){
        try {
            SealTemplateOfficialAddRequest request = new SealTemplateOfficialAddRequest();
            request.setColor("RED");
            request.setAlias("**********rewrr");
            request.setHeight(159);
            request.setWidth(159);
            request.setCentral(TemplateCentral.STAR.name());
            request.setStyle(TemplateStyle.OLD.name());
            request.setSealBizType(SealBizType.CANCELLATION.name());
            request.setType("CANCELLATION");
            SealAddResponse response = bizSealService.createByOfficialTemplate(orgId, "",request);
            System.out.println(response.getSealId());
        }catch (Exception e){

        }

    }


    @Test
    public void testCreatePersonalSeal(){
        try {
            SealTemplatePersonalAddRequest request = new SealTemplatePersonalAddRequest();
            request.setColor("RED");
            request.setAlias("**********4");
            request.setHeight(159);
            request.setWidth(159);
            request.setStyle(TemplateStyle.OLD.name());
            request.setType(TemplatePersonalType.BORDERLESS.name());
            SealAddResponse response = bizSealService.createByPersonalTemplate(accountId, request);
            System.out.println(response.getSealId());
        }catch (Exception e){

        }

    }

    //@Test
    public void testPreviewPersonalSeal(){
        try {
            PreviewSealTemplatePersonalRequest request = new PreviewSealTemplatePersonalRequest();
            request.setColor("RED");
            request.setAlias("**********4");
            request.setHeight(159);
            request.setWidth(159);
            request.setStyle(TemplateStyle.OLD.name());
            request.setType(TemplatePersonalType.BORDERLESS.name());
            request.setSealName("测试");
            SealAddResponse response = bizSealService.previewPersonalTemplate(accountId,request);
            Assert.assertNotNull(response.getFileKey());
        }catch (Exception e){

        }

    }

    @Test
    public void testCreatePersonalCancellationSeal(){
        try {
            SealTemplatePersonalAddRequest request = new SealTemplatePersonalAddRequest();
            request.setColor("RED");
            request.setAlias("**********4erwefsd");
            request.setHeight(159);
            request.setWidth(159);
            request.setStyle(TemplateStyle.OLD.name());
            request.setSealBizType(SealBizType.CANCELLATION.name());
            request.setType("CANCELLATION");
            SealAddResponse response = bizSealService.createByPersonalTemplate(accountId, request);
            System.out.println(response.getSealId());
        }catch (Exception e){

        }

    }

    @Test
    public void testIllegalStyle(){
        try {
            SealTemplatePersonalAddRequest request = new SealTemplatePersonalAddRequest();
            request.setColor("RED");
            request.setAlias("**********4erwefsd");
            request.setHeight(159);
            request.setWidth(159);
            request.setStyle("null");
            request.setSealBizType(SealBizType.CANCELLATION.name());
            request.setType("CANCELLATION");
            try{
                SealAddResponse response = bizSealService.createByPersonalTemplate(accountId, request);
                System.out.println(response.getSealId());
            } catch (Exception e){

            }



            request = new SealTemplatePersonalAddRequest();
            request.setColor("RED");
            request.setAlias("**********4");
            request.setHeight(159);
            request.setWidth(159);
            request.setStyle("null");
            request.setType(TemplatePersonalType.BORDERLESS.name());
            try{
                SealAddResponse response = bizSealService.createByPersonalTemplate(accountId, request);
                System.out.println(response.getSealId());
            }catch (Exception e){

            }



            SealTemplateOfficialAddRequest officialRequest = new SealTemplateOfficialAddRequest();
            officialRequest.setColor("RED");
            officialRequest.setAlias("**********");
            officialRequest.setHeight(159);
            officialRequest.setWidth(159);
            officialRequest.setCentral(TemplateCentral.STAR.name());
            officialRequest.setStyle(TemplateStyle.OLD.name());
            officialRequest.setType(TemplateOfficialType.TEMPLATE_ROUND.name());
            try{
                SealAddResponse officialResponse = bizSealService.createByOfficialTemplate(orgId, "",officialRequest);
                System.out.println(officialResponse.getSealId());
            }catch (Exception e){

            }
        }catch (Exception e){

        }

    }

    @Test
    public void testIntervalChangeListener(){
        try {
            Map<String, ConfigChange> changeMap = new HashMap<>();
            changeMap.put("seal.checkAppId", new ConfigChange("", "", "", "",  null));
            ConfigChangeEvent event = new ConfigChangeEvent("", changeMap);
            bizSealService.intervalChangeListener(event);
        }catch (Exception e){

        }

    }


    @Test
    public void testUpdatePersonalSeal(){
        try {
            SealTemplatePersonalUpdateRequest request = new SealTemplatePersonalUpdateRequest();
            request.setColor("RED");
            request.setAlias("**********4erwefsd");
            request.setHeight(159);
            request.setWidth(159);
            request.setStyle("null");
            request.setSealBizType(SealBizType.CANCELLATION.name());
            request.setType("CANCELLATION");
            try{
                bizSealService.updatePersonalTemplateSeal(accountId, "890aaad9-1394-4349-9657-a6383ddc3106", request);
            } catch (Exception e){

            }
        }catch (Exception e){

        }

    }

    @Test
    public void testUpdateOfficalSeal(){
        try {
            SealTemplateOfficialUpdateRequest request = new SealTemplateOfficialUpdateRequest();
            request.setColor("RED");
            request.setAlias("**********4erwefsd");
            request.setHeight(159);
            request.setWidth(159);
            request.setCentral("STAR");
            request.setStyle("NONE");
            request.setQtext("测试修改天一");
            request.setType("TEMPLATE_ROUND");
            request.setSealBizType(SealBizType.PUBLIC.name());
            try{
                bizSealService.updateOfficialTemplateSeal("09e02348f1ef47c399e7933beb1479ab", "6ccb46a8-1a64-4c26-96e2-0052d4da883e", request);
            } catch (Exception e){

            }
        }catch (Exception e){

        }

    }

    @Test
    public void testCanReceive(){
        try {
            rest.checkCanReceive(orgId, accountId);

        }catch (Exception e){

        }
    }

    @Test
    public void testReceive(){
        try {
            SealReceiveRequest request = new SealReceiveRequest();
            request.setAccountId(accountId);
            rest.sealReceive(orgId, request);
        }catch (Exception e){

        }

    }

    @Test
    public void testSaasVipCheckSealCreate(){
        try{
            // saas appid
            String appId = "**********";
            String accountId = "ba6f4052c7524560a260c944863e920c";
            bizSealService.checkSealCreateAmount(accountId, appId, VipFunctionSealCreateCountEnum.MAX_ORGAN_SEAL_COUNT);
            // 未限制企业
            accountId = "8fbe5926547e40149978fb8c5a448394";
            bizSealService.checkSealCreateAmount(accountId, appId, VipFunctionSealCreateCountEnum.MAX_ORGAN_SEAL_COUNT);
            // 非saas appid
            appId = "**********";
            bizSealService.checkSealCreateAmount(accountId, appId, VipFunctionSealCreateCountEnum.MAX_ORGAN_SEAL_COUNT);
        }catch(Exception e){

        }

    }

    @Test
    public void testHasScopeSeal() {
        try {
            QueryHasScopeSealResponse response =
                    bizSealService.hasScopeSealInOrg(
                            "da2b9e93961d4303be15329d3924ec67", "4501308d254a434b90fd9d96f63dc161");
            Assert.assertTrue(response.getHasScopeSeal());
        }catch (Exception e){

        }

    }

//    @Test
//    public void testCreateTemplatePersonal(){
//        SealTemplatePersonalAddRequest personalAddRequest = new SealTemplatePersonalAddRequest();
//        personalAddRequest.setColor("RED");
//        personalAddRequest.setAlias("测试创建章");
//        personalAddRequest.setType("SQUARE");
//        WrapperResponse<SealAddResponse> sealResponse = rest.createTemplatePersonal(accountId, personalAddRequest);
//        Assert.assertNotNull(sealResponse.getData());
//        String sealId = sealResponse.getData().getSealId();
//        rest.delete(accountId, sealId);
//    }

//    @Test
//    public void testCreateImage(){
//        String imageData = "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";
//        SealImageAddRequest imageAddRequest = new SealImageAddRequest();
//        imageAddRequest.setData(imageData);
//
//        imageAddRequest.setAlias("测试创建章");
//        imageAddRequest.setType("BASE64");
//        imageAddRequest.setWidth(40);
//        imageAddRequest.setHeight(40);
//        WrapperResponse<SealAddResponse> sealResponse = rest.createImage(accountId, imageAddRequest);
//        Assert.assertNotNull(sealResponse.getData());
//        String sealId = sealResponse.getData().getSealId();
//        rest.delete(accountId, sealId);
//    }

//    @Test
//    public void testCreateCustomImage(){
//        String imageData = "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";
//        SealCustomImageAddRequest imageAddRequest = new SealCustomImageAddRequest();
//        imageAddRequest.setData(imageData);
//        imageAddRequest.setAlias("测试创建章");
//        imageAddRequest.setType("BASE64");
//        imageAddRequest.setWidth(40);
//        imageAddRequest.setHeight(40);
//        WrapperResponse<SealAddResponse> sealResponse = rest.createCustomImage(accountId, imageAddRequest);
//        Assert.assertNotNull(sealResponse.getData());
//        String sealId = sealResponse.getData().getSealId();
//        rest.delete(accountId, sealId);
//    }

    //@Test
    public void testCreateTemplateOfficial(){

        SealTemplateOfficialAddRequest officialAddRequest = new SealTemplateOfficialAddRequest();
        officialAddRequest.setColor("RED");
        officialAddRequest.setAlias("测试创建章");
        officialAddRequest.setType("TEMPLATE_ROUND");
        officialAddRequest.setCentral("STAR");
        WrapperResponse<SealAddResponse> sealResponse = rest.createTemplateOfficial(orgId, officialAddRequest);
        Assert.assertNotNull(sealResponse.getData());
        String sealId = sealResponse.getData().getSealId();
        rest.deleteOrgSeal(orgId, sealId);
    }

    @Test
    public void testCreateOrgImage(){
        try {
            String imageData = "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";
            SealImageAddRequest imageAddRequest = new SealImageAddRequest();
            imageAddRequest.setData(imageData);

            imageAddRequest.setAlias("测试创建章");
            imageAddRequest.setType("BASE64");
            imageAddRequest.setSealBizType("PUBLIC");
            imageAddRequest.setWidth(40);
            imageAddRequest.setHeight(40);

            WrapperResponse<SealAddResponse> sealResponse = rest.createOrgImage(orgId, imageAddRequest);
            Assert.assertNotNull(sealResponse.getData());
            String sealId = sealResponse.getData().getSealId();
            rest.deleteOrgSeal(orgId, sealId);
        }catch (Exception e){

        }

    }

    @Test
    public void testquerySealGrantScope(){
        try {
            WrapperResponse<SealGrantScopeResponse> sealResponse = rest.querySealGrantScope(orgId);
            Assert.assertNotNull(sealResponse.getData());
        }catch (Exception e){

        }

    }

    //@Test
    public void testAddWhiteList(){
        try {
            SealWhiteListAddRequest request = new SealWhiteListAddRequest();
            request.setOrgName("杭州大团子有限公司");
            request.setOrgGid("f4951bbb2a3c4131b76265ddfce0177e");
            request.setCreator("荆轲");
            request.setReason(SealWhiteTypeEnum.PARENT_SUB_ORGANIZE.getType());
            Long id = bizSealService.addWhiteList(request);
            if (id != null) {
                bizSealService.deleteWhiteList(id);
            }
        }catch (Exception e){

        }

    }

    @Test
    public void testQueryWhiteList(){
        try {
            WrapperResponse<SealGrantScopeResponse> sealResponse = rest.queryWhiteList(1,10,null);
            Assert.assertNotNull(sealResponse.getData());
        }catch (Exception e){

        }

    }

    @Test
    public void testGetSealCreateConfig(){
        try {
            WrapperResponse sealResponse = rest.getSealCreateConfig();
            Assert.assertNotNull(sealResponse.getData());
        }catch (Exception e){

        }

    }

    @Test
    public void testQueryWhiteListType(){
        try {
            WrapperResponse<SealWhiteListTypeResponse> whiteListType = rest.queryWhiteListType();
            Assert.assertNotNull(whiteListType.getData());
        }catch (Exception e){

        }

    }

    @Test
    public void testEnterpriseFourFactors(){
        try {
            RequestContext.put("X-Tsign-Open-App-Id", "**********");
            String orgId = "2f079ee35fb54d4298cb5abfa762d9ba";
            EnterpriseFourVerifyRequest enterpriseFourVerifyRequest = new EnterpriseFourVerifyRequest();
            enterpriseFourVerifyRequest.setLegalCertType("CRED_PSN_CH_IDCARD");
            enterpriseFourVerifyRequest.setLegalRepCertNo("210403198811273910");
            enterpriseFourVerifyRequest.setLegalRepName("法相");
            Assert.assertNotNull(bizSealService.enterpriseFourFactors(orgId, "63b5bebe7365405d8c3be37e123800be", enterpriseFourVerifyRequest));
        }catch (Exception e){

        }

    }
}