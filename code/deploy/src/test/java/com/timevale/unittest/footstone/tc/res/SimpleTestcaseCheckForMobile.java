package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestBean;
import org.junit.Assert;

public enum SimpleTestcaseCheckForMobile implements SimpleTestcaseChecker {
    T_1("12345678911", "123****8911"),

    T_2("1234567891", "123***7891");

    private String input;

    private String anwser;

    SimpleTestcaseCheckForMobile(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestBean t = new TestBean();
        t.setMobile(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getMobile());
    }
}
