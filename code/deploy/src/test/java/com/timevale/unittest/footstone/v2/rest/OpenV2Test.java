package com.timevale.unittest.footstone.v2.rest;

import com.timevale.TomcatHttpServletRequest;
import com.timevale.account.service.exception.Errors;
import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.*;
import com.timevale.footstone.user.service.model.account.request.v2.*;
import com.timevale.footstone.user.service.model.account.response.*;
import com.timevale.footstone.user.service.model.account.response.v2.OrgAuthInfoResponse;
import com.timevale.footstone.user.service.model.account.response.v2.OrgAuthRealNameResponse;
import com.timevale.footstone.user.service.model.account.response.v2.PsnAuthInfoResponse;
import com.timevale.footstone.user.service.model.account.response.v2.PsnRealNameStatusResponse;
import com.timevale.footstone.user.service.rest.OpenRest;
import com.timevale.footstone.user.service.rest.v2.OpenRestV2;
import com.timevale.footstone.user.service.utils.UUIDUtils;
import com.timevale.mandarin.base.enums.BaseResultCodeEnum;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.assist.AccountAssist;
import com.timevale.unittest.footstone.assist.OpenApiAssist;
import com.timevale.unittest.footstone.configuration.Application;
import com.timevale.unittest.footstone.tc.UTCGenerator;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Maps;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ Id: OpenV2Test.java, v0.1 2021年03月24日 14:45 WangYuWu $
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OpenV2Test {

    @Autowired
    private AccountAssist assist;

    @Autowired
    private OpenRestV2 openRestV2;

    @Autowired
    private OpenRest openRest;

    @Autowired
    private OpenApiAssist openApiAssist;

    @Before
    public void setUp() throws Exception {
        HttpServletRequest request = new TomcatHttpServletRequest();
        request.setAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE, Maps.newHashMap(
                "orgId", "orgId"));

        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));

    }

    @Test
    public void testThirdIdForCreateV1Person() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        PersonAddByIdV1Request request = new PersonAddByIdV1Request();
        request.setThirdPartyUserId("user-test-"+ UUIDUtils.getUUID());
        request.setName("折袖");
        WrapperResponse<AccountBaseResponse>  wrapperResponse = openRest.createUserByUserId(request);
        Assert.assertNotNull(wrapperResponse.getData().getAccountId());


//        try{
//            request = new PersonAddByIdRequest();
//            request.setThirdPartyUserId("user-test-"+UUIDUtils.getUUID());
//            request.setName("许");
//            wrapperResponse = openRest.createUserByUserId(request);
//            Assert.assertTrue(Boolean.FALSE);
//        }catch (FootstoneUserErrors.InvalidParameterRange e){
//            Assert.assertEquals(e.getCode(),BaseResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
//            Assert.assertEquals(e.getMessage(),"参数范围异常: 姓名最小字符数不少于2");
//        }

        try{
            request = new PersonAddByIdV1Request();
            request.setName("折袖");
            wrapperResponse = openRest.createUserByUserId(request);
            Assert.assertTrue(Boolean.FALSE);
        }catch (FootstoneUserErrors.MissingArgumentsWith e){
            Assert.assertEquals(e.getCode(), BaseResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
            Assert.assertTrue(e.getMessage().contains("缺少参数: thirdPartyUserId"));
        }

        try{
            request = new PersonAddByIdV1Request();
            request.setThirdPartyUserId("user-test-"+UUIDUtils.getUUID());
            request.setIdNumber("110101199001015537");
            wrapperResponse = openRest.createUserByUserId(request);
            Assert.assertNotNull(wrapperResponse.getData().getAccountId());
        }catch (Exception e){
            Assert.assertTrue(Boolean.FALSE);
        }

    }

    @Test
    public void testThirdIdForCreateV2Person() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        PersonAddByIdV2Request request = new PersonAddByIdV2Request();
        request.setThirdPartyUserId("user-test-"+UUIDUtils.getUUID());
        request.setName("折袖");
        WrapperResponse<AccountBaseResponse>  wrapperResponse = openRestV2.createUserByUserIdV2(request);
        Assert.assertNotNull(wrapperResponse.getData().getAccountId());


        try{
            request = new PersonAddByIdV2Request();
            request.setName("折袖");
            wrapperResponse = openRestV2.createUserByUserIdV2(request);
            Assert.assertTrue(Boolean.FALSE);
        }catch (FootstoneUserErrors.MissingArgumentsWith e){
            Assert.assertEquals(e.getCode(),BaseResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
            Assert.assertTrue(e.getMessage().contains("缺少参数: thirdPartyUserId"));
        }

        try{
            request = new PersonAddByIdV2Request();
            request.setThirdPartyUserId("user-test-"+UUIDUtils.getUUID());
            request.setIdNumber("**********");
            wrapperResponse = openRestV2.createUserByUserIdV2(request);
            Assert.assertTrue(Boolean.FALSE);
        }catch (FootstoneUserErrors.MissingArgumentsWith e){
            Assert.assertEquals(e.getCode(),BaseResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
            Assert.assertTrue(e.getMessage().contains("缺少参数: name"));
        }

    }

    @Test
    public void testThirdIdForCreatePerson() {
        try {
            openRestV2.createUserByUserIdV2(buildPerson());
        } catch (Exception e) {

        }
    }

    @Test
    public void test2ThirdIdForCreatePerson() {
        try {
            openRestV2.createUserByUserIdV2(buildPerson());
        } catch (Exception e) {

        }
    }

    @Test
    public void testThirdIdForCreateOrgan() {
        OrgAddByUserIdRequest request = new OrgAddByUserIdRequest();
        request.setCreator(openApiAssist.createEmptyUser());
        request.setThirdPartyUserId(UTCGenerator.utcThirdAppUser());
        request.setName("xxx"+ UUID.randomUUID().toString().replace("-","").substring(0,15));
        WrapperResponse<OrgBaseResponse> resp;
        try {
            resp = openRestV2.createOrgByThirdPartyUserIdV2(request);
        } catch (Exception e) {

        }
    }

    @Test
    public void testForUpdatePerson() {
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        PersonRenewRequest renewRequest = new PersonRenewRequest();
        renewRequest.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        renewRequest.setIdNumber("330327199002043711");
        renewRequest.setName("陈体铭");
        WrapperResponse<PersonGetResponse> resp;
        try {
            resp = openRestV2.updateUserByAccountIdV2(openApiAssist.createEmptyUser(), renewRequest);
        } catch (Exception e) {

        }
    }

    @Test
    public void testForUpdateOrgan() {
        String idType = BuiltinCredentials.CRED_ORG_REGCODE;

        OrgRenewRequest request = new OrgRenewRequest();
        request.setIdType(idType);
        request.setIdNumber("***************");
        String name = "杭州易签宝数字科技有限公司";
        request.setName(name);
        request.setOrgLegalName("zzz");
        request.setOrgLegalIdNumber("******************");

        WrapperResponse<OrgGetResponse> resp;
        try {
            resp = openRestV2.updateOrgByAccountIdV2("19f552649f084a21b83e0c07bbbc2a5a", request);
        } catch (Exception e) {

        }
    }

    @Test
    public void testCreateDigitalGovAccount() {
        DigitalGovAccountRequest request = new DigitalGovAccountRequest();
        request.setCertNo(UTCGenerator.utcPsnIdcard());
        request.setCertType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setSource("JIN_HUA_SOCIAL");
        String mobile = UTCGenerator.utcMobile();
        request.setMobile(mobile);
        request.setName(UTCGenerator.utcName());


        WrapperResponse<SyncAccountResponse> resp;
        WrapperResponse<PsnAuthInfoResponse> psnAuthInfo;

        try {
            resp = openRestV2.syncAccount(request);
            PsnAuthInfoParam psnAuthInfoParam = new PsnAuthInfoParam();
            psnAuthInfoParam.setAccountId(resp.getData().getAccountId());
            psnAuthInfo = openRestV2.psnAuthInfo(psnAuthInfoParam);

        } catch (Exception e) {

        }
    }

    @Test
    public void psnRealNameStatusQuery(){
        PsnAuthBaseParam baseParam = new PsnAuthBaseParam();
        baseParam.setMobile("***********");
        WrapperResponse<PsnRealNameStatusResponse> psnRealNamestatus;
        try {
            psnRealNamestatus = openRestV2.psnRealNameInfo(baseParam);
        } catch (Exception e) {
        }
    }

    @Test
    public void orgAuthInfoQuery(){
        WrapperResponse<OrgAuthInfoResponse> orgAuthResp;
        WrapperResponse<OrgAuthRealNameResponse> orgAuthResponse;
        OrgAuthInfoParam orgAuthInfoParam = new OrgAuthInfoParam();
        String orgId = "b95d7d37fcea43af8ce743a597a0c10b";
        orgAuthInfoParam.setOrgId(orgId);
        RequestContext.put("X-Tsign-Open-App-Id","**********");
        orgAuthResp = openRestV2.orgAuthInfo(orgAuthInfoParam);
        OrgAuthInfoBaseParam orgAuthInfoBaseParam = new OrgAuthInfoBaseParam();
        orgAuthInfoBaseParam.setName("黎音企业--单测专用");
        orgAuthResponse = openRestV2.orgRealNameInfo(orgAuthInfoBaseParam);
    }


    private PersonAddByIdV2Request buildPerson() {
        PersonAddByIdV2Request request = new PersonAddByIdV2Request();
        request.setThirdPartyUserId(UTCGenerator.utcThirdAppUser());
        request.setIdNumber(UTCGenerator.utcPsnIdcard());
        request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setEmail(UTCGenerator.utcEmail());
        request.setMobile(UTCGenerator.utcMobile());
        request.setName(UTCGenerator.utcName());
        return request;
    }

    private PersonAddByIdRequest buildPerson2() {
        PersonAddByIdRequest request = new PersonAddByIdRequest();
        request.setThirdPartyUserId(UTCGenerator.utcThirdAppUser());
        request.setIdNumber(UTCGenerator.utcPsnIdcard());
        request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setEmail(UTCGenerator.utcEmail());
        request.setMobile(UTCGenerator.utcMobile());
        request.setName(UTCGenerator.utcName());
        return request;
    }


}
