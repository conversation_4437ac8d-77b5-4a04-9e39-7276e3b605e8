package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizBillingService;
import com.timevale.footstone.user.service.model.billing.response.MealAllowanceResponse;
import com.timevale.footstone.user.service.rest.BillingRest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: ziye
 * @since: 2020-08-28 17:15
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BillingTest {

  @Autowired BillingRest billingRest;
  @Autowired BizBillingService bizBillingService;

  String accountId = "4501308d254a434b90fd9d96f63dc161";
  String orgId = "c76c2f6f136d4997aaccf30f1f767f59";
  String appId = "**********";

  @Test
  public void mealAllowanceTest() {
    try {
      MealAllowanceResponse result = bizBillingService.getMealMargin(orgId, appId);
      System.out.println("计费套餐余量接口：单测成功");
      System.out.println("计费套餐余量接口-最终响应给调用方的结果=" + result.toString());
    } catch (Exception e) {
      System.out.println("计费套餐余量接口：单测失败");
      e.printStackTrace();
    }
  }

  @Test
  public void purchaseUrlTest() {
    try {
      // 获取h5购买地址
      billingRest.purchaseUrl(accountId, "", true);
      // 获取pc购买地址
      billingRest.purchaseUrl(accountId, "", false);
    } catch (Exception e) {
      System.out.println("获取用户购买地址：单测失败");
      e.printStackTrace();
    }
  }

  @Test
  public void orderListUrlTest() {
    try {
      // 获取h5已购订单列表地址
      billingRest.orderListUrl(accountId, "", true);
      // 获取pc已购订单列表地址
      billingRest.orderListUrl(accountId, "", false);
    } catch (Exception e) {
      System.out.println("获取用户已购订单列表地址：单测失败");
      e.printStackTrace();
    }
  }

  @Test
  public void getShowCaseTest() {
    try {
      // 获取用户可购买商品橱窗id
      billingRest.getShowCase(accountId);
    } catch (Exception e) {
      System.out.println("获取用户可购买商品橱窗id：单测失败");
      e.printStackTrace();
    }
  }
}
