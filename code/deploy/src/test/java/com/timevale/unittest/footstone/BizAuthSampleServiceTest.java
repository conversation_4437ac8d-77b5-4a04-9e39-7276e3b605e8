package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizAuthSampleService;
import com.timevale.open.platform.oauth.standard.BaseAuthDTO;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * Author: liujiaxing
 * Date: 2022/6/28 8:48 下午
 * Description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class BizAuthSampleServiceTest {

    final String order = "test_order";

    @Resource
    BizAuthSampleService bizAuthSampleService;


    @Test
    public void getOrderInfo() {

        try {
            bizAuthSampleService.getOrderInfo(order);

        }catch (Exception e){

        }
    }


    @Test
    public void getOrderInfoWithOid() {
        try {
            BaseAuthDTO authDTO = new BaseAuthDTO();

            bizAuthSampleService.getOrderInfoWithOid(authDTO);
        }catch (Exception e ){

        }


    }


    @Test
    public void getOrderInfoWithSOid() {
        try {
            BaseAuthDTO authDTO = new BaseAuthDTO();
            bizAuthSampleService.getOrderInfoWithSOid(authDTO);
        }catch (Exception e){

        }


    }


    @Test
    public void saveOrder() {
        try {
            bizAuthSampleService.saveOrder(order);

        }catch (Exception e){

        }

    }


    @Test
    public void name() {
        try {
            bizAuthSampleService.getOrderInfo(order);

        }catch (Exception e){

        }

    }
}
