package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestBean;
import org.junit.Assert;

public enum SimpleTestcaseCheckForName022 implements SimpleTestcaseChecker {
    T_1("12345", "**45"),

    T_2("123", "*23"),

    T_3("12", "1*"),

    T_4("1", "*"),

    T_5("", ""),

    T_6("123456", "**56"),

    T_7("1234567", "**67"),
    ;

    private String input;

    private String anwser;

    SimpleTestcaseCheckForName022(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestBean t = new TestBean();
        t.setName022(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getName022());
    }
}
