package com.timevale.unittest.footstone;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.account.flow.service.api.RpcAccountApprovalService;
import com.timevale.account.flow.service.model.output.ApprovalIdOutput;
import com.timevale.doccooperation.service.util.UUIDUtil;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.biz.BizApprovalService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.flow.model.Approval;
import com.timevale.footstone.user.service.model.flow.model.ApprovalWithoutId;
import com.timevale.footstone.user.service.model.flow.model.BatchApproval;
import com.timevale.footstone.user.service.model.flow.request.ListApprovalRequest;
import com.timevale.footstone.user.service.model.flow.request.ReApplyApprovalRequest;
import com.timevale.footstone.user.service.model.flow.request.StartAprrovalRequest;
import com.timevale.footstone.user.service.model.flow.response.*;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: ziye
 * @since: 2020-07-28 09:32
 **/

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ApprovalTest {

    @Autowired
    private IdAdapt idAdapt;

    @Autowired
    private BizApprovalService bizApprovalService;


    //模板类型
    String modelType="CONTRACT";
    //审批实例Id
    Long approvalId = 0L;

    //子烨
    //模板Id
    String modeleId="D0E1E64757064E569FEC3A26123ABCA4";
    //账号Id（创建）
    String accountId ="73b3e22d7c624120af28a17b6cf9e617";
    //审批账号ID
    String approvalAccountId ="388d1adca73c468b8402f05573260c8f";
    //组织id
    String orgId = "2765cfbd878049c1bc314f68136ab26e";

    //长歌
    //模板Id
    //String modeleId="D0E1E64757064E569FEC3A26123ABCA4";
    //账号Id（创建）
    //String accountId ="9f2d1ebaf2034138ac19d65fbf776c2a";
    //审批账号ID
    //String approvalAccountId ="36dc74167add40a28e96cf9c5995eabc";
    //组织id
    //String orgId = "79fc3ff6a15b406dbe31f6701670763a";

    Set<Long> batchApprovalId = new HashSet<>();

    /**
     * 发起审批
     */
    @Test
    public void startApprovalTest(){
        try {
            StartAprrovalRequest request = new StartAprrovalRequest();
            request.setModelId(modeleId);
            request.setApplyOid(accountId);
            request.setOrgId(orgId);
            request.setApplyName("请假审批-ziye");
            request.setModelType(modelType);
            request.setBizId("xxxx");
            request.setBizType("xxxxx");
            RpcOutput<ApprovalIdOutput> out = bizApprovalService.createApprovalInstance(request);
            approvalId=out.getData().getApprovalId();
            System.out.println("创建审批实例 单测成功");
            System.out.println("审批实例Id："+out.getData().getApprovalId() +" 审批状态 "+out.getData().getCreateStatus());
        } catch (Exception e) {
            System.out.println("创建审批实例 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 审批同意
     */
    @Test
    public void agreeTest(){
        try {
            startApprovalTest();
            ApprovalWithoutId request =new ApprovalWithoutId();
            request.setAccountId(approvalAccountId);
            request.setApprovalRemark("审批通过-ziye");
            System.out.println("请求入参：approvalId=["+approvalId+"] orgId=["+orgId+"] approvalAccountId=["+approvalAccountId+"]");
            bizApprovalService.agree(approvalId,orgId,request);
            System.out.println("创建新审批的实例Id："+approvalId +"审批-同意 单测成功");
        } catch (Exception e) {
            System.out.println("创建新审批的实例Id："+approvalId +"审批-同意 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 审批拒绝
     */
    @Test
    public void refuseApprovalTest(){
        try {
            startApprovalTest();
            ApprovalWithoutId request = new ApprovalWithoutId();
            request.setAccountId(approvalAccountId);
            request.setApprovalRemark("审批拒绝-ziye");
            bizApprovalService.refuse(approvalId,orgId,request);
            System.out.println("创建新审批的实例Id："+approvalId +"审批-拒绝 单测成功");
        } catch (Exception e) {
            System.out.println("创建新审批的实例Id："+approvalId +"审批-拒绝 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 审批撤回
     */
    @Test
    public void cancelApprovalTest(){
        try {
            startApprovalTest();
            ApprovalWithoutId request = new ApprovalWithoutId();
            request.setAccountId(accountId);
            request.setApprovalRemark("审批撤回");
            System.out.println("撤销入参 approvalId=["+approvalId+"] orgid=["+orgId+"] accountId=["+accountId+"]");
            bizApprovalService.cancel(approvalId,orgId,request);
            System.out.println("创建新审批的实例Id："+approvalId +"审批-撤回 单测成功");
        } catch (Exception e) {
            System.out.println("创建新审批的实例Id："+approvalId +"审批-撤回 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 重新发起
     */
    @Test
    public void reApplyApprovalTest(){
        try {
            cancelApprovalTest();
            ReApplyApprovalRequest request = new ReApplyApprovalRequest();
            request.setApprovalId(approvalId);
            request.setModelId(modeleId);
            request.setModelType(modelType);
            bizApprovalService.reApply(orgId,accountId,request);
            System.out.println("重新发起新审批的实例Id："+approvalId +"审批-重新发起 单测成功");
        } catch (Exception e) {
            System.out.println("重新发起新审批的实例Id："+approvalId +"审批-重新发起 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 查询审批列表实例
     */
    @Test
    public void listApprovalTest() {
      ListApprovalRequest request = new ListApprovalRequest();
      request.setType(2); // 	1 我收到的 2我发起的 3待我审批 4我已审批
      request.setApprovalstatus(Lists.newArrayList(1, 2, 3, 4, 5)); // 1 待审批 2审批通过 3 审批驳回 4-已撤回 5-终止
      ApiPageResult<ListApprovalRespose> listApproval = bizApprovalService.listApproval(orgId, accountId, request);
      listApproval.getResult().stream()
          .forEach(
              e -> {
                System.out.println(e.toString());
              });
    }

    /**
     * 查看审批进度
     */
    @Test
    public void approvalProgressTest(){
        try {
            agreeTest();
            ApprovalProgressResponse approvalProgressResponse = bizApprovalService.approvalProgress(approvalId,orgId,accountId);
            System.out.println("查询审批进度 单测成功");
            System.out.println("进度详情"+ approvalProgressResponse.toString());
        } catch (Exception e) {
            System.out.println("查询审批进度 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 批量 同意审批
     */
    @Test
    public void batchAgreeApprovalTest(){
        batchCreate();
        BatchApproval request = new BatchApproval();
        request.setApprovalIds(batchApprovalId);
        //此处账号为 审批人账号
        request.setAccountId(approvalAccountId);
        request.setApprovalRemark("批量审批同意");
        BatchApprovaleResponse bathResponse = bizApprovalService.batchAgree(request,orgId);
        System.out.println(bathResponse.toString());
    }

    /**
     * 审批撤回(批量)
     */
    @Test
    public void batchCancelApprovalTest(){
        batchCreate();
        BatchApproval request =  new BatchApproval();
        request.setApprovalIds(batchApprovalId);
        request.setAccountId(accountId); //撤回人必须是发起人
        request.setApprovalRemark("批量审批撤回");
        BatchApprovaleResponse bathResponse = bizApprovalService.batchCancel(request,orgId);
        System.out.println(bathResponse.toString());
    }


    @Test
      public void batchCreate() {
        try {
            for (int i = 0; i < 2; i++) {
              StartAprrovalRequest request = new StartAprrovalRequest();
              request.setModelId(modeleId);
              request.setApplyOid(accountId);
              request.setOrgId(orgId);
              request.setApplyName("请假审批-ziye");
              request.setModelType(modelType);
              request.setBizId("xxxx");
              request.setBizType("xxxxx");
              RpcOutput<ApprovalIdOutput> out = bizApprovalService.createApprovalInstance(request);
              approvalId = out.getData().getApprovalId();
              batchApprovalId.add(approvalId);
          }
            System.out.println("批量创建审批实例 成功");
            System.out.println("批量审批流审批ID=["+batchApprovalId.toString()+"]");
        } catch (Exception e) {
            System.out.println("批量创建审批实例 失败");
        }
    }

    @Test
    public void approvalStatusTest(){
        try {
            agreeTest();
            GetApprovalStatusResponse response = bizApprovalService.queryApprovalStatus(approvalId, orgId, accountId);
            System.out.println("查询审批状态 单测成功");
            System.out.println("审批状态详情"+ response.toString());
        } catch (Exception e) {
            System.out.println("查询审批状态 单测失败");
            e.printStackTrace();
        }
    }

    //@Test
    public void statisticsTest(){
        ApprovalStatisticsResponse statistics = bizApprovalService.statistics(accountId, orgId);
        Assert.assertTrue(statistics.getContract().getInitiateCount() > 0);
    }

    /** 获取审批链接 */
    @Test
    public void getApprovalUrlTest() {
        try {
            String token = UUIDUtil.generateUUID();
            GetApprovalUrlResponse response =
                    bizApprovalService.getApprovalUrl(
                            1L,
                            "042a8a5ab42f447cbdbfab83772fb9f4",
                            "042a8a5ab42f447cbdbfab83772fb9f4",
                            token,
                            "h5",
                            true);
            Assert.assertTrue(StringUtils.isNotEmpty(response.getShortUrl()));
        } catch (Exception e) {
            System.out.println("获取审批链接失败");
            e.printStackTrace();
        }
    }
}
