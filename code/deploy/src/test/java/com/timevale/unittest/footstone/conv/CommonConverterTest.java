package com.timevale.unittest.footstone.conv;

import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.utils.conv.CommonConverter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 2025/1/15 09:58
 */
public class CommonConverterTest {


    @Mock
    private RpcIcOrgPlusServiceAdapt orgPlusServiceAdapt;

    @Test
    public void batchSearchByBiFunctionPutByConsumerTest(){
        Map<String, List<MemberDetail>> organsAdmin = new ConcurrentHashMap<>();
//        CommonConverter.batchSearchByBiFunctionPutByConsumer();
    }

}
