package com.timevale.unittest.footstone.v2.rest;

import com.google.common.collect.Lists;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.organization.service.enums.ActivateEnum;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.configuration.IsolationConfig;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.impl.biz.BizOrganServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.realname.RpcInfoQueryServiceAdapt;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.account.mods.OrgProperties;
import com.timevale.footstone.user.service.model.account.request.OrgCreateRequest;
import com.timevale.footstone.user.service.model.member.mods.MemberDetailModel;
import com.timevale.footstone.user.service.rest.v2.OrganizationRestV2;
import com.timevale.mandarin.weaver.utils.RequestContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.timevale.account.service.model.constants.BuiltinContacts.MOBILE;
import static com.timevale.account.service.model.constants.BuiltinCredentials.CRED_ORG_USCC;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrganizationV2MockTest {

    @InjectMocks
    OrganizationRestV2 organizationRestV2;


    @InjectMocks
    private BizOrganServiceImpl bizOrganService;

    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Mock
    private BizMemberService bizMemberService;


    @Mock
    private RpcOrgPlusService rpcOrgPlusService;

    @InjectMocks
    private IsolationConfig isolationConfig;


    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

    @Mock
    private SystemProperty systemProperty;

    @Mock
    private RpcInfoQueryServiceAdapt rpcInfoQueryServiceAdapt;

    String appId  = "7488";
    String appRequestHeader = "X-Tsign-Open-App-Id";



    @Before
    public void setUp(){
        Set<String> set = new HashSet<>();
        set.add(appId);
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        ReflectionTestUtils.setField(organizationRestV2, "servletRequest", servletRequest);
        ReflectionTestUtils.setField(organizationRestV2, "bizOrganService", bizOrganService);

        mockSearchOrgByEs(ActivateEnum.ACTIVATED);
        when(bizMemberService.checkMemberInOrg(any(),any())).thenReturn(true);
        ApiPageResult<MemberDetailModel> admins = new ApiPageResult<>();
        admins.setTotal(1);
        List<MemberDetailModel> memberDetailModels = Lists.newArrayList();
        MemberDetailModel model = new MemberDetailModel();
        model.setActive(true);
        model.setVirtual(false);
        memberDetailModels.add(model);
        admins.setResult(memberDetailModels);
        mockGetMemberByEs();
        ReflectionTestUtils.setField(bizOrganService, "bizMemberService", bizMemberService);
        ReflectionTestUtils.setField(bizOrganService, "esAccountServiceAdapt", esAccountServiceAdapt);
        ReflectionTestUtils.setField(bizOrganService, "rpcOrgPlusService", rpcOrgPlusService);
        BizOrganOuid organOuid = new BizOrganOuid();
        organOuid.setOuid("organOuid");
    }

    private void mockGetMemberByEs() {
        PagerResult<MemberDetail> pageResult = new PagerResult<>();
        MemberDetail memberDetail  = new MemberDetail();
        memberDetail.setOrganId("organId");
        List<MemberDetail> items = new ArrayList<>();
        items.add(memberDetail);
        pageResult.setItems(items);
        pageResult.setTotal(items.size());
    }


    @Test
    public void createOrgTest(){
        RequestContext.put(appRequestHeader,appId);
        OrgCreateRequest createRequest = new OrgCreateRequest();
        createRequest.setCreater("creater");
        OrgProperties properties = new OrgProperties();
        properties.setName("name");
        createRequest.setProperties(properties);
        when(systemProperty.getCreateOrgCheckIdentityCodeAlreadyExistsActiveInterruptOnOff()).thenReturn(false);
        //是实名组织成员
        organizationRestV2.create(createRequest);
    }

    private void mockSearchOrgByEs(ActivateEnum unActivate) {
        PagerResult<EsOrgSearhOutput> esSearchOutput = getEsOrgSearchOutputList(unActivate.getCode());
        when(esAccountServiceAdapt.getOrgsByEs(any()))
                .thenReturn(esSearchOutput);
    }

    private PagerResult<EsOrgSearhOutput> getEsOrgSearchOutputList(Integer activate) {
        PagerResult<EsOrgSearhOutput> esResult = new PagerResult<>();
        List<EsOrgSearhOutput> items= new ArrayList<>();
        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        esOrgSearhOutput.setActivate(activate);
        EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
        AccountBaseDetail baseDetail = new AccountBaseDetail();
        baseDetail.setProjectApp(new ProductApp());
        user.setBase(baseDetail);
        EsId id =new EsId();
        id.setGuid("GId");
        id.setOuid("oid");
        id.setUuid("uuid");
        user.setId(id);
        user.setStatus(RealnameStatus.ACCEPT);
        esOrgSearhOutput.setUser(user);
        items.add(esOrgSearhOutput);
        esResult.setItems(items);
        esResult.setTotal(items.size());
        List<Property> properties = new ArrayList<>();
        Property name = new Property();
        name.setType(INFO_NAME);
        name.setValue("name");
        properties.add(name);
        user.setProperties(properties);
        List<Property> credentials = new ArrayList<>();
        Property credenty = new Property();
        credenty.setType(CRED_ORG_USCC);
        credenty.setValue("CRED_ORG_USCC");
        credentials.add(credenty);
        user.setCredentials(properties);
        List<Property> contacts = new ArrayList<>();
        Property contact = new Property();
        contact.setType(MOBILE);
        contact.setValue("***********");
        contacts.add(contact);
        user.setContacts(contacts);
        return esResult;
    }

    private void mockGetIcUserList(){
        PagerResult<BizOrgBaseInfoOutput> pagerResult = new PagerResult<>();
        List<BizOrgBaseInfoOutput> baseInfoOutputs = new ArrayList<>();
        BizOrgBaseInfoOutput baseInfo = new BizOrgBaseInfoOutput();
        baseInfo.setActivate(ActivateEnum.ACTIVATED.getCode());
        BizICUserOutput bizICUserOutput = new BizICUserOutput();
        AccountBaseDetail baseDetail =new AccountBaseDetail();
        baseDetail.setProjectApp(new ProductApp());
        baseDetail.setAccountUid("accountUid");
        bizICUserOutput.setBase(baseDetail);
        ICUserId id = new ICUserId();
        id.setOuid("oid");
        id.setUuid("uid");
        id.setUuid("uid");
        bizICUserOutput.setId(id);
        List<Property> properties = new ArrayList<>();
        Property name = new Property();
        name.setType(INFO_NAME);
        name.setValue("name");
        properties.add(name);
        bizICUserOutput.setProperties(properties);
        baseInfo.setAccount(bizICUserOutput);
        baseInfoOutputs.add(baseInfo);
        baseInfo.setRoleDetailList(new ArrayList<>());
        pagerResult.setTotal(baseInfoOutputs.size());
        pagerResult.setItems(baseInfoOutputs);
//        when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(),any())).thenReturn(pagerResult);
    }
}
