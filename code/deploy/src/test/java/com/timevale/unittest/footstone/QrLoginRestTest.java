package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.response.QrUidApplyResponse;
import com.timevale.footstone.user.service.model.account.response.QrUidResultResponse;
import com.timevale.footstone.user.service.model.enums.QrUidStatusEnum;
import com.timevale.footstone.user.service.rest.QrLoginRest;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2020/3/23
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class QrLoginRestTest {

    @Autowired
    QrLoginRest qrLoginRest;

    @Test
    public void applyTest(){
        RequestContext.put("X-Tsign-Open-App-Id", "3876545303");
        WrapperResponse<QrUidApplyResponse> apply = qrLoginRest.apply();
        QrUidApplyResponse data = apply.getData();
        String qruid = data.getQruid();
        String url = data.getUrl();
        Assert.assertNotNull(qruid);
        Assert.assertTrue(url.contains(url));
    }

    @Test
    public void resultTest(){
        RequestContext.put("X-Tsign-Open-App-Id", "3876545303");
        WrapperResponse<QrUidApplyResponse> apply = qrLoginRest.apply();
        QrUidApplyResponse data = apply.getData();
        String qruid = data.getQruid();
        WrapperResponse<QrUidResultResponse> result = qrLoginRest.result(qruid);
        QrUidResultResponse response = result.getData();
        Assert.assertSame(QrUidStatusEnum.WATING.name(), response.getStatus());
    }


}
