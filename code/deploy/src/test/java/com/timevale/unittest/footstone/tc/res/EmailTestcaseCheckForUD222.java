package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestEmailBean;
import org.junit.Assert;

public enum EmailTestcaseCheckForUD222 implements SimpleTestcaseChecker {
    T_1("12345@123456", "12**45@12**56"),

    T_2("1234@12345", "1**@12**45"),

    T_3("1@1234", "1**@1**"),

    T_4("@123456", "@12**56"),

    T_5("123456@", "12**56@"),

    T_6("@", "@");

    private String input;

    private String anwser;

    EmailTestcaseCheckForUD222(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestEmailBean t = new TestEmailBean();
        t.setEmailud(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getEmailud());
    }
}
