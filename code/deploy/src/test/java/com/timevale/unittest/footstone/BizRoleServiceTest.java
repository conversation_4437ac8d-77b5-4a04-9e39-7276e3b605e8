package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * Author: liujiaxing
 * Date: 2022/12/13 4:37 下午
 * Description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizRoleServiceTest {

    @Resource
    BizRoleService bizRoleService;

    static final String orgId = "ab352cc5dbe348e991973b0440802140";

    static final String oid = "fd38b5a4f69f4388a6029e86d3328c35";

    @Test
    public void checkIfOrganLegal() {

        boolean b = bizRoleService.checkIfOrganLegal(orgId, oid);

        boolean b1 = bizRoleService.checkIfIsAdminOrLegal(orgId, oid);

        System.out.println(b);

        System.out.println(b1);
    }
}
