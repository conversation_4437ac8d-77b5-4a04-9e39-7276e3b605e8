package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizAccountService;
import com.timevale.footstone.user.service.inner.impl.biz.SyncAccountServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.model.account.request.SyncAccountRequest;
import com.timevale.footstone.user.service.model.account.response.SyncAccountResponse;
import com.timevale.footstone.user.service.model.organization.request.CreateOrgAndLegalRequest;
import com.timevale.footstone.user.service.model.organization.response.CreateOrgAndLegalResponse;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年09月14日 14:09:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SyncAccountTest {
    @Autowired SyncAccountServiceImpl syncAccountService;

    @Autowired
    BizAccountService accountService;

    @Autowired
    RpcIcOrgPlusServiceAdapt orgPlusServiceAdapt;


    @Autowired
    RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;
    //@Test
    public void testSyncPsnAccount() {
        try {
            SyncAccountRequest request = new SyncAccountRequest();
            request.setSource("aa");
            request.setName("sync-" + UUID.randomUUID().toString());
            request.setCertType("CRED_PSN_CH_IDCARD");
            request.setCertNo("152634197901034228");
            syncAccountService.syncAccount(request, "**********");
        } catch (Exception e) {

        }
    }

    @Test
    public void testSyncOrgAccount() {
        try {
            CreateOrgAndLegalRequest request = new CreateOrgAndLegalRequest();
            request.setLegalCertNo("152634197901034228");
            request.setLegalCertType("CRED_PSN_CH_IDCARD");
            request.setLegalName("sync-" + UUID.randomUUID().toString());
            request.setOrgCertNo("913232900002913232");
            request.setOrgCertType("CRED_ORG_USCC");
            request.setOrgName("syncOrg-" + UUID.randomUUID().toString());
            request.setSource("aaa");
            syncAccountService.createOrgAndLegal(request, "**********");
        } catch (Exception e) {

        }
    }

    //@Test
    public void syncAccountNewTest(){
        try{
            SyncAccountRequest request = new SyncAccountRequest();
            request.setSource("aa");
            request.setName("sync-" + UUID.randomUUID().toString());
            request.setCertType("CRED_PSN_CH_IDCARD");
            request.setCertNo("220283199405050617");
            SyncAccountResponse response = syncAccountService.syncAccount(request, "**********");
            icUserPlusServiceAdapt.delete(response.getAccountId());
        } catch (Exception e){

        }

    }

    @Test
    public void syncOrgNewTest(){
        try{
            CreateOrgAndLegalRequest request = new CreateOrgAndLegalRequest();
            request.setLegalCertNo("152123199307041241");
            request.setLegalCertType("CRED_PSN_CH_IDCARD");
            request.setLegalName("sync-" + UUID.randomUUID().toString());
            request.setOrgCertNo("9133010339934009XT");
            request.setOrgCertType("CRED_ORG_USCC");
            request.setOrgName("syncOrg-" + UUID.randomUUID().toString());
            request.setSource("aaa");
            CreateOrgAndLegalResponse response = syncAccountService.createOrgAndLegal(request, "**********");
            icUserPlusServiceAdapt.delete(response.getOrgId());
            orgPlusServiceAdapt.deleteOrg(response.getOrgId());
            icUserPlusServiceAdapt.delete(response.getLegalOid());
        } catch (Exception e){

        }
    }

}
