package com.timevale.unittest.footstone;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.TomcatHttpServletRequest;
import com.timevale.account.service.exception.Errors;
import com.timevale.account.service.model.service.biz.acc.output.BizAccountOutput;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.sender.input.BizSenderCommitInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.inner.biz.BizSenderAuthService;
import com.timevale.footstone.user.service.inner.geetest.GeetestRobotAuthService;
import com.timevale.footstone.user.service.inner.geetest.bean.GeetestRobotRegisterResponse;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.senderauth.GeetestRobotAuthApplyRequest;
import com.timevale.footstone.user.service.model.account.senderauth.GeetestRobotAuthVerifyModel;
import com.timevale.footstone.user.service.model.account.senderauth.request.*;
import com.timevale.footstone.user.service.model.account.senderauth.response.SenderWillAssembleCheckResponse;
import com.timevale.footstone.user.service.model.account.senderauth.response.WillAssembleAuthApplyResponse;
import com.timevale.footstone.user.service.model.account.senderauth.response.WillAuthApplyResponse;
import com.timevale.footstone.user.service.rest.SenderAuthRest;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年06月03日 21:05:00
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SendAuthTest {

    @Autowired
    SenderAuthRest rest;
    @Autowired
    private GeetestRobotAuthService geetestRobotAuthService;
    @Autowired
    private IdAdapt idAdapt;
    @Autowired
    RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;
    @Autowired
    private RpcSenderAuthService svc;
    @Autowired
    private BizSenderAuthService bizSenderAuthService;

    @Test
    public void deleteIdcard() {
        ServiceIdRequest request = new ServiceIdRequest();

        try {
            request.setServiceId("aa");
            rest.deleteIdcard(request);
        } catch (Exception e) {

        }


        try {
            rest.deleteModifyIdcard(request);
        } catch (Exception e) {

        }
    }



    @Test
    public void sendMobile() {

        GeetestRobotAuthApplyRequest robotAuthRequest = new GeetestRobotAuthApplyRequest();
        String principal = "15869154156";
        robotAuthRequest.setPrincipal(principal);
        robotAuthRequest.setClientType("web");
        WrapperResponse<GeetestRobotRegisterResponse> robotApply = rest.robotApply(robotAuthRequest);
        Assert.assertTrue("申请人机校验异常", null != robotApply.getData());
        MobileSenderRequest request = new MobileSenderRequest();
        request.setPrincipal(principal);
        request.setBizType("MODIFY_IDCARD");
        request.setPreServiceId("");
        request.setGeetestAuth(true);

        GeetestRobotAuthVerifyModel model = new GeetestRobotAuthVerifyModel();
        model.setGeetest_challenge(robotApply.getData().getChallenge());
        String validate = UUID.randomUUID().toString();
        model.setGeetest_validate(validate);
        model.setGeetest_seccode(validate + "|jordan");

        request.setModel(model);
        HttpServletRequest myHttpRequest = new TomcatHttpServletRequest();
        myHttpRequest.getSession().setAttribute("SESSION_ROBOT_GEETEST_DATA", UUID.randomUUID().toString());
        RequestContext.put("httpServletRequest", myHttpRequest);


        try {
            geetestRobotAuthService.validResult(model, request.getPrincipal());
            rest.mobileSenderApply(request);
        } catch (Errors.AccIdcardNotExist e) {
            // 手机号不存在， 允许范围内
        } catch (Exception e) {
//            Assert.assertTrue("发送短信验证码异常", false);
        }

    }

    @Test
    public void registerTest() {
        try {
            RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(new MyTestHttpServletRequest()));

            String principal = "15912358798";
            MobileSenderRequest request = new MobileSenderRequest();
            request.setPrincipal(principal);
            request.setBizType("REGISTER");
            request.setPreServiceId("");
            request.setGeetestAuth(false);
            String serviceId = rest.mobileSenderApply(request).getData().getServiceId();
            BizSenderCommitInput checkInput = new BizSenderCommitInput();
            checkInput.setServiceId(serviceId);
            checkInput.setCredentials("123456");
            svc.codeAuth(new RpcInput<>(checkInput));
            RegisterRequest rr = new RegisterRequest();
            rr.setServiceId(serviceId);
            String auid = ((BizAccountOutput) rest.register(rr).getData()).getBase().getAccountUid();
            String oid=idAdapt.accountId(auid);
            icUserPlusServiceAdapt.delete(oid);
            request.setBizType("REGISTER_LOGIN");
            serviceId = rest.mobileSenderApply(request).getData().getServiceId();
            checkInput.setServiceId(serviceId);
            svc.codeAuth(new RpcInput<>(checkInput));
            rr.setServiceId(serviceId);
            rest.registerLogin(rr);
            String serviceId2=  rest.mobileSenderApply(request).getData().getServiceId();
            checkInput.setServiceId(serviceId2);
            svc.codeAuth(new RpcInput<>(checkInput));
            rr.setServiceId(serviceId2);
            oid = rest.registerLogin(rr).getData().getOuid();
            icUserPlusServiceAdapt.delete(oid);
        }catch (Exception e){

        }


    }

    @Test
    public void testAdminTransfer() {
        try {
            String liyinAccountUid = "c632f31cde0e4763a38bb4e191f018c7";
            String liyinoid = "7882bc9368014ca6a8ab49bd1ad9ce42";
            String xiaozhaoAccountUid = "592784afad344c4fb7fa5cc39f79dcab";
            String xiaozhaoOid = "3bc25a2489a440999e0589248ea55182";
            String organId = "939d98d082134a918e5694b04d05fd02";
            AdminTransferRequest transferRequest = new AdminTransferRequest();
            transferRequest.setNewAdminAccountId(xiaozhaoAccountUid);
            transferRequest.setOrgId(organId);
            transferRequest.setSwitchedAccountId(liyinoid);
            transferRequest.setServiceId(UUID.randomUUID().toString().replace("-", ""));
            rest.adminTransfer(transferRequest);
        } catch (Exception e) {
            log.info("transfer error,",e);
        }


    }


    @Test
    public void sendEmail() {

        GeetestRobotAuthApplyRequest robotAuthRequest = new GeetestRobotAuthApplyRequest();
        String principal = "<EMAIL>";
        robotAuthRequest.setPrincipal(principal);
        robotAuthRequest.setClientType("web");
        WrapperResponse<GeetestRobotRegisterResponse> robotApply = rest.robotApply(robotAuthRequest);
        Assert.assertTrue("申请人机校验异常", null != robotApply.getData());
        EmailSenderRequest request = new EmailSenderRequest();
        request.setPrincipal(principal);
        request.setBizType("MODIFY_IDCARD");
        request.setPreServiceId("");
        request.setGeetestAuth(true);

        GeetestRobotAuthVerifyModel model = new GeetestRobotAuthVerifyModel();
        model.setGeetest_challenge(robotApply.getData().getChallenge());
        String validate = UUID.randomUUID().toString();
        model.setGeetest_validate(validate);
        model.setGeetest_seccode(validate + "|jordan");
        //request.setAuth(model);

        try {
            rest.emailSenderApply(request);
        } catch (Errors.AccIdcardNotExist e) {
            // 手机号不存在， 允许范围内
        } catch (Exception e) {
            // 发送短信验证码异常
        }

    }

    @Test
    public void testBroadcastAdminTransferCompleted() {
        String switchedAccountUid = "a74062cfd67a46e7846307a67157e189";
        String newAdminOid = "2b74ffd3fdaa4f47b1ceaaea954ee446";
        String orgOid = "4c720441bfe745bbb2c11cb72049daf6";
        AdminTransferRequest transferRequest = new AdminTransferRequest();
        transferRequest.setNewAdminAccountId(newAdminOid);
        transferRequest.setOrgId(orgOid);
        transferRequest.setSwitchedAccountId(switchedAccountUid);
        transferRequest.setServiceId(UUID.randomUUID().toString().replace("-", ""));
        bizSenderAuthService.broadcastAdminTransferCompleted(transferRequest);

    }

}
