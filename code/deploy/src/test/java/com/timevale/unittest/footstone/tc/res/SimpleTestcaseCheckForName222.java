package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestBean;
import org.junit.Assert;

public enum SimpleTestcaseCheckForName222 implements SimpleTestcaseChecker{
    T_1("12345", "12*45"),

    T_2("1234", "1**"),

    T_3("1", "*"),

    T_4("", ""),

    T_5("123456", "12**56"),

    T_6("1234567", "12**67"),
    ;

    private String input;

    private String anwser;

    SimpleTestcaseCheckForName222(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestBean t = new TestBean();
        t.setName222(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getName222());
    }
}
