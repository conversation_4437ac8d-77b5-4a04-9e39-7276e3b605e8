package com.timevale.unittest.footstone;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.timevale.easun.service.enums.rules.ExpireReasonEnums;
import com.timevale.easun.service.enums.rules.RuleGrantStatusEnums;
import com.timevale.easun.service.model.rule.mods.RuleGrantModel;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.exception.RpcBaseException;
import com.timevale.footstone.user.service.inner.biz.BizRuleGrantService;
import com.timevale.footstone.user.service.inner.impl.biz.BizRuleGrantServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.rule.BizRuleGrantServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.rule.RpcRuleServiceAdapt;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.model.rules.request.*;
import com.timevale.footstone.user.service.model.rules.response.*;
import com.timevale.footstone.user.service.mq.consumer.RuleGrantRevokeFlowsConsumer;
import com.timevale.footstone.user.service.rest.RulesRest;
import com.timevale.footstone.user.service.utils.DateUtils;
import com.timevale.footstone.user.service.utils.sdk.client.model.StartProcessRequest;
import com.timevale.framework.mq.client.producer.Msg;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.privilege.service.enums.BuiltinRoleType;
import com.timevale.privilege.service.enums.rules.ExpireReason;
import com.timevale.privilege.service.enums.rules.ResourceType;
import com.timevale.privilege.service.enums.rules.RuleGrantStatus;
import com.timevale.privilege.service.enums.rules.TemplateKeyRegister;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import com.timevale.unittest.footstone.assist.OpenApiAssist;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@Rollback()
//@Transactional(transactionManager = "")
//@Transactional
public class RuleTest {

    private static String sealId = "92e121f8-6fa6-41c9-8ce2-6f571637775d";
    @Autowired
    private OpenApiAssist assist;
    @Autowired
    private IdAdapt idAdapt;
    @Autowired
    private RpcRoleServiceAdapt roleServiceAdapt;
    @Autowired
    private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;

    @Autowired
    private RulesRest rest;
    @Autowired
    private BizRuleGrantServiceImpl ruleGrantService;

    @Autowired
    private BizRuleGrantServiceAdapt bizRuleGrantServiceAdapt;
    @Autowired
    private SystemProperty systemProperty;
    @Autowired
    private RuleGrantRevokeFlowsConsumer ruleGrantRevokeFlowsConsumer;


    private Boolean initialized = false;

    @Autowired
    private BizRuleGrantService bizRuleGrantService;

    @Autowired
    private RpcRuleServiceAdapt rpcRuleServiceAdapt;

    //@Test
    public void testBindRuleGrant() {
        String person = "f254a9e6e1934dcf94f3c3609db39a6f";
        String organ = "dace182f80f54a56afb2812e0c15ec99";
        PagerResult<BizRoleDetail> builtinRoles =
                roleServiceAdapt.getPagedBuiltinRolesByOrgan(
                        idAdapt.orgIdToOrganId(organ), 0, 20, null);
        String roleId =
                builtinRoles
                        .getItems()
                        .stream()
                        .filter(index -> index.getRoleKey().equals(BuiltinRoleType.ADMIN))
                        .findFirst()
                        .map(BizRoleDetail::getRoleId)
                        .orElse(null);
        List<String> roleIds = new ArrayList<>();
        roleIds.add(roleId);
        // 授权管理员
        rolePrivilegePlusServiceAdapt.grantUser(
                idAdapt.orgIdToOrganId(organ), idAdapt.oidToMemberId(person, organ), roleIds);
        BindRuleRequest request = new BindRuleRequest();
        request.setAutoFall(false);
        request.setExpireReason(ExpireReason.NOT_EXPIRE.name());
        request.setNotifySetting(true);
        request.setGrantedUser(person);
        request.setScope("ALL");
        request.setResourceId(sealId);
        request.setResourceType(ResourceType.SEAL.name());
        request.setGranter(person);
        request.setExpireTime(System.currentTimeMillis() + 10000);
        request.setTemplateKey(TemplateKeyRegister.SEAL_AUTH.name());
        request.setEffectiveTime(System.currentTimeMillis());
        request.setRoleKey("SEAL_USER");
        BindRuleResponse result;
        try {
            result = ruleGrantService.bindRuleGrant(organ, request);
        } catch (Exception e) {
            request.setAutoFall(Boolean.FALSE);
            result = ruleGrantService.bindRuleGrant(organ, request);
        }

        try {
            ChangeNotifySettingRequest request1 = new ChangeNotifySettingRequest();
            request1.setShouldNotify(true);
            rest.changeNotifySetting(organ, result.getRuleGrantId(), request1);
        } catch (Exception e) {
            //todo nothing
        }

        try {
            rest.getRuleGrantByGrantId(organ, result.getRuleGrantId());
        } catch (Exception e) {
            //todo nothing
        }

        try {
            GrantedDetailRequest grantedDetailRequest = GrantedDetailRequest.builder()
                    .resourceId(sealId)
                    .ruleGrantStatus(null)
                    .build();
            rest.getRuleGrantByResourceId(organ, grantedDetailRequest);

        } catch (Exception e) {
            //todo nothing
        }

        try {
            request.setRoleKey("SUPER_ADMIN");
            rest.updateRuleGrant(organ, result.getRuleGrantId(), request);

        } catch (Exception e) {

        }
        try {
            ruleGrantService.deleteRoleGrant(organ, result.getRuleGrantId());

        } catch (Exception e) {
            //todo nothing
        }

    }

//    @Test
//    public void batchUpdateRuleGrant() {
//        String person = "e9b1f5ae69994a5eb639e1e65c927335";
//        String organ = "96fc3fd6895440978c2855c3d7efca76";
//        String resourceId = "c943e0bd-df2a-4a79-9bb0-4cdd148e26a9";
//        Long expireTim = System.currentTimeMillis() + 30 * 24 * 3600 * 1000l;
//        Long effectiveTime = System.currentTimeMillis();
//
//        GrantedDetailRequest grantedDetailRequest = GrantedDetailRequest.builder()
//                .resourceId(resourceId)
//                .ruleGrantStatus(RuleGrantStatusEnums.INVALID.name())
//                .build();
//        GetRuleGrantByResourceIdResponse resourceIdResponse =
//                ruleGrantService.getRuleGrantByResourceId(
//                        organ, person, grantedDetailRequest);
//        String ruleGrantId = resourceIdResponse.getItems().get(0).getRuleGrantId();
//        resourceIdResponse.setItems(
//                resourceIdResponse.getItems().stream()
//                        .filter(
//                                ruleGrantByGrantIdResponse ->
//                                        ruleGrantByGrantIdResponse
//                                                .getExpireDesc()
//                                                .equals(ExpireReasonEnums.NOT_SIGN.getDesc()))
//                        .collect(Collectors.toList()));
//        GetRuleGrantByGrantIdResponse getRuleGrantByGrantIdResponse =
//                resourceIdResponse.getItems().get(0);
//        BatchRuleUpdateRequest batchRuleUpdateRequest = new BatchRuleUpdateRequest();
//        batchRuleUpdateRequest.setResourceId(resourceId);
//        batchRuleUpdateRequest.setRuleGrantedList(new ArrayList<>());
//        BatchRuleItem ruleItem = new BatchRuleItem();
//
//        ruleItem.setEffectiveTime(effectiveTime);
//        ruleItem.setExpireTime(expireTim);
//        ruleItem.setRuleGrantedId(ruleGrantId);
//        batchRuleUpdateRequest.getRuleGrantedList().add(ruleItem);
//        ruleGrantService.batchUpdateRuleGrant(organ, person, batchRuleUpdateRequest);
//
//        ruleItem.setEffectiveTime(effectiveTime);
//        ruleItem.setExpireTime(expireTim);
//        BatchBindRuleFlowResponse response =
//                ruleGrantService.batchBindRuleGrant(organ, person, batchRuleUpdateRequest);
//        getBatchSignUrlRequest getBatchSignUrlRequest = new getBatchSignUrlRequest();
//        getBatchSignUrlRequest.setFlowIds(response.getFlowIds());
//        ruleGrantService.getBatchSignUrl(organ, person, getBatchSignUrlRequest);
//
//        ruleGrantService.getResourceRuleGrantAmount(
//                organ, resourceId, RuleGrantStatusEnums.INVALID.name());
//        ruleGrantService.getResourceRuleGrantAmount(organ, resourceId, "ALL");
//
//    }


//    @Test
//    public void testBatchCreate() {
//        String person = "9ac6a3400744441e85b6dd24a2d2699b";
//        String granter = "88e44a1ebef441d69306d2abcc0eb276";
//        String organ = "5ccb1c1b0cc84eb9abced907cf392717";
//        String resourceId = "c079c136-9421-4c81-81bc-c57f8497c2bb";
//        Set<String> scopeList = Sets.newHashSet("041b382c4701411182e5e8e9314786bf", "b4abcf5dafb94a07967bcad6b3448803", "bc8a42ea0ee14360ae24145eb0b88eb2");
//
//        AddRulesRequest add = new AddRulesRequest();
//        add.setAutoFall(false);
//        add.setExpireReason(ExpireReason.NOT_EXPIRE.name());
//        add.setNotifySetting(true);
//        add.setGrantedUser(person);
//        add.setScopeList(scopeList);
//        add.setResourceId(resourceId);
//        add.setResourceType(ResourceType.SEAL.name());
//        add.setGranter(granter);
//        add.setExpireTime(System.currentTimeMillis() + 10000);
//        add.setTemplateKey(TemplateKeyRegister.SEAL_AUTH.name());
//        add.setEffectiveTime(System.currentTimeMillis());
//        add.setRoleKey("SEAL_USER");
//        AddRulesResponse addResp = ruleGrantService.addRuleGrants(organ, add);
//        Long expireTime = System.currentTimeMillis() + 10000;
//        Long effectiveTime = System.currentTimeMillis();
//        BatchRuleUpdateRequest update = new BatchRuleUpdateRequest();
//        update.setResourceId(resourceId);
//        List<BatchRuleItem> ruleGrantedList = new ArrayList<>();
//        addResp.getRuleGrantIds().forEach(e -> {
//            BatchRuleItem item = new BatchRuleItem();
//            item.setRuleGrantedId(e);
//            item.setExpireTime(expireTime);
//            item.setEffectiveTime(effectiveTime);
//            ruleGrantedList.add(item);
//        });
//
//        update.setRuleGrantedList(ruleGrantedList);
//        ruleGrantService.updateRuleGrants(organ, granter, update);
//        GrantedDetailRequest grantedDetailRequest = GrantedDetailRequest.builder()
//                .resourceId(resourceId)
//                .ruleGrantStatus("ALL")
//                .build();
//        ruleGrantService.getRuleGrantByResourceId(organ, person, grantedDetailRequest);
//        addResp.getRuleGrantIds().forEach(e -> {
//            ruleGrantService.deleteRoleGrant(organ, e);
//        });
//    }


    //@Test
    public void testGetPagedGrantByResourceId() {
        String resourceId = "7475be63-ebf2-4763-a4a5-408f58883a34";
        String operator = "7882bc9368014ca6a8ab49bd1ad9ce42";
        String orgId = "448515a6563545758c95d55d5cdf28b9";
        GrantedDetailRequest request = GrantedDetailRequest
                .builder()
                .offset(0)
                .size(20)
                .resourceId(resourceId)
                .ruleGrantStatus("ALL")
                .build();
        GetRuleGrantByResourceIdResponse resourceIdResponse = ruleGrantService.getRuleGrantByResourceId(orgId, operator, request);
        Assert.assertNotNull(resourceIdResponse);

    }

    //@Test
    public void updateRuleGrantsTest() {
        String resourceId = "7475be63-ebf2-4763-a4a5-408f58883a34";
        String operator = "7882bc9368014ca6a8ab49bd1ad9ce42";
        String orgId = "448515a6563545758c95d55d5cdf28b9";
        GrantedDetailRequest grantedDetailRequest = new GrantedDetailRequest();
        grantedDetailRequest.setOffset(0);
        grantedDetailRequest.setResourceId(resourceId);
        grantedDetailRequest.setRuleGrantStatus(RuleGrantStatus.VALID.name());
        grantedDetailRequest.setType(2);
        grantedDetailRequest.setSize(20);
        GetRuleGrantByResourceIdResponse ruleGrantResponse = ruleGrantService.getRuleGrantByResourceId(orgId, operator
                , grantedDetailRequest);
        List<GetRuleGrantByGrantIdResponse> ruleGrantList = ruleGrantResponse.getItems();
        if (CollectionUtils.isEmpty(ruleGrantList)) {
            return;
        }
        List<GetRuleGrantByGrantIdResponse> ruleGrantListFilter = ruleGrantList
                .stream()
                .filter(e -> !Objects.equals("ALL", e.getScope()))
                .collect(Collectors.toList());
        List<BatchRuleItem> ruleGrantedList = new ArrayList<>();
        for (GetRuleGrantByGrantIdResponse ruleGrant : ruleGrantListFilter) {
            BatchRuleItem item = new BatchRuleItem();
            item.setEffectiveTime(DateUtils.getTodayStartTime());
            item.setExpireTime(System.currentTimeMillis() + 30 * 24 * 3600 * 1000l);
            item.setRuleGrantedId(ruleGrant.getRuleGrantId());
            ruleGrantedList.add(item);
            break;
        }

        BatchRuleUpdateRequest request = new BatchRuleUpdateRequest();
        request.setType(2);
        request.setResourceId(resourceId);
        request.setRuleGrantedList(ruleGrantedList);
        ruleGrantService.updateRuleGrants(orgId, operator, request);
    }


    //@Test
    public void bizRuleGrantServiceAdapt(){

        String addRulesParam = "{\"roleKey\":\"SEAL_USER\",\"scope\":\"\",\"scopeName\":\"\",\"autoFall\":true,\"effectiveTime\":1608825600812,\"expireReason\":\"\",\"expireTime\":1640447999812,\"grantedUser\":\"\",\"grantedUserName\":\"苏州新云同泰汽车贸易有限公司\",\"granter\":\"\",\"notifySetting\":true,\"resourceType\":\"SEAL\",\"resourceId\":\"7475be63-ebf2-4763-a4a5-408f58883a34\",\"templateKey\":\"SEAL_AUTH\",\"grantedUserCode\":\"91320508MA1MW1TR1D\",\"grantType\":2,\"scopeList\":[\"HR\"]}";
        AddRulesRequest addRulesRequest = JSONObject.parseObject(addRulesParam, AddRulesRequest.class);
        addRulesRequest.setEffectiveTime(System.currentTimeMillis());
        addRulesRequest.setExpireTime(System.currentTimeMillis() + 10 * 24 * 3600 * 1000l);
        String orgId = "448515a6563545758c95d55d5cdf28b9";
        String operator = "7882bc9368014ca6a8ab49bd1ad9ce42";
        addRulesRequest.setGranter(operator);
        ruleGrantService.addRuleGrants(orgId, addRulesRequest);
        GrantedDetailRequest grantedDetailRequest =  new GrantedDetailRequest();
        grantedDetailRequest.setResourceId("7475be63-ebf2-4763-a4a5-408f58883a34");
        grantedDetailRequest.setRuleGrantStatus(RuleGrantStatus.INVALID.name());
        GetRuleGrantByResourceIdResponse resourceIdResponse = ruleGrantService.getRuleGrantByResourceId(orgId,operator,grantedDetailRequest);
        GetRuleGrantByGrantIdResponse last = resourceIdResponse.getItems().get(resourceIdResponse.getItems().size()-1);
        StartProcessRequest startProcessRequest = new StartProcessRequest();
        startProcessRequest.setPlatform(1);
        startProcessRequest.setApproveTemplateId("1");
        startProcessRequest.setFlowTemplateId("1");
        startProcessRequest.setInitiatorAccountId("1");
        startProcessRequest.setPlatform(1);
        startProcessRequest.setRedirectUrl("1");
        startProcessRequest.setScene(1);
        startProcessRequest.setSignEndTime(1);
        startProcessRequest.setSkipFill(false);
        startProcessRequest.setSolutionNum("");
        startProcessRequest.setTaskName("");
        List<RuleGrantModel> ruleGrantModels = new ArrayList<>();
        RuleGrantModel ruleGrantModel = new RuleGrantModel();
        BeanUtils.copyProperties(last,ruleGrantModel);
        ruleGrantModel.setStatus(RuleGrantStatusEnums.valueOf(last.getStatus()));
        ruleGrantModels.add(ruleGrantModel);
        DeleteRoleGrantResponse deleteRoleGrantResponse = new DeleteRoleGrantResponse();
        deleteRoleGrantResponse.setGrantId(last.getRuleGrantId());

        DeleteRoleGrantResponse response = bizRuleGrantServiceAdapt
                .bizDeletedRuleGrant(orgId,ruleGrantModels.get(0),
                        deleteRoleGrantResponse,systemProperty.getAppId(),systemProperty.getSenderId());
        System.out.println(JSONObject.toJSONString(resourceIdResponse));
    }

    //@Test
    public void bizRuleGrantServiceAdaptAll(){

        String addRulesParam = "{\"roleKey\":\"SEAL_USER\",\"scope\":\"\",\"scopeName\":\"\",\"autoFall\":true,\"effectiveTime\":1608825600812,\"expireReason\":\"\",\"expireTime\":1640447999812,\"grantedUser\":\"\",\"grantedUserName\":\"苏州新云同泰汽车贸易有限公司\",\"granter\":\"\",\"notifySetting\":true,\"resourceType\":\"SEAL\",\"resourceId\":\"7475be63-ebf2-4763-a4a5-408f58883a34\",\"templateKey\":\"SEAL_AUTH\",\"grantedUserCode\":\"91320508MA1MW1TR1D\",\"grantType\":2,\"scopeList\":[\"ALL\"]}";
        AddRulesRequest addRulesRequest = JSONObject.parseObject(addRulesParam, AddRulesRequest.class);
        addRulesRequest.setEffectiveTime(System.currentTimeMillis());
        addRulesRequest.setExpireTime(System.currentTimeMillis() + 10 * 24 * 3600 * 1000l);
        String orgId = "448515a6563545758c95d55d5cdf28b9";
        String operator = "7882bc9368014ca6a8ab49bd1ad9ce42";
        addRulesRequest.setGranter(operator);
        Integer amount = rpcRuleServiceAdapt.getResourceRuleAmount(addRulesRequest.getResourceId());
        if (amount != null && amount >= 10) {
            return;
        }
        ruleGrantService.addRuleGrants(orgId, addRulesRequest);
        GrantedDetailRequest grantedDetailRequest =  new GrantedDetailRequest();
        grantedDetailRequest.setResourceId("7475be63-ebf2-4763-a4a5-408f58883a34");
        grantedDetailRequest.setRuleGrantStatus(RuleGrantStatus.INVALID.name());
        GetRuleGrantByResourceIdResponse resourceIdResponse = ruleGrantService.getRuleGrantByResourceId(orgId,operator,grantedDetailRequest);
        GetRuleGrantByGrantIdResponse last = resourceIdResponse.getItems().get(resourceIdResponse.getItems().size()-1);
        List<GetRuleGrantByGrantIdResponse> items  = resourceIdResponse.getItems();
        StartProcessRequest startProcessRequest = new StartProcessRequest();
        startProcessRequest.setPlatform(1);
        startProcessRequest.setApproveTemplateId("1");
        startProcessRequest.setFlowTemplateId("1");
        startProcessRequest.setInitiatorAccountId("1");
        startProcessRequest.setPlatform(1);
        startProcessRequest.setRedirectUrl("1");
        startProcessRequest.setScene(1);
        startProcessRequest.setSignEndTime(1);
        startProcessRequest.setSkipFill(false);
        startProcessRequest.setSolutionNum("");
        startProcessRequest.setTaskName("");
        List<RuleGrantModel> ruleGrantModels = new ArrayList<>();
        RuleGrantModel ruleGrantModel = new RuleGrantModel();
        BeanUtils.copyProperties(last,ruleGrantModel);
        ruleGrantModel.setStatus(RuleGrantStatusEnums.valueOf(last.getStatus()));
        ruleGrantModels.add(ruleGrantModel);
        DeleteRoleGrantResponse deleteRoleGrantResponse = new DeleteRoleGrantResponse();
        deleteRoleGrantResponse.setGrantId(last.getRuleGrantId());

        DeleteRoleGrantResponse response = bizRuleGrantServiceAdapt
                .bizDeletedRuleGrant(orgId,ruleGrantModels.get(0),
                        deleteRoleGrantResponse,systemProperty.getAppId(),systemProperty.getSenderId());
        Integer deleteCount = ruleGrantService.deleteRuleGrantForTest(null, last.getRuleGrantId());
        System.out.println("deleteCount === " + deleteCount);
        Assert.assertTrue(deleteCount > 0);
        System.out.println(JSONObject.toJSONString(resourceIdResponse));
    }

   // @Test
    public void testRevokeConsumer(){
        Msg msg = new Msg();
        msg.setBody("{\"flowResult\":[\"3e954c7d-9e09-4789-a9bc-ead057e9c9af\",\"a5236088-659d-4a43-90ab-fbd4c52bf0e9\",\"06d4018f-3226-4f3c-996e-903f71fbc89f\",\"b72c8441-3ec6-4bac-91b6-76842f44e21f\",\"22918e1e-931e-4028-aecc-ab057a737688\",\"0499a6e8-b8e7-475f-b568-f8b5b40ac82a\"],\"revokeReason\":\"资源被删除\"}".getBytes());
        List<Msg> msgs =new ArrayList<>();
        msgs.add(msg);
        ruleGrantRevokeFlowsConsumer.receive(msgs);
    }

   // @Test(expected = RpcBaseException.class)
    public void testSaasVipSealGrant() {
        String person = "2a149121528e451598073592debc1f75";
        String granter = "2a149121528e451598073592debc1f75";
        String organ = "21582328137b40828b42d283e52d8806";
        String resourceId = "a5e5d238-20bd-49de-930a-204ad2236964";
        Set<String> scopeList = Sets.newHashSet("ALL");

        AddRulesRequest add = new AddRulesRequest();
        add.setAutoFall(false);
        add.setExpireReason(ExpireReason.NOT_EXPIRE.name());
        add.setNotifySetting(true);
        add.setGrantedUser(person);
        add.setScopeList(scopeList);
        add.setResourceId(resourceId);
        add.setResourceType(ResourceType.SEAL.name());
        add.setGranter(granter);
        add.setExpireTime(System.currentTimeMillis() + 10000);
        add.setTemplateKey(TemplateKeyRegister.SEAL_AUTH.name());
        add.setEffectiveTime(System.currentTimeMillis());
        add.setRoleKey("SEAL_USER");
        add.setH5(true);
        ruleGrantService.addRuleGrants(organ, add);

    }
}
