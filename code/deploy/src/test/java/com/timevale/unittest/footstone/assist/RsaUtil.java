package com.timevale.unittest.footstone.assist;

import com.timevale.open.platform.service.service.api.RSAService;
import com.timevale.open.platform.service.service.model.request.RSAEncryptRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> on 2020-02-12
 */
@Component
public class RsaUtil {

    @Autowired private RSAService rsaService;

    public String encryptByPublic(String userId) {
        // appId = "3876545303"
        String publicSecret = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiZcY7Se01UcdVT0mupqTlCSHYoiCPcMZz8Nbjxe" +
                "/QEpxRRGnjWkg8olKbb0vQrLR5MBCC/eLLlpgVMFr60Q6wUkXLtcLB1rhb6nRpeHYEWazL2o6QqeUM6tA3Rf136bf4" +
                "/Gmpm53UhynzQXXSboaDBwdflAYyt9uN1dYv8oJ4gMEF9vkDnUUoE8L/7fkdZraiBczHAFHeD9" +
                "/hSR2iCwPo5f0XfT2w8arN2cwTCcIvWs6zK3JOPgqUp5PP1dYuaL3bfi3DbTAJR0fc5HWsaZQ5CF3kG7dQbUgJMy79V9DVIlr+n2" +
                "/lzVo0Cl6aQT58f+jMrgye0/eul73BlHO2OG1/wIDAQAB";

        RSAEncryptRequest enRequest = new RSAEncryptRequest();
        enRequest.setPublicKey(publicSecret);
        enRequest.setEncryptContent("{\"userId\":\"" + userId + "\",\"startTime\":" + System.currentTimeMillis() / 1000 +
                ",\"timeout\":86400}");
        String content = rsaService.encryptByPublicKey(enRequest).getData();
        return content;
    }

}
