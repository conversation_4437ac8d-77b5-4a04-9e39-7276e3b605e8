package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.model.RedisUtilRequest;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.rest.QrLoginRest;
import com.timevale.footstone.user.service.rest.RedisRest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021-07-15 20:43
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RedisRestTest {

    @Autowired
    RedisRest redisRest;

    private static String key = UUID.randomUUID().toString().replace("-","");


    @Test
    public void testSetRedisValue(){
        RedisUtilRequest redisUtilRequest =new RedisUtilRequest();
        redisUtilRequest.setTimeUnit(TimeUnit.SECONDS);
        redisUtilRequest.setKey(key);
        redisUtilRequest.setValue("test");
        redisUtilRequest.setExpireTime(300L);
        redisRest.setRedisValue(redisUtilRequest);

    }


    @Test
    public void testGetRedisValue(){
        WrapperResponse<?>  result = redisRest.getRedisValue(key);
        System.out.println(result != null ? result.getData() :" redis not contains key");

    }

}
