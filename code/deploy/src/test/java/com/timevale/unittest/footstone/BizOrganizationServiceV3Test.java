package com.timevale.unittest.footstone;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcIcOrgPlusService;
import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import com.timevale.easun.service.model.account.AdminInfo;
import com.timevale.easun.service.model.account.LegalInfo;
import com.timevale.easun.service.model.organization.ICOrg.output.BizICCreateMemberOutput;
import com.timevale.easun.service.model.organization.output.v3.BizOrgSummaryOutputV3;
import com.timevale.easun.service.model.organization.output.v3.LegalCheckOutput;
import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcThirdOrgConnectorServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizOrganizationServiceV3Impl;
import com.timevale.footstone.user.service.inner.model.v3.ThirdSyncModel;
import com.timevale.footstone.user.service.model.dept.request.v3.BatchCheckLegalRequest;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.privilege.service.model.service.biz.output.BizGetRolesByUsersOutput;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @date 2023/8/31 17:45
 */

@RunWith(MockitoJUnitRunner.Silent.class)
public class BizOrganizationServiceV3Test {

    @InjectMocks
    private BizOrganizationServiceV3Impl bizOrganizationServiceV3;

    @Mock
    private RpcIcOrgPlusServiceAdapt rpcIcOrgPlusServiceAdapt;

    @Mock
    private RpcSaaSVipServiceAdapt rpcSaasVipServiceAdapt;

    @Mock
    private RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdapt;

    @Mock
    private RpcRoleServiceAdapt rpcRoleServiceAdapt;

    @Mock
    private RpcOrgPlusServiceAdapt rpcOrgPlusServiceAdapt;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;


    @Mock
    private RpcIcOrgPlusService icOrgPlusService;

    @Mock
    private IdAdapt idAdapt;


    private static final String LEGAL_CHECK_CACHE_PREFIX = "legal_check_result_";

//    @Value("${batch.check.organ.legal.chche.time:30}")
//    private int legalCheckCacheTime;


    @Mock
    private Tedis tedis;

    @Before
    public void setUp() {
        StringOperations<String, Map<String, Boolean>> stringOperations = new MyDefaultStringOperations();
        Map<String, Boolean> organMap = new HashMap<>();
        organMap.put("orgId1", true);
        stringOperations.set("legal_check_result_memberOid", organMap);
        ReflectionTestUtils.setField(tedis, "valueOps", stringOperations);
        TedisUtil.setTedis(tedis);
        when(tedis.string()).thenReturn(stringOperations);
        when(icUserPlusService.getRealNameByOuid(any())).thenReturn(MockUntils.getRealNameOutput());
        when(icOrgPlusService.createMember(any())).thenReturn(RpcOutput.with(new BizICCreateMemberOutput()));
        BizGetRolesByUsersOutput output = new BizGetRolesByUsersOutput();
        List<BizRoleDetail> roles = new ArrayList<>();
        BizRoleDetail roleDetail = new BizRoleDetail();
        roles.add(roleDetail);
        output.setRoles(roles);
        when(rpcRoleServiceAdapt.getUserRoleListByOrgan(any(), any())).thenReturn(output);
        ReflectionTestUtils.setField(bizOrganizationServiceV3, "legalCheckCacheTime", 10);
        when(rpcIcOrgPlusServiceAdapt.getLegalSummary(any())).thenReturn(MockUntils.mockLegalInfo());
    }

    @Test
    public void checkLegalTest() {
        BatchCheckLegalRequest request = new BatchCheckLegalRequest();
        request.setOrganOids(Lists.newArrayList("orgId1"));
        request.setMemberOid("memberOid");
        bizOrganizationServiceV3.batchAddMemberAndGrantLegal(request);
        when(rpcIcOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(true);

        bizOrganizationServiceV3.batchAddMemberAndGrantLegal(request);
    }

    @Test
    public void batchCheckOrganLegalTest() {
        LegalCheckOutput output = new LegalCheckOutput();
        Map<String, LegalCheckOutput> organMap = new HashMap<>();
        output.setHasOrganLegalRole(true);
        output.setOrganLegal(true);
        output.setOrganLegalFreeze(false);
        organMap.put("orgId1", output);
        when(rpcIcOrgPlusServiceAdapt.batchCheckOrganLegal(any(), any())).thenReturn(organMap);
        BatchCheckLegalRequest request = new BatchCheckLegalRequest();
        request.setOrganOids(Lists.newArrayList("orgId1"));
        request.setMemberOid("memberOid");
        bizOrganizationServiceV3.batchCheckOrganLegal(request);
        output.setOrganLegalFreeze(true);
        when(rpcIcOrgPlusServiceAdapt.batchCheckOrganLegal(any(), any())).thenReturn(organMap);
        bizOrganizationServiceV3.batchCheckOrganLegal(request);
    }

    @Test
    public void hasRelatedOrgRelationshipsTest() {
        when(this.idAdapt.guid("orgId1")).thenReturn("guid1");
        when(this.idAdapt.guid("orgId2")).thenReturn("guid2");
        when(this.rpcSaasVipServiceAdapt
            .getAllRelaOrgIds(anyString(), anyString()))
            .thenReturn(Lists.newArrayList("orgId1", "orgId2"));
        boolean b1 = this.bizOrganizationServiceV3.hasRelatedOrgRelationships("orgId1", "orgId2");
        AssertSupport.assertTrue(b1, new RuntimeException("期望有关联企业关系，但实际没有"));
        boolean b2 = this.bizOrganizationServiceV3.hasRelatedOrgRelationships("orgId1", "noGuid");
        AssertSupport.assertTrue(!b2, new RuntimeException("期望没有关联企业关系，但实际有:b2"));
        boolean b3 = this.bizOrganizationServiceV3.hasRelatedOrgRelationships(null, "orgId2");
        AssertSupport.assertTrue(!b3, new RuntimeException("期望没有关联企业关系，但实际有:b3"));
    }


    @Test
    public void getSummaryTest(){
        BizOrgSummaryOutputV3 orgSummaryOutputV3 = new BizOrgSummaryOutputV3();
        LegalInfo legalInfo = new LegalInfo();
        legalInfo.setEmail("eamil");
        legalInfo.setMobile("130******06");
        legalInfo.setLegalName("name");
        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setName("name");
        orgSummaryOutputV3.setLegalInfo(legalInfo);
        orgSummaryOutputV3.setAdmin(adminInfo);
        when(this.rpcIcOrgPlusServiceAdapt.getOrgSummary(anyString())).thenReturn(orgSummaryOutputV3);
        String vipCode = "vipCode";
        when(this.rpcSaasVipServiceAdapt.getVipCode(anyString())).thenReturn(vipCode);
        bizOrganizationServiceV3.getSummary("orgId");
    }

    @Test
    public void getOrganSummaryTest(){
        bizOrganizationServiceV3.getOrganLegalSummary("orgId");
    }


    @Test
    public void dataSyncOrganTest(){
        String orgId = "orgId";
        Map<String, ThirdSyncModel> thirdSyncModelMap = new HashMap<>();
        ThirdSyncModel syncModel = new ThirdSyncModel();
        syncModel.setPlatformName(ThirdPartyPlatFormEnum.FEI_SHU.getPlatformName());
        syncModel.setOpenThirdSync(false);
        syncModel.setPlatform(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform());
        thirdSyncModelMap.put(orgId,syncModel);
        when(rpcThirdOrgConnectorServiceAdapt.batchGetOrgConnectors(any())).thenReturn(thirdSyncModelMap);
        when(rpcOrgPlusServiceAdapt.getOrganByOid(orgId,null)).thenReturn(MockUntils.mockBizGetOrganOutput());
        bizOrganizationServiceV3.getOrganHasDataSync("orgId");

    }
}
