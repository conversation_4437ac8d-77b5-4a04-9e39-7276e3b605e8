package com.timevale.unittest.footstone.v2.rest;

import com.timevale.footstone.user.service.inner.biz.BizChangeNameService;
import com.timevale.footstone.user.service.inner.impl.biz.changename.enums.ChangeNameChannelEnum;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.mods.changename.ChangeNameUrlContextInfo;
import com.timevale.footstone.user.service.model.account.request.changename.ChangeOrgNameByIdRequest;
import com.timevale.footstone.user.service.model.account.request.changename.GetChangeNameInfoRequest;
import com.timevale.footstone.user.service.model.account.request.changename.GetChangeNameUrlRequest;
import com.timevale.footstone.user.service.model.account.response.changename.ChangeOrgNameByIdResponse;
import com.timevale.footstone.user.service.model.account.response.changename.GetChangeNameInfoResponse;
import com.timevale.footstone.user.service.model.account.response.changename.GetChangeNameUrlResponse;
import com.timevale.footstone.user.service.rest.changename.ChangeNameFlowRest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @DATE 2024/7/2 15:58
 */
@RunWith(MockitoJUnitRunner.class)
public class ChangeNameFlowRestMockTest {
    @InjectMocks
    private ChangeNameFlowRest rest;
    @Mock
    private BizChangeNameService bizChangeNameService;
    public static String changeNameRecordId = "CNR-2vh7s4n60g014";
    public static String appId = "**********";
    public static String appId7488 = "**********";
    public static String expectOrgName = "esigntest芒草改名测试1";
    public static String orgCertNo = "911232132132123123";
    public static String outBizId = "29137e28f5924fc6abc315f2c9cc88ba";

    public static String orgId = "8653bb567a574b6bb51c22e1c3be92a1";

    @Test
    public void getChangeNameUrlTest() {
        GetChangeNameUrlRequest request = new GetChangeNameUrlRequest();
        request.setBizAppId(appId);
        request.setChangeNameChannel(ChangeNameChannelEnum.SIGN.name());
        request.setExpectOrgName(expectOrgName);
        request.setOrgCertNo(orgCertNo);
        request.setOutBizId(outBizId);
        request.setExtendMap(new HashMap<>());
        ChangeNameUrlContextInfo contextInfo = new ChangeNameUrlContextInfo();
        contextInfo.setReturnUrl("https://www.baidu.com/");
        request.setChangeNameUrlContextInfo(contextInfo);

        WrapperResponse<GetChangeNameUrlResponse> response = rest.getChangeNameUrl(request);
        Assert.assertNotNull(response);
    }

    @Test
    public void changeOrgNameByIdTest() {
        ChangeOrgNameByIdRequest request = new ChangeOrgNameByIdRequest();
        request.setChangeNameRecordId(changeNameRecordId);
        WrapperResponse<ChangeOrgNameByIdResponse> response = rest.changeOrgNameById(request);
        Assert.assertNotNull(response);
    }

    @Test
    public void testGetChangeNameInfo() {
        GetChangeNameInfoRequest request = new GetChangeNameInfoRequest();
        request.setChangeNameRecordId(changeNameRecordId);
        WrapperResponse<GetChangeNameInfoResponse> response = rest.getChangeNameInfo(request);
        Assert.assertNotNull(response);
    }
}
