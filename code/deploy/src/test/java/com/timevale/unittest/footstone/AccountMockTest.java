package com.timevale.unittest.footstone;


import com.alibaba.fastjson.JSONObject;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.organization.service.enums.ActivateEnum;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.esearch.input.EsGetMembersByOidInput;
import com.timevale.easun.service.model.organization.OrganAccountDetail;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.configuration.IsolationConfig;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.impl.biz.BizOrganServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoLoginConfigServiceImpl;
import com.timevale.footstone.user.service.rest.AccountRest;
import com.timevale.footstone.user.service.rest.OrganizationRest;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@ContextConfiguration(classes = Application.class)
public class AccountMockTest {

    @InjectMocks
    AccountRest accountRest;


    @InjectMocks
    private BizOrganServiceImpl bizOrganService;

    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

    @Mock
    private IsolationConfig config;

    @Mock
    private CommonConfig commonConfig;

    @Mock
    private BizSsoLoginConfigServiceImpl ssoLoginConfigService;


    @Before
    public void setUp(){
        ReflectionTestUtils.setField(bizOrganService, "icOrgPlusServiceAdapt", icOrgPlusServiceAdapt);
        ReflectionTestUtils.setField(accountRest, "bizOrganService", bizOrganService);
    }

    private void mockGetIcUserOrganlist() {
        PagerResult<BizOrgBaseInfoOutput> pagerResult = new PagerResult<>();
        List<BizOrgBaseInfoOutput> baseInfoOutputs = new ArrayList<>();
        BizOrgBaseInfoOutput baseInfo = new BizOrgBaseInfoOutput();
        baseInfo.setActivate(ActivateEnum.ACTIVATED.getCode());
        BizICUserOutput bizICUserOutput = new BizICUserOutput();
        AccountBaseDetail baseDetail =new AccountBaseDetail();
        baseDetail.setProjectApp(new ProductApp());
        baseDetail.setAccountUid("accountUid");
        bizICUserOutput.setBase(baseDetail);
        ICUserId id = new ICUserId();
        id.setOuid("oid");
        id.setUuid("uid");
        id.setUuid("uid");
        bizICUserOutput.setId(id);
        List<Property> properties = new ArrayList<>();
        Property name = new Property();
        name.setType(INFO_NAME);
        name.setValue("name");
        properties.add(name);
        bizICUserOutput.setProperties(properties);
        baseInfo.setAccount(bizICUserOutput);
        baseInfoOutputs.add(baseInfo);
        baseInfo.setRoleDetailList(new ArrayList<>());
        pagerResult.setTotal(baseInfoOutputs.size());
        pagerResult.setItems(baseInfoOutputs);
        when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(),any())).thenReturn(pagerResult);
    }

    @Test
    public void orgRolesTest(){
        mockGetIcUserOrganlist();
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"192.168.*.*");
        ReflectionTestUtils.setField(accountRest,"servletRequest",servletRequest);
        when(commonConfig.getUserIpHeaderName()).thenReturn(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP);
        accountRest.listOrgAndRole("account",0,20);
        when(config.isolationSwitch(any())).thenReturn(true);
//        when(config.isolationSwitch()).thenReturn(true);
        accountRest.listOrgAndRole("account",0,20);
    }


    @Test
    public void listSaasOrgsTest(){
        mockGetMemberOrganList();
        accountRest.listSaasOrgs("account",500,0,true);
    }

    private void mockGetMemberOrganList(){
        OrganAccountDetail organAccountDetail = new OrganAccountDetail();
        List<OrganAccountDetail> organInfos = new ArrayList<>();
        organInfos.add(organAccountDetail);
        PagerResult<OrganAccountDetail> pagerResult = new PagerResult<>();
        pagerResult.setItems(organInfos);
        when(icOrgPlusServiceAdapt.getMemberOrganList(any())).thenReturn(pagerResult);
    }
}
