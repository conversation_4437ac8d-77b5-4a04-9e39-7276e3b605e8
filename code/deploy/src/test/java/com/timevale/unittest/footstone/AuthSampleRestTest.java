package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.rest.AuthSampleRest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * Author: liujiaxing
 * Date: 2022/6/29 10:07 上午
 * Description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AuthSampleRestTest {

    private final String orderNo="test_order";

    @Resource
    AuthSampleRest authSampleRest;

    @Test
    public void scope() {
        try {
            authSampleRest.scope();
        }catch (Exception e){
        }
    }

    @Test
    public void scopeAndOid() {
        try {
            authSampleRest.scopeAndOid(orderNo);
        }catch (Exception e){
        }
    }

    @Test
    public void scopeAndSubjectOid() {
        try {
            authSampleRest.scopeAndSubjectOid(orderNo);

        }catch (Exception e){
        }
    }
}
