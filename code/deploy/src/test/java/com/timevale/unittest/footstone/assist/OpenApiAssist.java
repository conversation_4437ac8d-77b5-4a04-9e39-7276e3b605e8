package com.timevale.unittest.footstone.assist;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.OrgAddByIdNoRequest;
import com.timevale.footstone.user.service.model.account.request.OrgAddByUserIdRequest;
import com.timevale.footstone.user.service.model.account.request.OrgCreateByThirdPartyUserId;
import com.timevale.footstone.user.service.model.account.request.PersonAddByCredRequest;
import com.timevale.footstone.user.service.model.account.request.PersonAddByIdRequest;
import com.timevale.footstone.user.service.model.account.request.PersonAddByIdV1Request;
import com.timevale.footstone.user.service.model.account.response.AccountBaseResponse;
import com.timevale.footstone.user.service.model.account.response.OrgGetResponse;
import com.timevale.footstone.user.service.model.account.response.PersonGetResponse;
import com.timevale.footstone.user.service.model.account.response.PersonGetV1Response;
import com.timevale.footstone.user.service.rest.OpenRest;
import com.timevale.footstone.user.service.utils.ResponseUtil;
import com.timevale.unittest.footstone.tc.UTCGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * <AUTHOR> on 2019-08-01
 */
@Component
public class OpenApiAssist {

    @Autowired
    private OpenRest rest;

    public String createEmptyUser() {
        PersonAddByCredRequest request = new PersonAddByCredRequest();
        request.setName("xxx");

        return createUser(request);
    }

    public String createUserWithCred() {
        PersonAddByCredRequest request = new PersonAddByCredRequest();
        request.setName("xxx");

        request.setIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setIdNumber(UTCGenerator.utcPsnIdcard());

        return createUser(request);
    }

    private String createUser(PersonAddByCredRequest request) {
        WrapperResponse<AccountBaseResponse> resp = rest.createUserByCredentials(request);
        AccountBaseResponse data = ResponseUtil.get(resp);
        return data.getAccountId();
    }

    public PersonGetResponse getUser(String ouid) {
        WrapperResponse<PersonGetV1Response> resp = rest.getUserByAccountId(ouid);
        return ResponseUtil.get(resp);
    }

    public OrgGetResponse getOrg(String ouid) {
        WrapperResponse<OrgGetResponse> resp = rest.getOrgByAccountId(ouid);
        return ResponseUtil.get(resp);
    }

    public String createUserWithThirdId(String userId) {
        PersonAddByIdV1Request request = new PersonAddByIdV1Request();
        request.setThirdPartyUserId(userId);
        request.setName("xxx");
        return ResponseUtil.get(rest.createUserByUserId(request)).getAccountId();
    }



    public String createOrgWithThirdId(String creator, String userId) {
        OrgCreateByThirdPartyUserId request = new OrgCreateByThirdPartyUserId();
        request.setCreator(creator);
        request.setThirdPartyUserId(userId);
        request.setName("xxx" + UUID.randomUUID().toString().replace("-", "").substring(0, 15));
        return ResponseUtil.get(rest.createOrgByThirdPartyUserId(request)).getOrgId();
    }

    public String createOrgWithCred(String creator, String idType, String idNumber) {
        OrgAddByIdNoRequest request = new OrgAddByIdNoRequest();
        request.setCreator(creator);
        request.setName("xxx");
        request.setIdType(idType);
        request.setIdNumber(idNumber);
        request.setOrgLegalName("ddd");
        return ResponseUtil.get(rest.createOrgByIdNo(request)).getOrgId();
    }
}
