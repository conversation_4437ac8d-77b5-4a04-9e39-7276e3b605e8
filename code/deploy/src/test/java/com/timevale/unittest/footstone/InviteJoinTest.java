package com.timevale.unittest.footstone;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.enums.JoinOrgApplyStatusEnum;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.biz.BizTInviteService;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.invite.mods.InviteJoinPerson;
import com.timevale.footstone.user.service.model.invite.mods.InvitePassedJoinMember;
import com.timevale.footstone.user.service.model.invite.request.ApplyJoinOrganRequest;
import com.timevale.footstone.user.service.model.invite.request.ApplyJoinRequest;
import com.timevale.footstone.user.service.model.invite.request.BatchInviteJoinRequest;
import com.timevale.footstone.user.service.model.invite.request.BatchPassedJoinReq;
import com.timevale.footstone.user.service.model.invite.response.ApplyJoinOrganResponse;
import com.timevale.footstone.user.service.model.invite.response.ApplyJoinResultResponse;
import com.timevale.footstone.user.service.model.invite.response.InviteJoinListRes;
import com.timevale.footstone.user.service.rest.TInviteRest;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2019年12月11日 15:01:00
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class InviteJoinTest {
    @Autowired BizTInviteService bizTInviteService;

    public static String orgId = "dace182f80f54a56afb2812e0c15ec99";
    public static String operatorId = "974a2665419d4d3c817780fb299c681f";
    @Autowired
    BizMemberService memberService;

    @Autowired
    TInviteRest rest;

    @Before
    public void init() {
        // 审计日志初始化请求头参数
        HttpServletRequest httpServletRequest = new MyTestHttpServletRequest();
        httpServletRequest.setAttribute(WeaverConstants.APP_ID_LABEL,"**********");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_REAL_IP,"127.0.0.1");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"d6a34fcf82774b8ba34549a3fe103113");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"cd7ee89e84bc458e99dd13c1e1745345");
        RequestContext.put(WeaverConstants.REQUEST_KEY,httpServletRequest);
        AuditLogCommon.initAuditLogContext();
    }

    @Test
    public void batchInviteJoin() {
        BatchInviteJoinRequest request = new BatchInviteJoinRequest();
        List<InviteJoinPerson> joinPersonList = new ArrayList<>();
        InviteJoinPerson inviteJoinPerson1 = new InviteJoinPerson();
        inviteJoinPerson1.setMobile("***********");
        inviteJoinPerson1.setMemberInfo(new HashMap<>());
        joinPersonList.add(inviteJoinPerson1);

        InviteJoinPerson inviteJoinPerson2 = new InviteJoinPerson();
        inviteJoinPerson2.setMemberInfo(new HashMap<>());
        inviteJoinPerson2.setMobile("14000000309");

        InviteJoinPerson inviteJoinPerson3 = new InviteJoinPerson();
        Map<String,String> memInfo = new HashMap<>();
        memInfo.put("memberName","tt");
        inviteJoinPerson3.setMemberInfo(new HashMap<>());
        inviteJoinPerson3.setMobile("14000001002");
        joinPersonList.add(inviteJoinPerson3);
        request.setInvitedPersons(joinPersonList);
        try {

            bizTInviteService.bachInviteJoin(request, orgId, operatorId);
            memberService.deleteMember(orgId,"c7790f02d1404fd783c026c52832e307");
        } catch (Exception e) {

        }
    }

//    @Test
//    public void batchInviteJoin2() {
//        BatchInviteJoinRequest request = new BatchInviteJoinRequest();
//        List<InviteJoinPerson> joinPersonList = new ArrayList<>();
//        InviteJoinPerson inviteJoinPerson1 = new InviteJoinPerson();
//        inviteJoinPerson1.setMobile("***********");
//        inviteJoinPerson1.setMemberInfo(new HashMap<>());
//        request.setInvitedPersons(joinPersonList);
//
//        joinPersonList.add(inviteJoinPerson1);
//        rest.bachInviteJoin(orgId,request);
//    }

    @Test
    public void applyJoinOrg() {
        String joinOrg = "5f9c84ffd2fc4926876d99c84281d6c6";
        String serviceId = "20277e7d43a04cd8b9f5661fca5084bd";
        String accountId = "88e44a1ebef441d69306d2abcc0eb276";

        try {
            ApplyJoinOrganRequest request = new ApplyJoinOrganRequest();
            request.setServiceId(serviceId);
            request.setAccountId(accountId);
            bizTInviteService.applyJoinOrg(joinOrg, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void queryInviteJoinMembers() {
        try {
            InviteJoinListRes res =
                    bizTInviteService.getInviteJoinList(
                            orgId, "ALL", null, 0, 10, null, null, null);
            res.getTotal();
            bizTInviteService.getInviteJoinList(
                    orgId, "109ea5ab82434f76b762d9f93921f8ef", null, 0, 10, null, null, null);
        } catch (Exception e) {

        }
    }

    @Test
    public void batchAccept() {
        BatchPassedJoinReq req = new BatchPassedJoinReq();
        List<InvitePassedJoinMember> passedJoinMembers = new ArrayList<>();
        InvitePassedJoinMember member = new InvitePassedJoinMember();
        member.setInviteId("dace182f80f54a56afb2812e0c15ec99");
        member.setInviteeAccount("37Ux9BNrOW0iE");
        passedJoinMembers.add(member);
        req.setInvitedPersons(passedJoinMembers);

        try {
            bizTInviteService.bachPassedJoin(req, "dace182f80f54a56afb2812e0c15ec99");
        } catch (Exception e) {

        }
    }

    @Test
    public void applyJoin() {
        ApplyJoinRequest req = new ApplyJoinRequest();
        req.setAccountId("f5af9eaef5f24ac2ab8efce3b92a24b3");
        try {
            RequestContext.put("X-Tsign-Open-App-Id", "**********");
            rest.applyJoinOrg("dace182f80f54a56afb2812e0c15ec99", req);
            req.setAccountId("055580ea596b47099ec4e50399021951");
            WrapperResponse<ApplyJoinOrganResponse> response = rest.applyJoinOrg("dace182f80f54a56afb2812e0c15ec99", req);
            WrapperResponse<ApplyJoinResultResponse> joinResult = rest.applyJoinResult(response.getData().getServiceId(), false);
            Assert.assertTrue("加入企业失败", JoinOrgApplyStatusEnum.FAIL.equals(joinResult.getData().getJoinResult()));
            joinResult = rest.applyJoinResult(response.getData().getServiceId(), true);
        } catch (Exception e) {
            log.warn("applyJoin failed", e);
        }
    }


}
