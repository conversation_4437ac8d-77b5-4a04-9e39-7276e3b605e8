package com.timevale.unittest.footstone;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.inner.biz.BizCostStrategyService;
import com.timevale.footstone.user.service.model.cost.strategy.response.CostStrategieResponse;
import com.timevale.footstone.user.service.model.cost.strategy.response.GetOrgCostStrategyResponse;
import com.timevale.footstone.user.service.model.cost.strategy.response.GetOrgProductResponse;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/16 14:25
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CostStrategyTest {

    @Autowired
    private BizCostStrategyService service ;

    private String orgId = "12864e6a00c54728a2850212f4a4ca87";

    private String accountId = "88e44a1ebef441d69306d2abcc0eb276";

    @Test
    public void testStrategy() {
        CostStrategieResponse types = service.getStrategyList();
        Assert.assertTrue(types.getStrategies().size() > 0);
        service.updateOrgStrategy(orgId,1,accountId);
        GetOrgCostStrategyResponse get = service.getStrategyByOrgId(orgId);
        Assert.assertTrue(get.getStrategyKey() == 1);
    }

    @Test
    public void getProducts(){
        try{
            GetOrgProductResponse products = service.getOrgProductByOrgId(orgId);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}
