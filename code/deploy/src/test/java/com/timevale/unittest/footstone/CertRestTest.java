/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertRestTest.java <br/>
 * 包：com.timevale.unittest.footstone <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2020/6/1218:07]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizCertService;
import com.timevale.footstone.user.service.model.cert.response.CertInfo;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 类名：CertRestTest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2020/6/1218:07]创建类 by flh
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CertRestTest {

    private String certId = "4df8af3c-2db0-4782-96ba-768a2a49e8cb";

    @Autowired
    private BizCertService bizCertService;

    @Test
    public void testQuery(){
        RequestContext.put("X-Tsign-Open-App-Id","1111563786");
        CertInfo certInfo = bizCertService.queryCert(certId);
        Assert.assertNotNull(certInfo);
    }
}