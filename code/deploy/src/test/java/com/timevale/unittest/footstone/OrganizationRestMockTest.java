package com.timevale.unittest.footstone;

import static com.timevale.footstone.user.service.constants.HeaderConstants.HEADER_OPERATOR_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.timevale.account.service.api.RpcICUserService;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.easun.service.api.RpcIcOrgPlusService;
import com.timevale.easun.service.api.RpcIcUserPlusService;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.footstone.user.service.enums.ResultEnum;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.impl.AppIdPrivilegeServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.response.RealNameOrganResponse;
import com.timevale.footstone.user.service.rest.OrganizationRest;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;

/**
 * <AUTHOR>
 * @version 2024/6/28 11:08
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrganizationRestMockTest {


    @InjectMocks
    private OrganizationRest organizationRest;


    @Mock
    private BizOrganService bizOrganService;

    @Mock
    private RpcOrgPlusService rpcOrgPlusService;

    @Mock
    private RpcIcUserPlusService icUserPlusService;

    @Mock
    private HttpServletRequest servletRequest;

    @Mock
    private RpcICUserService icUserSvc;

    @Mock
    private DecorationWalker walker;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Mock
    private AppIdPrivilegeServiceImpl appIdPrivilegeService;

    @Mock
    private RpcIcOrgPlusService icOrgPlusService;

    @Mock
    private SystemProperty systemProperty;

    @Mock
    private BizMemberService memberService;

    @Mock
    private BizRoleService bizRoleService;


    @Before
    public void setUp() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader(HEADER_OPERATOR_ID, "operator");
        when(bizOrganService.getRealOrganInfo(any())).thenReturn(
                MockUntils.mockRealNameOrganResponse());
        RequestContext.put(HEADER_OPERATOR_ID, "operator");
    }

    @Test
    public void testDeleteOrg() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader(HEADER_OPERATOR_ID, "operator");
        RealNameOrganResponse realNameOrganResponse = MockUntils.mockRealNameOrganResponse();
        realNameOrganResponse.setActivate(1);
        realNameOrganResponse.setStatus(RealnameStatus.INIT);
        when(bizOrganService.getRealOrganInfo(any())).thenReturn(realNameOrganResponse);
        RequestContext.put("httpServletRequest", request);
        when(memberService.checkMemberOrCreator(any(), any())).thenReturn(false);
        WrapperResponse<?> notMember = organizationRest.deleteOrg("orgId");
//        Assert.assertTrue(
//                Objects.equals(notMember.getCode(), ResultEnum.NOT_ALLOW_OPERATOR.getCode()));
        when(memberService.checkMemberOrCreator(any(), any())).thenReturn(true);
        when(bizOrganService.checkOrgCreator(any(), any())).thenReturn(true);
        when(bizRoleService.checkIfIsAdminOrLegal(any(), any())).thenReturn(true);
        WrapperResponse<?> delete = organizationRest.deleteOrg("orgId");
//        Assert.assertTrue(Objects.equals(delete.getCode(), ResultEnum.SUCCESS.getCode()));
    }


}
