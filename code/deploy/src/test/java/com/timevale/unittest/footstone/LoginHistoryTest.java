package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.rest.AccountRest;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年08月06日 17:30:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class LoginHistoryTest {

    @Autowired
    private AccountRest rest;

    @Test
    public void loginHistoryTest(){
        rest.loginHistory("974a2665419d4d3c817780fb299c681f",1,10);
    }


}
