package com.timevale.unittest.footstone;

import com.timevale.easun.service.api.RpcIcOrgPlusService;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.rest.AccountRest;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.timevale.account.service.model.constants.BuiltinCredentials.CRED_ORG_USCC;

/**
 * <AUTHOR> on 2019-08-12
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class RegisterTest {
    @Autowired
    private RpcAccountPlusServiceAdapt accountPlusServiceAdapt;
    @Autowired
    private AccountRest rest;
    @Autowired
    private RpcOrgPlusServiceAdapt rpcOrgPlusServiceAdapt;
    @Autowired
    private RpcIcOrgPlusService svc;

    @Autowired
    private IdAdapt idAdapt;

    @Test
    public void test() {

        try {
            String oid = accountPlusServiceAdapt.createMobileThridparty("159********", "WE_CHAT", "************");
            rest.deleteAccount(oid);
            String organId = rpcOrgPlusServiceAdapt.createOrgAndCompany("f17bd9bf11b749cd98577fe81797e112", CRED_ORG_USCC,
                    "91011719900407751N", "********单元测试企业");

            svc.deleteOrg(new RpcInput<>(new BizOrganOuid(idAdapt.organIdToOrgId(organId))));
        }catch (Exception e){
        }

//        bizRegisterInput.setRegisterSource(CreateSourceUtil.genCreateSourceStr(appId,
//                RequestUtil.getClientId(RequestContext.getRequest())));
    }
}
