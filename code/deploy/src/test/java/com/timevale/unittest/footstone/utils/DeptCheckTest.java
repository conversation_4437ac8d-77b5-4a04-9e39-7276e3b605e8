package com.timevale.unittest.footstone.utils;

import com.timevale.footstone.user.service.utils.DeptCheck;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 2024/6/18 16:06
 */
public class DeptCheckTest {




    @Test
    public void checkNameTest(){
        String deptName = "  带空格  测试 ";
        Assert.assertTrue(DeptCheck.checkName(deptName));
        deptName = "不带空格";
        Assert.assertTrue(DeptCheck.checkName(deptName));
        deptName = "带特殊字符！@##¥";
        Assert.assertFalse(DeptCheck.checkName(deptName));
        deptName = "带\\x20特殊字符";
        Assert.assertFalse(DeptCheck.checkName(deptName));
    }

}
