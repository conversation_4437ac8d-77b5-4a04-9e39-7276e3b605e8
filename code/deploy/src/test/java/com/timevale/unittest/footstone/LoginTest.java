package com.timevale.unittest.footstone;
import com.google.common.collect.Maps;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.login.request.SandBoxLoginRequest;
import com.timevale.footstone.user.service.model.account.login.response.TokenInvalidateCauseResponse;
import com.timevale.footstone.user.service.model.eshieldlogin.EShieldLoginRequest;
import com.timevale.footstone.user.service.rest.LoginRest;
import com.timevale.footstone.user.service.utils.ResponseUtil;
import com.timevale.footstone.user.service.utils.conv.LoginConverter;
import com.timevale.token.service.enums.LoginType;
import com.timevale.token.service.model.output.AuthBasicLoginOutput;
import com.timevale.unittest.footstone.assist.LoginAssist;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;

/** <AUTHOR> on 2019-09-02 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LoginTest {

    @Autowired private LoginRest rest;

    @Autowired private LoginAssist assist;

    //todo test delete
    //@Test
    public void testDuplicateLogin() {
        // 先登录一次
        String token1 = assist.getTokenByPwd("***********", "5d93ceb70e2bf5daa84ec3d0cd2c731a");
        WrapperResponse<TokenInvalidateCauseResponse> cause1 = rest.validateTokenCause(token1);
        Assert.assertEquals("ACTIVE", ResponseUtil.get(cause1).getStatus());

        // 再登录一次
        String token2 = assist.getTokenByPwd("***********", "5d93ceb70e2bf5daa84ec3d0cd2c731a");
        WrapperResponse<TokenInvalidateCauseResponse> cause2 = rest.validateTokenCause(token2);
        Assert.assertEquals("ACTIVE", ResponseUtil.get(cause2).getStatus());

        // 第一次登录应当由于重复而失效
        WrapperResponse<TokenInvalidateCauseResponse> cause3 = rest.validateTokenCause(token1);
        TokenInvalidateCauseResponse resp = ResponseUtil.get(cause3);
        Assert.assertEquals("REPEAT_LOGIN", resp.getStatus());
        Assert.assertEquals("***********", resp.getAccount());
    }

    @Test
    public void sandboxLogin(){
        SandBoxLoginRequest request = new SandBoxLoginRequest();
        request.setAccessKey("123456");
        request.setOuid("459de21fc4b448f19ab5380bed2a92f4");
        rest.sandBoxLogin(request);
    }

    @Test public void certLogin(){
        EShieldLoginRequest request = new EShieldLoginRequest();
        request.setOriginData("MTIzNDU2Nzg=");
        request.setSignData("jfoKnbdz38ZvZheBQuo5rvieGpciWeanLs/y9o6825Tz2eBCHx11YE/MAsBh06UF6qWBZ7ICSNQLDATD3L2xIHXW6lASkqoO+9LG/yXZaXbPrQO/t0ywSjcA3cKvoB7X7fsiJgQO03DhWg1blgwc7z8AocOyTPL6l6j/h9Ezs1RF/qCRO1enjJWk+yUqlg0r0roTu+1wKvnlHT1lCDDton7ac9i0Pc9Nx9cPL/7DwJV+NSzZHHFGezr4D3ZQqhSkj42hJ4hbRW60W1DjrUjyCt/ZOP9sBL157dlb2UQithzOlPvX0a+sVKxYQoLtetroQxXa+hkOUzL+swgFBhJwyg==");
        request.setCertId("yichen_SHA1withRSA_certID");
        request.setAlgorithm("SHA1withRSA");
        try {
            rest.eShieldLogin(request);
        } catch (Exception e){

        }
    }

    @Test
    public void conv() {
        AuthBasicLoginOutput authBasicLoginOutput = new AuthBasicLoginOutput();
        authBasicLoginOutput.setEquipId("");
        authBasicLoginOutput.setLoginUser("");
        authBasicLoginOutput.setSourceCode("");
        authBasicLoginOutput.setAccountUid("");
        authBasicLoginOutput.setAccountId("");
        authBasicLoginOutput.setProjectId("");
        authBasicLoginOutput.setProductKey("");
        authBasicLoginOutput.setProductAppId("");
        authBasicLoginOutput.setLoginType(LoginType.PASSWORD_LOGIN);
        authBasicLoginOutput.setToken("");
        authBasicLoginOutput.setExpireTime(0);
        authBasicLoginOutput.setExpireDate(new Date());
        authBasicLoginOutput.setProperties(Maps.newHashMap());
        LoginConverter.conv(authBasicLoginOutput);
    }


    @Test
    public void logoutUser() {

        String accountIdByMobile = "1e3b4a0585b94972a06772fb26961b00";
        rest.logoutUser(accountIdByMobile);
    }
}
