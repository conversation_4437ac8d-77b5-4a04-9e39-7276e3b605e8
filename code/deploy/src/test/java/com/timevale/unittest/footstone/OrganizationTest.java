package com.timevale.unittest.footstone;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.google.common.collect.Sets;
import com.timevale.account.service.exception.Errors;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.enums.OrganConfigCategoryEnum;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.flow.FlowAdapter;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.mods.OrgProperties;
import com.timevale.footstone.user.service.model.account.request.GetOrganConfigRequest;
import com.timevale.footstone.user.service.model.account.request.GetOrganPageUrlRequest;
import com.timevale.footstone.user.service.model.account.request.OrgCreateRequest;
import com.timevale.footstone.user.service.model.account.request.OrgUpdateRequest;
import com.timevale.footstone.user.service.model.account.response.CreateOrgResponse;
import com.timevale.footstone.user.service.model.account.response.UserInfoResponse;
import com.timevale.footstone.user.service.model.organization.model.BatchOrgRealnameModel;
import com.timevale.footstone.user.service.model.organization.model.OrganRealnameRequest;
import com.timevale.footstone.user.service.model.organization.request.OrgUpdateLegalRequest;
import com.timevale.footstone.user.service.model.organization.request.OrgUpdateNameRequest;
import com.timevale.footstone.user.service.model.organization.request.OrganRealnameListRequest;
import com.timevale.footstone.user.service.model.organization.request.PreDeleteOrganRequest;
import com.timevale.footstone.user.service.model.organization.response.OrganBaseExtendResponse;
import com.timevale.footstone.user.service.rest.OrganizationRest;
import com.timevale.footstone.user.service.rest.RealnameRest;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.privilege.authenticate.exception.PrivilegeAuthenticateRejectException;
import com.timevale.unittest.footstone.configuration.Application;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年03月17日 16:05:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OrganizationTest {

    @Autowired
    OrganizationRest organizationRest;

    @Autowired
    RealnameRest realnameRest;
    String accountId = "9ac6a3400744441e85b6dd24a2d2699b";
    String orgId = "a6a191d598504dd8af7d6269274fc51a";
    String legalRepName = "张三";
    String legalRepCertNo = "342222199001013040";
    String orgName = "测试企业99";
    @Autowired
    private FlowAdapter flowAdapter;
    @Autowired
    private BizOrganService bizOrganService;
    @Autowired
    private OrganizationRest rest;

    @Autowired
    CommonConfig commonConfig;

    @Test
    public void orgSearchTest() {
        organizationRest.searchOrgNew(
                "esigntest测试企业(一) ", null, null, false, null, null, false, 0, 10);

        organizationRest.searchOrgNew("esigntest ", null, null, false, null, null, false, 0, 10);
    }

    @Test
    public void orgSearchWithIsStandardLabelTest() {
        organizationRest.searchOrg(
                "子烨邀请企业测试企业", null, null, false, "f956657324e44fa2afa3668a180ac163", false, false, 0, 10, true,null);
    }

    @Test
    public void getOrgTest() {
        try {
            WrapperResponse<UserInfoResponse> res =
                    organizationRest.getOrg("8af82bef9d7141c2b20c6e8b44800e22");
            res.getData().getHeadImgUrl();
        } catch (Exception e) {

        }
    }

    @Test
    public void updateLegal() {
        try {
            OrgUpdateLegalRequest request = new OrgUpdateLegalRequest();
            request.setAccountId(accountId);
            request.setLegalRepName(legalRepName);
            request.setLegalRepCertNo(legalRepCertNo);
            rest.updateLegal(orgId, request);
        } catch (Exception e) {

        }

    }

    @Test
    public void updateOrgName() {
        try {
            OrgUpdateNameRequest request = new OrgUpdateNameRequest();
            request.setAccountId(accountId);
            request.setOrgName(orgName);
            rest.updateOrgName(orgId, request);
        } catch (Exception e) {

        }

    }

    @Test
    public void preDelete() {
        try {
            Map<String, Object> context = new HashMap<>();
            context.put("X-Tsign-Open-App-Id", "**********");
            RequestContext.setContextMap(context);
            PreDeleteOrganRequest request = new PreDeleteOrganRequest();
            request.setAccountId(accountId);
            request.setRemark("注销");
            rest.preDelete(orgId, request);
        } catch (Exception e) {

        }
    }

    @Test
    public void deleteResult() {
        try {
            String serviceId = "db29d190a3cd41b4995e41855bae1e0d";
            rest.deleteResult(serviceId);
        } catch (Exception e) {

        }

    }

    @Test
    public void getFlowStatus() {
        try {
            String flowId = "8e00a6bb0574427991022f77e56ec284";
            flowAdapter.queryFlowStatus(flowId);
        } catch (Exception e) {

        }
    }

    @Test
    public void createOrg() {
        try {
            OrgCreateRequest request = new OrgCreateRequest();
            request.setCreater("88e44a1ebef441d69306d2abcc0eb276");
            OrgProperties properties = new OrgProperties();
            properties.setName("浙江优创信息技术有限公司");
            request.setProperties(properties);
            WrapperResponse<CreateOrgResponse> response = rest.create(request);
            rest.deleteOrg(response.getData().getOrgId());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }



    @Test
    public void getOrgsByNameOrCode(){
        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        organizationRest.getOrgsByNameOrCode("田东县思林镇黄华南养殖农民专业合作社","93451022330769904E"
                ,"**********","");
    }

    @Test
    public void getRealNamedOrgByCode(){
        try {
            RequestContext.put("X-Tsign-Open-App-Id", "**********");
            organizationRest.getRealNamedOrgByCode("93451022330769904E");
        }catch (Exception e ){

        }

    }



    @Test
    public void getPageUrlTest(){
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);

        RequestContext.put("X-Tsign-Open-App-Id", "**********");
        GetOrganPageUrlRequest request = new GetOrganPageUrlRequest();
        request.setOrgId(orgId);
        request.setToken("");

        // 获取H5地址
        request.setH5(true);
        organizationRest.getUrl(request);

        // 获取PC地址
        request.setH5(false);
        organizationRest.getUrl(request);
    }
    @Test
    public void queryOrgsRealName() {
        OrganRealnameListRequest initalRequest = buildNullArgRequest();

        //test query appId privilege
        try {
            bizOrganService.queryOrganRealname(initalRequest);
        } catch (FootstoneUserErrors.AppNotAlowedQueryOrgRealException e) {
            AssertUtil.isTrue(true, "无权限查询");
        }
        //test query argments
        RequestContext.put("X-Tsign-Open-App-Id", "testAppId");
        try {
            bizOrganService.queryOrganRealname(initalRequest);
        } catch (FootstoneUserErrors.QueryOrgRealnameArgsError e) {
            AssertUtil.isTrue(true, "查询企业参数校验");
        }

        // 查询数量
        OrganRealnameRequest requestModel = new OrganRealnameRequest();
        requestModel.setName("testOrgName");
        //todo
        for (int i = 0; i < 301; i++) {
            initalRequest.getList().add(requestModel);
        }
        try {
            bizOrganService.queryOrganRealname(initalRequest);
        } catch (FootstoneUserErrors.QueryOrgRealnameLimitExection e) {
            AssertUtil.isTrue(true, "查询企业参数校验");
        }

        // 只查询企业名称校验 ,一个实名，一个非实名
        String unRealOrgName1 = "testOrgName";
        String realOrgName1 = "esigntest邀请测试企业11";
        initalRequest.getList().clear();
        requestModel.setName(unRealOrgName1);
        initalRequest.getList().add(requestModel);
        OrganRealnameRequest requestModel2 = new OrganRealnameRequest();
        requestModel2.setName(realOrgName1);
        initalRequest.getList().add(requestModel2);
        List<BatchOrgRealnameModel> result = bizOrganService.queryOrganRealname(initalRequest);
        AssertUtil.isTrue(result.size() == 2, "查询返回结果数量");
        BatchOrgRealnameModel realResultModel = result.stream().filter(i -> StringUtils.equals(i.getName(), realOrgName1)).findFirst().get();
        AssertUtil.isTrue(realResultModel.getRealname() == true, "查询实名结果");
        BatchOrgRealnameModel unRealResultModel = result.stream().filter(i -> StringUtils.equals(i.getName(), unRealOrgName1)).findFirst().get();
        AssertUtil.isTrue(unRealResultModel.getRealname() == false, "查询非实名结果");


        // 只查询企业证件号
        String realCode1 = "913101133509915711";
        String unRealCode1 = "test-unreal-code";
        initalRequest.getList().clear();
        requestModel.setName(null);
        requestModel.setCode(realCode1);
        initalRequest.getList().add(requestModel);
        requestModel2.setName(null);
        requestModel2.setCode(unRealCode1);
        initalRequest.getList().add(requestModel2);
        result = bizOrganService.queryOrganRealname(initalRequest);
        realResultModel = result.stream().filter(i -> StringUtils.equals(i.getCode(), realCode1)).findFirst().get();
        AssertUtil.isTrue(realResultModel.getRealname() == true, "查询实名结果");
        unRealResultModel = result.stream().filter(i -> StringUtils.equals(i.getCode(), unRealCode1)).findFirst().get();
        AssertUtil.isTrue(unRealResultModel.getRealname() == false, "查询非实名结果");

        // 查询既有名称和非名称
        //第一个是实名的
        initalRequest.getList().clear();
        requestModel.setCode(realCode1);
        requestModel.setName(realOrgName1);
        initalRequest.getList().add(requestModel);
        requestModel2.setName(realOrgName1);
        requestModel2.setCode(unRealCode1);
        initalRequest.getList().add(requestModel2);
        result = bizOrganService.queryOrganRealname(initalRequest);
        realResultModel = result.stream().filter(i -> StringUtils.equals(i.getCode(), realCode1)).findFirst().get();
        AssertUtil.isTrue(realResultModel.getRealname() == true, "查询实名结果");
        unRealResultModel = result.stream().filter(i -> StringUtils.equals(i.getCode(), unRealCode1)).findFirst().get();
        AssertUtil.isTrue(unRealResultModel.getRealname() == false, "查询非实名结果");

        Assert.assertTrue(StringUtils.equals(realResultModel.getName(), realOrgName1));
    }

    @Test
    public void getOrgBaseInfo(){
        String orgId = "dace182f80f54a56afb2812e0c15ec99";
        HashMap contextMap = new HashMap();
        contextMap.put("X-Tsign-Open-App-Id","3438757891");
        RequestContext.setContextMap(contextMap);
        try {
            organizationRest.getBaseInfo(orgId);
        }catch (FootstoneUserErrors.AppIdInvalide e){
            //app invalid
        }

        contextMap.put("X-Tsign-Open-App-Id",null);
        RequestContext.setContextMap(contextMap);
        WrapperResponse<OrganBaseExtendResponse> res = organizationRest.getBaseInfo("dace182f80f54a56afb2812e0c15ec99");
        Assert.assertEquals(res.getData().getOuid(),orgId);
    }

    @Test
    public void deleteOrgTest(){
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);

        rest.deleteOrg("f2c841796d154fe2b131e445ad8651e6 ");
    }

    @Test
    public void testCheckUpdateOrg(){
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);

        RequestContext.put("X-Tsign-Open-App-Id", "**********");

        OrgUpdateRequest request = new OrgUpdateRequest();

        try{

            servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR, "974a2665419d4d3c817780fb299c681f");

            rest.updateOrgan("dace182f80f54a56afb2812e0c15ec99",request);
        } catch (PrivilegeAuthenticateRejectException e){
            return;
        }
    }

    @Test
    public void testGetOrgInfo(){
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.addHeader("X-Tsign-Open-App-Id","7876698694");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);

        rest.getInfo("dace182f80f54a56afb2812e0c15ec99");

        //test deleted
        rest.getInfo("b771cf192e334aed88e051cbff4c18dc");


        rest.getInfo("dace182f80f54a56afb2812e0c15ec99");

    }

    @Test
    public void notAllowedOrgDeleteTest(){
        commonConfig.setNotAllowedDeleteOrgListConfig("test1,test2");
        commonConfig.init();

        try{
            bizOrganService.deleteOrg("test1");
        }catch (FootstoneUserErrors.OrgNotAllowDelete e){
            //
        }

        commonConfig.setNotAllowedDeleteOrgListConfig("test2");
        commonConfig.init();

        try{
            bizOrganService.deleteOrg("test1");
        }catch (Errors.AccOpenUserNotExistWith e){
            //
        }

    }

    @Test
    public void queryOrganConfigTest(){
        String accountId = "878d296d0b404854a34645d058857a77";
        String adminId = "e2368fdcd51a48c5bcc5b5da2277e8c0";
        String orgId = "96724b80b481421fb5b8ad168d863d63";
        List<String> categories = Lists.newArrayList(OrganConfigCategoryEnum.CUSTOM_SETTING.getType());
        // 校验权限, 普通成员
        try{
            bizOrganService.checkOrganConfigCategoryPrivilege(categories, accountId, orgId, false);
            Assert.assertTrue("普通成员无权查看个性化定制配置",false);
        }catch (Errors.AccOpenUserNotExistWith | FootstoneUserErrors.CustomBizException e){
            //
        }
        // 校验权限, 管理员
        try{
            bizOrganService.checkOrganConfigCategoryPrivilege(categories, adminId, orgId, false);
        }catch (Errors.AccOpenUserNotExistWith e){
            //
        }
        // 查询配置
        GetOrganConfigRequest request = new GetOrganConfigRequest();
        request.setCategory(Sets.newHashSet(categories));
        try{
            bizOrganService.queryOrganConfig(orgId, request, true);
        }catch (Errors.AccOpenUserNotExistWith e){
            //
        }
    }

    private OrganRealnameListRequest buildNullArgRequest() {
        OrganRealnameListRequest request = new OrganRealnameListRequest();
        List<OrganRealnameRequest> list = new ArrayList<>();
        request.setList(list);
        return request;
    }


}
