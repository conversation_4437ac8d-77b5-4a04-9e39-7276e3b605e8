//package com.timevale.unittest.footstone;
//
//import com.alibaba.fastjson.JSON;
//import com.timevale.footstone.user.service.model.WrapperResponse;
//import com.timevale.footstone.user.service.model.cert.request.BlockChainCertApplyRequest;
//import com.timevale.footstone.user.service.model.cert.request.PostponeRequest;
//import com.timevale.footstone.user.service.model.cert.response.BlockChainCertApplyResponse;
//import com.timevale.footstone.user.service.model.cert.response.CertDelayResponse;
//import com.timevale.footstone.user.service.model.cert.response.CertInfo;
//import com.timevale.footstone.user.service.rest.CertRest;
//import com.timevale.mandarin.common.result.BaseResult;
//import com.timevale.mandarin.weaver.utils.RequestContext;
//import com.timevale.openca.common.service.exception.OpencaBaseException;
//import com.timevale.unittest.footstone.configuration.Application;
//import org.junit.Assert;
//import org.junit.FixMethodOrder;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.MethodSorters;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
///**
// * <AUTHOR>
// * @version 1.0.0
// * @createTime 2019年11月13日 16:09:00
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)
//public class CertTest {
//
//    @Autowired private CertRest certRest;
//
//    private static String certId;
//
//    @Test
//    public void order1_createBlockChainCerts() {
//        RequestContext.put("X-Tsign-Open-App-Id", "1111563841");
//
//        BlockChainCertApplyRequest request = new BlockChainCertApplyRequest();
//        request.setIdType("CRED_PSN_CH_IDCARD");
//        request.setIdNumber("130132199210092561");
//        request.setName("test123123");
//        request.setSceneDescription("123123");
//        request.setUseBlockChain(true);
//        request.setValidDuration("1Y");
//        WrapperResponse<BlockChainCertApplyResponse> result = certRest.certs(request);
//        validResult(result);
//        certId = result.getData().getCertId();
//    }
//
//    @Test
//    public void order2_queryCert() {
//        RequestContext.put("X-Tsign-Open-App-Id", "1111563841");
//
//        WrapperResponse<CertInfo> result = certRest.query(certId);
//        validResult(result);
//    }
//
//    public void order3_postpone() {
//        RequestContext.put("X-Tsign-Open-App-Id", "1111563841");
//
//        try {
//            PostponeRequest request = new PostponeRequest();
//            request.setValidDuration("1D");
//            WrapperResponse<CertDelayResponse> result = certRest.delayCert(certId, request);
//        }catch (OpencaBaseException e){
//
//        }
//
//    }
//
//    @Test
//    public void order4_revokeCert() {
//        RequestContext.put("X-Tsign-Open-App-Id", "1111563841");
//
//        WrapperResponse<BaseResult> result = certRest.revokeCert(certId);
//        validResult(result);
//    }
//
//    private void validResult(WrapperResponse result) {
//        System.out.println(JSON.toJSONString(result));
//        Assert.assertEquals(result.ifSuccess(), true);
//    }
//}