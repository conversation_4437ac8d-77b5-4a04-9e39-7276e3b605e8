package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizGlobalConfigService;
import com.timevale.footstone.user.service.model.globalConfig.response.ChannelProjectConfigResponse;
import com.timevale.footstone.user.service.model.globalConfig.response.ProductIdMappingResponse;
import com.timevale.footstone.user.service.rest.GlobalConfigRest;
import com.timevale.unittest.footstone.configuration.Application;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: sicheng
 * @since: 2020-07-10 16:56
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class GlobalConfigTest {
    @Autowired private GlobalConfigRest globalConfigRest;

    @Autowired private BizGlobalConfigService svc;

    /** 开放平台登录页商品渠道商url文案code */
    private static final String channelConfigs = "allweblogo,shopRedirectUrl,shopUrlDesc";

    @Test
    public void testGetChannelProjectConfig() {
        globalConfigRest.getChannelProjectConfig(null,null);

    }


    /**
     * 获取渠道方商品配置（橱窗动态跳转页,文案,LogoUrl）
     */
    @Test
    public void getChannelConfigTest(){
        String accountId="f956657324e44fa2afa3668a180ac163";
        String appId="**********";
        try {
            ChannelProjectConfigResponse channelShopConfig = svc
                .getChannelGlobalConfig(accountId, appId, Arrays.asList(channelConfigs.split(",")));
            System.out.println("获取渠道方商品配置 单测成功 result="+channelShopConfig.toString());
        } catch (Exception e) {
            System.out.println("获取渠道方商品配置 单测失败");
            e.printStackTrace();
        }
    }

    /**
     * 商品id映射关系（渠道方商品Id映射E签宝商品Id）
     */
    @Test
    public void productIdMappingTest(){
        String appId="**********";
        String outProductId="o1";
        try {
            ProductIdMappingResponse response = svc.externalToInternalId(appId, outProductId);
            System.out.println("商品id映射关系 单测成功 result="+response.toString());
        } catch (Exception e) {
            System.out.println("商品id映射关系 单测失败");
            e.printStackTrace();
        }
    }

    @Test
    public void productIdMappingExceptionTest(){
        String appId="**********";
        String outProductId="o11111";
        try {
            ProductIdMappingResponse response = svc.externalToInternalId(appId, outProductId);
            svc.externalToInternalId(appId, "");
        } catch (Exception e) {
            System.out.println("商品id查询不到 单测成功");
        }
    }

}
