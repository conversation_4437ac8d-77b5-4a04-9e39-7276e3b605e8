package com.timevale.unittest.footstone;

import com.timevale.footstone.user.service.inner.biz.BizRuleAuditService;
import com.timevale.footstone.user.service.model.rules.request.RulesAuditRequest;
import com.timevale.footstone.user.service.model.rules.response.RuleAuditResponse;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @Date 2021/3/25 2:19 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizRuleGrantAuditServiceTest {

    @Autowired
    private BizRuleAuditService ruleAuditService;

    @Test
    public void testSelectPage() {
        RuleAuditResponse response = ruleAuditService.selectPage(1, 1, null, null, null, null, null, null);
        Assert.assertNotNull(response.getList());
    }

    // @Test
    public void testAudit() {
        RulesAuditRequest request = new RulesAuditRequest();
        request.setAuditUser("荆轲");
        request.setAuditId(9l);
        request.setReason("拒绝");
        request.setStatus(2);
        ruleAuditService.audit(request);
    }
}
