package com.timevale.unittest.footstone;

import static com.timevale.footstone.user.service.constants.CommonConstants.MEMBER_LIMIT_COUNT;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.easun.service.api.RpcEsAccountService;
import com.timevale.easun.service.enums.DeleteStatusEnum;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsSearchInput;
import com.timevale.easun.service.model.organization.output.v3.BizOrgSummaryOutputV3;
import com.timevale.esign.platform.toolkit.utils.page.Pages;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.footstone.user.service.constants.ThirdPartyConstant;
import com.timevale.footstone.user.service.model.member.mods.AdminListOpenResponse;
import com.timevale.footstone.user.service.utils.RemoteHttpUtil;
import com.timevale.footstone.user.service.utils.conv.CommonConverter;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.unittest.footstone.configuration.Application;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

/**
 * <AUTHOR>
 * @since 2020-12-04 17:13
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class JsonTest {


    @Test
    public void jsontest() throws UnknownHostException {
        String json = "{\"X-Tsign-Api-Type\":\"openapi\",\"X-Tsign-Login-Id\":\"723131564e824e578c4524e5dc195cfb\",\"X-Tsign-Open-App-Id\":\"3438757422\",\"X-Tsign-Open-Auth-Mode\":\"simple\",\"X-Tsign-xlink-label\":\"\",\"x-tsign-gray-debug-config\":\"\",\"x-tsign-gray-tag\":\"\",\"x-tsign-open-language\":\"zh-CN\",\"x-tsign-open-operator-id\":\"50ccb183b5df4ec0891006830bf18d32\",\"x-tsign-open-tenant-id\":\"50ccb183b5df4ec0891006830bf18d32\",\"x-tsign-open-webserver-trait\":\"dHJhaXQ6MTExMTEx\",\"x-tsign-operator\":\"50ccb183b5df4ec0891006830bf18d32\",\"x-tsign-token\":\"TOKEN-AUTH-42e72c4d-b349-4a9f-9ae4-d8e3b22cead8\",\"x-tsign-webserver-request-ip\":\"**************\"}";
        Map<String,String> jsonObject = JSON.parseObject(json,HashMap.class);
        for (Map.Entry<String,String> entry:jsonObject.entrySet()){
            System.out.println(entry.getKey()+":"+entry.getValue());
        }
        String trim = "00635368eaed4cccbba09eed35b09f98";
        System.out.println(DateUtils.format(DateUtils.addDays(new Date(), -180), "yyyy-MM-dd HH:mm:ss"));
        System.out.println(Objects.toString(null, ""));

        long ipToCheckLong = ipToLong(InetAddress.getByName("*************").getAddress());
        System.out.println(ipToCheckLong);
    }


    private static long ipToLong(byte[] ip) {
        long result = 0;
        for (byte b : ip) {
            result = result << 8 | b & 0xFF;
        }
        return result;
    }


    @Test
    public void calculateTest(){
        List<String> domainList = new ArrayList<>();
        String url = "^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])\\d{3}(\\d|X|x)$";
        JSONArray jsonArray = new JSONArray();
        jsonArray.add("^[HhMm](\\d{8}|\\d{10})$");
        jsonArray.add("^\\d{8}|^[0-9]{10}[a-zA-Z]$|^\\d{10}$");
        jsonArray.add("^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])\\d{3}(\\d|X|x)$");

        String code = "371326199410033434";
        Pattern pattern = Pattern.compile(url);
        System.out.println(jsonArray);
        JSONObject requestBody = new JSONObject();
//        requestBody.put(ThirdPartyConstant.THIRD_APP_ID, "tt3f894934a0bfca2601");
//        requestBody.put(ThirdPartyConstant.THIRD_APP_SECRET, "4ca9b8c1f6959cbb0b39e51df69dcae836a93127");
//        requestBody.put(ThirdPartyConstant.THIRD_OAUTH_CODE, "AmbM2l6E2ph4Yb-8qxUIhr2Fs3puVpdsXYjDtR-UUIyc47tBrdQqQTbsb1iwqB9kpcbEPN1FgttSKEIkv4SVfWdFbHeV4SmxMFo5D8Txc68I9Zf8zc6dk2sXwVc");
//       JSONObject result =  RemoteHttpUtil.sendByPost(url,requestBody,new HashMap<>(),JSONObject.class);
//       System.out.println(result);

    }

    public static final PathMatcher pathMatcher = new AntPathMatcher();

    public static boolean checkUrl(String url, List<String> domainList) {
        if (CollectionUtils.isEmpty(domainList)) {
            return true;
        }
        String host = getHost(url);
        if (StringUtils.isBlank(host)) {
            return false;
        }
        for (String domain : domainList) {
            if (pathMatcher.matchStart(domain, host)) {
                return true;
            }
        }
        return false;
    }


    public static String getHost(String urlStr) {

        try {
            //替换掉反斜线和井号
            urlStr = urlStr.replaceAll("[\\\\#]","/");
            URL url = new URL(urlStr);
            return url.getHost().toLowerCase();
        } catch (MalformedURLException e) {
            log.warn("url:{} is not valid", urlStr);
        }
        return null;
    }

}
