package com.timevale.unittest.footstone;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.biz.BizAsyncInviteService;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.invite.mods.InviteJoinPerson;
import com.timevale.footstone.user.service.model.invite.request.BatchInviteJoinAsyRequest;
import com.timevale.footstone.user.service.model.invite.response.InviteBatchInitResponse;
import com.timevale.footstone.user.service.model.invite.response.InviteBatchUserTaskResponse;
import com.timevale.footstone.user.service.rest.v2.TInviteRestV2;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: liujiaxing
 * Date: 2022/11/17 7:38 下午
 * Description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AsyncInviteTest {

    @Resource
    BizAsyncInviteService bizAsyncInviteService;

    @Resource
    TInviteRestV2 inviteRestV2;

    BatchInviteJoinAsyRequest asyRequest;

    @Resource
    ApplicationContext applicationContext;

    final static String operateId = "5b6c5c86062849eab5fb234e003b34c4";

    final static String orgId = "d6a34fcf82774b8ba34549a3fe103113";


    @Before
    public void initData() {

        HttpServletRequest httpServletRequest = new MyTestHttpServletRequest();
        httpServletRequest.setAttribute(WeaverConstants.APP_ID_LABEL,"3438757422");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_REAL_IP,"127.0.0.1");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"d6a34fcf82774b8ba34549a3fe103113");
        httpServletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,operateId);
        httpServletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR,operateId);

        RequestContext.put(WeaverConstants.REQUEST_KEY,httpServletRequest);

        //RequestContext.put(HeaderConstants.HEADER_OPERATOR,operateId);

        asyRequest = new BatchInviteJoinAsyRequest();

        asyRequest.setOrgId(orgId);
        asyRequest.setOperatorId(operateId);

        List<InviteJoinPerson> list = new ArrayList<>();
        InviteJoinPerson inviteJoinPerson = new InviteJoinPerson();
        inviteJoinPerson.setMobile("15557182260");
        //inviteJoinPerson.setEmail();
        Map<String,String> map = new HashMap<>();
        map.put("memberName","dazuo");
        inviteJoinPerson.setMemberInfo(map);

        list.add(inviteJoinPerson);

        asyRequest.setInvitedPersons(list);
    }

    @Test
    public void bachInviteJoinAsync() {
        //asyRequest.setDeptId("");

        String user = System.currentTimeMillis() +"";
        asyRequest.setOperatorId(user);
        bizAsyncInviteService.bachInviteJoinAsync(asyRequest);

    }

    @Test
    public void queryUserTask() {
        String user = System.currentTimeMillis() +"";
        asyRequest.setOperatorId(user);
        asyRequest.setOrgId(orgId);
        InviteBatchInitResponse response = bizAsyncInviteService.bachInviteJoinAsync(asyRequest);
        //等待bachInviteJoinAsync 完毕
        try {
            Thread.sleep(2000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        InviteBatchUserTaskResponse res = bizAsyncInviteService.queryUserTask(user,orgId);

        System.out.println("queryUserTask:" + res);
    }


    @Test
    public void bachInviteJoinAsyncRest() {
        String user = System.currentTimeMillis() +"";

        RequestContext.getRequest().setAttribute(HeaderConstants.HEADER_OPERATOR,user);
        asyRequest.setOperatorId(user);
        asyRequest.setOrgId(orgId);
        WrapperResponse<InviteBatchInitResponse> inviteBatchInitResponseWrapperResponse = inviteRestV2.bachInviteJoinAsync(orgId, asyRequest);

    }

    @Test
    public void queryDownloadUrlRest() {

        String fileKey = "$68b45542-5e0a-4ecb-9335-77d03462f177$1460166392";
        inviteRestV2.queryDownloadUrl(fileKey);
    }

    @Test
    public void queryUserTaskRest() {
        String testUser = System.currentTimeMillis() +"";
        asyRequest.setOperatorId(testUser);
        asyRequest.setOrgId(orgId);
        InviteBatchInitResponse response = bizAsyncInviteService.bachInviteJoinAsync(asyRequest);
        bizAsyncInviteService.queryUserTask(testUser,orgId);
    }
}
