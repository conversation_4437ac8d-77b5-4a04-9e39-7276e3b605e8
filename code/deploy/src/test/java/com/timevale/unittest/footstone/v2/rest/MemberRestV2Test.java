package com.timevale.unittest.footstone.v2.rest;

import com.timevale.footstone.user.service.model.member.mods.DeptListResponse;
import com.timevale.footstone.user.service.model.member.mods.DeptSubListResponse;
import com.timevale.footstone.user.service.model.member.mods.MemberListResponse;
import com.timevale.footstone.user.service.model.member.request.DeptIdsOrMemberIdsParam;
import com.timevale.footstone.user.service.model.member.request.ListByDeptParam;
import com.timevale.footstone.user.service.rest.v2.MemberRestV2;
import com.timevale.unittest.footstone.configuration.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2021-06-23 17:51
 **/

@Slf4j
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MemberRestV2Test {

    @Mock
    private MemberRestV2 restV2;


    @Test
    public void testListsub() {
        restV2.listsub("f64c0186ee3d47728f342990d23ef647", "1", "all", 1);
        restV2.membersSearch("f64c0186ee3d47728f342990d23ef647", "", 100, 0);
        restV2.deptSearch("f64c0186ee3d47728f342990d23ef647", "", 100, 0);
    }
}
