package com.timevale.unittest.footstone.tc.res;

import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.inner.respdecoration.walker.ObjectWalk;
import com.timevale.unittest.footstone.tc.TestBean;
import org.junit.Assert;

public enum SimpleTestcaseCheckForName223 implements SimpleTestcaseChecker{
    T_1("12345", "***45"),

    T_2("234", "*34"),

    T_3("1", "*"),

    T_4("", ""),

    T_5("123456", "***56"),

    T_6("56 78", "56***"),
    T_7("56.78", "56***"),
    T_8(".78", "***"),
    T_9("1.78", "1***"),
    T_10("78", "*8"),
    T_11("jack jonse", "jack***"),
    T_12("jack hello jonse", "jack***"),
    T_13("迪丽热巴·迪力木拉提·迪力木拉提", "迪丽热巴***"),
    T_14("56.", "56*");
    private String input;

    private String anwser;

    SimpleTestcaseCheckForName223(String input, String anwser) {
        this.input = input;
        this.anwser = anwser;
    }

    @Override
    public void doAndCheck(DecorationWalker walker) {

        TestBean t = new TestBean();
        t.setName223(input);

        ObjectWalk.walkBean(t, walker);

        Assert.assertEquals(anwser, t.getName223());
    }
}
