package com.timevale.unittest.footstone;

import com.google.common.collect.Lists;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.organization.service.enums.ActivateEnum;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.easun.service.model.esearch.output.EsRealnameOrgAndAdminsOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.configuration.IsolationConfig;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.impl.biz.BizOrganServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.realname.RpcInfoQueryServiceAdapt;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.account.mods.OrgGlobalCredentials;
import com.timevale.footstone.user.service.model.account.mods.OrgProperties;
import com.timevale.footstone.user.service.model.account.request.OrgCreateRequest;
import com.timevale.footstone.user.service.model.member.mods.MemberDetailModel;
import com.timevale.footstone.user.service.model.organization.response.OrganDetailModel;
import com.timevale.footstone.user.service.rest.OrganizationRest;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.infoauth.service.response.query.AccurateQueryEnterpriseInformationResponse;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.unittest.footstone.configuration.Application;
import org.apache.poi.poifs.property.PropertyConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.timevale.account.service.model.constants.BuiltinContacts.MOBILE;
import static com.timevale.account.service.model.constants.BuiltinCredentials.CRED_ORG_USCC;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrganizationMockTest {

    @InjectMocks
    OrganizationRest organizationRest;


    @InjectMocks
    private BizOrganServiceImpl bizOrganService;

    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Mock
    private BizMemberService bizMemberService;


    @Mock
    private RpcOrgPlusService rpcOrgPlusService;

    @InjectMocks
    private IsolationConfig isolationConfig;

    @Mock
    private BizOrganServiceImpl mockBizOrganService;


    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

    @Mock
    private SystemProperty systemProperty;

    @Mock
    private RpcInfoQueryServiceAdapt rpcInfoQueryServiceAdapt;

    String appId  = "7488";
    String appRequestHeader = "X-Tsign-Open-App-Id";



    @Before
    public void setUp(){
        Set<String> set = new HashSet<>();
        set.add(appId);
        ReflectionTestUtils.setField(isolationConfig, "appIds", set);
        ReflectionTestUtils.setField(isolationConfig, "isolationSwitch", true);
        ReflectionTestUtils.setField(organizationRest, "isolationConfig", isolationConfig);
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        ReflectionTestUtils.setField(organizationRest, "servletRequest", servletRequest);
        ReflectionTestUtils.setField(organizationRest, "bizOrganService", bizOrganService);
        mockSearchOrgByEs(ActivateEnum.ACTIVATED);
        when(bizMemberService.checkMemberInOrg(any(),any())).thenReturn(true);
        ApiPageResult<MemberDetailModel> admins = new ApiPageResult<>();
        admins.setTotal(1);
        mockGetMemberByEs();
        when(bizMemberService.admins(any(), any(), any())).thenReturn(admins);
        ReflectionTestUtils.setField(bizOrganService, "bizMemberService", bizMemberService);
        ReflectionTestUtils.setField(bizOrganService, "esAccountServiceAdapt", esAccountServiceAdapt);
        when(bizMemberService.admins(any(), any(), any())).thenReturn(admins);
        ReflectionTestUtils.setField(bizOrganService, "rpcOrgPlusService", rpcOrgPlusService);
        BizOrganOuid organOuid = new BizOrganOuid();
        organOuid.setOuid("organOuid");
        when(rpcOrgPlusService.createICOrgAndCompanyWithSource(any())).thenReturn(RpcOutput.with(organOuid));
    }

    private void mockGetMemberByEs() {
        PagerResult<MemberDetail> pageResult = new PagerResult<>();
        MemberDetail memberDetail  = new MemberDetail();
        memberDetail.setOrganId("organId");
        List<MemberDetail> items = new ArrayList<>();
        items.add(memberDetail);
        pageResult.setItems(items);
        pageResult.setTotal(items.size());
        when(esAccountServiceAdapt.getMemberListByEs(any())).thenReturn(pageResult);
    }

    private void mockGetIcUserList(){
        PagerResult<BizOrgBaseInfoOutput> pagerResult = new PagerResult<>();
        List<BizOrgBaseInfoOutput> baseInfoOutputs = new ArrayList<>();
        BizOrgBaseInfoOutput baseInfo = new BizOrgBaseInfoOutput();
        baseInfo.setActivate(ActivateEnum.ACTIVATED.getCode());
        BizICUserOutput bizICUserOutput = new BizICUserOutput();
        AccountBaseDetail baseDetail =new AccountBaseDetail();
        baseDetail.setProjectApp(new ProductApp());
        baseDetail.setAccountUid("accountUid");
        bizICUserOutput.setBase(baseDetail);
        ICUserId id = new ICUserId();
        id.setOuid("oid");
        id.setUuid("uid");
        id.setUuid("uid");
        bizICUserOutput.setId(id);
        List<Property> properties = new ArrayList<>();
        Property name = new Property();
        name.setType(INFO_NAME);
        name.setValue("name");
        properties.add(name);
        bizICUserOutput.setProperties(properties);
        baseInfo.setAccount(bizICUserOutput);
        baseInfoOutputs.add(baseInfo);
        baseInfo.setRoleDetailList(new ArrayList<>());
        pagerResult.setTotal(baseInfoOutputs.size());
        pagerResult.setItems(baseInfoOutputs);
//        when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(),any())).thenReturn(pagerResult);
    }


    private PagerResult<EsOrgSearhOutput> getEsOrgSearchOutputList(Integer activate) {
        PagerResult<EsOrgSearhOutput> esResult = new PagerResult<>();
        List<EsOrgSearhOutput> items= new ArrayList<>();
        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        esOrgSearhOutput.setActivate(activate);
        EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
        AccountBaseDetail baseDetail = new AccountBaseDetail();
        baseDetail.setProjectApp(new ProductApp());
        user.setBase(baseDetail);
        EsId id =new EsId();
        id.setGuid("GId");
        id.setOuid("oid");
        id.setUuid("uuid");
        user.setId(id);
        user.setStatus(RealnameStatus.ACCEPT);
        esOrgSearhOutput.setUser(user);
        items.add(esOrgSearhOutput);
        esResult.setItems(items);
        esResult.setTotal(items.size());
        List<Property> properties = new ArrayList<>();
        Property name = new Property();
        name.setType(INFO_NAME);
        name.setValue("name");
        properties.add(name);
        user.setProperties(properties);
        List<Property> credentials = new ArrayList<>();
        Property credenty = new Property();
        credenty.setType(CRED_ORG_USCC);
        credenty.setValue("CRED_ORG_USCC");
        credentials.add(credenty);
        user.setCredentials(properties);
        List<Property> contacts = new ArrayList<>();
        Property contact = new Property();
        contact.setType(MOBILE);
        contact.setValue("13093781206");
        contacts.add(contact);
        user.setContacts(contacts);
        return esResult;
    }


    @Test
    public void createOrgTest(){
        RequestContext.put(appRequestHeader,appId);
        OrgCreateRequest createRequest = new OrgCreateRequest();
        createRequest.setCreater("creater");
        OrgProperties properties = new OrgProperties();
        properties.setName("name");
        createRequest.setProperties(properties);
        when(systemProperty.getCreateOrgCheckIdentityCodeAlreadyExistsActiveInterruptOnOff()).thenReturn(false);
        //是实名组织成员
        organizationRest.create(createRequest);
        when(bizMemberService.checkMemberInOrg(any(),any())).thenReturn(false);
        RequestContext.put(appRequestHeader,appId);
        //不是实名组织成员但实名组织是已激活的
        organizationRest.create(createRequest);
        ApiPageResult pageResult = new ApiPageResult<>();
        pageResult.setTotal(0);
        when(bizMemberService.admins(any(), any(), any())).thenReturn(pageResult);
        mockSearchOrgByEs(ActivateEnum.UN_ACTIVATE);
        RequestContext.put(appRequestHeader,appId);
        organizationRest.create(createRequest);
        PagerResult<EsOrgSearhOutput> esSearchOutput = new PagerResult<>();
        esSearchOutput.setItems(new ArrayList<>());
        esSearchOutput.setTotal(0);
        when(esAccountServiceAdapt.getOrgsByEs(any()))
                .thenReturn(esSearchOutput);
        RequestContext.put(appRequestHeader,appId);
        organizationRest.create(createRequest);
        RequestContext.put(appRequestHeader,"7499");
        mockGetIcUserList();
        organizationRest.create(createRequest);
    }

    @Test
    public void createOrgTestCheckCreatedOrgsByIdentitCode() {
        RequestContext.put(appRequestHeader, appId);
        OrgCreateRequest createRequest = new OrgCreateRequest();
        createRequest.setCreater("creater");
        OrgProperties properties = new OrgProperties();
        properties.setName("name");
        createRequest.setProperties(properties);

        when(systemProperty.getCreateOrgCheckIdentityCodeAlreadyExistsActiveInterruptOnOff())
                .thenReturn(true);
        when(bizMemberService.checkMemberInOrg(any(), any())).thenReturn(false);
        PagerResult<EsOrgSearhOutput> esSearchOutput = new PagerResult<>();
        esSearchOutput.setItems(new ArrayList<>());
        esSearchOutput.setTotal(0);
        when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(esSearchOutput);
        AccurateQueryEnterpriseInformationResponse orgInfo =
                new AccurateQueryEnterpriseInformationResponse();
        orgInfo.setCodeUSC("1");
        when(rpcInfoQueryServiceAdapt.accurateQueryEnterpriseInformation(any()))
                .thenReturn(orgInfo);
        EsRealnameOrgAndAdminsOutput output = new EsRealnameOrgAndAdminsOutput();
        EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
        output.setUser(user);
        EsId id = new EsId();
        user.setId(id);
        id.setOuid("1");
        when(esAccountServiceAdapt.getRealnameOrgAndAdminsByEs(any(), any())).thenReturn(output);

        Assert.assertEquals("1", organizationRest.create(createRequest).getData().getOperateCode());
    }


    @Test
    public void getByNameTest(){
        PagerResult<OrganDetailModel> pagerResult = new PagerResult<>();
        pagerResult.setItems(Lists.newArrayList(MockUntils.mockOrganDetailModel()));
        when(mockBizOrganService.getOrgByName(any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(pagerResult);
        ReflectionTestUtils.setField(organizationRest,"bizOrganService",mockBizOrganService);
        organizationRest.getOrgByName("测试","ACCEPT","7488",true,"acountId",0,10,"accountId");

    }

    private void mockSearchOrgByEs(ActivateEnum unActivate) {
        PagerResult<EsOrgSearhOutput> esSearchOutput = getEsOrgSearchOutputList(unActivate.getCode());
        when(esAccountServiceAdapt.getOrgsByEs(any()))
                .thenReturn(esSearchOutput);
    }
}
