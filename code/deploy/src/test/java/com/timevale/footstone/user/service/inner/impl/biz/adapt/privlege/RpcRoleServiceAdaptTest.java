package com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege;

import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcIcRolePrivilegePlusService;
import com.timevale.easun.service.api.RpcRolePrivilegePlusService;
import com.timevale.easun.service.model.role.output.RoleInfo;
import com.timevale.easun.service.model.role.output.UserRoleOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.privilege.service.api.RpcRoleService;
import com.timevale.privilege.service.enums.BuiltinRoleType;
import com.timevale.privilege.service.model.service.biz.output.BizGetRelaSyncRoleIdsOutput;
import com.timevale.privilege.service.model.service.biz.output.BizGetSyncSourceOrganOutput;
import com.timevale.privilege.service.model.service.biz.output.BizRoleSyncOutput;
import com.timevale.privilege.service.model.service.biz.output.BizUserRoleListByOrganOutput;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @Date 2023/3/6 11:45
 * @Description
 */
@RunWith(MockitoJUnitRunner.class)
public class RpcRoleServiceAdaptTest {

    @InjectMocks
    private RpcRoleServiceAdapt rpcRoleServiceAdapt;

    @Mock
    private RpcIcRolePrivilegePlusService icRolePrivilegePlusService;

    /**
     * 这个是调底层privilege服务的rpc
     */
    @Mock
    private RpcRoleService roleService;

    /**
     * 这个调easun-service服务的rpc
     */
    @Mock
    private RpcRolePrivilegePlusService rolePrivilegePlusService;

    @Mock
    private RpcEsAccountServiceAdapt esAdapt;

    @Mock
    private IdAdapt idAdapt;

    @Before
    public void setUp() {
        when(idAdapt.orgIdToOrganId(any())).thenReturn("organId");
        when(idAdapt.accountUid(any())).thenReturn("accountUid");
        BizUserRoleListByOrganOutput roleListByOrganOutput = new BizUserRoleListByOrganOutput();
        roleListByOrganOutput.setBizRoleDetailList(MockUntils.getBizRoleDetails());
        when(roleService.getSyncRolesByAccountUid(any())).thenReturn(RpcOutput.with(roleListByOrganOutput));
        BizGetRelaSyncRoleIdsOutput relaSyncRoleIdsOutput = new BizGetRelaSyncRoleIdsOutput();
        relaSyncRoleIdsOutput.setSyncRoleIds(Lists.newArrayList("xxxx", "2222"));
        when(roleService.getRelaSyncRoleIdsBySourceRoleId(any())).thenReturn(RpcOutput.with(relaSyncRoleIdsOutput));
        BizRoleSyncOutput bizRoleSyncOutput = new BizRoleSyncOutput();
        when(roleService.syncRolesFromOtherOrg(any())).thenReturn(RpcOutput.with(bizRoleSyncOutput));
        when(roleService.getUserRolesByRoleIds(any())).thenReturn(RpcOutput.with(roleListByOrganOutput));
        BizRoleDetail bizRoleDetail = new BizRoleDetail();
        when(roleService.getRoleByRoleKey(any())).thenReturn(RpcOutput.with(bizRoleDetail));
    }

    @Test
    public void testGetUserRolesNonBlockForMoreRoles() {
        final String memberOidMoreRoles = "testMemberOidHasMoreRoles";
        final String orgOidTest1 = "testOrg1";
        RpcOutput<UserRoleOutput> mockMoreUserRolesOutput = this.mockMoreUserRoles(memberOidMoreRoles, orgOidTest1);
        when(this.icRolePrivilegePlusService.getUserRolesByCache(Mockito.any())).thenReturn(mockMoreUserRolesOutput);
        List<RoleInfo> roleInfos = this.rpcRoleServiceAdapt.getUserRolesNonBlock(orgOidTest1, memberOidMoreRoles);
        Assert.assertNotNull(roleInfos);
        Assert.assertTrue(roleInfos.size() > 0);
    }

    @Test
    public void testGetUserRolesNonBlockForRpcError() {
        final String memberOidMoreRoles = "testMemberOidForRpcError";
        final String orgOidTest1 = "testOrg1";
        Mockito.doThrow(new BaseBizRuntimeException("企业已删除")).when(this.icRolePrivilegePlusService).getUserRolesByCache(Mockito.any());
        List<RoleInfo> roleInfos = this.rpcRoleServiceAdapt.getUserRolesNonBlock(orgOidTest1, memberOidMoreRoles);
        Assert.assertNotNull(roleInfos);
        Assert.assertTrue(roleInfos.size() == 0);
    }

    @Test
    public void testGetUserRolesNonBlockForEmptyRole() {
        final String memberOidMoreRoles = "testMemberOidEmptyRole";
        final String orgOidTest1 = "testOrg1";
        when(this.icRolePrivilegePlusService.getUserRolesByCache(Mockito.any())).thenReturn(new RpcOutput<>());
        List<RoleInfo> roleInfos = this.rpcRoleServiceAdapt.getUserRolesNonBlock(orgOidTest1, memberOidMoreRoles);
        Assert.assertNotNull(roleInfos);
        Assert.assertTrue(roleInfos.size() == 0);
    }

    @Test
    public void getTheSameRootRoleIdsByMemberOidTest() {
        rpcRoleServiceAdapt.getTheSameRootRoleIdsByMemberOid("sourceOrgId", "memberOid", "targetOrgId");
    }

    @Test
    public void getRoleSyncSourceOraIdTest() {
        BizGetSyncSourceOrganOutput output = new BizGetSyncSourceOrganOutput();
        output.setOrganId("organId");
        when(roleService.getSyncSourceOranId(any())).thenReturn(RpcOutput.with(output));
        rpcRoleServiceAdapt.getRoleSyncSourceOraId(any());
    }

    @Test
    public void getSyncRolesByGrantedMemberOidTest() {
        rpcRoleServiceAdapt.getSyncRolesByGrantedMemberOid("sourceOrgId", "memberOid");
    }

    @Test
    public void syncRolesFromOtherOrgTetst() {
        rpcRoleServiceAdapt.syncRolesFromOtherOrg("organId", Lists.newArrayList(new BizRoleDetail()), Lists.newArrayList());
    }

    @Test
    public void syncPrivilegesByRoleAsyncTest() {
        rpcRoleServiceAdapt.syncPrivilegesByRoleAsync("orgId","operatorId", Lists.newArrayList("xxx"));
    }

    @Test
    public void checkRolesOwnerValidTest() {
        rpcRoleServiceAdapt.checkRolesOwnerValid(MockUntils.getRoleSyncItemModelList(), "ornerId");
    }

    @Test
    public void getRoleByRoleKeyTest(){
        rpcRoleServiceAdapt.getRoleByRoleKey("organId", BuiltinRoleType.ORGAN_LEGAL);
    }

    private RpcOutput<UserRoleOutput> mockMoreUserRoles(String memberOid,
                                                        String orgOid) {
        final UserRoleOutput userRoleOutput = new UserRoleOutput();
        final List<RoleInfo> roleInfos = new ArrayList<>();
        RoleInfo roleInfo1 = new RoleInfo();
        roleInfo1.setName("testRoleName1");
        RoleInfo roleInfo2 = new RoleInfo();
        roleInfo2.setName("testRoleName2");
        roleInfos.add(roleInfo1);
        roleInfos.add(roleInfo2);
        userRoleOutput.setRoleInfos(roleInfos);
        return RpcOutput.with(userRoleOutput);
    }

}