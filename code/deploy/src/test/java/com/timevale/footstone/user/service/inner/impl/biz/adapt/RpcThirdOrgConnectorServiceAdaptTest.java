package com.timevale.footstone.user.service.inner.impl.biz.adapt;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.besp.lowcode.integration.response.TenantConfigResponse;
import com.timevale.besp.lowcode.integration.response.ThirdConfigResponse;
import com.timevale.besp.lowcode.integration.third.ThirdConfigBaseRpcService;
import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcThirdOrgConnectorServiceAdapt;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;


import com.timevale.footstone.user.service.inner.model.v3.ThirdSyncModel;


import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RpcThirdOrgConnectorServiceAdaptTest {

    // 测试数据定义
    private static final String INPUT_ORGAN_OUID = "testOrganOuid"; // 测试用组织OUID
    private static final String INPUT_PLATFORM = "WECHAT_WORK"; // 测试用平台名称

    private static final String EXPECTED_PLATFORM = "WECHAT_WORK"; // 预期的平台名称
    private static final String EXPECTED_NULL = null; // 预期的null结果

    @InjectMocks
    private RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdapt;

    @Mock
    private ThirdConfigBaseRpcService thirdConfigBaseRpcService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void batchGetOrgConnectorsTest() {
        List<TenantConfigResponse> responseList = Lists.newArrayList();
        TenantConfigResponse tenantConfigResponse = new TenantConfigResponse();
        List<ThirdConfigResponse> responses = Lists.newArrayList();
        ThirdConfigResponse configResponse = new ThirdConfigResponse();
        configResponse.setConfigType("SYNC_ORG_ALL");
        configResponse.setEnable(true);
        responses.add(configResponse);
        tenantConfigResponse.setConfigs(responses);
        tenantConfigResponse.setOrgOuId("orgId");
        responseList.add(tenantConfigResponse);
        when(thirdConfigBaseRpcService.getTenantConfigs(any())).thenReturn(responseList);
        rpcThirdOrgConnectorServiceAdapt.batchGetOrgConnectors(Arrays.asList("orgId"));
    }


    /**
     * TC01: batchGetOrgConnectors返回空map
     * 输入: organOuid="testOrganOuid", batchGetOrgConnectors返回空map
     * 预期输出: null
     * 描述: 没有找到ThirdSyncModel
     */
    @Test
    public void testGetOpenSyncPlatform_EmptyMap() {
        Map<String, ThirdSyncModel> resultMap = new HashMap<>();
        RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdaptSpy= Mockito.spy(rpcThirdOrgConnectorServiceAdapt);
        doReturn(resultMap).when(rpcThirdOrgConnectorServiceAdaptSpy).batchGetOrgConnectors(anyList());

        String result = rpcThirdOrgConnectorServiceAdaptSpy.getOpenSyncPlatform(INPUT_ORGAN_OUID);
        assertEquals(EXPECTED_NULL, result);
    }

    /**
     * TC02: thirdSyncModel为null
     * 输入: organOuid="testOrganOuid", batchGetOrgConnectors返回包含null值的map
     * 预期输出: null
     * 描述: 没有找到ThirdSyncModel
     */
    @Test
    public void testGetOpenSyncPlatform_ThirdSyncModelNull() {
        RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdaptSpy = Mockito.spy(rpcThirdOrgConnectorServiceAdapt);
        Map<String, ThirdSyncModel> resultMap = new HashMap<>();
        resultMap.put(INPUT_ORGAN_OUID, null);

        doReturn(resultMap).when(rpcThirdOrgConnectorServiceAdaptSpy).batchGetOrgConnectors(anyList());

        String result = rpcThirdOrgConnectorServiceAdaptSpy.getOpenSyncPlatform(INPUT_ORGAN_OUID);
        assertEquals(EXPECTED_NULL, result);
    }

    /**
     * TC03: isOpenThirdSync为false
     * 输入: organOuid="testOrganOuid", isOpenThirdSync=false
     * 预期输出: null
     * 描述: 未开启第三方同步
     */
    @Test
    public void testGetOpenSyncPlatform_NotEnabled() {
        RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdaptSpy = Mockito.spy(rpcThirdOrgConnectorServiceAdapt);
        ThirdSyncModel syncModel = new ThirdSyncModel();
        syncModel.setOpenThirdSync(false);
        syncModel.setPlatform(INPUT_PLATFORM);

        Map<String, ThirdSyncModel> resultMap = new HashMap<>();
        resultMap.put(INPUT_ORGAN_OUID, syncModel);

        doReturn(resultMap).when(rpcThirdOrgConnectorServiceAdaptSpy).batchGetOrgConnectors(anyList());

        String result = rpcThirdOrgConnectorServiceAdaptSpy.getOpenSyncPlatform(INPUT_ORGAN_OUID);
        assertEquals(EXPECTED_NULL, result);
    }

    /**
     * TC04: 正常流程
     * 输入: organOuid="testOrganOuid", isOpenThirdSync=true, platform="WECHAT_WORK"
     * 预期输出: "WECHAT_WORK"
     * 描述: 完整正常流程
     */
    @Test
    public void testGetOpenSyncPlatform_NormalFlow() {
        RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdaptSpy = Mockito.spy(rpcThirdOrgConnectorServiceAdapt);
        ThirdSyncModel syncModel = new ThirdSyncModel();
        syncModel.setOpenThirdSync(true);
        syncModel.setPlatform(INPUT_PLATFORM);

        Map<String, ThirdSyncModel> resultMap = new HashMap<>();
        resultMap.put(INPUT_ORGAN_OUID, syncModel);

        doReturn(resultMap).when(rpcThirdOrgConnectorServiceAdaptSpy).batchGetOrgConnectors(anyList());

        String result = rpcThirdOrgConnectorServiceAdaptSpy.getOpenSyncPlatform(INPUT_ORGAN_OUID);
        assertEquals(EXPECTED_PLATFORM, result);
    }

    @Test
    public void testGetOpenSyncPlatform() {
        String str = "[{\"orgOuId\":\"0b4a97fd1ff645269ac1d361ab412511\",\"tenantKey\":\"15195a338cd6d758\",\"platform\":\"FEISHU\",\"configs\":[{\"configType\":\"APPROVE\",\"enable\":true},{\"configType\":\"MESSAGE\",\"enable\":true},{\"configType\":\"SYNC_ORG_ALL\",\"enable\":true},{\"configType\":\"SYNC_ORG_INC\",\"enable\":true}]},{\"orgOuId\":\"0b4a97fd1ff645269ac1d361ab412511\",\"tenantKey\":\"wp2ucRDgAABpdmJGU_3Q7GmbBju079mQ\",\"platform\":\"WECHATWORK\",\"configs\":[{\"configType\":\"APPROVE\",\"enable\":false},{\"configType\":\"MESSAGE\",\"enable\":false},{\"configType\":\"SYNC_ORG_ALL\",\"enable\":false},{\"configType\":\"SYNC_ORG_INC\",\"enable\":false}]}]";
        Mockito.when(thirdConfigBaseRpcService.getTenantConfigs(any())).thenReturn(JSON.parseArray(str, TenantConfigResponse.class));
        String openSyncPlatform = rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform("0b4a97fd1ff645269ac1d361ab412511");
        Assert.assertEquals(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform(), openSyncPlatform);
        System.out.println(openSyncPlatform);

    }

}
