package com.timevale.footstone.user.service.rest;
import com.google.common.collect.Lists;

import com.timevale.easun.service.api.RpcICUserExtendService;
import com.timevale.easun.service.model.account.output.BizICUserLoginOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoLoginConfigServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoThirdAuthServiceImpl;
import com.timevale.footstone.user.service.model.sso.SsoAccountBindOrgan;
import com.timevale.footstone.user.service.model.sso.SsoOrganInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;

/**
 * Author: liujiaxing
 * Date: 2023/8/16 5:17 下午
 * Description:
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class SsoThirdAuthRestMockTest {

    @InjectMocks
    SsoThirdAuthRest ssoThirdAuthRest;

    @Mock
    BizSsoThirdAuthServiceImpl thirdAuthService;

    @Mock
    RpcICUserExtendService rpcICUserExtendService;

    @Mock
    private BizSsoLoginConfigServiceImpl ssoLoginConfigService;
    @Before
    public void before() {
    }


    @Test
    public void listOrgans() {
        String provider = "dingding";
        Mockito.when(thirdAuthService.querySsoOrganInfos(any())).thenReturn(Lists.newArrayList(new SsoOrganInfo()));
        ssoThirdAuthRest.listOrgans(provider);
    }

//    @Test
//    public void bindOrgans() {
//        Mockito.doNothing().when(thirdAuthService).checkAndJoinOrgan(any(),any());
//        Mockito.when(thirdAuthService.getTargetUrlByProvider(any())).thenReturn("test_redirectUrl");
//
//        SsoAccountBindOrgan ssoAccountBindOrgan = new SsoAccountBindOrgan();
//        ssoAccountBindOrgan.setOrganOidList(Lists.newArrayList("test_organOid"));
//        ssoAccountBindOrgan.setSsoProvider("dingding");
//
//        BizICUserLoginOutput output = new BizICUserLoginOutput();
//        output.setMobile("111111");
//        output.setEmail("<EMAIL>");
//        RpcOutput<BizICUserLoginOutput> rpcOutput = RpcOutput.with(output);
//        Mockito.when(rpcICUserExtendService.getContactInfoByOuid(new RpcInput<>(any()))).thenReturn(rpcOutput);
//        ssoThirdAuthRest.bindOrgans("test_oid",ssoAccountBindOrgan);
//    }

}
