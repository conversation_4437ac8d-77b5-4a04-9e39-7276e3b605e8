package com.timevale.footstone.user.service.inner.impl.biz.v2;

import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.inner.impl.biz.BizContactServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.ContactServiceAdapter;
import com.timevale.footstone.user.service.inner.impl.biz.conv.BizContactConv;
import com.timevale.footstone.user.service.model.contact.request.ContactBatchDeleteRequest;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.exception.BaseIllegalArgumentException;
import com.timevale.unittest.footstone.configuration.Application;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.context.ContextConfiguration;

/**
 * <AUTHOR>
 * @Date 2023/2/23 16:02
 * @Description
 */
@RunWith(MockitoJUnitRunner.class)
@ContextConfiguration(classes = Application.class)
public class BizContactServiceTest {

  @InjectMocks
  private BizContactServiceImpl bizContactService;

  @Mock
  private ContactServiceAdapter contactServiceAdapter;

  @Rule
  public ExpectedException exception = ExpectedException.none();

  private static final String currentOid = "123test";
  private static final List<String> currentOidContactIds = Arrays.asList("123456", "4567890");
  private static final List<String> notCurrentOidContactIds = Arrays.asList("901122", "900221");
  private static final List<String> emptyContactIds = new ArrayList<>();

  /**
   *  测试正常批量删除CASE
   */
  @Test
  public void batchDeleteContactsTest() {
    ContactBatchDeleteRequest request = new ContactBatchDeleteRequest();
    request.setContactIds(currentOidContactIds);
    Mockito.doNothing().when(this.contactServiceAdapter).batchDeleteContact(Mockito.any());
    this.bizContactService.batchDeleteContact(currentOid, request);
  }

  /**
   * 测试越权删除CASE
   */
  @Test
  public void testDeleteNotCurrentOidContactIds() {
    ContactBatchDeleteRequest request = new ContactBatchDeleteRequest();
    request.setContactIds(notCurrentOidContactIds);
    Mockito.doThrow(new BaseBizRuntimeException("越权删除错误")).when(this.contactServiceAdapter).batchDeleteContact(Mockito.any());
    exception.expect(FootstoneUserErrors.CustomBizException.class);
    this.bizContactService.batchDeleteContact(currentOid, request);
  }

  /**
   * 测试空参数CASE
   */
  @Test
  public void testDeleteEmptyContactIds() {
    ContactBatchDeleteRequest request = new ContactBatchDeleteRequest();
    request.setContactIds(emptyContactIds);
    Mockito.doThrow(new BaseIllegalArgumentException("空参数删除错误")).when(this.contactServiceAdapter).batchDeleteContact(Mockito.any());
    exception.expect(FootstoneUserErrors.CustomBizException.class);
    this.bizContactService.batchDeleteContact(currentOid, request);
  }

  /**
   * 测试系统异常CASE
   */
  @Test
  public void testDeleteSystemError() {
    ContactBatchDeleteRequest request = new ContactBatchDeleteRequest();
    request.setContactIds(emptyContactIds);
    Mockito.doThrow(new RuntimeException("未知运行时异常")).when(this.contactServiceAdapter).batchDeleteContact(Mockito.any());
    exception.expect(FootstoneUserErrors.CustomBizException.class);
    this.bizContactService.batchDeleteContact(currentOid, request);
  }

  @Test
  public void convertInputBatchDeleteTest() {
    ContactBatchDeleteRequest request = new ContactBatchDeleteRequest();
    request.setContactIds(currentOidContactIds);
    BizContactConv.convertInput(currentOid, request);
  }

}
