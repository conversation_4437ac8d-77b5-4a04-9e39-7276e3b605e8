package com.timevale.footstone.user.service.inner.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.google.common.collect.Lists;
import com.timevale.account.organization.service.model.service.newbiz.output.BizSubOrganOutput;
import com.timevale.footstone.user.service.inner.impl.biz.BizPrivilegeServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcIcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.model.Privilege.mods.PrivilegeModel;
import com.timevale.footstone.user.service.model.Privilege.response.PrivilegeResponse;
import com.timevale.privilege.service.model.service.mods.BizPrivilege;
import com.timevale.privilege.service.model.service.mods.BizPrivilegeDetail;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BizPrivilegeServiceImplTest {

    // 步骤1：定义被测试对象和依赖
    @InjectMocks
    private BizPrivilegeServiceImpl bizPrivilegeService;

    @Mock
    private RpcIcRolePrivilegePlusServiceAdapt icRolePrivilegePlusServiceAdapt;

    @Mock
    private IdAdapt idAdapt;
    
    @Mock
    private RpcOrgPlusServiceAdapt rpcOrgPlusServiceAdapt;

    // 步骤3：定义测试数据
    private static final String INPUT_ACCOUNT_ID = "acc_123";
    private static final String INPUT_ORG_ID = "org_456";
    private static final String INPUT_ROOT_ORG_ID = "root_789";
    private static final boolean EXPECTED_RESULT = true;
    
    // 测试权限数据
    private static final PrivilegeModel TEST_PRIVILEGE = new PrivilegeModel();
    static {
        TEST_PRIVILEGE.setTargetClassKey("SEAL");
        TEST_PRIVILEGE.setOperationPermit("USE");
    }
    private static final List<PrivilegeModel> INPUT_PRIVILEGES = 
        Lists.newArrayList(TEST_PRIVILEGE);

    @Before
    public void setUp() {
        // 基础mock配置
        when(icRolePrivilegePlusServiceAdapt.getPrivilegesByUser(
            anyString(), anyString(), anyList()))
            .thenReturn(Lists.newArrayList(new BizPrivilegeDetail()));
    }

    // 步骤4：测试用例 - 单组织场景
    @Test
    public void testUserPermissionsCheck_WhenSingleOrgan() {
        // 执行测试
        PrivilegeResponse response = bizPrivilegeService.userPermissionsCheck(
            INPUT_ACCOUNT_ID, INPUT_ORG_ID, INPUT_PRIVILEGES, false);

        // 验证结果
        assertNotNull("响应对象不应为空", response);
        assertEquals("权限数量不匹配", 
            1, response.getBizPrivilegeModels().size());
        
        // 验证依赖调用
        verify(icRolePrivilegePlusServiceAdapt, times(1))
            .getPrivilegesByUser(eq(INPUT_ORG_ID), eq(INPUT_ACCOUNT_ID), anyList());
        verify(rpcOrgPlusServiceAdapt, never()).getOrganRelationByBranchOrgId(anyString());
    }

    // 测试用例 - 多组织场景（含根组织）
    @Test
    public void testUserPermissionsCheck_WhenMultipleOrgan() {
        // 配置根组织场景
        BizSubOrganOutput mockSubOrgan = new BizSubOrganOutput();
        mockSubOrgan.setRootOrgOid(INPUT_ROOT_ORG_ID);
        when(rpcOrgPlusServiceAdapt.getOrganRelationByBranchOrgId(any()))
            .thenReturn(mockSubOrgan);
        
        // 执行测试
        PrivilegeResponse response = bizPrivilegeService.userPermissionsCheck(
            INPUT_ACCOUNT_ID, INPUT_ORG_ID, INPUT_PRIVILEGES, true);

        // 验证结果
        assertEquals("合并后权限数量应为2", 
            2, response.getBizPrivilegeModels().size());
        
        // 验证依赖调用
        verify(icRolePrivilegePlusServiceAdapt, times(2))
            .getPrivilegesByUser(anyString(), eq(INPUT_ACCOUNT_ID), anyList());
    }

    // 测试用例 - 根组织查询异常场景
    @Test
    public void testUserPermissionsCheck_WhenRootOrganQueryFails() {
        // 配置异常场景
        when(rpcOrgPlusServiceAdapt.getOrganRelationByBranchOrgId(anyString()))
            .thenThrow(new RuntimeException("模拟服务异常"));

        // 执行测试
        PrivilegeResponse response = bizPrivilegeService.userPermissionsCheck(
            INPUT_ACCOUNT_ID, INPUT_ORG_ID, INPUT_PRIVILEGES, true);

        // 验证结果
        assertEquals("主组织权限应保留", 
            1, response.getBizPrivilegeModels().size());
        
        // 验证日志记录（需实际项目添加日志验证）
    }

    // 步骤5：对象正确性检查
    @Test
    public void testDependencyInjection() {
        assertNotNull("RpcOrgPlusServiceAdapt注入失败",
            ReflectionTestUtils.getField(bizPrivilegeService, "rpcOrgPlusServiceAdapt"));
        
        // 验证非@Autowired字段注入
        ReflectionTestUtils.setField(bizPrivilegeService, "hiddenClassKeys", "test_config");
        assertEquals("hiddenClassKeys注入失败",
            "test_config", ReflectionTestUtils.getField(bizPrivilegeService, "hiddenClassKeys"));
    }
}
