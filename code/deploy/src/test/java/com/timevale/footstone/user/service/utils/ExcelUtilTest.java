package com.timevale.footstone.user.service.utils;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.regex.Pattern;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @since 2023/5/31 19:51
 */
@RunWith(MockitoJUnitRunner.class)
public class ExcelUtilTest {

  @Test
  public void testMemberNameParttern() {
    String pattern = "^[A-Za-z\u4e00-\u9fa5\\d]{1,21}$";
    String a1 = "你好-";
    String a2 = "你好.";
    String a3 = "a/";
    AssertSupport.assertTrue(!Pattern.matches(pattern, a1), new RuntimeException("正则匹配错误:" + a1));
    AssertSupport.assertTrue(!Pattern.matches(pattern, a2), new RuntimeException("正则匹配错误:" + a2));
    AssertSupport.assertTrue(!Pattern.matches(pattern, a3), new RuntimeException("正则匹配错误:" + a3));
  }

  @Test
  public void readDeptsV3ByEasyExcel() {
    ExcelUtil.readDeptsV3ByEasyExcel(new ByteArrayInputStream(new byte[1]));
  }

}