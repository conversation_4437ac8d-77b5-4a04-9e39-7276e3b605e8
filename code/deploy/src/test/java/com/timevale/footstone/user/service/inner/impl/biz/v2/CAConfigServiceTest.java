package com.timevale.footstone.user.service.inner.impl.biz.v2;

import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CAConfigServiceTest {


    @InjectMocks
    private CAConfigService caConfigService;

    @Test
    public void test() throws Exception {
        caConfigService.supportUpdateCa = "ZJCA OCA1,ZJCA,ZJCA-SINGLE,ZJCA OCA3,ZACA,TEST23";

        caConfigService.afterPropertiesSet();

        CAConfigService.conditionBySupportUpdateCa("ZACA");

        Map<String, ConfigChange> changes = new HashMap<>();
        ConfigChange configChange = new ConfigChange("application",
                "ca.support.update.issuser",
                "", "ZJCA OCA1,ZJCA,ZJCA-SINGLE,ZJCA OCA3,ZACA,TEST23", null);
        changes.put("ca.support.update.issuser", configChange);


        ConfigChangeEvent changeEvent = new ConfigChangeEvent("application", changes);
        caConfigService.intervalChangeListener(changeEvent);


    }
}
