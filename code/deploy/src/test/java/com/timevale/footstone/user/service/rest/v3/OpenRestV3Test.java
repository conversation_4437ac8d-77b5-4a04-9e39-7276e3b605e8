package com.timevale.footstone.user.service.rest.v3;

import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizOpenApiServiceImplV3Impl;
import com.timevale.footstone.user.service.model.BizAppIdModel;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.v3.AccountBindUrlRequest;
import com.timevale.footstone.user.service.model.account.request.v3.AccountUnbindUrlRequest;
import com.timevale.footstone.user.service.model.account.response.v3.AccountBindUrlResponse;
import com.timevale.footstone.user.service.model.account.response.v3.AccountUnbindUrlResponse;
import com.timevale.mandarin.weaver.utils.RequestContext;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OpenRestV3Test {

    @Mock
    private BizOpenApiServiceImplV3Impl mockApiServiceImplV3;

    @InjectMocks
    private OpenRestV3 openRestV3UnderTest;

    @Test
    public void testGetAccountUnbindUrl() {
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.addHeader("X-Tsign-Open-App-Id","**********");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);



        // Setup
        final AccountUnbindUrlRequest request = new AccountUnbindUrlRequest();
        request.setAccount("***********");
        request.setHideTopBar(false);
        request.setRedirectUrl("redirectUrl");
        request.setCustomBizNum("customBizNum");

        // Configure BizOpenApiServiceImplV3Impl.builderAccountUnbindUrl(...).
        final AccountUnbindUrlResponse accountUnbindUrlResponse = new AccountUnbindUrlResponse("accountUnbindUrl",
                "accountUnbindShortUrl");
        final AccountUnbindUrlRequest request1 = new AccountUnbindUrlRequest();
        request1.setAccount("***********");
        request1.setHideTopBar(false);
        request1.setRedirectUrl("redirectUrl");
        request1.setCustomBizNum("customBizNum");

        BizAppIdModel appIdModel  = new BizAppIdModel();
        appIdModel.setAppId("**********");
//        when(mockApiServiceImplV3.builderAccountUnbindUrl(request1,appIdModel)).thenReturn(accountUnbindUrlResponse);

        WrapperResponse<AccountUnbindUrlResponse> result = openRestV3UnderTest.getAccountUnbindUrl(request);
    }

    @Test
    public void testGetAccountBindUrl() {
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(servletRequest));
        servletRequest.addHeader(HeaderConstants.HEADER_OPERATOR_ID, "5121f65e93484712ae04f869f77ded6e");
        servletRequest.addHeader("X-Tsign-Open-App-Id","**********");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        RequestContext.put("httpServletRequest",servletRequest);
        // Setup
        final AccountBindUrlRequest request = new AccountBindUrlRequest();
        request.setAccount("***********");
        request.setHideTopBar(false);
        request.setRedirectUrl("redirectUrl");
        request.setCustomBizNum("customBizNum");
        BizAppIdModel appIdModel  = new BizAppIdModel();
        appIdModel.setAppId("**********");
        // Configure BizOpenApiServiceImplV3Impl.builderAccountUnbindUrl(...).
        final AccountBindUrlResponse accountUnbindUrlResponse = new AccountBindUrlResponse("accountUnbindUrl");
        when(mockApiServiceImplV3.builderAccountBindUrl(Mockito.any(),Mockito.any())).thenReturn(accountUnbindUrlResponse);

        openRestV3UnderTest.getAccountBindUrl(request);
    }

    @Test
    public void testGetAccountUnbindUrlErrorParam() {
        try {
            AccountUnbindUrlRequest request = new AccountUnbindUrlRequest();
            request.setAccount("");
            openRestV3UnderTest.getAccountUnbindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("account必填", e.getMessage());
        }

        try {
            AccountUnbindUrlRequest request = new AccountUnbindUrlRequest();
            request.setAccount("********");
            openRestV3UnderTest.getAccountUnbindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("account格式有误", e.getMessage());
        }

        try {
            AccountUnbindUrlRequest request = new AccountUnbindUrlRequest();
            request.setAccount("***********");
            request.setHideTopBar(false);
            request.setRedirectUrl("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
            request.setCustomBizNum("customBizNum");
            openRestV3UnderTest.getAccountUnbindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("redirectUrl长度过长，不超过256", e.getMessage());
        }

        try {
            AccountUnbindUrlRequest request = new AccountUnbindUrlRequest();
            request.setAccount("***********");
            request.setHideTopBar(false);
            request.setRedirectUrl("");
            request.setCustomBizNum("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
            openRestV3UnderTest.getAccountUnbindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("customBizNum长度过长，不超过256", e.getMessage());
        }
    }

    @Test
    public void testGetAccountBindUrlErrorParam() {
        try {
            AccountBindUrlRequest request = new AccountBindUrlRequest();
            request.setAccount("");
            openRestV3UnderTest.getAccountBindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("account必填", e.getMessage());
        }

        try {
            AccountBindUrlRequest request = new AccountBindUrlRequest();
            request.setAccount("********");
            openRestV3UnderTest.getAccountBindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("account格式有误", e.getMessage());
        }

        try {
            AccountBindUrlRequest request = new AccountBindUrlRequest();
            request.setAccount("***********");
            request.setChangeType("1");
            request.setHideTopBar(false);
            request.setRedirectUrl("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
            request.setCustomBizNum("customBizNum");
            openRestV3UnderTest.getAccountBindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("参数格式异常: changeType, 请确认: 1", e.getMessage());
        }

        try {
            AccountBindUrlRequest request = new AccountBindUrlRequest();
            request.setAccount("***********");
            request.setHideTopBar(false);
            request.setRedirectUrl("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
            request.setCustomBizNum("customBizNum");
            openRestV3UnderTest.getAccountBindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("redirectUrl长度过长，不超过256", e.getMessage());
        }

        try {
            AccountBindUrlRequest request = new AccountBindUrlRequest();
            request.setAccount("***********");
            request.setHideTopBar(false);
            request.setRedirectUrl("");
            request.setCustomBizNum("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
            openRestV3UnderTest.getAccountBindUrl(request);
        } catch (Exception e) {
            Assert.assertEquals("customBizNum长度过长，不超过256", e.getMessage());
        }
    }
}