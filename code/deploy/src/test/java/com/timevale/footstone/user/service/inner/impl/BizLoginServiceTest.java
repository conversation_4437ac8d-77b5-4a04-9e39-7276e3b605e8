package com.timevale.footstone.user.service.inner.impl;

import static com.timevale.footstone.user.service.constants.ThirdPartyConstant.SESSION_KEY;
import static com.timevale.footstone.user.service.constants.ThirdPartyConstant.UNION_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSONObject;
import com.timevale.doccooperation.service.util.UUIDUtil;
import com.timevale.footstone.user.service.inner.impl.biz.BizLoginServiceImpl;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.model.thirdlogin.request.ThirdLoginInfoRequestParam;
import com.timevale.footstone.user.service.utils.RemoteHttpUtil;
import com.timevale.framework.puppeteer.enums.PropertyChangeType;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @version 2024/8/15 10:02
 */
@RunWith(MockitoJUnitRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@ContextConfiguration(classes = Application.class)
public class BizLoginServiceTest {

    @InjectMocks
    private BizLoginServiceImpl bizLoginService;

    @InjectMocks
    private SystemProperty systemProperty;

    @Before
    public void setUp(){
        ReflectionTestUtils.setField(systemProperty,"thirdLoginConfig","{\"DOUYIN_LOAN\": \"https://developer.toutiao.com/api/apps/v2/jscode2session\"}");
        ReflectionTestUtils.setField(bizLoginService,"systemProperty",systemProperty);
        Map<String, ConfigChange> changeMap = new HashMap<>();
        String perporty = "third.login.info.request.config";
        String newValue = "{\"DOUYIN_LOAN\": \"https://developer.toutiao.com/api/apps/v2/jscode2session\"}";
        ConfigChange configChange = new ConfigChange("application",perporty,"",newValue,
                PropertyChangeType.MODIFIED);
        changeMap.put("third.login.info.request.config",configChange);
        ConfigChangeEvent changeEvent =  new ConfigChangeEvent("application",changeMap);
        systemProperty.intervalChangeListener(changeEvent);
    }


    @Test
    public void queryThirdPartyUserInfoTest(){
        ThirdLoginInfoRequestParam param = new ThirdLoginInfoRequestParam();
        param.setThirdType("DOUYIN_LOAN");
        MockedStatic<RemoteHttpUtil> httpUntil = mockStatic(RemoteHttpUtil.class);
        JSONObject result = new JSONObject();
        JSONObject data = new JSONObject();
        data.put(UNION_ID, UUIDUtil.generateUUID());
        data.put(SESSION_KEY,UUIDUtil.generateUUID());
        httpUntil.when(RemoteHttpUtil.sendByPost(any(), any(), any(), any())).thenReturn(result);
        bizLoginService.queryThirdPartyUserInfo(param);
        result.put("data",data);
        bizLoginService.queryThirdPartyUserInfo(param);
    }
}
