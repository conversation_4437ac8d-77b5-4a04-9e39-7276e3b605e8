package com.timevale.footstone.user.service.inner.impl.biz;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.besp.lowcode.integration.response.OrgResponse;
import com.timevale.besp.lowcode.integration.response.TenantInfoResponse;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.output.v3.BizDeptSubListOutputV3;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.biz.BizThirdPartyOrgSyncService;
import com.timevale.footstone.user.service.inner.biz.v3.ThirdPlatformNameSearchService;
import com.timevale.footstone.user.service.inner.impl.BizDeptServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.FileSystemServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdaptV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizDeptServiceImplV3;
import com.timevale.footstone.user.service.model.dept.request.v3.SubListRequest;
import com.timevale.footstone.user.service.model.dept.response.v3.DeptBaseResponseV3;
import com.timevale.footstone.user.service.utils.MockUntils;

import com.timevale.mandarin.weaver.utils.RequestContext;
import java.util.HashMap;
import java.util.List;

import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @date 2023/6/8 10:03
 */

@RunWith(MockitoJUnitRunner.Silent.class)
public class BizDeptServiceImplV3Test {

    @InjectMocks
    private BizDeptServiceImplV3 bizDeptServiceImplV3;


    @Mock
    private RpcDeptPlusServiceAdaptV3 deptPlusServiceAdaptV3;

    @Mock
    private RpcSaaSVipServiceAdapt rpcSaasVipServiceAdapt;

    @Mock
    private RpcIcOrgPlusServiceAdapt orgPlusServiceAdapt;

    @Mock
    private BizDeptServiceImpl bizDeptService;

    @Mock
    private IdAdapt idAdapt;

    @Mock
    private FileSystemServiceAdapt fileSystemServiceAdapt;

    @Mock
    private BizThirdPartyOrgSyncService bizThirdPartyOrgSyncService;

    @Mock
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;

    @Mock
    private ThirdPlatformNameSearchService thirdPlatformNameSearchService;


    @Before
    public void setUp() {
        when(orgPlusServiceAdapt.isRootMultiOrgan(any())).thenReturn(true);
        when(deptPlusServiceAdaptV3.getParentDeptTree(anyString(), anyString(), anyBoolean())).thenReturn(MockUntils.getBizGetParentDeptTreeOutputV3());
        when(rpcSaasVipServiceAdapt.getVipInfo(any())).thenReturn(MockUntils.getAccountVipQueryOutput());
        when(rpcSaasVipServiceAdapt.getVipInfo(any())).thenReturn(MockUntils.getAccountVipQueryOutput());
        when(orgPlusServiceAdapt.needMultiOrgData(any(), anyBoolean())).thenReturn(true);
        when(deptPlusServiceAdaptV3.deptSearchV3(any(), anyString(), any(), anyInt(), anyInt(), anyBoolean())).thenReturn(MockUntils.getDeptSearchListResponseV3());
        ReflectionTestUtils.setField(bizDeptServiceImplV3, "saasVipBatchPartition", 50);
        Map<String, List<MemberDetail>> deptLeader = new HashMap<>();
        deptLeader.put("deptId", Lists.newArrayList(MockUntils.getMemberDetail()));
        when(deptPlusServiceAdaptV3.batchGetDeptLeaders(any())).thenReturn(deptLeader);
        when(deptPlusServiceAdaptV3.subList(any())).thenReturn(MockUntils.mockBizDeptSubListOutputV3());
    }

    @Test
    public void getParentDeptTreeTest() {
        bizDeptServiceImplV3.getParentDeptTree("orgId", "deptId");
    }

    @Test
    public void deptSearchTest() {
        bizDeptServiceImplV3.deptSearch("orgId", "deptId", true, 10, 0);
    }

    @Test
    public void testGetMyRespDepts() {
        when(this.deptPlusServiceAdaptV3.getMyRespDepts(anyString(), anyString())).thenReturn(
                new BizDeptSubListOutputV3());
        this.bizDeptServiceImplV3.getMyRespDepts("orgId", "memberOid");
    }

    @Test
    public void addVipCodeToSubList() {
        List<DeptBaseResponseV3> deptBaseResponseV3List = Lists.newArrayList(MockUntils.getDeptBaseResponseV3());
        this.bizDeptServiceImplV3.addVipCodeToSubList(deptBaseResponseV3List);
    }

    @Test
    public void subListTest() {
        when(bizThirdPartyOrgSyncService.getThirdPartyUserId("orgId", "FEI_SHU")).thenReturn("tenantKey");
        TenantInfoResponse tenantInfo = new TenantInfoResponse();
        tenantInfo.setTenantName("测试");
        when(rpcTripartiteServiceAdapt.getTenantInfo(any(),any())).thenReturn(tenantInfo);
        List<OrgResponse> responses = com.google.common.collect.Lists.newArrayList();
        OrgResponse response = new OrgResponse();
        response.setName("name");
        response.setParentId("ALL");
        response.setHasChild(true);
        responses.add(response);
        when(rpcTripartiteServiceAdapt.getDeptSubList(any(),any(),any())).thenReturn(responses);
        bizDeptServiceImplV3.subList(SubListRequest.builder().orgId("orgId").deptId("1").item("1").singleOrg(true).platform("FEI_SHU").build());
        SubListRequest subListRequest = SubListRequest.builder()
                .orgId("orgId")
                .deptId("deptId")
                .item("1")
                .singleOrg(true)
                .platform("ESIGN")
                .showDeptLeader(true).build();
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"tentenid_id");
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"operator-id");
        servletRequest.setAttribute(HeaderConstants.HEADER_REAL_IP,"*************");
        servletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"tentenid_id");
        RequestContext.put("httpServletRequest",servletRequest);
        bizDeptServiceImplV3.subList(subListRequest);
    }
}
