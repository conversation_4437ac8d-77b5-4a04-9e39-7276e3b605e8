package com.timevale.footstone.user.service.inner.impl.biz;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.account.service.model.service.biz.icuser.output.BizFatICUserOutput;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.account.service.model.service.mods.idcard.MultiIdcardContentCollection;
import com.timevale.easun.service.model.account.output.BizFatICUserRealNameOutput;
import com.timevale.easun.service.model.organization.output.GetThirdPartTenantInfoOutput;
import com.timevale.easun.service.model.organization.output.ThirdPartyBindOrgSceneOutput;
import com.timevale.footstone.user.model.third.OrgBindSceneInfoDTO;
import com.timevale.footstone.user.model.third.OrgThirdPartyBindSceneDTO;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcThirdPartyPlusServiceAdapt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BizThirdpartyServiceTest {

    @InjectMocks
    private BizThirdpartyServiceImpl bizProcessService;

    @Mock
    private RpcThirdPartyPlusServiceAdapt rpcThirdPartyPlusServiceAdapt;

    @Mock
    private RpcIcUserPlusServiceAdapt rpcIcUserPlusService;

    @Mock
    private CommonConfig commonConfig;

    @Test
    public void getThirdPartyBindOrgSceneTest(){
        ThirdPartyBindOrgSceneOutput thirdPartyBindOrgSceneOutput = new ThirdPartyBindOrgSceneOutput();
        thirdPartyBindOrgSceneOutput.setBindScene("scene");
        List<ThirdPartyBindOrgSceneOutput.BindOrgan> bindOrgans = Lists.newArrayList();
        ThirdPartyBindOrgSceneOutput.BindOrgan bindOrgan = new ThirdPartyBindOrgSceneOutput.BindOrgan();
        bindOrgan.setOrganGuid("organGuid");
        bindOrgan.setOrganName("organName");
        bindOrgan.setOrganOuid("organOuid");
        bindOrgans.add(bindOrgan);
        thirdPartyBindOrgSceneOutput.setBindOrgan(bindOrgans);
        CommonConfig commonConfig = new CommonConfig();
        OrgThirdPartyBindSceneDTO sceneDTO = new OrgThirdPartyBindSceneDTO();
        sceneDTO.setClientId("FEI_SHU");
        sceneDTO.setIconUrl("www.baidu.com");
        List<OrgBindSceneInfoDTO> bindSceneInfos = Lists.newArrayList();
        OrgBindSceneInfoDTO orgBindSceneInfoDTO = new OrgBindSceneInfoDTO();
        orgBindSceneInfoDTO.setBindScene("test");
        orgBindSceneInfoDTO.setCancelText("");
        orgBindSceneInfoDTO.setConfirmText("");
        orgBindSceneInfoDTO.setIntroduceText("");
        sceneDTO.setBindSceneInfos(bindSceneInfos);
        commonConfig.getOrgBindSceneInfoDTOMap().put("FEI_SHU",sceneDTO);
        ReflectionTestUtils.setField(bizProcessService,"commonConfig",commonConfig);
        when(rpcThirdPartyPlusServiceAdapt.getThirdPartyBindOrgScene(any(),any(),any())).thenReturn(thirdPartyBindOrgSceneOutput);
        bizProcessService.getThirdPartyBindOrgScene("FEI_SHU","operatorOid","tk","tkUserId");
    }

    @Test
    public void getThirdPartyTenantInfoTest(){
        BizFatICUserRealNameOutput userRealNameOutput = new BizFatICUserRealNameOutput();
        BizFatICUserOutput bizFatICUserOutput = new BizFatICUserOutput();
        MultiIdcardContentCollection<ContentSecurity> idcards = new MultiIdcardContentCollection<ContentSecurity>();
        List<IdcardContentThirdparty<ContentSecurity>> idcardsList =new ArrayList<>();
        IdcardContentThirdparty<ContentSecurity> idcardContentThirdparty = new IdcardContentThirdparty<>();
        IdcardThirdparty idcardThirdparty = new IdcardThirdparty();
        idcardThirdparty.setThirdpartyUserType("usertype");
        idcardThirdparty.setThirdpartyUserId("userId");
        idcardContentThirdparty.setDetail(idcardThirdparty);
        idcardsList.add(idcardContentThirdparty);
        idcards.addThirdparty(idcardContentThirdparty);
        bizFatICUserOutput.setIdcards(idcards);
        userRealNameOutput.setAccount(bizFatICUserOutput);
        when(rpcIcUserPlusService.getFatRealNameByOuid(any())).thenReturn(userRealNameOutput);
        GetThirdPartTenantInfoOutput getThirdPartTenantInfoOutput = new GetThirdPartTenantInfoOutput();
        getThirdPartTenantInfoOutput.setTenantName("tenantName");
//        when(rpcThirdPartyPlusServiceAdapt.getThirdPartyTenantInfo(any(),any())).thenReturn(getThirdPartTenantInfoOutput);
        bizProcessService.getThirdPartyTenantInfo("tenantOid");
    }
}
