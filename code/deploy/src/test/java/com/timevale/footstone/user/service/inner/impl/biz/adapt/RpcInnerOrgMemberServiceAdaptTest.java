package com.timevale.footstone.user.service.inner.impl.biz.adapt;

import com.google.common.collect.Lists;
import com.timevale.account.organization.service.api.RpcInnerOrgMemberService;
import com.timevale.account.organization.service.model.service.biz.input.BizUpdateWeightInput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizMemberGetOutput;
import com.timevale.account.service.api.RpcICUserService;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgMemberServiceAdapt;
import com.timevale.footstone.user.service.utils.MockUntils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/10/24 18:20
 */
@RunWith(MockitoJUnitRunner.class)
public class RpcInnerOrgMemberServiceAdaptTest {

    @InjectMocks
    private RpcInnerOrgMemberServiceAdapt rpcInnerOrgMemberServiceAdapt;


    @Mock
    private RpcInnerOrgMemberService innerOrgMemberService;

    @Mock
    private RpcICUserService icUserService;

    @Before
    public void setUp(){
        List<BizMemberGetOutput> memberGetOutputs = new ArrayList<>();
        memberGetOutputs.add(MockUntils.mockBizMemberGetOutput());
        when(innerOrgMemberService.batchGetMembersByIds(any())).thenReturn(RpcOutput.with(memberGetOutputs));
        List<BizICUserOutput> icUserOutputs = new ArrayList<>();
        icUserOutputs.add(MockUntils.mockBizICUserOutput());
        when(icUserService.batchGet(any())).thenReturn(RpcOutput.with(icUserOutputs));
        when(innerOrgMemberService.batchGetMember(any())).thenReturn(RpcOutput.with(memberGetOutputs));
    }

    @Test
    public void getOidsByMemberIdsTest(){
        rpcInnerOrgMemberServiceAdapt.getOidsByMemberIds("organId", Lists.newArrayList("1"));
    }

    @Test
    public void getMemberBatchOrganTest(){
        rpcInnerOrgMemberServiceAdapt.getMemberBatchOrgan(Lists.newArrayList("x"),"memberGuid");
    }

    @Test
    public void batchSetWeightTest(){
        rpcInnerOrgMemberServiceAdapt.batchSetWeight(Lists.newArrayList(new BizUpdateWeightInput()));
    }

}
