package com.timevale.footstone.user.service.inner.impl.biz.adapt.third;

import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.input.BizEsAccountIdcardSearchInput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdCardDetailOutput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.page.Pages;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class RpcIdCardAdaptTest {

    @InjectMocks
    private RpcIdCardAdapt rpcIdCardAdapt;

    @Mock
    private RpcEsAccountServiceAdapt rpcEsAccountServiceAdapt;

    // 测试数据定义
    private static final String INPUT_ORG_OUID = "orgOuid"; // 测试用组织OUID
    private static final String INPUT_ACCOUNT_ID = "accountId"; // 测试用账户ID
    private static final String INPUT_CLIENT_ID = ClientEnum.WE_WORK.getClientId(); // 测试用客户端ID
    private static final String INPUT_TENANT_KEY = "tenantKey"; // 测试用租户密钥
    private static final String INPUT_BUILTIN_THIRD_PARTY = "builtinThirdParty"; // 测试用内置第三方标识
    private static final List<String> INPUT_ACCOUNT_IDS = Arrays.asList("acc1", "acc2"); // 测试用账户ID列表
    private static final List<String> INPUT_THIRD_USER_IDS = Arrays.asList("third1", "third2"); // 测试用三方用户ID列表
    private static final List<String> EMPTY_LIST = Collections.emptyList(); // 空列表

    private static final String EXPECTED_TENANT_KEY = "testTenantKey"; // 预期的租户密钥
    private static final String EXPECTED_THIRD_USER_ID = "testThirdUserId"; // 预期的三方用户ID

    private static final Map<String, String> EXPECTED_BATCH_THIRD_USER_IDS = new HashMap<>();
    private static final Map<String, String> EXPECTED_BATCH_OUIDS = new HashMap<>();

    static {
        EXPECTED_BATCH_THIRD_USER_IDS.put("acc1", "third1");
        EXPECTED_BATCH_THIRD_USER_IDS.put("acc2", "third2");

        EXPECTED_BATCH_OUIDS.put("third1", "acc1");
        EXPECTED_BATCH_OUIDS.put("third2", "acc2");
    }

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // ================================
    // getTenantKey 测试用例
    // ================================

    /**
     * TC01: orgOuid=null, clientId="WECHAT_WORK"
     * 输入: orgOuid=null, clientId=INPUT_CLIENT_ID
     * 预期输出: null
     * 描述: 参数校验失败
     */
    @Test
    public void testGetTenantKey_OrgOuidNull() {
        String result = rpcIdCardAdapt.getTenantKey(null, INPUT_CLIENT_ID);
        assertNull(result);
    }

    /**
     * TC02: pagerResult=null
     * 输入: orgOuid=INPUT_ORG_OUID, clientId=INPUT_CLIENT_ID, pagerResult返回null
     * 预期输出: null
     * 描述: rpc调用返回null
     */
    @Test
    public void testGetTenantKey_PagerResultNull() {
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(null);

        String result = rpcIdCardAdapt.getTenantKey(INPUT_ORG_OUID, INPUT_CLIENT_ID);
        assertNull(result);
    }

    /**
     * TC03: idcardInfos为空
     * 输入: orgOuid=INPUT_ORG_OUID, clientId=INPUT_CLIENT_ID, items返回空
     * 预期输出: null
     * 描述: 没有数据
     */
    @Test
    public void testGetTenantKey_ItemsEmpty() {
        PagerResult<EsAccountIdcardInfoOutput> mockResult = new PagerResult<>();
        mockResult.setItems(Collections.emptyList());

        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(mockResult);

        String result = rpcIdCardAdapt.getTenantKey(INPUT_ORG_OUID, INPUT_CLIENT_ID);
        assertNull(result);
    }

    /**
     * TC04: 正常流程
     * 输入: orgOuid=INPUT_ORG_OUID, clientId=INPUT_CLIENT_ID, 返回有效tenantKey
     * 预期输出: "testTenantKey"
     * 描述: 完整正常流程
     */
    @Test
    public void testGetTenantKey_NormalFlow() {
        EsAccountIdcardInfoOutput mockOutput = new EsAccountIdcardInfoOutput();
        mockOutput.setIdcardInfo(new EsAccountIdCardDetailOutput());
        mockOutput.getIdcardInfo().setValue(EXPECTED_TENANT_KEY);

        PagerResult<EsAccountIdcardInfoOutput> mockResult = new PagerResult<>();
        mockResult.setItems(Collections.singletonList(mockOutput));

        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(mockResult);

        String result = rpcIdCardAdapt.getTenantKey(INPUT_ORG_OUID, INPUT_CLIENT_ID);
        assertEquals(EXPECTED_TENANT_KEY, result);
    }

    // ================================
    // getThirdUserIdByOuid 测试用例
    // ================================

    /**
     * TC11: accountId=null
     * 输入: accountId=null
     * 预期输出: null
     * 描述: 参数校验失败
     */
    @Test
    public void testGetThirdUserIdByOuid_AccountIdNull() {
        String result = rpcIdCardAdapt.getThirdUserIdByOuid(null, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertNull(result);
    }

    /**
     * TC12: pagerResult=null
     * 输入: accountId=INPUT_ACCOUNT_ID, pagerResult返回null
     * 预期输出: null
     * 描述: rpc调用返回null
     */
    @Test
    public void testGetThirdUserIdByOuid_PagerResultNull() {
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(null);

        String result = rpcIdCardAdapt.getThirdUserIdByOuid(INPUT_ACCOUNT_ID, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertNull(result);
    }

    /**
     * TC13: items为空
     * 输入: accountId=INPUT_ACCOUNT_ID, items返回空
     * 预期输出: null
     * 描述: 没有数据
     */
    @Test
    public void testGetThirdUserIdByOuid_ItemsEmpty() {
        PagerResult<EsAccountIdcardInfoOutput> mockResult = new PagerResult<>();
        mockResult.setItems(Collections.emptyList());

        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(mockResult);

        String result = rpcIdCardAdapt.getThirdUserIdByOuid(INPUT_ACCOUNT_ID, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertNull(result);
    }

    /**
     * TC14: 正常流程
     * 输入: accountId=INPUT_ACCOUNT_ID, 返回有效thirdUserId
     * 预期输出: "testThirdUserId"
     * 描述: 完整正常流程
     */
    @Test
    public void testGetThirdUserIdByOuid_NormalFlow() {
        EsAccountIdcardInfoOutput mockOutput = new EsAccountIdcardInfoOutput();
        mockOutput.setIdcardInfo(new EsAccountIdCardDetailOutput());
        mockOutput.getIdcardInfo().setValue(EXPECTED_THIRD_USER_ID);

        PagerResult<EsAccountIdcardInfoOutput> mockResult = new PagerResult<>();
        mockResult.setItems(Collections.singletonList(mockOutput));

        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(mockResult);

        String result = rpcIdCardAdapt.getThirdUserIdByOuid(INPUT_ACCOUNT_ID, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertEquals(EXPECTED_THIRD_USER_ID, result);
    }

    // ================================
    // batchGetThirdUserIdByOuids 测试用例
    // ================================

    /**
     * TC21: accountIds为空
     * 输入: accountIds=null
     * 预期输出: 空map
     * 描述: 参数校验失败
     */
    @Test
    public void testBatchGetThirdUserIdByOuids_AccountIdsEmpty() {
        Map<String, String> result = rpcIdCardAdapt.batchGetThirdUserIdByOuids(null, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * TC22: builtinThirdParty为空
     * 输入: accountIds=INPUT_ACCOUNT_IDS, clientId=null
     * 预期输出: 空map
     * 描述: 参数校验失败
     */
    @Test
    public void testBatchGetThirdUserIdByOuids_BuiltinThirdPartyEmpty() {
        Map<String, String> result = rpcIdCardAdapt.batchGetThirdUserIdByOuids(INPUT_ACCOUNT_IDS, ClientEnum.GAME.getClientId(), INPUT_TENANT_KEY);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * TC23: pagerResult=null
     * 输入: accountIds=INPUT_ACCOUNT_IDS, pagerResult返回null
     * 预期输出: 空map
     * 描述: rpc调用返回null
     */
    @Test
    public void testBatchGetThirdUserIdByOuids_PagerResultNull() {
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(null);

        Map<String, String> result = rpcIdCardAdapt.batchGetThirdUserIdByOuids(INPUT_ACCOUNT_IDS, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * TC24: 正常流程
     * 输入: accountIds=INPUT_ACCOUNT_IDS, 返回有效映射
     * 预期输出: {"acc1":"third1", "acc2":"third2"}
     * 描述: 完整正常流程
     */
    @Test
    public void testBatchGetThirdUserIdByOuids_NormalFlow() {
        List<EsAccountIdcardInfoOutput> mockItems = new ArrayList<>();
        for (int i = 0; i < INPUT_ACCOUNT_IDS.size(); i++) {
            EsAccountIdcardInfoOutput item = new EsAccountIdcardInfoOutput();
            item.setIdcardInfo(new EsAccountIdCardDetailOutput());
            item.getIdcardInfo().setValue(INPUT_THIRD_USER_IDS.get(i));
            item.setId(new EsId());
            item.getId().setOuid(INPUT_ACCOUNT_IDS.get(i));
            mockItems.add(item);
        }

        PagerResult<EsAccountIdcardInfoOutput> mockResult = new PagerResult<>();
        mockResult.setItems(mockItems);

        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(mockResult);

        Map<String, String> result = rpcIdCardAdapt.batchGetThirdUserIdByOuids(INPUT_ACCOUNT_IDS, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertEquals(EXPECTED_BATCH_THIRD_USER_IDS, result);
    }

    // ================================
    // batchGetOuidsByThirdUserIds 测试用例
    // ================================

    /**
     * TC31: thirdUserIds为空
     * 输入: thirdUserIds=null
     * 预期输出: 空map
     * 描述: 参数校验失败
     */
    @Test
    public void testBatchGetOuidsByThirdUserIds_ThirdUserIdsEmpty() {
        Map<String, String> result = rpcIdCardAdapt.batchGetOuidsByThirdUserIds(null, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * TC32: builtinThirdParty为空
     * 输入: thirdUserIds=INPUT_THIRD_USER_IDS, clientId=null
     * 预期输出: 空map
     * 描述: 参数校验失败
     */
    @Test
    public void testBatchGetOuidsByThirdUserIds_BuiltinThirdPartyEmpty() {
        Map<String, String> result = rpcIdCardAdapt.batchGetOuidsByThirdUserIds(INPUT_THIRD_USER_IDS, ClientEnum.GAME.getClientId(), INPUT_TENANT_KEY);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * TC33: pagerResult=null
     * 输入: thirdUserIds=INPUT_THIRD_USER_IDS, pagerResult返回null
     * 预期输出: 空map
     * 描述: rpc调用返回null
     */
    @Test
    public void testBatchGetOuidsByThirdUserIds_PagerResultNull() {
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(null);

        Map<String, String> result = rpcIdCardAdapt.batchGetOuidsByThirdUserIds(INPUT_THIRD_USER_IDS, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * TC34: 正常流程
     * 输入: thirdUserIds=INPUT_THIRD_USER_IDS, 返回有效映射
     * 预期输出: {"third1":"acc1", "third2":"acc2"}
     * 描述: 完整正常流程
     */
    @Test
    public void testBatchGetOuidsByThirdUserIds_NormalFlow() {
        List<EsAccountIdcardInfoOutput> mockItems = new ArrayList<>();
        for (int i = 0; i < INPUT_THIRD_USER_IDS.size(); i++) {
            EsAccountIdcardInfoOutput item = new EsAccountIdcardInfoOutput();
            item.setIdcardInfo(new EsAccountIdCardDetailOutput());
            item.getIdcardInfo().setValue(INPUT_THIRD_USER_IDS.get(i));
            item.setId(new EsId());
            item.getId().setOuid(INPUT_ACCOUNT_IDS.get(i));
            mockItems.add(item);
        }

        PagerResult<EsAccountIdcardInfoOutput> mockResult = new PagerResult<>();
        mockResult.setItems(mockItems);

        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any(BizEsAccountIdcardSearchInput.class))).thenReturn(mockResult);

        Map<String, String> result = rpcIdCardAdapt.batchGetOuidsByThirdUserIds(INPUT_THIRD_USER_IDS, INPUT_CLIENT_ID, INPUT_TENANT_KEY);
        assertEquals(EXPECTED_BATCH_OUIDS, result);
    }
}
