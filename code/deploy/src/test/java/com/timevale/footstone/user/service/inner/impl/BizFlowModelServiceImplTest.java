package com.timevale.footstone.user.service.inner.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.account.flow.service.api.RpcFlowDefineService;
import com.timevale.account.flow.service.model.output.CreateFlowDefineOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizGetOrganOutput;
import com.timevale.contractapproval.facade.api.ApprovalInstanceRpcService;
import com.timevale.contractapproval.facade.api.ApprovalTemplateRpcService;
import com.timevale.contractapproval.facade.dto.ApprovalAccountDTO;
import com.timevale.contractapproval.facade.dto.ApprovalLogDTO;
import com.timevale.contractapproval.facade.dto.ApprovalTemplateDTO;
import com.timevale.contractapproval.facade.output.VirtualStartApprovalOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.model.account.UserAccountDTO;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.UserCenterAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgServiceAdapt;
import com.timevale.footstone.user.service.model.flow.model.FlowNode;
import com.timevale.footstone.user.service.model.flow.model.FlowSeq;
import com.timevale.footstone.user.service.model.flow.model.FlowUser;
import com.timevale.footstone.user.service.model.flow.request.CreateFlowModelRequest;
import com.timevale.footstone.user.service.model.flow.request.ModelTypeRequest;
import com.timevale.gray.config.manage.service.api.GrayscaleRpcService;

import java.util.ArrayList;
import java.util.List;

import com.timevale.gray.config.manage.service.model.base.BaseResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2021/6/22
 */
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizFlowModelServiceImplTest {

    @InjectMocks private BizFlowModelServiceImpl bizFlowModelService;
    @Mock private RpcFlowDefineService rpcFlowDefineService;
    @Mock private IdAdapt idAdapt;
    @Mock private RpcInnerOrgServiceAdapt innerOrgServiceAdapt;
    @Mock private RpcDeptPlusServiceAdapt deptAdapt;
    @Mock
    private GrayscaleRpcService grayscaleRpcService;
    @Mock
    private UserCenterAdapt userCenterAdapt;
    @Mock
    private ApprovalTemplateRpcService approvalTemplateRpcService;
    @Mock
    private ApprovalInstanceRpcService approvalInstanceRpcService;
    ;

    private static final String orgOid = "orgOid";
    private static final String orgId = "orgId";
    private static final String orgUid = "orgUid";
    private static final String memberId = "memberId";

    private static final BizGetOrganOutput organ;

    static {
        organ = new BizGetOrganOutput();
        organ.setSource("DEFAULT");
    }

    @Before
    public void setUp() throws Exception {

        when(idAdapt.orgIdToOrganId(orgOid)).thenReturn(orgId);
//        when(idAdapt.accountUid(orgOid)).thenReturn(orgUid);
        when(innerOrgServiceAdapt.getOrganById(orgId)).thenReturn(organ);
    }

    @Test
    public void test_createFlowModel() {
        /*
           case: mock & 冒烟
        */
        CreateFlowDefineOutput createFlowDefineOutput = new CreateFlowDefineOutput();
        createFlowDefineOutput.setModelId("1");
        createFlowDefineOutput.setOrganId(orgOid);
        RpcOutput<CreateFlowDefineOutput> output = RpcOutput.with(createFlowDefineOutput);
        when(rpcFlowDefineService.createFlowDefine(any())).thenReturn(output);
//        when(deptAdapt.getAllChildDept(orgId, organ.getSource())).thenReturn(Lists.newArrayList());

        List<FlowNode> flowNodes = Lists.newArrayList();
        flowNodes.add(
                new FlowNode(
                        "flow_1",
                        "发起人",
                        "START",
                        null,
                        Sets.newHashSet(new FlowUser("ALL", "所有部门", 4))));
        flowNodes.add(
                new FlowNode(
                        "flow_2",
                        "发起人",
                        "USERTASK",
                        "SIGNAL",
                        Sets.newHashSet(new FlowUser("ALL", "所有部门", 4))));
        flowNodes.add(
                new FlowNode(
                        "flow_3",
                        "结束",
                        "END",
                        null,
                        Sets.newHashSet(new FlowUser("ALL", "所有部门", 4))));
        List<FlowSeq> flowSeqs = Lists.newArrayList();
        flowSeqs.add(new FlowSeq("seq_1", "连线_1", "flow_1", "flow_2"));
        flowSeqs.add(new FlowSeq("seq_2", "连线_2", "flow_2", "flow_3"));
        bizFlowModelService.createFlowModel(
                orgOid, new CreateFlowModelRequest("test", "type", flowNodes, flowSeqs));
    }


    @Test
    public void currentUserModelListTest() {

        UserAccountDTO subjectAccount = new UserAccountDTO();
        subjectAccount.setGid("gid");
        Mockito.when(userCenterAdapt.getAccountByOid(orgId)).thenReturn(subjectAccount);
        BaseResult<Boolean> baseResult = new BaseResult<>();
        baseResult.setData(true);
        Mockito.when(grayscaleRpcService.checkGrayscaleFunctionV2(Mockito.any())).thenReturn(baseResult);

        ApprovalTemplateDTO approvalTemplateDTO = new ApprovalTemplateDTO();
        approvalTemplateDTO.setApprovalTemplateId(1L);
        Mockito.when(approvalTemplateRpcService.queryOpenApprovalTemplate(Mockito.any()))
                .thenReturn(Lists.newArrayList(approvalTemplateDTO));
        ModelTypeRequest modelTypeRequest = new ModelTypeRequest();
        modelTypeRequest.setType("CONTRACT");
        bizFlowModelService.currentUserModelList(orgId, memberId, modelTypeRequest);
    }

    @Test
    public void queryModelDefineDetailTest() {

        UserAccountDTO subjectAccount = new UserAccountDTO();
        subjectAccount.setGid("gid");
        Mockito.when(userCenterAdapt.getAccountByOid(orgId)).thenReturn(subjectAccount);

        UserAccountDTO operatorAccount = new UserAccountDTO();
        operatorAccount.setGid("gid");
        Mockito.when(userCenterAdapt.getAccountByOid(memberId)).thenReturn(operatorAccount);
//        Mockito.when(userCenterAdapt.userDirectDept(orgOid, memberId)).thenReturn(new ArrayList<>());

        BaseResult<Boolean> baseResult = new BaseResult<>();
        baseResult.setData(true);
        Mockito.when(grayscaleRpcService.checkGrayscaleFunctionV2(Mockito.any())).thenReturn(baseResult);

        ApprovalAccountDTO approvalAccountDTO = new ApprovalAccountDTO();
        approvalAccountDTO.setName("name");
        ApprovalLogDTO approvalLogDTO = new ApprovalLogDTO();
        approvalLogDTO.setCandidates(Lists.newArrayList(approvalAccountDTO));
        VirtualStartApprovalOutput output = new VirtualStartApprovalOutput();
        output.setApprovalLogs(Lists.newArrayList(approvalLogDTO));
        Mockito.when(approvalInstanceRpcService.virtualStartApproval(Mockito.any())).thenReturn(output);
        bizFlowModelService.queryModelDefineDetail(orgId, "adapt_111", memberId);
    }
}
