package com.timevale.footstone.user.service.utils;

import static com.timevale.footstone.user.service.constants.CommonConstants.MEMBER_LIMIT_COUNT;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.timevale.account.organization.service.enums.ActivateEnum;
import com.timevale.account.organization.service.model.service.newbiz.output.BizGetOrganOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizListDeptOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizMemberGetOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizSubOrganOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.entity.Dept;
import com.timevale.account.service.enums.AccountType;
import com.timevale.account.service.enums.BuiltinCredentialsEnum;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.icuser.output.BizFatICUserOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.biz.rlnm.output.BizAccountRealnameOutput;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.account.service.model.service.mods.icuser.ICAccountUser;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.icuser.share.GlobalSysCredentials;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentCollection;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentEmail;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentMobile;
import com.timevale.account.service.model.service.mods.idcard.IdcardEmail;
import com.timevale.account.service.model.service.mods.idcard.IdcardMobile;
import com.timevale.account.service.model.service.mods.idcard.MultiIdcardContentCollection;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.model.account.AdminInfo;
import com.timevale.easun.service.model.account.LegalInfo;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.easun.service.model.account.output.EasunSpaceOutput;
import com.timevale.easun.service.model.account.output.SpaceItemDetailOutput;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.easun.service.model.mods.DepartDetail;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.ICOrg.output.BizICCreateMemberOutput;
import com.timevale.easun.service.model.organization.output.BizDeptChildOutput;
import com.timevale.easun.service.model.organization.output.BizDeptDetailOutput;
import com.timevale.easun.service.model.organization.output.v3.BizDeptDetailOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizDeptListOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizDeptSubListOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizGetParentDeptTreeOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizOrgSummaryOutputV3;
import com.timevale.easun.service.model.privilege.mods.BizBuiltinPrivilegeDetail;
import com.timevale.easun.service.model.privilege.mods.PrivilegResourceModel;
import com.timevale.easun.service.model.privilege.mods.ResourceGroupModel;
import com.timevale.easun.service.model.privilege.output.BizEasunGetBuiltinPrivilegesOutput;
import com.timevale.easun.service.model.sender.output.BizApplyWillAssembleAuthOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.inner.impl.biz.conv.BizAccountConv;
import com.timevale.footstone.user.service.model.account.mods.*;
import com.timevale.footstone.user.service.model.account.response.RealNameOrganResponse;
import com.timevale.footstone.user.service.model.account.response.UserInfoResponse;
import com.timevale.footstone.user.service.model.account.response.UserRealNameBaseInfoResponse;
import com.timevale.footstone.user.service.model.address.model.AddressModel;
import com.timevale.footstone.user.service.model.address.response.AddressListResponse;
import com.timevale.footstone.user.service.model.dept.response.v3.DeptBaseResponseV3;
import com.timevale.footstone.user.service.model.dept.response.v3.DeptSearchListResponseV3;
import com.timevale.footstone.user.service.model.member.mods.AdminListOpenResponse;
import com.timevale.footstone.user.service.model.member.mods.DeptBaseResponse;
import com.timevale.footstone.user.service.model.member.mods.DeptSubListResponse;
import com.timevale.footstone.user.service.model.member.mods.MemberBaseResponse;
import com.timevale.footstone.user.service.model.organization.request.OrganSortRequest;
import com.timevale.footstone.user.service.model.organization.response.OrgIdAndNameResponse;
import com.timevale.footstone.user.service.model.organization.response.OrganDetailModel;
import com.timevale.footstone.user.service.model.role.model.RoleModel;
import com.timevale.footstone.user.service.model.role.model.RoleSyncItemModel;
import com.timevale.footstone.user.service.model.sso.manage.SsoOrganizationDomainModel;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.notice.service.model.service.biz.mods.TInviteContextData;
import com.timevale.notice.service.model.service.biz.output.InviteJoinMember;
import com.timevale.notice.service.model.service.biz.output.InviteJoinMembersOutput;
import com.timevale.notificationmanager.service.enums.AgentEnum;
import com.timevale.notificationmanager.service.enums.SendStatusEnum;
import com.timevale.notificationmanager.service.model.optimize.response.SendMsgResponse;
import com.timevale.privilege.service.enums.RoleUserType;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import com.timevale.privilege.service.model.service.mods.BizRoleUser;
import com.timevale.saas.common.manage.common.service.enums.VipCodeEnum;
import com.timevale.saas.common.manage.common.service.enums.VipTypeEnum;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipQueryOutput;

import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import com.timevale.token.service.model.output.SsoIpIsolationOutput;
import com.timevale.token.service.model.output.SsoLoginConfigsOutput;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.timevale.saas.common.manage.common.service.model.output.OrganizationInfoQueryOutput;
import org.assertj.core.util.Lists;

/**
 * <AUTHOR>
 * @date 2023/6/6 20:43
 */

public class MockUntils {


    public static InviteJoinMembersOutput getInviteJoin() {
        InviteJoinMembersOutput inviteJoinMembersOutput = new InviteJoinMembersOutput();
        inviteJoinMembersOutput.setInviteId("inviteId");
        List<InviteJoinMember> inviteJoinMembers = new ArrayList<>();
        inviteJoinMembers.add(getInviteJoinMember());
        inviteJoinMembersOutput.setJoinMembers(inviteJoinMembers);
        inviteJoinMembersOutput.setOrgId("orgId");
        inviteJoinMembersOutput.setTotal(1);
        return inviteJoinMembersOutput;

    }




    public static BizSubOrganOutput mockBizSubOrganOutput(){
        BizSubOrganOutput output = new BizSubOrganOutput();
        output.setBranchOrgOid("orgId");
        output.setBranchOrganId("branchOrganId");
        output.setRootOrgOid("rootOrgOid");
        output.setRootOrganId("rootOrganId");
        output.setParentOrgOid("parentOrgOid");
        output.setParentOrganId("parentOrganId");
        return output;
    }

    public static InviteJoinMember getInviteJoinMember() {
        InviteJoinMember joinMember = new InviteJoinMember();
        joinMember.setInviteId("inviteId");
        joinMember.setDeptId("deptId");
        Map<String, String> memberInfo = new HashMap<>();
        memberInfo.put("INFO_NAME", "name");
        memberInfo.put("memberOid","memberOid");
        joinMember.setMemberInfo(memberInfo);
        joinMember.setLoginAccount("1230xxxx");
        joinMember.setOrgId("orgId");
        joinMember.setStatus(10);
        joinMember.setType(1);
        return joinMember;
    }

    public static RealNameOrganResponse mockRealNameOrganResponse(){
        RealNameOrganResponse organResponse = new RealNameOrganResponse();
        organResponse.setGid("gid");
        organResponse.setOrganGuid("organGuid");
        organResponse.setActivate(ActivateEnum.ACTIVATED.getCode());
        organResponse.setStatus(RealnameStatus.ACCEPT);
        organResponse.setOid("oid");
        return organResponse;
    }

    public static List<BizDeptChildOutput> getDeptChildren() {
        List<BizDeptChildOutput> children = new ArrayList<>();
        children.add(getBizDept());
        return children;
    }

    public static BizDeptChildOutput getBizDept() {
        BizDeptChildOutput childOutput = new BizDeptChildOutput();
        childOutput.setDeptId("deptID");
        childOutput.setDeptName("deptName");
        childOutput.setParentDeptId("parentId");
        childOutput.setSeq(System.currentTimeMillis());
        return childOutput;
    }

    public static BizAccountRealNameOutput getRealNameOutput(){
        BizAccountRealNameOutput realNameOutput = new BizAccountRealNameOutput();
        realNameOutput.setAccount(mockBizICUserOutput());
        realNameOutput.setStatus(RealnameStatus.ACCEPT);
        return realNameOutput;
    }

    public static BizAccountRealnameOutput mockBizAccountRealnameOutput(){
        BizAccountRealnameOutput  realnameOutput = new BizAccountRealnameOutput();
        realnameOutput.setStatus(RealnameStatus.ACCEPT);
        realnameOutput.setAccountUid("accountUid");
        realnameOutput.setGuid("gid");
        realnameOutput.setServiceId("serviceId");
        realnameOutput.setModifyTime(new Date());

        return realnameOutput;
    }


    public static GlobalSysCredentials mockGlobalSysCredentials(){
        GlobalSysCredentials credentials =new GlobalSysCredentials();
        credentials.setType(BuiltinCredentialsEnum.CRED_PSN_CH_IDCARD.getCode());
        credentials.setSysId("gid");
        credentials.setValue("371326********3434");
        return credentials;
    }


    public static EasunEsearchAccountOutput getAccountOutPut() {
        EasunEsearchAccountOutput accountOutput = new EasunEsearchAccountOutput();
        accountOutput.setStatus(RealnameStatus.ACCEPT);
        List<Property> contacts = mockproperties("MOBILE", "1309378xxxx");
        accountOutput.setContacts(contacts);
        List<Property> credentials = mockproperties(BuiltinCredentials.CRED_PSN_CH_IDCARD,
                "371326xxxxxxxxxxxx");
        accountOutput.setCredentials(credentials);
        List<Property> properties = mockproperties(BuiltinProperty.INFO_NAME, "xxx有限公司");
        accountOutput.setProperties(properties);
        AccountBaseDetail accountBase = mockAccountBaseDetail();
        accountOutput.setBase(accountBase);
        EsId esId = new EsId();
        esId.setOuid("ouid");
        esId.setGuid("guid");
        accountOutput.setId(esId);
        IdcardContentCollection<ContentSecurity> idCards = new IdcardContentCollection<>();
        IdcardContentMobile<ContentSecurity> idCardMoblie = new IdcardContentMobile<ContentSecurity>();
        ContentSecurity mobileContent = new ContentSecurity();
        mobileContent.setExist(true);
        mobileContent.setEncrypter("1309378xxxx");
        idCards.setMobile(idCardMoblie);
        IdcardContentEmail<ContentSecurity> idCardEmail = new IdcardContentEmail<ContentSecurity>();
        ContentSecurity idCardEmailContent = new ContentSecurity();
        idCardEmailContent.setExist(true);
        idCardEmailContent.setEncrypter("<EMAIL>");
        idCards.setEmail(idCardEmail);
        accountOutput.setIdcards(idCards);
        return accountOutput;
    }

    public static <T> PagerResult<T>  mockPageResultEsSearchAccount(List<T> list){
        PagerResult pagerResult =new PagerResult();
        pagerResult.setItems(list);
        pagerResult.setTotal(1);
        return pagerResult;
    }

    public static EsOrgSearhOutput mockEsOrgSearhOutput(){
        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        esOrgSearhOutput.setUser(getAccountOutPut());
        esOrgSearhOutput.setId("organId");
        return esOrgSearhOutput;
    }


    public static BizICCreateMemberOutput getBizICCreateMemberOutput() {
        BizICCreateMemberOutput memberOutput = new BizICCreateMemberOutput();
        memberOutput.setMemberId("memberId");
        memberOutput.setOrgId("orgId");
        memberOutput.setAccountId("accountId");
        memberOutput.setMemberAccountUid("memberAccountUid");
        return memberOutput;
    }


    public static TInviteContextData getTInviteContextData(){
        TInviteContextData contextData = new TInviteContextData();
        Map<String, String> bizData = new HashMap<>();
        bizData.put("INFO_NAME","name");
        bizData.put("orgId","orgId");
        bizData.put("deptId","deptId");
        bizData.put("mobile","1309378xxxx");
        bizData.put("deptIds","111,222");
        bizData.put("roleIds","111,222");
        contextData.setBizData(bizData);
        contextData.setInviteId("inviteId");
        contextData.setAppId("appId");
        contextData.setBizId("bizId");
        contextData.setInitiator("initiatorId");
        contextData.setNotifyPushUrl("notifyPushUrl");
        return contextData;
    }


    public static AdminListOpenResponse getAdminListOpenResponse(){
        AdminListOpenResponse response = new AdminListOpenResponse();
        response.setAccountId("accountId");
        response.setMobile("mobile");
        response.setName("name");
        response.setEmail("email");
        return response;
    }

    public static BizICUserBaseOutput getBizICUserBaseOutput() {
        BizICUserBaseOutput output = new BizICUserBaseOutput();
        AccountBaseDetail detail = mockAccountBaseDetail();
        output.setBase(detail);

        ICAccountUser id = new ICAccountUser();
        id.setGuid("GUID");
        id.setOuid("ouid");
        output.setId(id);
        return output;
    }


    public static BizICUserOutput mockBizICUserOutput(){
        BizICUserOutput output = new BizICUserOutput();
        IdcardContentCollection<ContentSecurity> idCards = mockIdcards();
        output.setIdcards(idCards);
        AccountBaseDetail accountBase = mockAccountBaseDetail();
        output.setBase(accountBase);
        List<Property> properties = mockproperties(
                "INFO_NAME", "memberName");
        output.setProperties(properties);
        List<Property> contacts = mockproperties("MOBILE", "130xxxxxxxx");
        output.setContacts(contacts);
        List<Property> credentials = mockproperties(BuiltinCredentials.CRED_PSN_CH_IDCARD,
                "371326xxxxxxxxxxxx");
        output.setCredentials(credentials);
        ICUserId id = mockIcUserId();
        output.setId(id);
        return output;
    }

    private static IdcardContentCollection<ContentSecurity> mockIdcards() {
        IdcardContentCollection<ContentSecurity> idCards = new IdcardContentCollection<>();
        IdcardContentMobile<ContentSecurity> idCardMoblie = new IdcardContentMobile<ContentSecurity>();
        IdcardMobile mobileContent = new IdcardMobile();
        mobileContent.setValue("1309378xxxx");
        idCardMoblie.setDetail(mobileContent);
        idCards.setMobile(idCardMoblie);
        IdcardContentEmail<ContentSecurity> idCardEmail = new IdcardContentEmail<ContentSecurity>();
        IdcardEmail idCardEmailContent = new IdcardEmail();
        idCardEmailContent.setValue("<EMAIL>");
        idCardEmail.setDetail(idCardEmailContent);
        idCards.setEmail(idCardEmail);
        return idCards;
    }

    private static List<Property> mockproperties(String INFO_NAME, String memberName) {
        List<Property> properties = new ArrayList<>();
        Property name = new Property(INFO_NAME, memberName);
        properties.add(name);
        return properties;
    }

    private static AccountBaseDetail mockAccountBaseDetail() {
        AccountBaseDetail accountBase = new AccountBaseDetail();
        accountBase.setType(AccountType.PERSON);
        accountBase.setProjectApp(new ProductApp());
        return accountBase;
    }

    private static ICUserId mockIcUserId() {
        ICUserId id = new ICUserId();
        id.setGuid("GUID");
        id.setOuid("ouid");
        return id;
    }


    public static BizDeptChildOutput mockBizDeptChildOutput(){
        BizDeptChildOutput output = new BizDeptChildOutput();
        output.setDeptId("detpId");
        output.setDeptName("deptName");
        output.setParentDeptId("partentId");
        return output;
    }


    public static BizGetOrganOutput mockBizGetOrganOutput(){
        BizGetOrganOutput bizGetOrganOutput = new BizGetOrganOutput();
        bizGetOrganOutput.setOrganId("organId");
        bizGetOrganOutput.setOrganGuid("organGuid");
        bizGetOrganOutput.setActivate(1);
        bizGetOrganOutput.setOrganAccountUid("organAccountUid");
        bizGetOrganOutput.setWeight(1);
        bizGetOrganOutput.setTop(1);
        bizGetOrganOutput.setSource("DING_TALK");
        bizGetOrganOutput.setAllowSearchJoin(0);
        bizGetOrganOutput.setCreaterAccountUid("createrAccountUid");
        bizGetOrganOutput.setIsRootOrgan(1);
        return bizGetOrganOutput;
    }

    public static LegalInfo mockLegalInfo(){
        LegalInfo legalInfo = new LegalInfo();
        legalInfo.setOrganMember(true);
        legalInfo.setOuid("ouid");
        legalInfo.setLegalName("name");
        legalInfo.setNickName("nickName");
        legalInfo.setMobile("130****1206");
        legalInfo.setEmail("29720***@qq.com");
        return legalInfo;
    }


    public static MemberDetail getMemberDetail(){
        MemberDetail detail = new MemberDetail();
        List<DepartDetail> depts = new ArrayList<>();
        DepartDetail departDetail = new DepartDetail();
        departDetail.setDeptId("deptId");
        departDetail.setPath("001");
        departDetail.setName("name");
        departDetail.setMainMember(false);
        depts.add(departDetail);
        detail.setDepartDetails(depts);
        detail.setOrganId("organId");
        detail.setOrganMemberGid("memberGuid");
        detail.setOrgId("orgId");
        detail.setMemberId("memberId");
        detail.setAccountName("accountname");
        detail.setActive(true);
        detail.setHasRoleId(true);
        detail.setOrgName("orgaName");
        IdcardContentCollection<ContentSecurity> idcardContentCollection = new IdcardContentCollection<>();
        IdcardContentEmail idcardEmail = new IdcardContentEmail<>();
        IdcardEmail idcardEmail1 = new IdcardEmail();
        idcardEmail1.setValue("<EMAIL>");
        idcardEmail.setDetail(idcardEmail1);
        idcardContentCollection.setEmail(idcardEmail);
        detail.setLoginUser(idcardContentCollection);
        List<BizRoleDetail> roleDetails = getBizRoleDetails();

        detail.setRoleDetails(roleDetails);
        detail.setRealnameStatus(RealnameStatus.ACCEPT);
        detail.setMemberOid("memberOId");
        detail.setMemberGid("memberGuid");
        List<Property> properties = mockproperties("INFO_NAME", "memberName");
        detail.setProperties(properties);
        detail.setOrgName("orgname");
        return detail;
    }

    public static List<BizRoleDetail> getBizRoleDetails() {
        List<BizRoleDetail> roleDetails =new ArrayList<>();
        BizRoleDetail roleDetail = getBizRoleDetail();
        roleDetails.add(roleDetail);
        return roleDetails;
    }

    public static RoleSyncItemModel  getRoleSyncItemModel(){
        RoleSyncItemModel model =         new RoleSyncItemModel();
        model.setRoleId("roleId");
        model.setName("name");
        return model;
    }


    public static List<RoleSyncItemModel>  getRoleSyncItemModelList(){
        List<RoleSyncItemModel> roleList= new ArrayList<>();
        RoleSyncItemModel model =    getRoleSyncItemModel();
        roleList.add(model);
        return roleList;
    }

    public static BizRoleDetail getBizRoleDetail() {
        BizRoleDetail roleDetail = new BizRoleDetail();
        roleDetail.setRoleUser(new BizRoleUser(RoleUserType.ACCOUNT_UID,"accountUid"));
        roleDetail.setRoleKey("MEMBER");
        roleDetail.setRoleId("roleId");
        roleDetail.setRoleGranted(new BizRoleUser(RoleUserType.ACCOUNT_UID,"uid"));
        roleDetail.setName("成员");
        roleDetail.setGroup("DEFAULT");
        return roleDetail;
    }

    public static PagerResult<MemberDetail> getPagerResultMemberDetail() {
        PagerResult pagerResult = new PagerResult();
        pagerResult.setItems(Lists.newArrayList(getMemberDetail()));
        pagerResult.setTotal(pagerResult.getItems().size());
        return pagerResult;
    }

    public static PagerResult<MemberDetail> getTwoPagerResultMemberDetail() {
        PagerResult pagerResult = new PagerResult();
        pagerResult.setItems(Lists.newArrayList(getMemberDetail(),getMemberDetail()));
        pagerResult.setTotal(pagerResult.getItems().size());
        return pagerResult;
    }

    public static UserRealNameBaseInfoResponse getUserRealNameBaseInfoResponse(){
        UserRealNameBaseInfoResponse response = new UserRealNameBaseInfoResponse();
        response.setGuid("Guid");
        response.setOuid("Ouid");
        response.setStatus(true);
        response.setRealnameTime(new Date());
        response.setType("type");
        return response;
    }

    public static UserInfoResponse getUserInfoResponse(){
        UserInfoResponse response = new UserInfoResponse();
        response.setAccountId("accountId");
        response.setEmail("<EMAIL>");
        response.setUserName("name");
        response.setCredentical("credentical");
        response.setRealNameStatus(true);
        response.setHeadImg("headImg");
        response.setThirdId("thirdId");
        response.setCredenticalType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        response.setRegisterTime("xxxxx");
        return response;
    }

    public static BizEasunGetBuiltinPrivilegesOutput getBizEasunGetBuiltinPrivilegesOutput(){
        BizEasunGetBuiltinPrivilegesOutput privilegesOutput = new BizEasunGetBuiltinPrivilegesOutput();
        List<BizBuiltinPrivilegeDetail> privilegeDetails = new ArrayList<>();
        BizBuiltinPrivilegeDetail privilegeDetail = getBizBuiltinPrivilegeDetail();
        privilegeDetails.add(privilegeDetail);
        privilegesOutput.setPrivilegeList(privilegeDetails);
        return privilegesOutput;
    }

    public static List<ResourceGroupModel> getGroups() {
        return Lists.newArrayList(getResourceGroupModel());
    }

    public static ResourceGroupModel getResourceGroupModel(){
        ResourceGroupModel groupModel = new ResourceGroupModel();
        groupModel.setGroupKey("ORGAN");
        groupModel.setGroupName("组织机构");
        groupModel.setWeight(1);
        groupModel.setId(1L);
        return groupModel;
    }


    public static List<PrivilegResourceModel> getResources(){
        return Lists.newArrayList(getPrivilegResourceModel());

    }


    public static PrivilegResourceModel getPrivilegResourceModel(){
        PrivilegResourceModel resourceModel = new PrivilegResourceModel();
        resourceModel.setGroupKey("ORGAN");
        resourceModel.setResourceId(1L);
        resourceModel.setWeight(1);
        resourceModel.setResourceName("resourceName");
        resourceModel.setResourceKey("SEAL");
        resourceModel.setGroupName("组织机构");
        return resourceModel;

    }

    public static BizBuiltinPrivilegeDetail getBizBuiltinPrivilegeDetail(){
        BizBuiltinPrivilegeDetail privilegeDetail = new BizBuiltinPrivilegeDetail();
        privilegeDetail.setOperationWeight(2);
        privilegeDetail.setClassDataOperationId(1);
        privilegeDetail.setOperationPermit("QUERY");
        privilegeDetail.setTargetClassKey("SEAL");
        privilegeDetail.setTargetClassKeyName("管理员");
        return privilegeDetail;
    }

    public static OrgIdAndNameResponse getOrgIdAndNameResponse(){
        OrgIdAndNameResponse response =  BizAccountConv.convToOrgIdAndName(mockBizICUserOutput());
        return response;
    }


    public static BizGetParentDeptTreeOutputV3 getBizGetParentDeptTreeOutputV3(){
        BizGetParentDeptTreeOutputV3 treeOutputV3 = new BizGetParentDeptTreeOutputV3();
        BizDeptDetailOutputV3 outputV3 = getBizDeptDetailOutputV3();
        treeOutputV3.setDepartment(outputV3);
        treeOutputV3.setBuildInfo(Lists.newArrayList(outputV3));
        return treeOutputV3;
    }

    public static BizDeptDetailOutputV3 getBizDeptDetailOutputV3(){
        BizDeptDetailOutputV3 outputV3 = new BizDeptDetailOutputV3();
        outputV3.setDeptId("deptId");
        outputV3.setDeptName("deptName");
        outputV3.setParentDeptId("parentId");
        outputV3.setPath("001");
        outputV3.setHasUnit(true);
        outputV3.setRootOrganMemberCount(1);
        outputV3.setMemberCount(1);
        outputV3.setParentDeptName("parentName");
        outputV3.setNodeType("unit");
        outputV3.setRootOrganPath("001");
        outputV3.setOwnerOrgId("ownerOrgId");
        outputV3.setBranchOrganId("branchOrganId");
        return outputV3;
    }

    public static AccountVipQueryOutput getAccountVipQueryOutput(){
        AccountVipQueryOutput queryOutput = new AccountVipQueryOutput();
        queryOutput.setGid("guid");
        queryOutput.setVipCode("vipCode");
        queryOutput.setShare(true);
        queryOutput.setUseVipCode(true);
        return queryOutput;
    }


    public static DeptSearchListResponseV3 getDeptSearchListResponseV3(){
        DeptSearchListResponseV3 responseV3 = new DeptSearchListResponseV3();
        DeptBaseResponseV3 organs = getDeptBaseResponseV3();
        responseV3.setUnitList(Lists.newArrayList(organs));
        DeptBaseResponseV3 depts = getDeptBaseResponseV3();
        depts.setNodeType("dept");
        responseV3.setDeptList(Lists.newArrayList(depts));
        responseV3.setTotal(2);
        return responseV3;

    }


    public static DeptBaseResponseV3 getDeptBaseResponseV3(){
        DeptBaseResponseV3 responseV3 = new DeptBaseResponseV3();
        responseV3.setDeptId("deptId");
        responseV3.setPath("001");
        responseV3.setMemberCount(1);
        responseV3.setNodeType("unit");
        responseV3.setOwnerOrgId("ownerORgId");
        responseV3.setHasSubdivision(true);
        responseV3.setDeptFullName("deptFullName");
        return responseV3;
    }

    public static PagerResult<UserInfoResponse> getPagerUserInfoResp() {
        PagerResult pagerResult = new PagerResult();
        pagerResult.setItems(Lists.newArrayList(getUserInfoResponse(),getUserInfoResponse()));
        pagerResult.setTotal(pagerResult.getItems().size());
        return pagerResult;
    }


    public static PagerResult<BizRoleDetail> mockPageBizRole(){
        PagerResult<BizRoleDetail> bizRoleDetailPagerResult = new PagerResult<>();
        BizRoleDetail roleDetail = new BizRoleDetail();
        roleDetail.setRoleKey("ADMIN");
        roleDetail.setRoleId("roleId");
        bizRoleDetailPagerResult.setItems(Lists.newArrayList(roleDetail));
        return bizRoleDetailPagerResult;
    }

    public static PagerResult<BizRoleDetail> mockPageBlackRoles(){
        PagerResult<BizRoleDetail> bizRoleDetailPagerResult = new PagerResult<>();
        BizRoleDetail roleDetail = new BizRoleDetail();
        roleDetail.setRoleKey("ADMIN");
        roleDetail.setRoleId("roleId1");

        BizRoleDetail roleDetail2 = new BizRoleDetail();
        roleDetail2.setRoleKey("SPACE_ADMIN");
        roleDetail2.setRoleId("roleId2");

        BizRoleDetail roleDetail3 = new BizRoleDetail();
        roleDetail3.setRoleKey("ORGAN_LEGAL");
        roleDetail3.setRoleId("roleId3");

        bizRoleDetailPagerResult.setItems(Lists.newArrayList(roleDetail, roleDetail2, roleDetail3));
        return bizRoleDetailPagerResult;
    }


    public static BizMemberGetOutput mockBizMemberGetOutput(){
        BizMemberGetOutput memberGetOutput = new BizMemberGetOutput();
        memberGetOutput.setMemberId("memberId");
        memberGetOutput.setMemberOid("memberOid");
        memberGetOutput.setMemberAccountUid("memberAccountUid");
        memberGetOutput.setOrganId("organId");
        return memberGetOutput;
    }

    public static DeptSubListResponse mockDeptSubListResponse(){
        DeptSubListResponse response = new DeptSubListResponse();
        List<DeptBaseResponse> deptList = new ArrayList<>();
        deptList.add(mockDeptBaseResponse());
        response.setDeptList(deptList);
        List<MemberBaseResponse> memberList = new ArrayList<>();
        MemberBaseResponse memberBaseResponse = mockMemberBaseResponse();
        memberList.add(memberBaseResponse);
        response.setMemberList(memberList);
        return response;
    }


    public static BizOrgSummaryOutputV3 mockBizOrgSummaryOutputV3(){
        BizOrgSummaryOutputV3 orgSummaryOutputV3 = new BizOrgSummaryOutputV3();
        orgSummaryOutputV3.setMemberCount(1);
        orgSummaryOutputV3.setOrgName("orgName");
        orgSummaryOutputV3.setOuid("oid");
        orgSummaryOutputV3.setIsRootOrgan(true);
        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setName("name");
        adminInfo.setOuid("adminOid");
        adminInfo.setEmail("<EMAIL>");
        adminInfo.setGuid("adminGid");
        adminInfo.setAccountUid("accountUid");
        orgSummaryOutputV3.setAdmin(adminInfo);
        return orgSummaryOutputV3;
    }


    public static AccountVipQueryOutput mockAccountVipQueryOutput(){
        AccountVipQueryOutput output = new AccountVipQueryOutput();
        output.setVipCode(VipCodeEnum.SENIOR.getCode());
        output.setGid("gid");
        output.setEffectiveTo(DateUtils.addDays(new Date(),365));
        output.setLevel(VipCodeEnum.SENIOR.getLevel());
        output.setVipType(VipTypeEnum.NORMAL.getType());
        output.setTenantName("tenantName");
        return output;
    }


    public static VipFunctionQueryOutput mockVipFunctionQueryOutput(){
        VipFunctionQueryOutput output =new VipFunctionQueryOutput();
        Map<String,Object> limit = new HashMap<>();
        limit.put(MEMBER_LIMIT_COUNT,Integer.MAX_VALUE);
        output.setLimit(limit);
        output.setVipCode(VipCodeEnum.SENIOR.getCode());
        return output;
    }

    public static DeptBaseResponse mockDeptBaseResponse(){
        DeptBaseResponse response = new DeptBaseResponse();
        response.setDeptId("deptId");
        response.setDeptName("deptName");
        response.setParentId("parentId");
        response.setPath("001");
        return response;
    }


    public static MemberBaseResponse mockMemberBaseResponse(){
        MemberBaseResponse response = new MemberBaseResponse();
        response.setMemberGid("memberGid");
        response.setMemberName("memberName");
        response.setMemberOid("memberOid");
        response.setRoleModels(Lists.newArrayList(mockRoleModel()));
        return response;
    }

    public static RoleModel mockRoleModel(){
        RoleModel roleModel  = new RoleModel();
        roleModel.setRoleKey("ADMIN");
        roleModel.setId("roleId");
        roleModel.setName("管理员");
        return roleModel;
    }


    public static OrganDetailModel mockOrganDetailModel(){
        OrganDetailModel organDetailModel = new OrganDetailModel();
        OrgPropCollection collection = new OrgPropCollection();
        OrgProperties properties = new OrgProperties();
        properties.setName("organName");
        properties.setAgentIdno("agentIdNumber");
        properties.setLegalIdno("legalIdNumber");
        properties.setLegalName("legalName");
        collection.setProperties(properties);
        organDetailModel.setProp(collection);
        return organDetailModel;
    }


    public static OrganSortRequest mockOrganSortRequest(){
        OrganSortRequest  organSortRequest = new OrganSortRequest();
        organSortRequest.setOrgId("organId");
        organSortRequest.setTop(1);
        return organSortRequest;
    }



    public static UserInfoModel mockUserInfoModel(){
        UserInfoModel infoModel = new UserInfoModel();
        infoModel.setOrgId("orgId");
        infoModel.setTop(1);
        infoModel.setWeight(10);
        infoModel.setGuid("guid");
        infoModel.setActivate(1);
        infoModel.setRealName(true);
        infoModel.setRoleDetailList(Lists.newArrayList(mockRoleModel()));
        return infoModel;
    }


    public static OrganizationInfoQueryOutput mockOrganizationInfoQueryOutput(){
        OrganizationInfoQueryOutput output = new OrganizationInfoQueryOutput();
        output.setGid("guid");
        output.setVipCode("SENIOR");
        output.setMargin(BigDecimal.valueOf(10));
        output.setPackageExpireDate(new Date());
        output.setEffectiveTo(new Date());
        output.setUnits("uints");
        output.setUseVipCode(true);
        return output;
    }

    public static PersonPropCollection mockPersonPropCollection(){
        PersonPropCollection collection = new PersonPropCollection();
        PersonProperties properties = new PersonProperties();
        properties.setName("name");
        properties.setRealnameContext("renameContext");
        properties.setBankNum("622*************764");
        properties.setClentId("Android");
        properties.setRealnameMobile("103****1206");
        properties.setHead("headImg");
        properties.setUserConfig("{\"userConfig\":\"config\"}");
        collection.setProperties(properties);
        PersonCredentials credentials = new PersonGlobalCredentials();
        credentials.setIdno("371***********3434");
        collection.setCredentials(credentials);
        CommonContacts contacts = new CommonContacts();
        contacts.setMobile("130*******6");
        contacts.setEmail("<EMAIL>");
        collection.setContacts(contacts);
        return collection;
    }

    public static AddressListResponse mockAddressListResponse(){
        AddressListResponse response = new AddressListResponse();
        response.setTotal(1L);
        List<AddressModel> addressModels = new ArrayList<>();
        AddressModel addressModel = new AddressModel();
        addressModel.setAddressId("673300");
        addressModels.add(addressModel);
        response.setAddresses(addressModels);
        return response;
    }

    public static SendMsgResponse mockSendMsgResponse(){
        SendMsgResponse response = new SendMsgResponse();
        response.setAgentName(AgentEnum.MW);
        response.setTaskId(UUIDUtils.getUUID());
        response.setSendStatus(SendStatusEnum.SEND_SUCCESS);
        response.setReceiveTime(new Date(System.currentTimeMillis()-1000*3000));
        response.setSendTime(new Date(System.currentTimeMillis()-1000*3600));
        return response;
    }


    public static BizApplyWillAssembleAuthOutput mockBizApplyWillAssembleAuthOutput(){
        BizApplyWillAssembleAuthOutput output = new BizApplyWillAssembleAuthOutput();
        output.setAuthUrl("www.baidu.com");
        output.setBizId("bizId");
        output.setServiceId("serviceId");
        return output;
    }


    public static EasunSpaceOutput mockEasunSpaceOutput(){
        EasunSpaceOutput easunSpaceOutput = new EasunSpaceOutput();
        easunSpaceOutput.setList(mockSpaceItems());
        easunSpaceOutput.setSpaceId("spaceId");
        easunSpaceOutput.setName("spaceName");
        easunSpaceOutput.setRootOrgan(true);
        return easunSpaceOutput;
    }

    public static List<SpaceItemDetailOutput> mockSpaceItems(){
        SpaceItemDetailOutput detailOutput = new SpaceItemDetailOutput();
        detailOutput.setOrganOuid("organOid");
        detailOutput.setMembers(getTwoPagerResultMemberDetail().getItems());
        detailOutput.setDepts(Lists.newArrayList(mockBizDeptDetailOutput()));
        return Lists.newArrayList(detailOutput);
    }


    public static BizDeptDetailOutput mockBizDeptDetailOutput(){
        BizDeptDetailOutput detailOutput = new BizDeptDetailOutput();
        detailOutput.setDeptId("deptId");
        detailOutput.setDeptName("deptName");
        detailOutput.setMainMembers(Lists.newArrayList("memberId"));
        return detailOutput;
    }

    public static Dept mockDept(){
        Dept dept = new Dept();
        dept.setDeptId("deptId");
        dept.setDeptName("deptName");
        return dept;
    }


    public static BizListDeptOutput mockBizListDeptOutput(){
        BizListDeptOutput deptOutput = new BizListDeptOutput();
        deptOutput.setList(Lists.newArrayList(mockDept()));
        return deptOutput;
    }


    public static SsoOrganizationDomainModel mockSsoOrganizationDomainModel(){
        SsoOrganizationDomainModel domainModel = new SsoOrganizationDomainModel();
        domainModel.setDomain("baicu.com");
        domainModel.setSite("site");
        return domainModel;
    }

    public static SsoLoginConfigsOutput mockSsoLoginConfigsOutput(){
        SsoLoginConfigsOutput output = new SsoLoginConfigsOutput();
        JSONArray config = mockSsoConfigJsonArray();
        output.setConfig(config);
        return output;
    }

    public static JSONArray mockSsoConfigJsonArray() {
        JSONArray config = new JSONArray();
        JSONObject ipIsolation =new JSONObject();
        ipIsolation.put("key","ORGAN_IP_ISOLATION");
        ipIsolation.put("value","[{\"startIp\":\"**************\",\"endIp\":\"**************\",\"ipTag\":\"single\"},{\"startIp\":\"*************\",\"endIp\":\"*************\",\"ipTag\":\"selection\"},{\"startIp\":\"**************\",\"endIp\":\"**************\",\"ipTag\":\"selection\"}]");
        config.add(ipIsolation);
        JSONObject loginType =new JSONObject();
        loginType.put("key","ORGAN_SUPPORT_LOGIN_TYPE");
        loginType.put("value","ALL");
        config.add(loginType);
        return config;
    }


    public static SsoIpIsolationOutput mockSsoIpIsolationOutput(long startIp,long endIp){
        SsoIpIsolationOutput output = new SsoIpIsolationOutput();
        output.setStartIp(startIp);
        output.setEndIp(endIp);
        return output;
    }


    public static BizDeptSubListOutputV3 mockBizDeptSubListOutputV3(){
        BizDeptSubListOutputV3 outputV3 = new BizDeptSubListOutputV3();
        outputV3.setDeptList(Lists.newArrayList(getBizDeptDetailOutputV3()));
        return outputV3;
    }

    public static BizFatICUserOutput mockBizFatICUserOutput(){
        BizFatICUserOutput fatICUserOutput = new BizFatICUserOutput();
        ICUserId id = mockIcUserId();
        fatICUserOutput.setId(id);
        AccountBaseDetail accountBase = mockAccountBaseDetail();
        fatICUserOutput.setBase(accountBase);
        List<Property> properties = mockproperties(
                "INFO_NAME", "memberName");
        fatICUserOutput.setProperties(properties);
        List<Property> contacts = mockproperties("MOBILE", "130xxxxxxxx");
        fatICUserOutput.setContacts(contacts);
        List<Property> credentials = mockproperties(BuiltinCredentials.CRED_PSN_CH_IDCARD,
                "371326xxxxxxxxxxxx");
        fatICUserOutput.setContacts(contacts);
        MultiIdcardContentCollection<ContentSecurity> idCards = mockMultiIdcardContentCollection();
        fatICUserOutput.setIdcards(idCards);
        return fatICUserOutput;
    }


    private static MultiIdcardContentCollection<ContentSecurity> mockMultiIdcardContentCollection() {
        MultiIdcardContentCollection<ContentSecurity> idCards = new MultiIdcardContentCollection<>();
        IdcardContentMobile<ContentSecurity> idCardMoblie = new IdcardContentMobile<ContentSecurity>();
        IdcardMobile mobileContent = new IdcardMobile();
        mobileContent.setValue("1309378xxxx");
        idCardMoblie.setDetail(mobileContent);
        idCards.addMobile(idCardMoblie);
        IdcardContentEmail<ContentSecurity> idCardEmail = new IdcardContentEmail<ContentSecurity>();
        IdcardEmail idCardEmailContent = new IdcardEmail();
        idCardEmailContent.setValue("<EMAIL>");
        idCardEmail.setDetail(idCardEmailContent);
        idCards.addEmail(idCardEmail);
        return idCards;
    }


    public static BizDeptListOutputV3 mockBizDeptDetailOutputV3(){
        BizDeptListOutputV3 bizDeptListOutput = new BizDeptListOutputV3();
        bizDeptListOutput.setDeptList(Lists.newArrayList(getBizDeptDetailOutputV3()));
        return bizDeptListOutput;
    }

}
