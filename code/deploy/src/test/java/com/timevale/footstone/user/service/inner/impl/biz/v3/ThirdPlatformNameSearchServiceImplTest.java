package com.timevale.footstone.user.service.inner.impl.biz.v3;

import com.timevale.besp.lowcode.integration.response.OrgResponse;
import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import com.timevale.footstone.user.service.constants.ThirdPartyConstant;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcThirdOrgConnectorServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcIdCardAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static com.timevale.easun.service.constant.ThirdPartyConstant.SAAS_PLATFORM_CLIENT_MAPPING;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class ThirdPlatformNameSearchServiceImplTest {

    @InjectMocks
    private ThirdPlatformNameSearchServiceImpl thirdPlatformNameSearchService;

    @Mock
    private RpcIdCardAdapt rpcIdCardAdapt;

    @Mock
    private RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdapt;

    @Mock
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;

    // 测试数据定义
    private static final String INPUT_ORG_ID = "testOrgId"; // 测试用组织ID
    private static final String INPUT_KEYWORD = "testKeyword"; // 测试用关键词
    private static final String INPUT_EMPTY_KEYWORD = ""; // 测试用空关键词
    private static final String INPUT_TENANT_KEY = "testTenantKey"; // 测试用租户密钥
    private static final String INPUT_CLIENT_ID = "testClientId"; // 测试用客户端ID
    private static final List<String> INPUT_THIRD_USER_IDS = Arrays.asList("user1", "user2"); // 测试用第三方用户ID列表
    private static final List<OrgResponse> INPUT_ORG_RESPONSES = Lists.newArrayList();     // 测试用部门响应列表

    private static final List<String> EXPECTED_OUIDS = Arrays.asList("ouid1", "ouid2"); // 预期的OUID结果
    private static final List<String> EXPECTED_DEPT_IDS = Arrays.asList("dept1", "dept2"); // 预期的部门ID结果
    private static final List<String> EMPTY_LIST = Collections.emptyList(); // 空列表

    static {
        OrgResponse orgResponse1 = new OrgResponse();
        orgResponse1.setId("dept1");
        OrgResponse orgResponse2 = new OrgResponse();
        orgResponse2.setId("dept2");
        INPUT_ORG_RESPONSES.add(orgResponse1);
        INPUT_ORG_RESPONSES.add(orgResponse2);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        // 初始化静态变量映射
        SAAS_PLATFORM_CLIENT_MAPPING.put(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), INPUT_CLIENT_ID);
    }

    // ================================
    // getOuidsByPersonNameFromThirdPlatform 测试用例
    // ================================

    /**
     * TC01: keyword为空
     * 输入: orgId="testOrgId", keyword=""
     * 预期输出: 空列表
     * 描述: 关键字为空直接返回
     */
    @Test
    public void testGetOuidsByPersonNameFromThirdPlatform_KeywordEmpty() {
        List<String> result = thirdPlatformNameSearchService.getOuidsByPersonNameFromThirdPlatform(INPUT_ORG_ID, INPUT_EMPTY_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    /**
     * TC02: openSyncPlatform不匹配
     * 输入: orgId="testOrgId", keyword="testKeyword", getOpenSyncPlatform返回"ALI_PAY"
     * 预期输出: 空列表
     * 描述: 不是企微平台
     */
    @Test
    public void testGetOuidsByPersonNameFromThirdPlatform_PlatformNotMatch() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn("ALI_PAY");

        List<String> result = thirdPlatformNameSearchService.getOuidsByPersonNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    /**
     * TC03: tenantKey为空
     * 输入: orgId="testOrgId", keyword="testKeyword", getTenantKey返回null
     * 预期输出: 空列表
     * 描述: 租户密钥未绑定
     */
    @Test
    public void testGetOuidsByPersonNameFromThirdPlatform_TenantKeyEmpty() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_ID, INPUT_CLIENT_ID)).thenReturn(null);

        List<String> result = thirdPlatformNameSearchService.getOuidsByPersonNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    /**
     * TC04: 正常流程
     * 输入: orgId="testOrgId", keyword="testKeyword", 所有依赖返回有效值
     * 预期输出: ["ouid1", "ouid2"]
     * 描述: 完整正常流程
     */
    @Test
    public void testGetOuidsByPersonNameFromThirdPlatform_NormalFlow() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_ID, INPUT_CLIENT_ID)).thenReturn(INPUT_TENANT_KEY);
        when(rpcTripartiteServiceAdapt.queryUser(INPUT_TENANT_KEY, INPUT_KEYWORD, ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform())).thenReturn(INPUT_THIRD_USER_IDS);

        Map<String, String> ouidMap = new HashMap<>();
        ouidMap.put("user1", "ouid1");
        ouidMap.put("user2", "ouid2");
        when(rpcIdCardAdapt.batchGetOuidsByThirdUserIds(INPUT_THIRD_USER_IDS, INPUT_CLIENT_ID, INPUT_TENANT_KEY)).thenReturn(ouidMap);

        List<String> result = thirdPlatformNameSearchService.getOuidsByPersonNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EXPECTED_OUIDS, result);
    }

    /**
     * TC05: queryUser返回空
     * 输入: orgId="testOrgId", keyword="testKeyword", queryUser返回空列表
     * 预期输出: 空列表
     * 描述: 无用户匹配
     */
    @Test
    public void testGetOuidsByPersonNameFromThirdPlatform_QueryUserReturnsEmptyList() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_ID, INPUT_CLIENT_ID)).thenReturn(INPUT_TENANT_KEY);
        when(rpcTripartiteServiceAdapt.queryUser(INPUT_TENANT_KEY, INPUT_KEYWORD, ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform())).thenReturn(EMPTY_LIST);

        List<String> result = thirdPlatformNameSearchService.getOuidsByPersonNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    /**
     * TC06: batchGetOuids返回空
     * 输入: orgId="testOrgId", keyword="testKeyword", batchGetOuidsByThirdUserIds返回空map
     * 预期输出: 空列表
     * 描述: 无OUID映射
     */
    @Test
    public void testGetOuidsByPersonNameFromThirdPlatform_BatchGetOuidsReturnsEmptyMap() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_ID, INPUT_CLIENT_ID)).thenReturn(INPUT_TENANT_KEY);
        when(rpcTripartiteServiceAdapt.queryUser(INPUT_TENANT_KEY, INPUT_KEYWORD, ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform())).thenReturn(INPUT_THIRD_USER_IDS);
        when(rpcIdCardAdapt.batchGetOuidsByThirdUserIds(INPUT_THIRD_USER_IDS, INPUT_CLIENT_ID, INPUT_TENANT_KEY)).thenReturn(Collections.emptyMap());

        List<String> result = thirdPlatformNameSearchService.getOuidsByPersonNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    // ================================
    // getDeptThirdIdsByNameFromThirdPlatform 测试用例
    // ================================

    /**
     * TC11: keyword为空
     * 输入: orgId="testOrgId", keyword=""
     * 预期输出: 空列表
     * 描述: 关键字为空直接返回
     */
    @Test
    public void testGetDeptThirdIdsByNameFromThirdPlatform_KeywordEmpty() {
        List<String> result = thirdPlatformNameSearchService.getDeptThirdIdsByNameFromThirdPlatform(INPUT_ORG_ID, INPUT_EMPTY_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    /**
     * TC12: openSyncPlatform不匹配
     * 输入: orgId="testOrgId", keyword="testKeyword", getOpenSyncPlatform返回"ALI_PAY"
     * 预期输出: 空列表
     * 描述: 不是企微平台
     */
    @Test
    public void testGetDeptThirdIdsByNameFromThirdPlatform_PlatformNotMatch() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn("ALI_PAY");

        List<String> result = thirdPlatformNameSearchService.getDeptThirdIdsByNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    /**
     * TC13: tenantKey为空
     * 输入: orgId="testOrgId", keyword="testKeyword", getTenantKey返回null
     * 预期输出: 空列表
     * 描述: 租户密钥未绑定
     */
    @Test
    public void testGetDeptThirdIdsByNameFromThirdPlatform_TenantKeyEmpty() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_ID, INPUT_CLIENT_ID)).thenReturn(null);

        List<String> result = thirdPlatformNameSearchService.getDeptThirdIdsByNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }

    /**
     * TC14: 正常流程
     * 输入: orgId="testOrgId", keyword="testKeyword", 所有依赖返回有效值
     * 预期输出: ["dept1", "dept2"]
     * 描述: 完整正常流程
     */
    @Test
    public void testGetDeptThirdIdsByNameFromThirdPlatform_NormalFlow() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_ID, INPUT_CLIENT_ID)).thenReturn(INPUT_TENANT_KEY);
        when(rpcTripartiteServiceAdapt.searchDeptList(INPUT_TENANT_KEY, null, ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), INPUT_KEYWORD)).thenReturn(INPUT_ORG_RESPONSES);

        List<String> result = thirdPlatformNameSearchService.getDeptThirdIdsByNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EXPECTED_DEPT_IDS, result);
    }

    /**
     * TC15: searchDeptList返回空
     * 输入: orgId="testOrgId", keyword="testKeyword", searchDeptList返回空列表
     * 预期输出: 空列表
     * 描述: 无部门匹配
     */
    @Test
    public void testGetDeptThirdIdsByNameFromThirdPlatform_SearchDeptListReturnsEmptyList() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_ID)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_ID, INPUT_CLIENT_ID)).thenReturn(INPUT_TENANT_KEY);
        when(rpcTripartiteServiceAdapt.searchDeptList(INPUT_TENANT_KEY, null, ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), INPUT_KEYWORD)).thenReturn(Lists.newArrayList());

        List<String> result = thirdPlatformNameSearchService.getDeptThirdIdsByNameFromThirdPlatform(INPUT_ORG_ID, INPUT_KEYWORD);
        assertEquals(EMPTY_LIST, result);
    }
}
