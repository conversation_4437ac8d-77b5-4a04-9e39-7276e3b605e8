package com.timevale.footstone.user.service.inner.impl;

import static com.timevale.privilege.service.enums.BuiltinRoleType.ADMIN;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.mods.idcard.IdcardMobile;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.inner.biz.v3.BizOrganizationServiceV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.RpcUserAshmanServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.ShortLinkServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.*;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcIcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.footstone.user.service.inner.biz.v3.BizOrganizationServiceV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.RpcUserAshmanServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.*;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcIcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.dept.response.v3.OrganizationDataSyncResponseV3;
import com.timevale.footstone.user.service.model.member.mods.MemberDetailModel;
import com.timevale.footstone.user.service.model.member.request.ChangeMemberDeptRequest;
import com.timevale.footstone.user.service.model.member.request.UpdateMemberAllRequest;
import com.timevale.footstone.user.service.mq.RocketMqClient;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.notificationmanager.service.model.SendMessageRequest;
import com.timevale.privilege.service.model.service.biz.output.BizGetRolesByUsersOutput;
import com.timevale.footstone.user.service.model.member.request.UpdateMemberAllRequest;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.privilege.service.model.service.biz.output.BizGetRolesByUsersOutput;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import java.util.ArrayList;
import java.util.List;

import org.checkerframework.checker.units.qual.A;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Author: liujiaxing
 * Date: 2023/4/11 11:03 上午
 * Description:
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class BizMemberServiceMockTest {

    @InjectMocks
    private BizMemberServiceImpl memberService;
    @Mock
    private RpcOrgPlusServiceAdapt orgPlusServiceAdapt;
    @Mock
    private RpcRolePrivilegePlusServiceAdapt rpcrolePrivilegePlusServiceAdapt;
    @Mock
    private RpcIcRolePrivilegePlusServiceAdapt icRolePrivilegePlusServiceAdapt;
    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;
    @Mock
    private RpcInnerOrgMemberServiceAdapt innerOrgMemberServiceAdapt;
    @Mock
    private IdAdapt idAdapt;
    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;
    @Mock
    private RpcOrgPlusService rpcOrgPlusService;
    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;
    @Mock
    private RpcUserAshmanServiceAdapt userAshmanService;
    @Mock
    private RpcRoleServiceAdapt rpcRoleServiceAdapt;
    @Mock
    private ShortLinkServiceAdapt shortLinkServiceAdapt;
    @Mock
    private RocketMqClient<SendMessageRequest> rocketMqClient;
    @Mock
    private RpcInnerOrgServiceAdapt innerOrgService;
    @Mock
    private RpcOrgServiceAdapt orgService;
    @Mock
    private BizOrganizationServiceV3 bizOrganizationServiceV3;

    @Mock
    private RpcDeptPlusServiceAdapt deptPlusServiceAdapt;

    @Before
    public void setUp(){
        OrganizationDataSyncResponseV3 responseV3 = new OrganizationDataSyncResponseV3();
        responseV3.setIsOpenThirdSync(false);
        when(bizOrganizationServiceV3.getOrganHasDataSync(any())).thenReturn(responseV3);
        MemberDetail memberDetail = MockUntils.getMemberDetail();
        when(orgPlusServiceAdapt.getMemberDetail(any(), any())).thenReturn(memberDetail);
        when(rpcRoleServiceAdapt.getPagedBuiltinRolesByOrgan(any(),any(),any(),any())).thenReturn(MockUntils.mockPageBizRole());
        BizGetRolesByUsersOutput roles = new BizGetRolesByUsersOutput();
        roles.setRoles(MockUntils.mockPageBizRole().getItems());
        when(rpcRoleServiceAdapt.getUserRoleListByOrgan(any(),anyString())).thenReturn(roles);
        PagerResult<MemberDetail> memberList= new PagerResult<>();
        memberList.setItems(Lists.newArrayList(memberDetail));
        when( esAccountServiceAdapt.getMemberListByEs(any())).thenReturn(memberList);
        when( esAccountServiceAdapt.getMemberListByEs(any(),any())).thenReturn(memberList);
        when(rpcRoleServiceAdapt.getPagedBuiltinRolesByOrgan(anyString(), anyInt(),anyInt(),anyString())).thenReturn(MockUntils.mockPageBizRole());

    }

    @Test
    public void testHandleResult() {
        ApiPageResult<MemberDetailModel> apiPageResult = new ApiPageResult<>();
        List<MemberDetailModel> result = new ArrayList<>();
        MemberDetailModel memberDetailModel = new MemberDetailModel();
        List<BizRoleDetail> bizRoleDetailList = new ArrayList<>();
        BizRoleDetail bizRoleDetail = new BizRoleDetail();
        bizRoleDetail.setRoleId("111");
        bizRoleDetail.setRoleKey(ADMIN);
        bizRoleDetailList.add(bizRoleDetail);
        memberDetailModel.setRoleDetails(bizRoleDetailList);

        MemberDetailModel memberDetailModel2 = new MemberDetailModel();
        List<BizRoleDetail> bizRoleDetailList2 = new ArrayList<>();
        BizRoleDetail bizRoleDetail2 = new BizRoleDetail();
        bizRoleDetail2.setRoleId("222");
        bizRoleDetail2.setRoleKey(ADMIN);
        bizRoleDetailList2.add(bizRoleDetail);
        memberDetailModel2.setRoleDetails(bizRoleDetailList);

        result.add(memberDetailModel);
        result.add(memberDetailModel2);

        apiPageResult.setResult(result);
        this.memberService.handleResult(apiPageResult);
    }

    @Test
    public void updateMemberAllTest(){
        UpdateMemberAllRequest request = new UpdateMemberAllRequest();
        request.setGrantRoleIds(Lists.newArrayList("roleIduuu"));
        request.setMemberName("memberName");
        request.setEmployeeId("employeeId");
        request.setDeptIds(Lists.newArrayList("deptId2","deptI"));
        memberService.updateMemberAll("orgId","accountId",request);
    }


    @Test
    public void changeDetp(){
        ChangeMemberDeptRequest request = new ChangeMemberDeptRequest();
        request.setDeptIds(Lists.newArrayList("detpId"));
        request.setMemberIds(Lists.newArrayList("memberId"));
        memberService.changeDept("orgId",request,true);
    }

    @Test
    public void listAdminisTest(){
        PagerResult<EasunEsearchAccountOutput> esPageResult = new PagerResult<>();
        EasunEsearchAccountOutput es1 = MockUntils.getAccountOutPut();
        EasunEsearchAccountOutput es2 = MockUntils.getAccountOutPut();
        es2.getIdcards().getMobile().setDetail(null);
        esPageResult.setItems(Lists.newArrayList(es1,es2));
        PagerResult<MemberDetail> memberList= new PagerResult<>();
        MemberDetail memberDetail =  MockUntils.getMemberDetail();
        MemberDetail memberDetail2 =  MockUntils.getMemberDetail();
        memberDetail2.setMemberGid("gid");
        memberList.setItems(Lists.newArrayList(memberDetail,memberDetail2));
        when( esAccountServiceAdapt.getMemberListByEs(any())).thenReturn(memberList);
        when( esAccountServiceAdapt.getMemberListByEs(any(),any())).thenReturn(memberList);
        when(esAccountServiceAdapt.getAccountsByCondition(any())).thenReturn(esPageResult);
        memberService.listAdminis("orgId",0,10);
        EasunEsearchAccountOutput es3 = MockUntils.getAccountOutPut();
        es3.setStatus(RealnameStatus.INIT);
        EasunEsearchAccountOutput es4 = MockUntils.getAccountOutPut();
        es4.setStatus(RealnameStatus.INIT);
        es4.getIdcards().getEmail().setDetail(null);
        es4.getIdcards().getMobile().setDetail(null);
        EasunEsearchAccountOutput es5 = MockUntils.getAccountOutPut();
        es5.setStatus(RealnameStatus.ACCEPT);
        es5.getIdcards().getEmail().setDetail(null);
        es5.getIdcards().getMobile().setDetail(null);
        EasunEsearchAccountOutput es6 = MockUntils.getAccountOutPut();
        es6.setStatus(RealnameStatus.ACCEPT);
        es6.getIdcards().getMobile().setDetail(null);
        esPageResult.setItems(Lists.newArrayList(es4,es1,es5,es3,es4,es6,es2,es1,es6,es5));
        when(esAccountServiceAdapt.getAccountsByCondition(any())).thenReturn(esPageResult);
        memberService.listAdminis("orgId",0,10);
    }

}
