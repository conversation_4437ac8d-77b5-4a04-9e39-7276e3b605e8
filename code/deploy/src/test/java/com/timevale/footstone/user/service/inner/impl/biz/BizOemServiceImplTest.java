package com.timevale.footstone.user.service.inner.impl.biz;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcOrganizationPrivilegeServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.model.oem.AuthOrgListRes;
import com.timevale.footstone.user.service.model.organization.response.OrganDetailModel;
import com.timevale.open.platform.oauth.service.model.response.AuthorizeSubjectListResponse;
import com.timevale.open.platform.oauth.service.result.Response;
import com.timevale.open.platform.oauth.service.rpc.RpcOAuthRecordService;
import com.timevale.unittest.footstone.configuration.Application;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.assertj.core.util.Lists;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @since 2021/3/16
 **/
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizOemServiceImplTest {

  @InjectMocks
  private BizOemServiceImpl oemService;
  @Mock
  private RpcOAuthRecordService oAuthRecordService;
  @Mock
  private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;
  @Mock
  private RpcEsAccountServiceAdapt esAccountServiceAdapt;
  @Mock
  private RpcOrganizationPrivilegeServiceAdapt rpcOrganizationPrivilegeServiceAdapt;
  @Mock
  private RpcOrgPlusService rpcOrgPlusService;
  @Mock
  private BizOrganService organService;

  @Before
  public void setUp() throws Exception {
  }

  @After
  public void tearDown() throws Exception {
  }

  @Test
  public void getAuthOrgList_when_account_not_belong_org() {
    /*
      case: 当账户不是企业成员时
        * 账户不属于任何企业
     */
    final String accountId = "1";
    final String appId = "1";
    final String orgName = "testOrg";
    final List<String> queryOrgname = Lists.newArrayList(orgName);

    Response<AuthorizeSubjectListResponse> authSubListOut = new Response<>();
    AuthorizeSubjectListResponse authResponse = new AuthorizeSubjectListResponse();
    authResponse.setRecordList(Lists.newArrayList());
    authSubListOut.setData(authResponse);
    when(oAuthRecordService.getAuthorizedSubjectList(any())).thenReturn(authSubListOut);
    PagerResult<BizOrgBaseInfoOutput> icOrgList = new PagerResult<>(Lists.newArrayList(), 0);
    when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(), any())).thenReturn(icOrgList);
    EsOrgSearhOutput orgSearhOutput = new EsOrgSearhOutput();
    orgSearhOutput.setCreater("1");
    orgSearhOutput.setCreaterOid("1");
    orgSearhOutput.setId("1");
    orgSearhOutput.setOrganAccountUid("1");
    orgSearhOutput.setOrganGuid("1");
    orgSearhOutput.setDeleted(false);
    EasunEsearchAccountOutput user = new EasunEsearchAccountOutput();
    EsId ids = new EsId();
    ids.setGuid("1");
    ids.setOuid("1");
    ids.setUuid("1");
    user.setId(ids);
    user.setStatus(RealnameStatus.ACCEPT);
    user.setProperties(Lists.newArrayList(new Property(BuiltinProperty.INFO_NAME, orgName)));
    orgSearhOutput.setUser(user);
    PagerResult<EsOrgSearhOutput> queryRealOrgResult = new PagerResult<>(
        Lists.newArrayList(orgSearhOutput), 1);
    when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(queryRealOrgResult);
    Map<String, Boolean> roleGrantMap = Collections.singletonMap("test", false);
    when(rpcOrganizationPrivilegeServiceAdapt.getUserGrantedByOrganIdList(any(), any(), any()))
        .thenReturn(roleGrantMap);

    AuthOrgListRes res = oemService
        .getAuthOrgList(accountId, appId, queryOrgname);
    Assert.assertEquals(1, res.getTotal());
    Assert.assertFalse(res.getAuthList().get(0).isHasAdmin());

  }

  @Test
  public void getAuthOrgList_when_do_not_has_org() {
    /*
      case: 当企业不存在的时
        * 给用户新创建一个企业
     */
    final String accountId = "1";
    final String appId = "1";
    final String orgName = "testOrg";
    final List<String> queryOrgname = Lists.newArrayList(orgName);

    Response<AuthorizeSubjectListResponse> authSubListOut = new Response<>();
    AuthorizeSubjectListResponse authResponse = new AuthorizeSubjectListResponse();
    authResponse.setRecordList(Lists.newArrayList());
    authSubListOut.setData(authResponse);
    when(oAuthRecordService.getAuthorizedSubjectList(any())).thenReturn(authSubListOut);
    PagerResult<BizOrgBaseInfoOutput> icOrgList = new PagerResult<>(Lists.newArrayList(), 0);
    when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(), any())).thenReturn(icOrgList);
    PagerResult<EsOrgSearhOutput> queryRealOrgResult = new PagerResult<>(Lists.newArrayList(), 0);
    when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(queryRealOrgResult);
    RpcOutput<BizOrganOuid> rpcOutput = RpcOutput.with(new BizOrganOuid(true, "1"));
    when(rpcOrgPlusService.createICOrgAndCompanyWithSource(any())).thenReturn(rpcOutput);
    when(organService.getOrgByName(any(), any(), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(new PagerResult<OrganDetailModel>(Lists.newArrayList(), 0));

    AuthOrgListRes res = oemService
        .getAuthOrgList(accountId, appId, queryOrgname);
    Assert.assertEquals(1, res.getTotal());
    Assert.assertFalse(res.getAuthList().get(0).isHasAdmin());
  }
}