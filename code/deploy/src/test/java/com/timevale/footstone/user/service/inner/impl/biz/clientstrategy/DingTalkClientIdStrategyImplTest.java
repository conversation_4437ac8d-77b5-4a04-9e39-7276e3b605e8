package com.timevale.footstone.user.service.inner.impl.biz.clientstrategy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.biz.acc.output.BizAccountBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.biz.rlnm.output.BizAccountRealnameOutput;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.biz.BizThirdPlatformIntegrationService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.realname.RpcRealnameServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcThirdPartyPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import com.timevale.footstone.user.service.inner.model.QueryUserOrganListParam;
import com.timevale.footstone.user.service.inner.model.SetDefaultOrgParam;
import com.timevale.footstone.user.service.inner.model.SetUserPersonSpaceParam;
import com.timevale.footstone.user.service.model.organization.response.ListOrganAndRoleResponse;
import com.timevale.saas.auth.api.facade.api.RpcSubCorpDepartmentService;
import com.timevale.saas.auth.api.facade.result.org.GetUserDingSubOrgListRsp;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @since 2025/4/02
 */
@RunWith(MockitoJUnitRunner.StrictStubs.class)
public class DingTalkClientIdStrategyImplTest {
    @InjectMocks
    private DingTalkClientIdStrategyImpl dingTalkClientIdStrategy;

    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;
    @Mock
    private RpcThirdPartyPlusServiceAdapt rpcThirdPartyPlusServiceAdapt;
    @Mock
    private RpcEsAccountServiceAdapt rpcEsAccountServiceAdapt;
    @Mock
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;
    @Mock
    private BizOrganService bizOrganService;
    @Mock
    private RpcRealnameServiceAdapt realnameServiceAdapt;
    @Mock
    private RpcSubCorpDepartmentService rpcSubCorpDepartmentService;
    @Mock
    private BizThirdPlatformIntegrationService bizThirdPlatformIntegrationService;
    @Mock
    private RpcOrgPlusServiceAdapt rpcOrgPlusServiceAdapt;

    @Test
    public void testQueryUserOrganList() {
        DingTalkClientIdStrategyImpl dingTalkClientIdStrategySpy = Mockito.spy(dingTalkClientIdStrategy);
        Mockito.doReturn("primaryOrgOid111111111").when(dingTalkClientIdStrategySpy).getPrimaryOrgRealNameOrganOid(any(), any(), any());

        GetUserDingSubOrgListRsp userDingSubOrgListRsp = new GetUserDingSubOrgListRsp();
        userDingSubOrgListRsp.setSubOrgOidList(Sets.newHashSet("orgOid2222", "orgOid3333"));
        Mockito.when(rpcTripartiteServiceAdapt.getUserDingSubOrgList(any(), any(), any())).thenReturn(userDingSubOrgListRsp);
        BizAccountRealnameOutput bizAccountRealnameOutput = new BizAccountRealnameOutput();
        bizAccountRealnameOutput.setStatus(RealnameStatus.ACCEPT);
        Mockito.when(realnameServiceAdapt.getBaseRealnameInfo(any())).thenReturn(bizAccountRealnameOutput);

        List<BizOrgBaseInfoOutput> bizOrgBaseInfoOutputs = Lists.newArrayList();
        BizOrgBaseInfoOutput bizOrgBaseInfoOutput = new BizOrgBaseInfoOutput();
        BizICUserOutput account = new BizICUserOutput();
        ICUserId id = new ICUserId();
        id.setOuid("orgOid2222");
        account.setId(id);
        account.setBase(new BizAccountBaseOutput());
        account.setProperties(Lists.newArrayList());
        bizOrgBaseInfoOutput.setAccount(account);
        bizOrgBaseInfoOutput.setRoleDetailList(Lists.newArrayList());
        bizOrgBaseInfoOutputs.add(bizOrgBaseInfoOutput);
        Mockito.when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(), any())).thenReturn(new PagerResult<>(bizOrgBaseInfoOutputs, bizOrgBaseInfoOutputs.size()));

        List<EsOrgSearhOutput> esOrgSearchOutputs1 = Lists.newArrayList();
        EsOrgSearhOutput esOrgSearhOutput1 = new EsOrgSearhOutput();
        EasunEsearchAccountOutput user1 = new EasunEsearchAccountOutput();
        EsId id1 = new EsId();
        id1.setOuid("orgOid2222");
        user1.setId(id1);
        user1.setPropertyMaps(new HashMap<>());
        esOrgSearhOutput1.setUser(user1);
        esOrgSearchOutputs1.add(esOrgSearhOutput1);

        EsOrgSearhOutput esOrgSearhOutput2 = new EsOrgSearhOutput();
        EasunEsearchAccountOutput user2 = new EasunEsearchAccountOutput();
        EsId id2 = new EsId();
        id2.setOuid("orgOid3333");
        user2.setId(id2);
        user2.setPropertyMaps(new HashMap<>());
        esOrgSearhOutput2.setUser(user2);

        esOrgSearchOutputs1.add(esOrgSearhOutput2);
        Mockito.when(rpcEsAccountServiceAdapt.getOrgsByEs(any())).thenReturn(new PagerResult<>(esOrgSearchOutputs1, esOrgSearchOutputs1.size()));


        QueryUserOrganListParam param = QueryUserOrganListParam.builder().build();
        ListOrganAndRoleResponse response = dingTalkClientIdStrategySpy.queryUserOrganList(param);
        System.out.println(JSON.toJSONString(response));
        Assert.assertEquals(3, response.getBizUserInfoOutputs().size());
        Assert.assertTrue(response.isPersonSpace());
        Assert.assertEquals("DEFAULT", response.getLoginSource());
        Assert.assertEquals(3, (int) response.getTotal());
    }

    /**
     * 逻辑：根据三方企业id查询oid为null ,然后创建新企业
     */
    @Test
    public void testGetPrimaryOrgRealNameOrganOid() {
        QueryUserOrganListParam param = QueryUserOrganListParam.builder().build();
        dingTalkClientIdStrategy.getPrimaryOrgRealNameOrganOid(param, "企业A", ClientEnum.DING_TALK.getClientId());
        Mockito.verify(bizOrganService, Mockito.times(1)).createOrgAndBindTenantKey(any(), any(), any(), any(), any());
    }

    @Test
    public void testGetPrimaryOrgRealNameOrganOid2() {
        Mockito.when(rpcOrgPlusServiceAdapt.getOrgIdByThirdParty(any(), any())).thenReturn("oid111111");
        QueryUserOrganListParam param = QueryUserOrganListParam.builder().build();
        String oid = dingTalkClientIdStrategy.getPrimaryOrgRealNameOrganOid(param, "企业A", ClientEnum.DING_TALK.getClientId());
        Assert.assertEquals("oid111111", oid);
    }

    @Test
    public void testSetUserDefaultOrgInThirdPartyTenant() {
        Mockito.when(bizThirdPlatformIntegrationService.queryPersonThirdUserIdByOid(any(), any(), any())).thenReturn("unionId1111");

        SetDefaultOrgParam param = new SetDefaultOrgParam();
        param.setTenantKey("tenantKey111111");
        dingTalkClientIdStrategy.setUserDefaultOrgInThirdPartyTenant(param);
        Mockito.verify(rpcSubCorpDepartmentService, Mockito.times(1)).changeDefaultSubCorp(any());
        Mockito.verify(icOrgPlusServiceAdapt, Mockito.times(1)).createMember(any(), any(), any());
    }

    @Test
    public void testSetUserPersonSpaceInThirdPartyTenant() {
        Mockito.when(bizThirdPlatformIntegrationService.queryPersonThirdUserIdByOid(any(), any(), any())).thenReturn("unionId1111");

        SetUserPersonSpaceParam param = new SetUserPersonSpaceParam();
        param.setTenantKey("tenantKey111111");
        dingTalkClientIdStrategy.setUserPersonSpaceInThirdPartyTenant(param);

        Mockito.verify(rpcSubCorpDepartmentService, Mockito.times(1)).changePersonSpace(any());

    }
}