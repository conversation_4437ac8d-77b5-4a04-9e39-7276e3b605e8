package com.timevale.footstone.user.service.inner.impl.biz;

import com.timevale.footstone.user.service.inner.biz.BizQRService;
import com.timevale.footstone.user.service.model.account.response.QrUidApplyResponse;
import com.timevale.footstone.user.service.model.account.response.QrUidResultResponse;
import com.timevale.footstone.user.service.model.enums.QrUidStatusEnum;
import com.timevale.footstone.user.service.model.qrlogin.QrUidData;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.open.platform.service.service.api.AppService;
import com.timevale.open.platform.service.service.api.RSAService;
import com.timevale.open.platform.service.service.model.response.AppDetail;
import com.timevale.unittest.footstone.configuration.Application;
import junit.framework.TestCase;
import org.apache.commons.codec.binary.Base64;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizQRServiceImplTest extends TestCase {

    @Autowired
    BizQRService bizQRService;

    @Autowired
    private AppService appService;

    @Autowired
    private RSAService rsaService;

    @Test
    public void testApply() {
        RequestContext.put("X-Tsign-Open-App-Id", "3876545303");
        QrUidApplyResponse data = bizQRService.apply();
        String qruid = data.getQruid();
        String url = data.getUrl();
        Assert.assertNotNull(qruid);
        Assert.assertTrue(url.contains(url));
    }

    @Test
    public void testResult() throws BadPaddingException, UnsupportedEncodingException, IllegalBlockSizeException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidKeySpecException {
        RequestContext.put("X-Tsign-Open-App-Id", "3876545303");

        QrUidApplyResponse apply = bizQRService.apply();
        String qruid = apply.getQruid();
        QrUidResultResponse response = bizQRService.result(qruid);
        Assert.assertSame(QrUidStatusEnum.WATING.name(), response.getStatus());

        QrUidResultResponse response2 = bizQRService.result("1234");
        Assert.assertSame(QrUidStatusEnum.NOT_EXISTS.name(), response2.getStatus());

        QrUidData data = TedisUtil.get(qruid);
        data.setStatus(QrUidStatusEnum.OK).setUserId("12345");
        TedisUtil.set(qruid, data, 1, TimeUnit.MINUTES);
        HashMap<String, Object> context = new HashMap<>();
        context.put("X-Tsign-Open-App-Id","3876545303");
        RequestContext.setContextMap(context);
        QrUidResultResponse response3 = bizQRService.result(qruid);
        Assert.assertSame(QrUidStatusEnum.OK.name(), response3.getStatus());
        Assert.assertNotEquals("12345", response3.getAccoutId());
        Assert.assertNotNull(response3.getAccoutId());
        Assert.assertEquals("12345", decrypt(response3.getAccoutId()));
    }

    private String decrypt(String text) throws UnsupportedEncodingException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException, NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeySpecException {
        //取私钥
        AppDetail appInfo = appService.getAppInfo("3876545303");
        String privateKey = appInfo.getPrivateKey();
        String publicKey = appInfo.getPublicKey();
//        //加密
//        RSADecryptRequest decryptRequest = new RSADecryptRequest();
//
//        decryptRequest.setPrivateKey(privateKey);
//        decryptRequest.setPublicKey(publicKey);
//        decryptRequest.setEncryptContent(text);
//        String content = rsaService.decryptByPublicKey(decryptRequest).getData();
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(text.getBytes(StandardCharsets.UTF_8));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey key = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, key);
        String content = new String(cipher.doFinal(inputByte));
        return content;
    }
}