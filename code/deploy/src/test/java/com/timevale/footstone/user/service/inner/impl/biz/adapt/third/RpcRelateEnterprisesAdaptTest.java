package com.timevale.footstone.user.service.inner.impl.biz.adapt.third;

import com.timevale.account.organization.service.api.RpcThirdRelateEnterprisesService;
import com.timevale.account.organization.service.model.service.biz.output.BizThirdEnterprisesOrgRelGetOutput;
import com.timevale.account.organization.service.model.service.biz.output.BizThirdRelateEnterprisesGetOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.icuser.ICAccountUser;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsAccountIdCardDetailOutput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.page.Pages;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.model.thirdplatform.request.RelatedEnterprisesAddRequest;
import com.timevale.footstone.user.service.model.thirdplatform.request.RelatedOrgRequest;
import com.timevale.footstone.user.service.model.thirdplatform.response.RelatedOrganResponse;
import com.timevale.mandarin.common.result.PageQueryResult;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RpcRelateEnterprisesAdaptTest {

    @Mock
    private RpcThirdRelateEnterprisesService mockRpcThirdRelateEnterprisesService;
    @Mock
    private RpcIcUserPlusServiceAdapt mockRpcIcUserPlusServiceAdapt;
    @Mock
    private RpcEsAccountServiceAdapt mockRpcEsAccountServiceAdapt;
    @Mock
    private IdAdapt mockIdAdapt;

    @InjectMocks
    private RpcRelateEnterprisesAdapt rpcRelateEnterprisesAdaptUnderTest;

    @Test
    public void testPageQueryRelateEnterprises() {
        Pages pages = new Pages(0, 0);
        RpcOutput<PageQueryResult<BizThirdRelateEnterprisesGetOutput>> pageQueryResultRpcOutput = RpcOutput.with(
                new PageQueryResult<>(false));
        when(mockRpcThirdRelateEnterprisesService.pageQueryRelateEnterprisesSimple(any(RpcInput.class)))
                .thenReturn(pageQueryResultRpcOutput);
        PageQueryResult<BizThirdRelateEnterprisesGetOutput> result = rpcRelateEnterprisesAdaptUnderTest.pageQueryRelateEnterprises(
                "KINGDEE_K3EE", Arrays.asList("value"), pages);
    }

    @Test
    public void testPageQueryRelateEnterprises_RpcThirdRelateEnterprisesServiceReturnsNoItem() {
        Pages pages = new Pages(0, 0);
        when(mockRpcThirdRelateEnterprisesService.pageQueryRelateEnterprisesSimple(any(RpcInput.class)))
                .thenReturn(RpcOutput.empty());
        PageQueryResult<BizThirdRelateEnterprisesGetOutput> result = rpcRelateEnterprisesAdaptUnderTest.pageQueryRelateEnterprises(
                "KINGDEE_K3EE", Arrays.asList("value"), pages);

    }

    @Test
    public void testUpdateRelateEnterprises() {
        RelatedEnterprisesAddRequest request = new RelatedEnterprisesAddRequest();
        request.setClientId("KINGDEE_K3EE");
        request.setTenantKey("tenantKey");
        request.setOuid("ouid");
        RelatedOrgRequest relatedOrgRequest = new RelatedOrgRequest();
        relatedOrgRequest.setClientId("KINGDEE_K3EE");
        relatedOrgRequest.setTenantKey("tenantKey");
        relatedOrgRequest.setOuid("ouid");
        relatedOrgRequest.setOrgNo("orgNo");
        relatedOrgRequest.setOrgName("orgName");
        request.setRelatedOrganList(Arrays.asList(relatedOrgRequest));

        BizICUserOutput bizICUserOutput = new BizICUserOutput();
        ICUserId id = new ICUserId();
        id.setOuid("ouid");
        id.setUuid("03f15e91-338c-498c-bd44-bdd469cbd2ba");
        id.setGuid("1e6c5778-2bdf-4d79-8a27-11f6fe4a694b");
        bizICUserOutput.setId(id);
        when(mockRpcIcUserPlusServiceAdapt.getICUserByOuid("modifier")).thenReturn(bizICUserOutput);

        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsAccountIdCardDetailOutput idcardInfo = new EsAccountIdCardDetailOutput();
        idcardInfo.setKey("KINGDEE_K3EE_TENANT_value");
        idcardInfo.setValue("value");
        esAccountIdcardInfoOutput.setIdcardInfo(idcardInfo);
        list.add(esAccountIdcardInfoOutput);
        esAccountIdcardInfoOutput.setId(new EsId());
        pagerResult.setItems(list);
        when(mockRpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(pagerResult);

        when(mockRpcThirdRelateEnterprisesService.updateOrgRelation(any(RpcInput.class)))
                .thenReturn(RpcOutput.with(false));

        Boolean result = rpcRelateEnterprisesAdaptUnderTest.updateRelateEnterprises(request, "modifier");
        assertFalse(result);
    }


    @Test
    public void testGetRelateEnterprises() {
        // Setup
        final BizThirdRelateEnterprisesGetOutput enterprisesGetOutput = new BizThirdRelateEnterprisesGetOutput();
        enterprisesGetOutput.setPlatform("platform");
        enterprisesGetOutput.setTenantKey("tenantKey");
        enterprisesGetOutput.setOuid("ouid");
        enterprisesGetOutput.setGuid("a8d814ee-3faa-4531-b38c-b5e1daa4dda0");
        enterprisesGetOutput.setCreator("creator");
        final List<BizThirdRelateEnterprisesGetOutput> expectedResult = Arrays.asList(enterprisesGetOutput);

        // Configure RpcThirdRelateEnterprisesService.getRelateEnterprises(...).
        final BizThirdRelateEnterprisesGetOutput enterprisesGetOutput1 = new BizThirdRelateEnterprisesGetOutput();
        enterprisesGetOutput1.setPlatform("platform");
        enterprisesGetOutput1.setTenantKey("tenantKey");
        enterprisesGetOutput1.setOuid("ouid");
        enterprisesGetOutput1.setGuid("a8d814ee-3faa-4531-b38c-b5e1daa4dda0");
        enterprisesGetOutput1.setCreator("creator");
        final RpcOutput<List<BizThirdRelateEnterprisesGetOutput>> listRpcOutput = RpcOutput.with(
                Arrays.asList(enterprisesGetOutput1));
        when(mockRpcThirdRelateEnterprisesService.getRelateEnterprises(any(RpcInput.class))).thenReturn(listRpcOutput);

        // Run the test
        final List<BizThirdRelateEnterprisesGetOutput> result = rpcRelateEnterprisesAdaptUnderTest.getRelateEnterprises(
                "ouid", "platform", Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Test
    public void testAddRelateEnterprises() {
        // Setup
        final RelatedEnterprisesAddRequest request = new RelatedEnterprisesAddRequest();
        request.setClientId("KINGDEE_K3EE");
        request.setTenantKey("tenantKey");
        request.setOuid("ouid");
        final RelatedOrgRequest relatedOrgRequest = new RelatedOrgRequest();
        relatedOrgRequest.setClientId("KINGDEE_K3EE");
        relatedOrgRequest.setTenantKey("tenantKey");
        relatedOrgRequest.setOuid("ouid");
        relatedOrgRequest.setOrgNo("orgNo");
        relatedOrgRequest.setOrgName("orgName");
        request.setRelatedOrganList(Arrays.asList(relatedOrgRequest));

        // Configure IdAdapt.getICUserBase(...).
        final BizICUserBaseOutput bizICUserBaseOutput = new BizICUserBaseOutput();
        final ICAccountUser id = new ICAccountUser();
        id.setGuid("1e6c5778-2bdf-4d79-8a27-11f6fe4a694b");
        id.setAccountUid("accountUid");
        id.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        bizICUserBaseOutput.setId(id);
        final AccountBaseDetail base = new AccountBaseDetail();
        bizICUserBaseOutput.setBase(base);
        when(mockIdAdapt.getICUserBase("ouid")).thenReturn(bizICUserBaseOutput);

        // Configure RpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(...).
        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsAccountIdCardDetailOutput idcardInfo = new EsAccountIdCardDetailOutput();
        idcardInfo.setKey("KINGDEE_K3EE_TENANT_value");
        idcardInfo.setValue("value");
        esAccountIdcardInfoOutput.setIdcardInfo(idcardInfo);
        list.add(esAccountIdcardInfoOutput);
        esAccountIdcardInfoOutput.setId(new EsId());
        pagerResult.setItems(list);
        when(mockRpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(pagerResult);

        final BizICUserOutput bizICUserOutput = new BizICUserOutput();
        final ICUserId id2 = new ICUserId();
        id2.setOuid("ouid");
        id2.setUuid("03f15e91-338c-498c-bd44-bdd469cbd2ba");
        id2.setGuid("1e6c5778-2bdf-4d79-8a27-11f6fe4a694b");
        bizICUserOutput.setId(id2);
        when(mockRpcIcUserPlusServiceAdapt.getICUserByOuid("modifier")).thenReturn(bizICUserOutput);

        when(mockRpcThirdRelateEnterprisesService.initThirdRelateEnterprises(any(RpcInput.class)))
                .thenReturn(RpcOutput.with(false));

        rpcRelateEnterprisesAdaptUnderTest.addRelateEnterprises(request, "modifier");
    }


    @Test
    public void testGetRelatedOrg() {
        // Setup
        final RelatedOrganResponse relatedOrganResponse = new RelatedOrganResponse();
        relatedOrganResponse.setOrgNo("orgNo");
        relatedOrganResponse.setOrgName("orgName");
        relatedOrganResponse.setId(0L);
        relatedOrganResponse.setRelatedId(0L);
        final List<RelatedOrganResponse> expectedResult = Arrays.asList(relatedOrganResponse);

        final BizThirdEnterprisesOrgRelGetOutput bizThirdEnterprisesOrgRelGetOutput = new BizThirdEnterprisesOrgRelGetOutput();
        bizThirdEnterprisesOrgRelGetOutput.setThirdOrgNo("orgNo");
        bizThirdEnterprisesOrgRelGetOutput.setThirdOrgName("orgName");
        bizThirdEnterprisesOrgRelGetOutput.setSource("source");
        bizThirdEnterprisesOrgRelGetOutput.setCreator("creator");
        bizThirdEnterprisesOrgRelGetOutput.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        RpcOutput<List<BizThirdEnterprisesOrgRelGetOutput>> listRpcOutput = RpcOutput.with(
                Arrays.asList(bizThirdEnterprisesOrgRelGetOutput));
        when(mockRpcThirdRelateEnterprisesService.listOrgRel(any(RpcInput.class))).thenReturn(listRpcOutput);

        // Run the test
        List<RelatedOrganResponse> result = rpcRelateEnterprisesAdaptUnderTest.getRelatedOrg("platform",
                "tenantKey", "ouid");
    }


    @Test
    public void testDelRelatedEnterprises() {
        // Setup
        // Configure RpcIcUserPlusServiceAdapt.getICUserByOuid(...).
        final BizICUserOutput bizICUserOutput = new BizICUserOutput();
        final ICUserId id = new ICUserId();
        id.setOuid("ouid");
        id.setUuid("03f15e91-338c-498c-bd44-bdd469cbd2ba");
        id.setGuid("1e6c5778-2bdf-4d79-8a27-11f6fe4a694b");
        bizICUserOutput.setId(id);
        when(mockRpcIcUserPlusServiceAdapt.getICUserByOuid("modifier")).thenReturn(bizICUserOutput);

        // Configure RpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(...).
        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsAccountIdCardDetailOutput idcardInfo = new EsAccountIdCardDetailOutput();
        idcardInfo.setKey("KINGDEE_K3EE_TENANT_value");
        idcardInfo.setValue("value");
        esAccountIdcardInfoOutput.setIdcardInfo(idcardInfo);
        list.add(esAccountIdcardInfoOutput);
        esAccountIdcardInfoOutput.setId(new EsId());
        pagerResult.setItems(list);
        when(mockRpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(pagerResult);

        when(mockRpcThirdRelateEnterprisesService.delThirdRelateEnterprises(any(RpcInput.class)))
                .thenReturn(RpcOutput.with(false));

        // Run the test
        final Boolean result = rpcRelateEnterprisesAdaptUnderTest.delRelatedEnterprises("KINGDEE_K3EE", "tenantKey", "ouid",
                "modifier");

        // Verify the results
        assertFalse(result);
    }


}
