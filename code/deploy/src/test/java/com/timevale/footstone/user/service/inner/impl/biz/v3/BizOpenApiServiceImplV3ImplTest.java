package com.timevale.footstone.user.service.inner.impl.biz.v3;

import com.timevale.footstone.user.service.model.BizAppIdModel;
import com.timevale.footstone.user.service.model.account.request.v3.AccountBindUrlRequest;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizOpenApiServiceImplV3ImplTest {

    @Autowired
    private BizOpenApiServiceImplV3Impl bizOpenApiServiceImplV3;

    @Test
    public void testBuilderAccountBindUrl() throws Exception {
        // Setup
        final AccountBindUrlRequest request = new AccountBindUrlRequest();
        request.setAccount("***********");
        request.setChangeType("mobile");
        request.setHideTopBar(false);
        request.setRedirectUrl("redirectUrl");
        request.setCustomBizNum("customBizNum");
        try {
            BizAppIdModel appIdModel  = new BizAppIdModel();
            appIdModel.setAppId("**********");
            bizOpenApiServiceImplV3.builderAccountBindUrl(request, appIdModel);
        } catch (Exception e) {
            // 异常
        }
    }

    @Test
    public void testGetAccountInfoByContextId() {
        // Run the test
        bizOpenApiServiceImplV3.getAccountInfoByContextId("");
        bizOpenApiServiceImplV3.getAccountInfoByContextId("contextId");
        bizOpenApiServiceImplV3.getAccountInfoByContextId("dfd96033b78c4d41b7ad18f821d131d7");
    }
}