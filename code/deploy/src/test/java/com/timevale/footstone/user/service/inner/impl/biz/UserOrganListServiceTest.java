package com.timevale.footstone.user.service.inner.impl.biz;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.account.organization.service.model.service.newbiz.output.BizMemberGetOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.footstone.user.service.component.SaasOrganizationComponent;
import com.timevale.footstone.user.service.component.SaasTaskComponent;
import com.timevale.footstone.user.service.inner.impl.UserOrganListServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.FileSystemServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgMemberServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSGrayConfigAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.inner.model.OrganSortModel;
import com.timevale.footstone.user.service.model.account.mods.UserInfoModel;
import com.timevale.footstone.user.service.model.organization.request.OrganSortRequest;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import com.timevale.saas.common.manage.common.service.model.output.OrganizationInfoQueryOutput;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/11/20 15:26
 */
@RunWith(MockitoJUnitRunner.class)
public class UserOrganListServiceTest {

    @InjectMocks
    private UserOrganListServiceImpl userOrganListService;


    @Mock
    private RpcInnerOrgMemberServiceAdapt innerOrgMemberServiceAdapt;


    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Mock
    private SaasOrganizationComponent saasOrganizationComponent;

    @Mock
    private SaasTaskComponent saasTaskComponent;

    @Mock
    private FileSystemServiceAdapt fileSystemServiceAdapt;

//    @Mock
//    private OrganizationRpcService organizationRpcService;


    @Mock
    private RpcSaaSGrayConfigAdapt rpcSaaSGrayConfigAdapt;

    @Mock
    private RpcIcOrgPlusServiceAdapt orgPlusServiceAdapt;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;

    @Mock
    private RpcRoleServiceAdapt roleServiceAdapt;

    @Before
    public void setUp(){
        Map<String, BizRoleDetail> roleMap = Maps.newHashMap();
        roleMap.put("organId1",MockUntils.getBizRoleDetail());
        roleMap.put("organId999",MockUntils.getBizRoleDetail());
        when( roleServiceAdapt.getOrgansRoleByRoleKey(any(),any())).thenReturn(roleMap);
        Map<String, EsOrgSearhOutput> orgs= Maps.newHashMap();
        orgs.put("organId1",MockUntils.mockEsOrgSearhOutput());
        orgs.put("organId999",MockUntils.mockEsOrgSearhOutput());
        orgs.put("orgId2",MockUntils.mockEsOrgSearhOutput());
        orgs.put("orgId",MockUntils.mockEsOrgSearhOutput());
        orgs.put("orgId3",MockUntils.mockEsOrgSearhOutput());
        when(esAccountServiceAdapt.searchByOrgIds(any())).thenReturn(orgs);
        when(innerOrgMemberServiceAdapt.getMemberBatchOrgan(any(),any()))
                .thenReturn(Lists.newArrayList(MockUntils.mockBizMemberGetOutput()));
        Map<String, OrganizationInfoQueryOutput> map = new HashMap<>();
        map.put("guid",MockUntils.mockOrganizationInfoQueryOutput());
        when(saasOrganizationComponent.batchQueryOrganizationInfo(any())).thenReturn(map);
        Map<String,List<MemberDetail>> membermap = new HashMap<>();
        membermap.put("organId",Lists.newArrayList(MockUntils.getMemberDetail()));
        when( orgPlusServiceAdapt.batchGrgansAdminMap(any(),any())).thenReturn(membermap);
        when(icUserPlusService.getICUserByOuid(any())).thenReturn(MockUntils.mockBizICUserOutput());
        when(fileSystemServiceAdapt.upload(any(), any())).thenReturn("fileKey");
//        OrganizationInfoBatchQueryOutput output =new OrganizationInfoBatchQueryOutput();
//        output.setOrgInfoMap(map);
//        when(organizationRpcService.batchQueryOrganizationInfo(any())).thenReturn(output);
        ReflectionTestUtils.setField(userOrganListService,"subItem",100);
    }

    @Test
    public void fixUnOrganMemberDataTest(){
        BizICUserOutput member = MockUntils.mockBizICUserOutput();
        List<String> organIds = Lists.newArrayList("organId","organId1","organId999");
        List<BizMemberGetOutput > members = Lists.newArrayList(MockUntils.mockBizMemberGetOutput());
        Map<String, String> organMemberMap = Maps.newHashMap();
        userOrganListService.fixUnOrganMemberData(member,organIds,members,organMemberMap);
    }

    @Test
    public void convSortListTest(){
        List<OrganSortRequest> orgsList = new ArrayList<>();
        OrganSortRequest organSortRequest = new OrganSortRequest();
        organSortRequest.setOrgId("organId1");
        organSortRequest.setTop(1);
        orgsList.add(organSortRequest);
        OrganSortRequest organSortRequest2 = new OrganSortRequest();
        organSortRequest2.setOrgId("organId999");
        organSortRequest2.setTop(1);
        orgsList.add(organSortRequest2);
        userOrganListService.convSortList(orgsList);
    }



    @Test
    public void userOrganListSortTest(){
        BizICUserOutput member = MockUntils.mockBizICUserOutput();
        List<OrganSortModel> organList =  new ArrayList<>();
        OrganSortModel sortModel = new OrganSortModel();
        sortModel.setOrganId("organId");
        sortModel.setTop(1);
        sortModel.setWeight(1);
        organList.add(sortModel);
        OrganSortModel sortModel2 = new OrganSortModel();
        sortModel2.setOrganId("organId2");
        sortModel2.setTop(1);
        sortModel2.setWeight(1);
        organList.add(sortModel);
        userOrganListService.userOrganListSort(member,organList);
    }


    @Test
    public void organExportTest(){
        List<UserInfoModel> organList = new ArrayList<>();
        organList.add(MockUntils.mockUserInfoModel());
        UserInfoModel userInfoModel1 = MockUntils.mockUserInfoModel();
        userInfoModel1.setGuid("guid2");
        userInfoModel1.setOrgId("orgId2");
        organList.add(userInfoModel1);
        UserInfoModel userInfoModel3 = MockUntils.mockUserInfoModel();
        userInfoModel3.setGuid("guid3");
        userInfoModel3.setOrgId("orgId3");
        organList.add(userInfoModel3);
        String accountId = "accountId";
        userOrganListService.organExport(organList,accountId);
        ReflectionTestUtils.setField(userOrganListService,"subItem",1);
        userOrganListService.organExport(organList,accountId);
    }
}
