package com.timevale.footstone.user.service.rest.v3;

import com.timevale.footstone.user.service.inner.biz.v3.BizDeptServiceV3;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @version 2024/12/31 10:45
 */
@RunWith(MockitoJUnitRunner.Silent.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DeptRestV3Test {


    @InjectMocks
    private DeptRestV3 deptRestV3;



    @Mock
    private BizDeptServiceV3 bizDeptServiceV3;


    @Test
    public void subListTest(){
        deptRestV3.subList("orgId","ALL","dept",true,1,"ESIGN",false);
    }

}
