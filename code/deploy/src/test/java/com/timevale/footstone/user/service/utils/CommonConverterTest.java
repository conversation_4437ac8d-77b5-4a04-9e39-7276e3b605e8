package com.timevale.footstone.user.service.utils;

import com.timevale.footstone.user.service.utils.conv.CommonConverter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @date 2023/11/14 16:37
 */
@RunWith(MockitoJUnitRunner.class)
public class CommonConverterTest {

//    @InjectMocks
//    CommonConverter commonConverter;

    @Test
    public void decorateIdNoTest(){
        CommonConverter.decorateIdNo(null);
        CommonConverter.decorateIdNo("37154454787977874");
    }
}
