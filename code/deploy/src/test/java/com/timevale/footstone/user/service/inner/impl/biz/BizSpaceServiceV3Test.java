package com.timevale.footstone.user.service.inner.impl.biz;

import com.google.common.collect.Lists;
import com.timevale.account.organization.service.model.service.newbiz.output.BizSubOrganOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.MemberDeptCheckOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.SpaceBaseOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.SpaceDeptCheckOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.easun.service.api.RpcDeptPlusV3Service;
import com.timevale.easun.service.api.RpcEasunDeptMemberService;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.account.output.*;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.output.BizDeptDetailOutput;
import com.timevale.easun.service.model.organization.output.v3.BizDeptDetailOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizDeptListOutputV3;
import com.timevale.easun.service.model.sender.output.BizSenderServiceOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.DeptNotBelongOrg;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.DeptOrAdminNotEmpty;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.MemberCheckDeptError;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.NotBranchInRootOrgan;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdaptV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrganSpaceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizSpaceServiceV3Impl;
import com.timevale.footstone.user.service.model.member.mods.DeptBaseResponse;
import com.timevale.footstone.user.service.model.member.mods.DeptSubListResponse;
import com.timevale.footstone.user.service.model.member.request.v3.DeleteMemberBatchRequest;
import com.timevale.footstone.user.service.model.space.request.*;
import com.timevale.footstone.user.service.utils.MockUntils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/7/18 21:23
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class BizSpaceServiceV3Test {

    @InjectMocks
    BizSpaceServiceV3Impl bizSpaceServiceV3;

    @Mock
    private RpcOrganSpaceAdapt rpcOrganSpaceAdapt;


    @Mock
    private RpcSenderAuthService authService;

    @Mock
    private RpcDeptPlusServiceAdapt rpcDeptPlusServiceAdapt;


    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Mock
    private BizRoleService bizRoleService;


    @Mock
    private RpcDeptPlusServiceAdaptV3 rpcDeptPlusServiceAdaptV3;

    @Mock
    private IdAdapt idAdapt;


    @Mock
    private RpcOrgServiceAdapt rpcOrgServiceAdapt;

    @Mock
    private RpcDeptPlusV3Service rpcDeptPlusV3Service;


    @Before
    public void setUp(){
        when(bizRoleService.checkIfIsAdminOrLegal(any(),any())).thenReturn(true);
        ReflectionTestUtils.setField(bizSpaceServiceV3,"spaceMacCount",10);
        ReflectionTestUtils.setField(bizSpaceServiceV3,"spaceWillSwitch",true);
        EasunOrganSpaceCountOutput countOutput = new EasunOrganSpaceCountOutput();
        countOutput.setCount(1);
        when(rpcOrganSpaceAdapt.organSpaceCount(any())).thenReturn(countOutput);
        DeptMemberCheckOutput deptMemberCheckOutput = new DeptMemberCheckOutput();
        Map<String, Boolean> map =new HashMap<>();
        map.put("adminOid1",true);
        deptMemberCheckOutput.setMemberCheckMap(map);
        when( rpcDeptPlusServiceAdaptV3.batchCheckMember(any(),any(),any())).thenReturn(deptMemberCheckOutput);
        DeptSubListResponse subListResponse =new DeptSubListResponse();
        List<DeptBaseResponse> deptBaseResponses = new ArrayList<>();
        DeptBaseResponse baseResponse = new DeptBaseResponse();
        baseResponse.setDeptId("deptId");
        deptBaseResponses.add(baseResponse);
        subListResponse.setDeptList(deptBaseResponses);
        when(rpcDeptPlusServiceAdapt.listByDeptIdsOrMemberIds(any(), any(), any(), any(),any())).thenReturn(subListResponse);
        when(rpcOrganSpaceAdapt.createSpace(any())).thenReturn("spaceId");
//        when(rpcOrganSpaceAdapt.updateSpace(any())).thenReturn("spaceId");
        when(authService.willAuthCommit(any())).thenReturn(RpcOutput.with(new BizSenderServiceOutput()));
        EasunSpaceOutput spaceDetail = new EasunSpaceOutput();
        spaceDetail.setSpaceId("spaceId");
        spaceDetail.setName("name");
        List<SpaceItemDetailOutput> items = new ArrayList<>();
        SpaceItemDetailOutput itemDetailOutput = new SpaceItemDetailOutput();
        itemDetailOutput.setRootOrgan(true);
        List<MemberDetail> members = new ArrayList<>();
        MemberDetail memberDetail = new MemberDetail();
        memberDetail.setMemberOid("adminOid1");
        memberDetail.setMemberGid("adminGid1");
        members.add(memberDetail);
        itemDetailOutput.setMembers(members);
        List<BizDeptDetailOutput> depts = new ArrayList<>();
        BizDeptDetailOutput deptDetailOutput = new BizDeptDetailOutput();
        deptDetailOutput.setDeptId("deptId");
        depts.add(deptDetailOutput);
        itemDetailOutput.setDepts(depts);
        items.add(itemDetailOutput);
        spaceDetail.setList(items);
       when(rpcOrganSpaceAdapt.getSpaceDetail(any())).thenReturn(spaceDetail);
        BatchSpaceOutput spaceOutput = new BatchSpaceOutput();
        List<EasunSpaceListItemOutput> list = new ArrayList<>();
        EasunSpaceListItemOutput itemOutput = new EasunSpaceListItemOutput();
        itemOutput.setAdmins(members);
        itemOutput.setDepts(depts);
        list.add(itemOutput);
        spaceOutput.setList(list);
        spaceOutput.setDeptList(Lists.newArrayList("deptId"));
       when( rpcOrganSpaceAdapt.getSpaceList(any())).thenReturn(spaceOutput);
       when(rpcOrganSpaceAdapt.getSpaceDetail(any())).thenReturn(spaceDetail);
        BizICUserOutput icUserOutput =  MockUntils.mockBizICUserOutput();
        icUserOutput.getId().setOuid("adminOid1");
        icUserOutput.getId().setGuid("adminGid1");
        List<BizICUserOutput> icUserOutputs = new ArrayList<>();
        icUserOutputs.add(icUserOutput);
       when(icUserPlusServiceAdapt.getICUsers(anyList())).thenReturn(icUserOutputs);
        when(rpcOrganSpaceAdapt.getSpaceListByOrgan(any())).thenReturn(spaceOutput);
        BizSpaceUserChangeDeptOutput bizSpaceUserChangeDeptOutput =new BizSpaceUserChangeDeptOutput();
        Map<String, MemberDeptCheckOutput> memberDeptCheckOutputMap = new HashMap<>();
        MemberDeptCheckOutput memberDeptCheckOutput = new MemberDeptCheckOutput();
        memberDeptCheckOutput.setUnanimous(true);
        memberDeptCheckOutput.setSpaceList(Lists.newArrayList(new SpaceBaseOutput()));
        memberDeptCheckOutputMap.put("adminOid1",memberDeptCheckOutput);
        bizSpaceUserChangeDeptOutput.setUserChangeDeptSpaceCheck(memberDeptCheckOutputMap);
        when(rpcOrganSpaceAdapt.memberSpaceDeptCheck(any())).thenReturn(bizSpaceUserChangeDeptOutput);
        SpaceDeptCheckOutput checkOutput = new SpaceDeptCheckOutput();
        checkOutput.setOnlySpaceDept(true);
        checkOutput.setDeptSpaceList(Lists.newArrayList(new SpaceBaseOutput()));
        checkOutput.setDeptMemberSpaceList(Lists.newArrayList(new SpaceBaseOutput()));
//        checkOutput.setDeptMemberAdmins(Lists.newArrayList("adminUid1"));
        EasunSpaceDeptCheckOutput easunSpaceDeptCheckOutput = new EasunSpaceDeptCheckOutput();
        easunSpaceDeptCheckOutput.setOnlySpaceDept(true);
        easunSpaceDeptCheckOutput.setDeptSpaceList(Lists.newArrayList(new EasunSpaceListItemOutput()));
        EasunSpaceListItemOutput itemOutput1 =new EasunSpaceListItemOutput();
        itemOutput1.setAdmins(Lists.newArrayList(new MemberDetail()));
        easunSpaceDeptCheckOutput.setDeptMemberSpaceList(Lists.newArrayList(itemOutput1));
//        easunSpaceDeptCheckOutput.setDeptMemberAdmins(Lists.newArrayList(new MemberDetail()));
        when(rpcOrganSpaceAdapt.spaceDeptCheck(any())).thenReturn(easunSpaceDeptCheckOutput);
        EasunSpaceOutput easunSpaceOutput = new EasunSpaceOutput();
        easunSpaceOutput.setName("name");
        easunSpaceOutput.setSpaceId("spaceID");
        when(rpcOrganSpaceAdapt.getSpaceByName(any())).thenReturn(easunSpaceOutput);
        when(rpcOrganSpaceAdapt.organRelationSpaceCount(any())).thenReturn(countOutput );
        List<BizSubOrganOutput> branchs = new ArrayList<>();
        BizSubOrganOutput subOrganOutput = new BizSubOrganOutput();
        subOrganOutput.setBranchOrgOid("branchOrganOid");
        subOrganOutput.setRootOrgOid("organOid");
        branchs.add(subOrganOutput);
        when(rpcOrgServiceAdapt.getAllSubOrgansByRootOid(anyString())).thenReturn(branchs);
        ReflectionTestUtils.setField(bizSpaceServiceV3,"rpcDeptPlusServiceAdaptV3",rpcDeptPlusServiceAdaptV3);
        List<BizSubOrganOutput> subOrgans = new ArrayList<>();
        subOrgans.add(MockUntils.mockBizSubOrganOutput());
        when(rpcOrgServiceAdapt.getAllSubOrgansByRootOid(anyString())).thenReturn(subOrgans);
        BizDeptDetailOutputV3 bizDeptDetailOutputV3 = MockUntils.getBizDeptDetailOutputV3();
        bizDeptDetailOutputV3.setOwnerOrgId("organOid");
        BizDeptListOutputV3 bizDeptListOutputV3 = new BizDeptListOutputV3();
        bizDeptListOutputV3.setDeptList(Lists.newArrayList(bizDeptDetailOutputV3));
        when(rpcDeptPlusV3Service.getBatchDeptDetails(any())).thenReturn(RpcOutput.with(bizDeptListOutputV3));

    }

    @Test
    public void createTest(){
        SpaceRequest spaceRequest = new SpaceRequest();
        spaceRequest.setIsMulti(true);
        List<SpaceDeptDeatilRequest> list = new ArrayList<>();
        SpaceDeptDeatilRequest item = new SpaceDeptDeatilRequest();
        item.setIsRoot(true);
        item.setAdmins(Lists.newArrayList("adminOid1"));
        item.setDeptIds(Lists.newArrayList("deptId"));
        item.setOrgId("organOid");
        list.add(item);
        spaceRequest.setList(list);
        bizSpaceServiceV3.createSpace("operator","creater",spaceRequest);
    }

    @Test(expected = MemberCheckDeptError.class)
    public void createExceptionTest(){
        when(rpcDeptPlusServiceAdaptV3.batchCheckMember(anyList(),anyList(),anyString())).thenThrow(new RuntimeException());
        SpaceRequest spaceRequest = new SpaceRequest();
        spaceRequest.setIsMulti(true);
        List<SpaceDeptDeatilRequest> list = new ArrayList<>();
        SpaceDeptDeatilRequest item = new SpaceDeptDeatilRequest();
        item.setIsRoot(true);
        item.setAdmins(Lists.newArrayList("adminOid1"));
        item.setDeptIds(Lists.newArrayList("deptId"));
        item.setOrgId("organOid");
        list.add(item);
        spaceRequest.setList(list);
        bizSpaceServiceV3.createSpace("operator","creater",spaceRequest);
    }


    @Test(expected = DeptOrAdminNotEmpty.class)
    public void createSpaceParamCheckTest(){
        SpaceRequest spaceRequest = new SpaceRequest();
        spaceRequest.setIsMulti(true);
        List<SpaceDeptDeatilRequest> list = new ArrayList<>();
        SpaceDeptDeatilRequest itemRoot = new SpaceDeptDeatilRequest();
        itemRoot.setIsRoot(true);
        itemRoot.setOrgId("orgId");
        list.add(itemRoot);
        spaceRequest.setList(list);
        bizSpaceServiceV3.createSpace("operator","creater",spaceRequest);
    }


    @Test(expected = DeptNotBelongOrg.class)
    public void createSpaceDeptAndMemberCheckTest(){
        SpaceRequest spaceRequest = new SpaceRequest();
        spaceRequest.setIsMulti(true);
        List<SpaceDeptDeatilRequest> list = new ArrayList<>();
        SpaceDeptDeatilRequest itemRoot = new SpaceDeptDeatilRequest();
        itemRoot.setIsRoot(true);
        itemRoot.setAdmins(Lists.newArrayList("adminOid1"));
        itemRoot.setDeptIds(Lists.newArrayList("deptId"));
        itemRoot.setOrgId("orgId");
        list.add(itemRoot);
        SpaceDeptDeatilRequest item = new SpaceDeptDeatilRequest();
        item.setIsRoot(false);
        item.setAdmins(Lists.newArrayList("adminOid1"));
        item.setDeptIds(Lists.newArrayList("deptId"));
        item.setOrgId("ddd");
        list.add(item);
        spaceRequest.setList(list);
        bizSpaceServiceV3.createSpace("operator","creater",spaceRequest);
    }


    @Test(expected = DeptNotBelongOrg.class)
    public void createSpaceDeptAndMemberCheck2Test(){
        SpaceRequest spaceRequest = new SpaceRequest();
        spaceRequest.setIsMulti(true);
        List<SpaceDeptDeatilRequest> list = new ArrayList<>();
        SpaceDeptDeatilRequest itemRoot = new SpaceDeptDeatilRequest();
        itemRoot.setIsRoot(true);
        itemRoot.setAdmins(Lists.newArrayList("adminOid1"));
        itemRoot.setDeptIds(Lists.newArrayList("deptId111"));
        itemRoot.setOrgId("orgId");
        list.add(itemRoot);
        spaceRequest.setList(list);
        bizSpaceServiceV3.createSpace("operator","creater",spaceRequest);
    }

    @Test
    public void spaceAdminTransferTest(){
        SpaceAdminTransferRequest transferRequest =new SpaceAdminTransferRequest();
        transferRequest.setNewAdmin("adminOid1");
        transferRequest.setOriAdmin("adminOid2");
        transferRequest.setSpaceId("spaceId");
        when(idAdapt.guid(anyString())).thenReturn("adminGid1");
        bizSpaceServiceV3.spaceAdminTransfer("operator",transferRequest);
    }

    @Test
    public void getSpaceListTest(){
        SpaceCommonRequest commonRequest = new SpaceCommonRequest();
        bizSpaceServiceV3.getSpaceList(commonRequest);
    }

    @Test
    public void deleteSpaceTest(){
        SpaceCommonRequest request = new SpaceCommonRequest();
        request.setOrgId("orgId");
        request.setMemberOid("memberOid");
        request.setSpaceId("spaceid");
        bizSpaceServiceV3.deleteSpace(request,"serviceId");
    }

    @Test
    public void getSpaceDeptailTest(){
        SpaceCommonRequest commonRequest = new SpaceCommonRequest();
        bizSpaceServiceV3.getSpaceDeptail(commonRequest);
    }

    @Test
    public void batchCheckMemberSpacesTest(){
        DeleteMemberBatchRequest request = new DeleteMemberBatchRequest();
        request.setDeleteAccountList(Lists.newArrayList("adminOid1"));
        bizSpaceServiceV3.batchCheckMemberSpaces("orgId",request);
    }

    @Test
    public void checkUserSpaceDeptTest(){
        SpaceDeptChekcRequest request = new SpaceDeptChekcRequest();
        bizSpaceServiceV3.checkUserSpaceDept(request);
    }

    @Test
    public void deptIsOnlyInSpaceTest(){
        SpaceDeptChekcRequest request = new SpaceDeptChekcRequest();
        bizSpaceServiceV3.deptIsOnlyInSpace(request);
    }


    @Test
    public void updateSpaceNameTest(){
        SpaceCommonRequest request = new SpaceCommonRequest();
        bizSpaceServiceV3.updateSpaceName(request,"name");
    }

    @Test
    public void spaceHasExistTest(){
        bizSpaceServiceV3.spaceHasExist("orgId","name","spaceId");
    }

    @Test
    public void organHasSpaceTest(){
        SpaceCommonRequest request = new SpaceCommonRequest();
        bizSpaceServiceV3.organHasSpace(request);
    }

    @Test
    public void organHasRelationSpaceTest(){
        SpaceCommonRequest request = new SpaceCommonRequest();

        bizSpaceServiceV3.organHasRelationSpace(request);
    }

    @Test
    public void organSpaceHasFullTest(){
        SpaceCommonRequest request = new SpaceCommonRequest();
        bizSpaceServiceV3.organSpaceHasFull(request);
    }

    @Test
    public void deptMemberCheckTest(){
        bizSpaceServiceV3.deptMemberCheck(new MemberCheckInput(),"organOid");
    }
}
