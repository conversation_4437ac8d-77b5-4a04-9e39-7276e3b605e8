package com.timevale.footstone.user.service.rest;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.service.api.RpcICUserService;
import com.timevale.easun.service.api.RpcIcRolePrivilegePlusService;
import com.timevale.easun.service.api.RpcIcUserPlusService;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.biz.*;
import com.timevale.footstone.user.service.inner.impl.biz.clientstrategy.ClientIdStrategy;
import com.timevale.footstone.user.service.inner.impl.biz.clientstrategy.ClientStrategyFactory;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoLoginConfigServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoThirdAuthServicePlusImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.FileSystemServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountIdcardServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAuthenticationServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.respdecoration.deco.walker.DecorationWalker;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.idcard.request.BindOrgThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.idcard.request.OrgThirdPartyBindNoticeRequest;
import com.timevale.footstone.user.service.model.account.idcard.request.UnBindOrgThirdPartyRequest;
import com.timevale.footstone.user.service.model.account.login.model.SsoLoginModel;
import com.timevale.footstone.user.service.model.account.mods.PersonPropCollection;
import com.timevale.footstone.user.service.model.account.mods.PersonProperties;
import com.timevale.footstone.user.service.model.account.mods.UserInfoModel;
import com.timevale.footstone.user.service.model.organization.request.OrganSortListRequest;
import com.timevale.footstone.user.service.model.organization.request.OrganSortRequest;
import com.timevale.footstone.user.service.model.organization.response.ListOrganAndRoleResponse;
import com.timevale.footstone.user.service.model.role.model.RoleModel;
import com.timevale.footstone.user.service.model.third.response.CanBindOrgThirdPartyResponse;
import com.timevale.footstone.user.service.model.third.response.GetAccountInfoByThirdIdIgnoreAppIdResponse;
import com.timevale.footstone.user.service.model.third.response.GetThirdPartyBindOrgSceneResponse;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.footstone.user.service.utils.RequestUtil;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.weaver.WeaverConstants;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.notificationmanager.service.api.optimize.SendService;
import com.timevale.notificationmanager.service.model.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/9/7 19:29
 */

@RunWith(MockitoJUnitRunner.Silent.class)
public class AccountRestTest {

    @InjectMocks
    private AccountRest accountRest;

    @Mock
    private BizSsoLoginConfigServiceImpl ssoLoginConfigService;

    @Mock
    private BizThirdpartyService bizThirdpartyService;

    @Mock
    private BizAccountService bizAccountService;

    @Mock
    private BizOrganService bizOrganService;

    @Mock
    private ClientStrategyFactory clientStrategyFactory;
    @Mock
    private  ClientIdStrategy defaultClientIdStrategyList;
    @Mock
    private BizSsoThirdAuthServicePlusImpl ssoThirdAuthServicePlus;


    @Mock
    private RpcAuthenticationServiceAdapt authenticationService;

    @Mock
    private RpcIcUserPlusService icUserPlusService;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Mock
    private RpcAccountIdcardServiceAdapt idcardSvc;

    @Mock
    private RpcICUserService icUserSvc;

    @Mock
    private HttpServletRequest servletRequest;

    @Mock
    private IdAdapt idAdapt;
    @Mock
    private BizAddressService addressService;
    @Mock
    private RpcIcRolePrivilegePlusService icRolePrivilegePlusService;

    @Mock
    private DecorationWalker walker;
    @Mock
    private SendService sendService;
    @Mock
    private FileSystemServiceAdapt fileSystemServiceAdapt;

    @Mock
    private CommonConfig config;

    @Mock
    private Tedis tedis;

    @Mock
    private BizPrivilegeService bizPrivilegeService;



    @Before
    public void setUp(){

        Map<String, Set<String>> ipIsolationOrgMap = new HashMap<>();
        ipIsolationOrgMap.put("orgId", Sets.newHashSet("*************","103.90.//d{1,3}.//d{1,3}"));
        when(config.getIpIsolationOrgMap()).thenReturn(ipIsolationOrgMap);
        ListOrganAndRoleResponse andRoleResponse = new ListOrganAndRoleResponse();
        UserInfoModel userInfoModel = new UserInfoModel();
        userInfoModel.setOrgId("orgId");
        userInfoModel.setTop(1);
        userInfoModel.setWeight(10);
        RoleModel roleModel = new RoleModel();
        roleModel.setRoleKey("ADMIN");
        userInfoModel.setRoleDetailList(Lists.newArrayList(roleModel));
        UserInfoModel userInfoModel2 = new UserInfoModel();
        userInfoModel2.setOrgId("orgId2");
        userInfoModel2.setTop(0);
        userInfoModel2.setWeight(1);
        userInfoModel2.setRoleDetailList(Lists.newArrayList(roleModel));
        andRoleResponse.setBizUserInfoOutputs(Lists.newArrayList(userInfoModel,userInfoModel2));
        when(clientStrategyFactory.getStrategy(any())).thenReturn(defaultClientIdStrategyList);
        when(defaultClientIdStrategyList.queryUserOrganList(any())).thenReturn(andRoleResponse);
        SsoLoginModel ssoLoginModel = new SsoLoginModel(true);
        when(ssoThirdAuthServicePlus.checkAndGetSsoSource()).thenReturn(ssoLoginModel);
        when(ssoThirdAuthServicePlus.getOrganListBySsoDomain(any(),any())).thenReturn(Lists.newArrayList(userInfoModel,userInfoModel2));
        StringOperations<String, Map<String,Integer>> stringOperations = new MyDefaultStringOperations();
        Map<String,Integer> organMap = new HashMap<>();
        organMap.put("footstone_user_info_export_count_accountId",0);
        when(tedis.string()).thenReturn(stringOperations);
        ReflectionTestUtils.setField(tedis,"valueOps",stringOperations);
        TedisUtil.setTedis(tedis);
        when(bizAccountService.getUserPropeties(any())).thenReturn(MockUntils.mockPersonPropCollection());
        when(fileSystemServiceAdapt.download(any(),anyBoolean())).thenReturn(new byte[]{111,89,1});
        when(addressService.list(any(),any(),any())).thenReturn(MockUntils.mockAddressListResponse());
        when(sendService.send(any())).thenReturn(Result.success(MockUntils.mockSendMsgResponse()));
        when(bizPrivilegeService.checkUserPrivilege(any(),any(),any(),any())).thenReturn(true);
        ReflectionTestUtils.setField(accountRest,"ssoLoginConfigService",ssoLoginConfigService);
    }

    @Test
    public void listSaasOrgAndRoleTest(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute("X-Tsign-Open-App-Id","7488");
        servletRequest.setAttribute("X-Tsign-Open-Authorizer-Token","auth_token");
        servletRequest.setAttribute("X-Tsign-Webserver-Request-Ip","*************");
        servletRequest.setAttribute("X-Real-IP","*************");
        ReflectionTestUtils.setField(accountRest,"servletRequest",servletRequest);
        accountRest.listSaasOrgAndRole("accountId", "ADMIN", null, null);
        servletRequest.setAttribute("X-Tsign-Webserver-Request-Ip", "***********");
        accountRest.listSaasOrgAndRole("accountId", "ADMIN",  null,null);
        servletRequest.setAttribute("X-Tsign-Webserver-Request-Ip", "************");
        accountRest.listSaasOrgAndRole("accountId", "ADMIN",  null,null);
    }

    @Test
    public void organSortTest(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"accountId");
        OrganSortListRequest request = new OrganSortListRequest();
        OrganSortRequest sortRequest = new OrganSortRequest();
        sortRequest.setOrgId("orgId");
        sortRequest.setTop(1);
        OrganSortRequest sortRequest2 = new OrganSortRequest();
        sortRequest2.setOrgId("orgId2");
        sortRequest2.setTop(1);
        request.setOrgsList(Lists.newArrayList(sortRequest,sortRequest2));
        accountRest.organSort(request);
    }

    @Test
    public void exportUserInfoTest(){
        accountRest.exportUserInfo(UUID.randomUUID().toString().replace("-",""),"<EMAIL>");
    }

    @Test
    public void getBindSceneTest(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(RequestUtil.CLIENT_ID,"FEI_SHU");
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"operatorId");
        RequestContext.put(WeaverConstants.REQUEST_KEY, servletRequest);
        when(bizThirdpartyService.getThirdPartyBindOrgScene(any(),any(),any(),any())).thenReturn(new GetThirdPartyBindOrgSceneResponse());
        accountRest.getBindScene("TK");
    }

    @Test
    public void getThirdPartyTenantInfoTest(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(RequestUtil.CLIENT_ID,"FEI_SHU");
        RequestContext.put(WeaverConstants.REQUEST_KEY, servletRequest);
        when(bizThirdpartyService.getAccountInfoByThirdIdIgnoreAppId(any(),any())).thenReturn(new GetAccountInfoByThirdIdIgnoreAppIdResponse());
        accountRest.getThirdPartyTenantInfo("TK");
    }

    @Test
    public void getBindSceneTest1(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"TENANT_ID");
        RequestContext.put(WeaverConstants.REQUEST_KEY, servletRequest);
        accountRest.getBindScene();
    }

    @Test
    public void orgThirdPartyBindTest(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(RequestUtil.CLIENT_ID,"FEI_SHU");
        RequestContext.put(WeaverConstants.REQUEST_KEY, servletRequest);
        BindOrgThirdPartyRequest request = new BindOrgThirdPartyRequest();
        request.setOrganOuid("organOuid");
        request.setTenantKey("TK");
        accountRest.orgThirdPartyBind(request);
    }

    @Test
    public void orgThirdPartyUnBindTest(){
        UnBindOrgThirdPartyRequest request = new UnBindOrgThirdPartyRequest();
        request.setUnbindClient("FEI_SHU");
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,"TENANT_ID");
        RequestContext.put(WeaverConstants.REQUEST_KEY, servletRequest);
        accountRest.orgThirdPartyUnBind(request);
    }

    @Test
    public void orgThirdPartyBindNoticeTest(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(RequestUtil.CLIENT_ID,"FEI_SHU");
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"operatorId");
        RequestContext.put(WeaverConstants.REQUEST_KEY, servletRequest);
        OrgThirdPartyBindNoticeRequest request = new OrgThirdPartyBindNoticeRequest();
        request.setTenantKey("TK");
        request.setBindOrganOuid("organOuid");
        accountRest.orgThirdPartyBindNotice(request);
    }

    @Test
    public void canBindOrgThirdParty() {
        accountRest.canBindOrgThirdParty("企业A", "tenantKey1111");
    }

    @Test
    public void queryCanBindThirdIdOrgList() {
        accountRest.queryCanBindThirdIdOrgList();
    }

    @Test
    public void getTenantAndUserInfo() {
        accountRest.getTenantAndUserInfo("tenantKey1111");
    }


}
