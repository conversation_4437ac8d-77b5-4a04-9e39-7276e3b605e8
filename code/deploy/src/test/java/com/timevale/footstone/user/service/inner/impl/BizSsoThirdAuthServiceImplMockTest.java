package com.timevale.footstone.user.service.inner.impl;

import com.timevale.account.service.api.RpcAuthenticationService;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.auth.output.AuthOutput;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.model.account.output.BizTokenOutput;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.easun.service.model.organization.output.v3.BizOrgBaseInfoOutputV3;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAuthenticationServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoLoginConfigAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoLoginConfigServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoThirdAuthServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoAuthServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoDomainServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoOrganizationThirdRefServiceAdapt;
import com.timevale.footstone.user.service.inner.model.SsoAccessCodeContextModel;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.model.sso.SsoAccountAuthorizeAccountCheckRequest;
import com.timevale.footstone.user.service.model.sso.SsoAccountBindOrgan;
import com.timevale.footstone.user.service.model.sso.SsoOrganInfo;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.token.service.api.RpcSsoLoginConfigs;
import com.timevale.token.service.enums.SsoLoginConfigEnum;
import com.timevale.token.service.model.SsoOrganThirdRefModel;
import com.timevale.token.service.model.output.BizGetSsoDomainInfoOutput;
import com.timevale.token.service.model.output.SsoLoginConfigSingleOutput;
import java.sql.Ref;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

/**
 * Author: liujiaxing
 * Date: 2023/8/16 5:34 下午
 * Description:
 */
@RunWith(MockitoJUnitRunner.Silent.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizSsoThirdAuthServiceImplMockTest {

    @InjectMocks
    BizSsoThirdAuthServiceImpl bizSsoThirdAuthService;

    @Mock
    RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

    @Mock
    private IdAdapt idAdapt;

    @Mock
    private RpcSsoDomainServiceAdapt domainServiceAdapt;

    @Mock
    private RpcSsoOrganizationThirdRefServiceAdapt thirdRefServiceAdapt;

    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Mock
    RpcSaaSVipServiceAdapt rpcSaaSVipServiceAdapt;

    @Mock
    RpcSsoAuthServiceAdapt authServiceAdapt;


    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Mock
    private RpcSsoLoginConfigAdapt rpcSsoLoginConfigAdapt;

    @Mock
    private SystemProperty systemProperty;

    @Mock
    private RpcAuthenticationServiceAdapt rpcAuthenticationServiceAdapt;


    @Mock
    private RpcSsoLoginConfigs rpcSsoLoginConfigs;

    @Mock
    private RpcAuthenticationService rpcAuthenticationService;


    @Mock
    private BizSsoLoginConfigServiceImpl ssoLoginConfigService;

    @Before
    public void setUp(){
        when(systemProperty.getStdAppId()).thenReturn("7488");
        AuthOutput authOutput = new AuthOutput();
        authOutput.setOuid("ouid");
        //新版本 mock 重载方法好像有点问题，用doReturn的形式
        doReturn(authOutput).when(rpcAuthenticationServiceAdapt).checkThirdpartyWithProjectId(
                ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any());
        doReturn(authOutput).when(rpcAuthenticationServiceAdapt).checkThirdpartyWithProjectId(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any());
        SsoLoginConfigSingleOutput singleOutput = new SsoLoginConfigSingleOutput();
        singleOutput.setConfigValue("EMAIL");
        singleOutput.setConfigType(SsoLoginConfigEnum.ONLY_SSO_LOGIN_PERMIT.getConfigType());
        doReturn(singleOutput).when(rpcSsoLoginConfigAdapt).getSingleConfig(ArgumentMatchers.any(),ArgumentMatchers.any());
        BizICUserOutput bizICUserOutput = MockUntils.mockBizICUserOutput();
        bizICUserOutput.getIdcards().getEmail().getDetail().setValue(null);
        when(icUserPlusServiceAdapt.getICUserByOuid(anyString())).thenReturn(bizICUserOutput);
        doReturn(RpcOutput.with(authOutput)).when(rpcAuthenticationService).checkThirdparty(ArgumentMatchers.any());
        when(icOrgPlusServiceAdapt.checkMemberExistInRelaOrgs(anyString(),anyList())).thenReturn(true);
}


    @Test
    public void checkAndJoinOrgan() {
        try {
            //Mockito.doNothing().when(icOrgPlusServiceAdapt).createMember(any(),any(),any());

            Mockito.when(idAdapt.trueMemberOid(any(),any())).thenReturn("test_oid");

            String oid = "test_oid";
            SsoAccountBindOrgan ssoAccountBindOrgan = new SsoAccountBindOrgan();
            ssoAccountBindOrgan.setOrganOidList(Lists.newArrayList("test_organOid1"));
            ssoAccountBindOrgan.setSsoProvider("dingding");

            bizSsoThirdAuthService.checkAndJoinOrgan(oid,ssoAccountBindOrgan);
        }catch (Exception e ){

        }

    }

    @Test
    public void checkAndJoinOrgan_2() {
        try {
            //Mockito.doNothing().when(icOrgPlusServiceAdapt).createMember(any(),any(),any());

            Mockito.when(idAdapt.trueMemberOid(any(),any())).thenReturn(null);

            String oid = "test_oid";
            SsoAccountBindOrgan ssoAccountBindOrgan = new SsoAccountBindOrgan();
            ssoAccountBindOrgan.setOrganOidList(Lists.newArrayList("test_organOid1"));
            ssoAccountBindOrgan.setSsoProvider("dingding");

            bizSsoThirdAuthService.checkAndJoinOrgan(oid,ssoAccountBindOrgan);
        }catch (Exception e ){

        }

    }

    @Test
    public void querySsoOrganInfos() {

        try {

            String orgOuid ="test_organOid1";
            BizGetSsoDomainInfoOutput bizGetSsoDomainInfoOutput = new BizGetSsoDomainInfoOutput();
            bizGetSsoDomainInfoOutput.setOrganOuid(orgOuid);
            bizGetSsoDomainInfoOutput.setProvider("");
            bizGetSsoDomainInfoOutput.setProviderTitle("");
            bizGetSsoDomainInfoOutput.setDomain("");
            bizGetSsoDomainInfoOutput.setCode("");
            bizGetSsoDomainInfoOutput.setAuthType(0);
            bizGetSsoDomainInfoOutput.setAuthKey("");
            bizGetSsoDomainInfoOutput.setAuthUrl("");
            bizGetSsoDomainInfoOutput.setResourcesSpace(0);
            bizGetSsoDomainInfoOutput.setTargetUrl("");
            bizGetSsoDomainInfoOutput.setLogoutUrl("");

            Mockito.when(domainServiceAdapt.getSsoDomainInfoByProvider(any())).thenReturn(bizGetSsoDomainInfoOutput);

            List<SsoOrganThirdRefModel> ssoOrganThirdRefModels = new ArrayList<>();
            SsoOrganThirdRefModel ssoOrganThirdRefModel = new SsoOrganThirdRefModel();
            ssoOrganThirdRefModel.setId(0L);
            ssoOrganThirdRefModel.setOrganOuid(orgOuid);
            ssoOrganThirdRefModel.setThirdOrganId("");
            ssoOrganThirdRefModel.setThirdOrganName("");
            ssoOrganThirdRefModel.setThirdExtend("");
            ssoOrganThirdRefModel.setSource("");
            ssoOrganThirdRefModel.setCreateTime(new Date());
            ssoOrganThirdRefModel.setModifyTime(new Date());
            ssoOrganThirdRefModels.add(ssoOrganThirdRefModel);


            //Mockito.when(thirdRefServiceAdapt.getOrganThirdRefBySource(any())).thenReturn(ssoOrganThirdRefModels);

            PagerResult<EsOrgSearhOutput> result = new PagerResult<>();

            EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
            EasunEsearchAccountOutput easunEsearchAccountOutput = new EasunEsearchAccountOutput();
            EsId esId = new EsId();
            esId.setOuid(orgOuid);
            esId.setGuid("test_guid");
            easunEsearchAccountOutput.setId(esId);
            List<Property> properties = Lists.newArrayList(new Property(BuiltinProperty.INFO_NAME,"test_orgName",null));
            easunEsearchAccountOutput.setProperties(properties);

            esOrgSearhOutput.setUser(easunEsearchAccountOutput);
            esOrgSearhOutput.setCreater("");
            esOrgSearhOutput.setOrganAccountUid("");
            esOrgSearhOutput.setOrganGuid("");
            esOrgSearhOutput.setCreaterOid("");
            esOrgSearhOutput.setId("");
            esOrgSearhOutput.setDeleted(false);
            esOrgSearhOutput.setActivate(0);


            result.setTotal(1);
            result.setItems(Lists.newArrayList(esOrgSearhOutput));

            Mockito.when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(result);

            //Mockito.when(thirdRefServiceAdapt.getOrganThirdRefList(any())).thenReturn(ssoOrganThirdRefModels);


            String provider ="dingding";
            List<SsoOrganInfo> ssoOrganInfoList = bizSsoThirdAuthService.querySsoOrganInfos(provider);
        }catch (Exception e){
            e.printStackTrace();
        }


    }

    @Test
    public void queryAccountJoinOrganFlag() {
        String provider ="dingding";

        //Mockito.when(idAdapt.trueMemberOid(any(),any())).thenReturn("test_oid");
        Boolean joinOrganFlag = bizSsoThirdAuthService.queryAccountJoinOrganFlag("test_oid",provider,"user");
    }


    @Test
    public void getAccountRefOrganThirdInfo() {

        List<BizOrgBaseInfoOutputV3> bizOrgBaseInfoOutputV3List = new ArrayList<>();
        BizOrgBaseInfoOutputV3 bizOrgBaseInfoOutputV3 = new BizOrgBaseInfoOutputV3();
        bizOrgBaseInfoOutputV3.setOuid("test_oid");
        bizOrgBaseInfoOutputV3.setGuid("test_gid");
        bizOrgBaseInfoOutputV3.setAccountUid("test_a_id");
        bizOrgBaseInfoOutputV3.setOrgName("test_orgName");
        bizOrgBaseInfoOutputV3List.add(bizOrgBaseInfoOutputV3);


        //Mockito.when(icOrgPlusServiceAdapt.getAllSubOrg(any())).thenReturn(bizOrgBaseInfoOutputV3List);


        List<SsoOrganThirdRefModel> organThirdRefModelList = new ArrayList<>();

        SsoOrganThirdRefModel ssoOrganThirdRefModel = new SsoOrganThirdRefModel();
        ssoOrganThirdRefModel.setId(0L);
        ssoOrganThirdRefModel.setOrganOuid("test_oid");
        ssoOrganThirdRefModel.setThirdOrganId("test_t_oid");
        ssoOrganThirdRefModel.setThirdOrganName("test");
        ssoOrganThirdRefModel.setSource("dingding");
        ssoOrganThirdRefModel.setCreateTime(new Date());
        ssoOrganThirdRefModel.setModifyTime(new Date());
        ssoOrganThirdRefModel.setEnabled(1);

        organThirdRefModelList.add(ssoOrganThirdRefModel);

        Mockito.when(thirdRefServiceAdapt.getOrganThirdRefList(any())).thenReturn(organThirdRefModelList);

        Mockito.when(rpcSaaSVipServiceAdapt.getAllRelaOrgIdsByParent(any(),any())).thenReturn(Lists.newArrayList("test_oid","test_oid2"));


        PagerResult<EsOrgSearhOutput> result = new PagerResult<>();

        EsOrgSearhOutput esOrgSearhOutput = new EsOrgSearhOutput();
        EasunEsearchAccountOutput easunEsearchAccountOutput = new EasunEsearchAccountOutput();
        EsId esId = new EsId();
        esId.setOuid("test_oid");
        esId.setGuid("test_guid");
        easunEsearchAccountOutput.setId(esId);
        List<Property> properties = Lists.newArrayList(new Property(BuiltinProperty.INFO_NAME,"test_orgName",null));
        easunEsearchAccountOutput.setProperties(properties);

        esOrgSearhOutput.setUser(easunEsearchAccountOutput);
        esOrgSearhOutput.setCreater("");
        esOrgSearhOutput.setOrganAccountUid("");
        esOrgSearhOutput.setOrganGuid("");
        esOrgSearhOutput.setCreaterOid("");
        esOrgSearhOutput.setId("");
        esOrgSearhOutput.setDeleted(false);
        esOrgSearhOutput.setActivate(0);


        result.setTotal(1);
        result.setItems(Lists.newArrayList(esOrgSearhOutput));
        Mockito.when(esAccountServiceAdapt.getOrgsByEs(any())).thenReturn(result);

        bizSsoThirdAuthService.getAccountRefOrganThirdInfo("test_oid","dingding");
    }



    @Test
    public void testBulidSsoAccountAuthorize() {
        //  SsoAccountAuthorizeAccountCheckRequest request, AuthOutput authOutput, SsoAccessCodeContextModel
        SsoAccountAuthorizeAccountCheckRequest request = new SsoAccountAuthorizeAccountCheckRequest();
        request.setEndpoint("ESIGN_APP");
        AuthOutput authOutput = new AuthOutput();
        authOutput.setAccountUid("11");
        authOutput.setUuid("11");
        authOutput.setGuid("1111");
        authOutput.setOuid("223");
        SsoAccessCodeContextModel model = new SsoAccessCodeContextModel();
        model.setThirdUserMobile("**********");
        model.setThirdUserEmail("<EMAIL>");

        BizTokenOutput bizTokenOutput = new BizTokenOutput();
        bizTokenOutput.setToken("111");
        when(authServiceAdapt.loginById(any(),any())).thenReturn(bizTokenOutput);
        bizSsoThirdAuthService.bulidSsoAccountAuthorize(request, authOutput, model);
    }



    @Test
    public void accountAuthorizeTest() {
        SsoAccessCodeContextModel accessCodeContext = new SsoAccessCodeContextModel();
        SsoAccountAuthorizeAccountCheckRequest request = new SsoAccountAuthorizeAccountCheckRequest();
        bizSsoThirdAuthService.accountAuthorize(accessCodeContext, request);
    }
}
