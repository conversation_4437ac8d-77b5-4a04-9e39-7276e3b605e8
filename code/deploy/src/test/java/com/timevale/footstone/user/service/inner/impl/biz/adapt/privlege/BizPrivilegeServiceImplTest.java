package com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege;

import com.google.common.collect.Lists;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.easun.service.model.privilege.mods.PrivilegResourceModel;
import com.timevale.easun.service.model.privilege.mods.ResourceGroupModel;
import com.timevale.footstone.user.model.base.OrgBaseModel;
import com.timevale.footstone.user.service.configuration.PrivilegeConfig;
import com.timevale.footstone.user.service.inner.impl.RoleMsgServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.BizPrivilegeServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.model.Privilege.mods.UpdatePrivilegeModel;
import com.timevale.footstone.user.service.model.Privilege.request.UpdatePrivilegesByRoleRequest;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/6/7 21:22
 */

@RunWith(MockitoJUnitRunner.Silent.class)
public class BizPrivilegeServiceImplTest {

    @InjectMocks
    private BizPrivilegeServiceImpl bizPrivilegeService;


    @Mock
    private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;

    @Mock
    private RpcPrivilegeServiceAdapt rpcPrivilegeServiceAdapt;

    @Mock
    private IdAdapt idAdapt;

    /**资源/权限不能修改及删除，在前端隐藏， 需要隐藏的资源及权限配置中心拉取*/
    @Value("${display.none.classKey}")
    private String hiddenClassKeys;


    /**资源/权限不能修改及删除，在前端隐藏， 需要隐藏的资源及权限配置中心拉取*/
    @Mock
    private PrivilegeConfig privilegeConfig;

    @Mock
    private RoleMsgServiceImpl roleMsgService;


    @Before
    public void setUp(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute("X-Tsign-Open-App-Id","1111563841");
        servletRequest.setAttribute("X-Tsign-operator","d6bb1aa228034ac29a467eb0bc11d0aa");
        servletRequest.setAttribute("X-Tsign-Open-Operator-Id","operatorId");
        RequestContext.put("httpServletRequest",servletRequest);
        when(rolePrivilegePlusServiceAdapt.getPrivileges()).thenReturn(MockUntils.getBizEasunGetBuiltinPrivilegesOutput());
        when(     rpcPrivilegeServiceAdapt.getGroups()).thenReturn(MockUntils.getGroups());
        when(rpcPrivilegeServiceAdapt.getResource()).thenReturn(MockUntils.getResources());
        when(idAdapt.orgIdToOrganId(anyString())).thenReturn("organId");
        OrgBaseModel baseModel = new OrgBaseModel();
        baseModel.setOrganId("organId");
        when(idAdapt.getIds(anyString())).thenReturn(baseModel);
    }


    @Test
    public void getAllGroupPrivilegesTest(){

        bizPrivilegeService.getAllGroupPrivileges();

    }


    @Test
    public void updatePrivilegesByRoleTest(){
        UpdatePrivilegesByRoleRequest request = new UpdatePrivilegesByRoleRequest();
        UpdatePrivilegeModel privilegeModel = new UpdatePrivilegeModel();
        privilegeModel.setOperationPermit("QUERY");
        privilegeModel.setTargetClassKey("MEMBER");
        privilegeModel.setOperationPermitName("成员管理");
        request.setGrantPrivilegeList(Lists.newArrayList(privilegeModel));
        request.setRevokePrivilegeList(Lists.newArrayList(privilegeModel));
        bizPrivilegeService.updatePrivilegesByRole("roleId","orgId",request);
    }







}
