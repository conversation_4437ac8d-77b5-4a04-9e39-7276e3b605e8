package com.timevale.footstone.user.service.rest;

import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.inner.biz.BizThirdPlatformIntegrationService;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.thirdplatform.request.*;
import com.timevale.footstone.user.service.model.thirdplatform.response.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ThirdPlatformIntegrationRestTest {

    @Mock
    private BizThirdPlatformIntegrationService mockBizThirdPlatformIntegrationService;

    @InjectMocks
    private ThirdPlatformIntegrationRest thirdPlatformIntegrationRestUnderTest;

    @Test
    public void testValidateAppConfig() {
        // Setup
        final ThirdPlatformIntegrationBaseReq request = new ThirdPlatformIntegrationBaseReq();
        request.setOuid("ouid");
        request.setClientId("clientId");

        when(mockBizThirdPlatformIntegrationService.validateAppConfig("clientId", "ouid")).thenReturn(false);

        // Run the test
        final WrapperResponse<Boolean> result = thirdPlatformIntegrationRestUnderTest.validateAppConfig(request);

        // Verify the results
    }

    @Test
    public void testGetTenantList() {
        // Setup
        // Configure BizThirdPlatformIntegrationService.getTenantList(...).
        final TenantInfoResponse tenantInfoResponse = new TenantInfoResponse();
        tenantInfoResponse.setTenantKey("tenantKey");
        tenantInfoResponse.setTenantName("tenantName");
        final List<TenantInfoResponse> tenantInfoResponses = Arrays.asList(tenantInfoResponse);
        when(mockBizThirdPlatformIntegrationService.getTenantList("clientId", "ouid", "scene"))
                .thenReturn(tenantInfoResponses);

        // Run the test
        final WrapperResponse<List<TenantInfoResponse>> result = thirdPlatformIntegrationRestUnderTest.getTenantList(
                "clientId", "scene");

        // Verify the results
    }

    @Test
    public void testGetTenantList_BizThirdPlatformIntegrationServiceReturnsNoItems() {
        // Setup
        when(mockBizThirdPlatformIntegrationService.getTenantList("clientId", "ouid", "scene"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WrapperResponse<List<TenantInfoResponse>> result = thirdPlatformIntegrationRestUnderTest.getTenantList(
                "clientId", "scene");

        // Verify the results
    }

    @Test
    public void testPageQueryAppConfig() {
        // Setup
        // Configure BizThirdPlatformIntegrationService.pageQueryAppConfig(...).
        final ThirdPlatformAppConfigResponse thirdPlatformAppConfigResponse = new ThirdPlatformAppConfigResponse();
        thirdPlatformAppConfigResponse.setId(0L);
        thirdPlatformAppConfigResponse.setTenantKey("tenantKey");
        thirdPlatformAppConfigResponse.setTenantName("tenantName");
        thirdPlatformAppConfigResponse.setUserName("userName");
        thirdPlatformAppConfigResponse.setNotifyUrl("notifyUrl");
        final PagerResult<ThirdPlatformAppConfigResponse> thirdPlatformAppConfigResponsePagerResult = new PagerResult<>(
                Arrays.asList(thirdPlatformAppConfigResponse), 0);
        when(mockBizThirdPlatformIntegrationService.pageQueryAppConfig("ouid", "clientId", 0, 0))
                .thenReturn(thirdPlatformAppConfigResponsePagerResult);

        // Run the test
        final WrapperResponse<PagerResult<ThirdPlatformAppConfigResponse>> result = thirdPlatformIntegrationRestUnderTest.pageQueryAppConfig(
                "ouid", "clientId", 0, 0);

        // Verify the results
    }

    @Test
    public void testAddAppConfig() {
        // Setup
        final AppConfigAddRequest request = new AppConfigAddRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setUserName("userName");
        request.setNotifyUrl("notifyUrl");
        request.setTenantAppId("tenantAppId");

        // Configure BizThirdPlatformIntegrationService.addAppConfig(...).
        final AppConfigAddRequest request1 = new AppConfigAddRequest();
        request1.setClientId("clientId");
        request1.setTenantKey("tenantKey");
        request1.setUserName("userName");
        request1.setNotifyUrl("notifyUrl");
        request1.setTenantAppId("tenantAppId");
        when(mockBizThirdPlatformIntegrationService.addAppConfig(request1, "tenantOuid", "memberOid"))
                .thenReturn(false);

        // Run the test
        final WrapperResponse<Boolean> result = thirdPlatformIntegrationRestUnderTest.addAppConfig(request);

        // Verify the results
    }

    @Test
    public void testDelAppConfig() {
        // Setup
        final AppConfigDelRequest request = new AppConfigDelRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setAppConfigId(0L);

        // Configure BizThirdPlatformIntegrationService.delAppConfig(...).
        final AppConfigDelRequest request1 = new AppConfigDelRequest();
        request1.setClientId("clientId");
        request1.setTenantKey("tenantKey");
        request1.setAppConfigId(0L);
        when(mockBizThirdPlatformIntegrationService.delAppConfig(request1, "tenantOuid", "memberOid"))
                .thenReturn(false);

        // Run the test
        final WrapperResponse<Boolean> result = thirdPlatformIntegrationRestUnderTest.delAppConfig(request);

        // Verify the results
    }

    @Test
    public void testUpdateAppConfig() {
        // Setup
        final AppConfigAddRequest request = new AppConfigAddRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setUserName("userName");
        request.setNotifyUrl("notifyUrl");
        request.setTenantAppId("tenantAppId");

        // Configure BizThirdPlatformIntegrationService.updateAppConfig(...).
        final AppConfigAddRequest request1 = new AppConfigAddRequest();
        request1.setClientId("clientId");
        request1.setTenantKey("tenantKey");
        request1.setUserName("userName");
        request1.setNotifyUrl("notifyUrl");
        request1.setTenantAppId("tenantAppId");
        when(mockBizThirdPlatformIntegrationService.updateAppConfig(request1, "tenantOuid", "memberOid"))
                .thenReturn(false);

        // Run the test
        final WrapperResponse<Boolean> result = thirdPlatformIntegrationRestUnderTest.updateAppConfig(request);

        // Verify the results
    }

    @Test
    public void testGetTenantOrgList() {
        // Setup
        // Configure BizThirdPlatformIntegrationService.getTenantOrgList(...).
        final ThirdOrgBaseResponse thirdOrgBaseResponse = new ThirdOrgBaseResponse();
        thirdOrgBaseResponse.setOrgNo("orgNo");
        thirdOrgBaseResponse.setOrgName("orgName");
        final List<ThirdOrgBaseResponse> thirdOrgBaseResponses = Arrays.asList(thirdOrgBaseResponse);
        when(mockBizThirdPlatformIntegrationService.getTenantOrgList("clientId", "tenantKey"))
                .thenReturn(thirdOrgBaseResponses);

        // Run the test
        final WrapperResponse<List<ThirdOrgBaseResponse>> result = thirdPlatformIntegrationRestUnderTest.getTenantOrgList(
                "tenantKey", "clientId");

        // Verify the results
    }

    @Test
    public void testGetTenantOrgList_BizThirdPlatformIntegrationServiceReturnsNoItems() {
        // Setup
        when(mockBizThirdPlatformIntegrationService.getTenantOrgList("clientId", "tenantKey"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WrapperResponse<List<ThirdOrgBaseResponse>> result = thirdPlatformIntegrationRestUnderTest.getTenantOrgList(
                "tenantKey", "clientId");

        // Verify the results
    }

    @Test
    public void testPageQueryRelatedEnterprises() {
        // Setup
        // Configure BizThirdPlatformIntegrationService.pageQueryRelateEnterprises(...).
        final ThirdRelateEnterprisesResponse thirdRelateEnterprisesResponse = new ThirdRelateEnterprisesResponse();
        thirdRelateEnterprisesResponse.setTenantKey("tenantKey");
        thirdRelateEnterprisesResponse.setTenantName("tenantName");
        thirdRelateEnterprisesResponse.setOuid("ouid");
        thirdRelateEnterprisesResponse.setName("name");
        thirdRelateEnterprisesResponse.setCreator("creator");
        final PagerResult<ThirdRelateEnterprisesResponse> thirdRelateEnterprisesResponsePagerResult = new PagerResult<>(
                Arrays.asList(thirdRelateEnterprisesResponse), 0);
        when(mockBizThirdPlatformIntegrationService.pageQueryRelateEnterprises("clientId", "tenantKey", "orgOuid", 0,
                0)).thenReturn(thirdRelateEnterprisesResponsePagerResult);

        // Run the test
        final WrapperResponse<PagerResult<ThirdRelateEnterprisesResponse>> result = thirdPlatformIntegrationRestUnderTest.pageQueryRelatedEnterprises(
                "tenantKey", "clientId", 0, 0);

        // Verify the results
    }

    @Test
    public void testAddRelatedEnterprises() {
        // Setup
        final RelatedEnterprisesAddRequest request = new RelatedEnterprisesAddRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setOuid("ouid");
        final RelatedOrgRequest relatedOrgRequest = new RelatedOrgRequest();
        relatedOrgRequest.setOrgNo("orgNo");
        request.setRelatedOrganList(Arrays.asList(relatedOrgRequest));

        // Configure BizThirdPlatformIntegrationService.addRelatedEnterprises(...).
        final RelatedEnterprisesAddRequest request1 = new RelatedEnterprisesAddRequest();
        request1.setClientId("clientId");
        request1.setTenantKey("tenantKey");
        request1.setOuid("ouid");
        final RelatedOrgRequest relatedOrgRequest1 = new RelatedOrgRequest();
        relatedOrgRequest1.setOrgNo("orgNo");
        request1.setRelatedOrganList(Arrays.asList(relatedOrgRequest1));
        when(mockBizThirdPlatformIntegrationService.addRelatedEnterprises(request1, "operatorId")).thenReturn(false);

        // Run the test
        final WrapperResponse<Boolean> result = thirdPlatformIntegrationRestUnderTest.addRelatedEnterprises(request);

        // Verify the results
    }

    @Test
    public void testUpdatedRelatedEnterprises() {
        // Setup
        final RelatedEnterprisesAddRequest request = new RelatedEnterprisesAddRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setOuid("ouid");
        final RelatedOrgRequest relatedOrgRequest = new RelatedOrgRequest();
        relatedOrgRequest.setOrgNo("orgNo");
        request.setRelatedOrganList(Arrays.asList(relatedOrgRequest));

        // Configure BizThirdPlatformIntegrationService.updatedRelatedEnterprises(...).
        final RelatedEnterprisesAddRequest request1 = new RelatedEnterprisesAddRequest();
        request1.setClientId("clientId");
        request1.setTenantKey("tenantKey");
        request1.setOuid("ouid");
        final RelatedOrgRequest relatedOrgRequest1 = new RelatedOrgRequest();
        relatedOrgRequest1.setOrgNo("orgNo");
        request1.setRelatedOrganList(Arrays.asList(relatedOrgRequest1));
        when(mockBizThirdPlatformIntegrationService.updatedRelatedEnterprises(request1, "operatorId"))
                .thenReturn(false);

        // Run the test
        final WrapperResponse<Boolean> result = thirdPlatformIntegrationRestUnderTest.updatedRelatedEnterprises(
                request);

        // Verify the results
    }

    @Test
    public void testDelRelatedEnterprises() {
        // Setup
        final RelatedEnterprisesBaseRequest request = new RelatedEnterprisesBaseRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setOuid("ouid");

        when(mockBizThirdPlatformIntegrationService.delRelatedEnterprises("clientId", "tenantKey", "ouid",
                "operatorId")).thenReturn(false);

        // Run the test
        final WrapperResponse<Boolean> result = thirdPlatformIntegrationRestUnderTest.delRelatedEnterprises(request);

        // Verify the results
    }

    @Test
    public void testViewRelatedOrg() {
        // Setup
        // Configure BizThirdPlatformIntegrationService.viewRelatedOrg(...).
        final RelatedOrganResponse relatedOrganResponse = new RelatedOrganResponse();
        relatedOrganResponse.setId(0L);
        relatedOrganResponse.setRelatedId(0L);
        final List<RelatedOrganResponse> relatedOrganResponses = Arrays.asList(relatedOrganResponse);
        when(mockBizThirdPlatformIntegrationService.viewRelatedOrg("clientId", "tenantKey", "ouid"))
                .thenReturn(relatedOrganResponses);

        // Run the test
        final WrapperResponse<List<RelatedOrganResponse>> result = thirdPlatformIntegrationRestUnderTest.viewRelatedOrg(
                "tenantKey", "clientId", "ouid");

        // Verify the results
    }

    @Test
    public void testQueryTenantKeyOrg() {
        // Setup
        final ThirdPlatformOrgResponse thirdOrgBaseResponse = new ThirdPlatformOrgResponse();

        thirdOrgBaseResponse.setOrgOid("orgNo");
        ThirdPlatformOrgRequest request = new ThirdPlatformOrgRequest();
        request.setTenantKey("tenantKey");
        when(mockBizThirdPlatformIntegrationService.queryThirdPlatformOrg(any(), any()))
                .thenReturn(thirdOrgBaseResponse);
        // Run the test
        final WrapperResponse<ThirdPlatformOrgResponse> result = thirdPlatformIntegrationRestUnderTest.queryTenantKeyOrg(
                request);

        // Verify the results
    }

    @Test
    public void testViewRelatedOrg_BizThirdPlatformIntegrationServiceReturnsNoItems() {
        // Setup
        when(mockBizThirdPlatformIntegrationService.viewRelatedOrg("clientId", "tenantKey", "ouid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WrapperResponse<List<RelatedOrganResponse>> result = thirdPlatformIntegrationRestUnderTest.viewRelatedOrg(
                "tenantKey", "clientId", "ouid");

        // Verify the results
    }
}
