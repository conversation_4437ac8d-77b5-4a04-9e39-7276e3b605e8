package com.timevale.footstone.user.service.inner.impl.biz;


import com.timevale.account.organization.service.model.service.biz.output.BizThirdRelateEnterprisesGetOutput;
import com.timevale.account.service.enums.AccountType;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.constants.BuiltinThirdparty;
import com.timevale.account.service.model.service.biz.icuser.output.BizFatICUserOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.account.service.model.service.mods.idcard.MultiIdcardContentCollection;
import com.timevale.besp.lowcode.integration.response.TenantChildOrgResponse;
import com.timevale.besp.lowcode.integration.response.TenantInfoResponse;
import com.timevale.besp.lowcode.integration.response.TenantOpenConfigPageResponse;
import com.timevale.besp.lowcode.integration.response.TenantOpenConfigResponse;
import com.timevale.easun.service.model.account.output.BizFatICUserRealNameOutput;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.output.EsAccountIdCardDetailOutput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.page.Pages;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.constants.ThirdPartyConstant;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcRelateEnterprisesAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import com.timevale.footstone.user.service.model.thirdplatform.request.AppConfigAddRequest;
import com.timevale.footstone.user.service.model.thirdplatform.request.RelatedEnterprisesAddRequest;
import com.timevale.footstone.user.service.model.thirdplatform.request.RelatedOrgRequest;
import com.timevale.footstone.user.service.model.thirdplatform.request.ThirdPlatformOrgRequest;
import com.timevale.footstone.user.service.model.thirdplatform.response.RelatedOrganResponse;
import com.timevale.footstone.user.service.model.thirdplatform.response.ThirdPlatformOrgResponse;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.common.result.PageQueryResult;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static com.timevale.easun.service.constant.ThirdPartyConstant.PLATFORM_THIRDPARTYKEY_TENANT_MAPPING;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BizThirdPlatformIntegrationServiceImplTest {

    @InjectMocks
    BizThirdPlatformIntegrationServiceImpl bizThirdPlatformIntegrationServiceImpl;

    @Mock
    private RpcRelateEnterprisesAdapt rpcRelateEnterprisesAdapt;

    @Mock
    private RpcEsAccountServiceAdapt rpcEsAccountServiceAdapt;

    @Mock
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;

    @Mock
    private RpcIcUserPlusServiceAdapt rpcIcUserPlusServiceAdapt;

    @Mock
    private IdAdapt idAdapt;

    @Mock
    private Tedis tedis;

    @Mock
    private CommonConfig commonConfig;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;

    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;
    @Before
    public void setUp() {
        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsAccountIdCardDetailOutput idcardInfo = new EsAccountIdCardDetailOutput();
        idcardInfo.setKey("KINGDEE_K3EE_TENANT_value");
        idcardInfo.setValue("value");
        esAccountIdcardInfoOutput.setIdcardInfo(idcardInfo);
        list.add(esAccountIdcardInfoOutput);
        pagerResult.setItems(list);
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(pagerResult);

        StringOperations<String, String> tedisString = new MyDefaultStringOperations<>();
        when(tedis.string()).thenReturn(tedisString);
        TedisUtil.setTedis(tedis);

        List<String> supportList = Lists.newArrayList();
        supportList.add("KINGDEE_K3EE");
        when(commonConfig.getIhSupportList()).thenReturn(supportList);

        TenantOpenConfigPageResponse pageOpenConfig = new TenantOpenConfigPageResponse();
        List<TenantOpenConfigResponse> tenantInfoResponses = Lists.newArrayList();
        TenantOpenConfigResponse response = new TenantOpenConfigResponse();
        response.setAppId("appId");
        response.setPlatform("platform");
        response.setCallbackUrl("callbackUrl");
        response.setTenantName("tenantName");
        response.setTenantKey("tenantKey");
        tenantInfoResponses.add(response);
        pageOpenConfig.setList(tenantInfoResponses);
        pageOpenConfig.setTotal(1);
        when(rpcTripartiteServiceAdapt.pageOpenConfig("ouid", "KINGDEE_K3EE", 0, 100)).thenReturn(pageOpenConfig);

        when(rpcIcUserPlusServiceAdapt.getICUserByOuid("memberOuid")).thenReturn(MockUntils.mockBizICUserOutput());
    }

    @Test
    public void validateAppConfigTest() {
        TenantOpenConfigPageResponse pageOpenConfig = new TenantOpenConfigPageResponse();
        pageOpenConfig.setList(Arrays.asList(new TenantOpenConfigResponse()));
        pageOpenConfig.setTotal(1);
        when(rpcTripartiteServiceAdapt.pageOpenConfig("ouid", "KINGDEE_K3EE", 0, 100)).thenReturn(pageOpenConfig);
        bizThirdPlatformIntegrationServiceImpl.validateAppConfig("KINGDEE_K3EE", "ouid");
    }

    @Test
    public void getTenantListTest() {
        List<TenantInfoResponse> tenantInfoResponses = Lists.newArrayList();
        TenantInfoResponse tenantInfoResponse = new TenantInfoResponse();
        tenantInfoResponse.setTenantKey("KINGDEE_K3EE_TENANT_value");
        tenantInfoResponse.setTenantName("KINGDEE_K3EE_TENANT_name");
        tenantInfoResponses.add(tenantInfoResponse);
        when(rpcTripartiteServiceAdapt.getTenantInfoList("KINGDEE_K3EE", Arrays.asList("KINGDEE_K3EE_TENANT_value"))).thenReturn(tenantInfoResponses);
        bizThirdPlatformIntegrationServiceImpl.getTenantList("KINGDEE_K3EE", "ouid", "addAppConfig");
    }

    @Test
    public void pageQueryAppConfigTest() {
        bizThirdPlatformIntegrationServiceImpl.pageQueryAppConfig("ouid", "KINGDEE_K3EE", 0, 100);
    }

    @Test
    public void addAppConfigTest() {
        AppConfigAddRequest request = new AppConfigAddRequest();
        request.setTenantKey("tenantKeynew");
        request.setClientId("KINGDEE_K3EE");
        doNothing().when(rpcTripartiteServiceAdapt).saveOpenConfig(any(), any(), any(), any());
        bizThirdPlatformIntegrationServiceImpl.addAppConfig(request, "ouid", "memberOuid");
    }

    @Test
    public void delAppConfigTest() {
        doNothing().when(rpcTripartiteServiceAdapt).delAppConfig(any(), any(), any());
        bizThirdPlatformIntegrationServiceImpl.delAppConfig(any(), "ouid", "memberOuid");
    }

    @Test
    public void updateAppConfigTest() {
        doNothing().when(rpcTripartiteServiceAdapt).saveOpenConfig(any(), any(), any(), any());
        bizThirdPlatformIntegrationServiceImpl.updateAppConfig(any(), "ouid", "memberOuid");
    }

    @Test
    public void getTenantOrgListTest() {
        List<TenantChildOrgResponse> responses = Lists.newArrayList();
        TenantChildOrgResponse tenantChildOrgResponse = new TenantChildOrgResponse();
        tenantChildOrgResponse.setThirdOrgId("testThirdOrgId");
        tenantChildOrgResponse.setThirdOrgName("testThirdOrgName");
        responses.add(tenantChildOrgResponse);
        when(rpcTripartiteServiceAdapt.getTenantChildOrg("KINGDEE_K3EE", "tenantKey")).thenReturn(responses);
        bizThirdPlatformIntegrationServiceImpl.getTenantOrgList("KINGDEE_K3EE", "tenantKey");
    }

    @Test
    public void addRelatedEnterprisesTest() {
        RelatedEnterprisesAddRequest request = new RelatedEnterprisesAddRequest();
        request.setTenantKey("tenantKey");
        request.setClientId("KINGDEE_K3EE");
        request.setOuid("ouid");
        request.setRelatedOrganList(Arrays.asList(new RelatedOrgRequest()));
        when(rpcRelateEnterprisesAdapt.getRelateEnterprises(request.getOuid(), request.getClientId(), Arrays.asList("tenantKey"))).thenReturn(Lists.newArrayList());
        when(rpcRelateEnterprisesAdapt.addRelateEnterprises(request, "operatorId")).thenReturn(true);
        bizThirdPlatformIntegrationServiceImpl.addRelatedEnterprises(request, "operatorId");
    }

    @Test
    public void pageQueryRelateEnterprisesTest() {
        PageQueryResult<BizThirdRelateEnterprisesGetOutput> pageQueryResult = new PageQueryResult<>();
        pageQueryResult.setResultList(Lists.newArrayList(new BizThirdRelateEnterprisesGetOutput()));
        when(rpcRelateEnterprisesAdapt.pageQueryRelateEnterprises(any(), any(), any())).thenReturn(pageQueryResult);
        bizThirdPlatformIntegrationServiceImpl.pageQueryRelateEnterprises("KINGDEE_K3EE", "tenantKey", "ouid", 0, 100);
    }

    @Test
    public void delRelatedEnterprisesTest() {
        BizICUserBaseOutput icUserBase = new BizICUserBaseOutput();
        AccountBaseDetail accountBaseDetail = new AccountBaseDetail();
        accountBaseDetail.setType(AccountType.ORGANIZE);
        icUserBase.setBase(accountBaseDetail);
        when(idAdapt.getICUserBase("ouid")).thenReturn(icUserBase);
        when(rpcRelateEnterprisesAdapt.delRelatedEnterprises("KINGDEE_K3EE", "tenantKey11", "ouid", "operatorId")).thenReturn(true);

        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        pagerResult.setItems(Lists.newArrayList());
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(pagerResult);
        bizThirdPlatformIntegrationServiceImpl.delRelatedEnterprises("KINGDEE_K3EE", "tenantKey", "ouid", "operatorId");
    }

    @Test
    public void testIsBindTenantKey() {
        PLATFORM_THIRDPARTYKEY_TENANT_MAPPING.put(ClientEnum.DING_TALK.getClientId(), BuiltinThirdparty.DING_TALK);

        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsAccountIdCardDetailOutput idcardInfo = new EsAccountIdCardDetailOutput();
        idcardInfo.setKey(BuiltinThirdparty.DING_TALK);
        esAccountIdcardInfoOutput.setIdcardInfo(idcardInfo);
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(new PagerResult<>(Lists.newArrayList(esAccountIdcardInfoOutput), 1));
        boolean result = bizThirdPlatformIntegrationServiceImpl.isBindTenantKey("orgId1111", ClientEnum.DING_TALK.getClientId());
        Assert.assertTrue(result);
    }

    @Test
    public void queryIsBindTenantKeyOrgGuids() {
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsId esId = new EsId();
        esId.setGuid("guid11111");
        esAccountIdcardInfoOutput.setId(esId);
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(new PagerResult<>(Lists.newArrayList(esAccountIdcardInfoOutput), 1));
         List<String> strings = bizThirdPlatformIntegrationServiceImpl.queryIsBindTenantKeyOrgGuids(Lists.newArrayList("orgGid1111"), ClientEnum.DING_TALK.getClientId(), "ding2222222");
        Assert.assertTrue(strings.contains(esId.getGuid()));
    }

    @Test
    public void queryPersonThirdUserIdByOid() {
        BizFatICUserRealNameOutput bizFatICUserRealNameOutput = new BizFatICUserRealNameOutput();
        BizFatICUserOutput account = new BizFatICUserOutput();
        MultiIdcardContentCollection<ContentSecurity> idcards = new MultiIdcardContentCollection<>();
        IdcardContentThirdparty<ContentSecurity> idcardContentThirdparty = new IdcardContentThirdparty<>();
        IdcardThirdparty detail = new IdcardThirdparty();
        detail.setThirdpartyKey(BuiltinThirdparty.DING_TALK);
        detail.setThirdpartyUserId("thirdUserId111111");
        idcardContentThirdparty.setDetail(detail);
        idcards.getThirdparties().add(idcardContentThirdparty);
        account.setIdcards(idcards);
        bizFatICUserRealNameOutput.setAccount(account);
        when(icUserPlusService.getFatRealNameByOuid(any())).thenReturn(bizFatICUserRealNameOutput);
        String thirdUserId = bizThirdPlatformIntegrationServiceImpl.queryPersonThirdUserIdByOid("orgOid1111", ClientEnum.DING_TALK.getClientId(), "ding2222222");
        Assert.assertEquals(detail.getThirdpartyUserId(), thirdUserId);
    }

    @Test
    public void testQueryThirdPlatformOrg(){

        final EsId id = new EsId();
        id.setOuid("ouid");
        id.setUuid("03f15e91-338c-498c-bd44-bdd469cbd2ba");
        id.setGuid("1e6c5778-2bdf-4d79-8a27-11f6fe4a694b");

        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        esAccountIdcardInfoOutput.setId(id);
        list.add(esAccountIdcardInfoOutput);
        esAccountIdcardInfoOutput.setId(new EsId());
        pagerResult.setItems(list);
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(pagerResult);
        when(icOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(true);
        ThirdPlatformOrgRequest request = new ThirdPlatformOrgRequest();
        request.setClientId("FEI_SHU");
        ThirdPlatformOrgResponse response = bizThirdPlatformIntegrationServiceImpl.queryThirdPlatformOrg(request, "tenantKey11");
        Assert.assertNotNull(response);
    }

    @Test
    public void testQueryThirdPlatformOrg2(){

        final EsId id = new EsId();
        id.setOuid("ouid");
        id.setUuid("03f15e91-338c-498c-bd44-bdd469cbd2ba");
        id.setGuid("1e6c5778-2bdf-4d79-8a27-11f6fe4a694b");

        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        esAccountIdcardInfoOutput.setId(id);
        list.add(esAccountIdcardInfoOutput);
        esAccountIdcardInfoOutput.setId(new EsId());
        pagerResult.setItems(list);
        when(rpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(any())).thenReturn(pagerResult);
        when(icOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(false);
        ThirdPlatformOrgRequest request = new ThirdPlatformOrgRequest();
        request.setClientId("FEI_SHU");
        request.setTenantKey("123456");
        ThirdPlatformOrgResponse response = bizThirdPlatformIntegrationServiceImpl.queryThirdPlatformOrg(request, "tenantKey11");
        Assert.assertNotNull(response);
    }


}
