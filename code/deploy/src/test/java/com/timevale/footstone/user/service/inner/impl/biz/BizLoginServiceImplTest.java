package com.timevale.footstone.user.service.inner.impl.biz;

import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.billing.manager.sdk.response.OrderQueryResponse;
import com.timevale.easun.service.model.esearch.EsId;
import com.timevale.easun.service.model.esearch.input.BizEsAccountIdcardSearchInput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdCardDetailOutput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.billing.RpcBillingServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcProcessServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.inner.property.ThirdPlatformRuleConfig;
import com.timevale.footstone.user.service.model.thirdlogin.mods.ThirdLoginThirdInfoDTO;
import com.timevale.footstone.user.service.model.thirdlogin.request.*;
import com.timevale.footstone.user.service.model.thirdlogin.response.ThirdLoginRightsCheckResponse;
import com.timevale.footstone.user.service.model.thirdlogin.response.ThirdLoginUrlResponse;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BizLoginServiceImplTest {

    @Mock
    private SystemProperty mockSystemProperty;

    @Mock
    private RpcIcUserPlusServiceAdapt mockRpcIcUserPlusServiceAdapt;
    @Mock
    private RpcProcessServiceAdapt mockRpcProcessServiceAdapt;
    @Mock
    private RpcSaaSVipServiceAdapt mockRpcSaaSVipServiceAdapt;
    @Mock
    private RpcEsAccountServiceAdapt mockEsAccountServiceAdapt;
    @Mock
    private RpcBillingServiceAdapt mockRpcBillingServiceAdapt;

    @InjectMocks
    private BizLoginServiceImpl bizLoginServiceImplUnderTest;

    @Mock
    private Tedis tedis;

    @Before
    public void setUp() {
    }


    @Test
    public void testGetThirdLoginUrl() {
        // Setup
        final ThirdLoginUrlGetRequest request = new ThirdLoginUrlGetRequest();
        request.setBusinessType("FLOW_VIEW");
        final ThirdLoginInfoRequest loginInfo = new ThirdLoginInfoRequest();
        loginInfo.setTenantKey("tenantKey");
        loginInfo.setUnionId("operatorId");
        loginInfo.setPlatform("KINGDEE_K3EE");
        request.setLoginInfo(loginInfo);
        final ThirdLoginExtraRequest extra = new ThirdLoginExtraRequest();
        extra.setOrganOuid("organOuid");
        extra.setOperatorOid("operatorOid");
        extra.setProcessId("processId");
        extra.setDataId("dataId");
        extra.setDataSourceDataId("dataSourceDataId");
        request.setExtra(extra);

        final ThirdLoginUrlResponse expectedResult = new ThirdLoginUrlResponse();
        expectedResult.setUrl("url");
        expectedResult.setShortUrl("shortUrl");

        when(mockSystemProperty.getThirdAuthUrl()).thenReturn("https://www.baidu.com/");
        when(mockSystemProperty.getRedirectUrl("ENTERPRISE_CONSOLE")).thenReturn("https://www.google.com/");

        // Configure RpcEsAccountServiceAdapt.getAccountIdcardInfoByEs(...).
        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsAccountIdCardDetailOutput idcardInfo = new EsAccountIdCardDetailOutput();
        idcardInfo.setKey("KINGDEE_K3EE_TENANT_value");
        idcardInfo.setValue("value");
        esAccountIdcardInfoOutput.setIdcardInfo(idcardInfo);
        esAccountIdcardInfoOutput.setId(new EsId());
        list.add(esAccountIdcardInfoOutput);
        pagerResult.setItems(list);
        when(mockEsAccountServiceAdapt.getAccountIdcardInfoByEs(any()))
                .thenReturn(pagerResult);

        when(mockRpcProcessServiceAdapt.getProcessUrl(any(), any(), any())).thenReturn("result");
        when(mockRpcProcessServiceAdapt.getProcessStartUrl("", "dataSourceDataId")).thenReturn("result");
        when(mockSystemProperty.getAuthCodeSecret()).thenReturn("iJ8aHVH1q0KhkmBlt5VkxSlhM2Mo/IGky3XLcvwXNr0=");

        ThirdLoginUrlResponse result = bizLoginServiceImplUnderTest.getThirdLoginUrl(request);

    }

    @Test
    public void testRightsCheck() {
        // Setup
        final ThirdLoginRightsCheckRequest request = new ThirdLoginRightsCheckRequest();
        request.setTenantKey("tenantKey");
        request.setPlatform("KINGDEE_K3EE");
        request.setTenantOuid("tenantOuid");
        request.setConditionAdd(false);

        final ThirdLoginRightsCheckResponse expectedResult = new ThirdLoginRightsCheckResponse();
        expectedResult.setEnable(false);

        // Configure RpcIcUserPlusServiceAdapt.getICUserByOuid(...).
        final BizICUserOutput bizICUserOutput = new BizICUserOutput();
        final ICUserId id = new ICUserId();
        id.setOuid("ouid");
        id.setUuid("68a91d16-830b-4c57-ab90-8db5879c9835");
        id.setGuid("guid");
        bizICUserOutput.setId(id);
        when(mockRpcIcUserPlusServiceAdapt.getICUserByOuid("tenantOuid")).thenReturn(bizICUserOutput);

        Map<String, ThirdPlatformRuleConfig> params = new HashMap<>();
        ThirdPlatformRuleConfig ruleConfig = new ThirdPlatformRuleConfig();
        ruleConfig.setClientId("KINGDEE_K3EE");
        ruleConfig.setCommodityIds(Arrays.asList(1753l));
        ruleConfig.setFunctionCodes(Arrays.asList("info_collect"));
        ruleConfig.setVipLimit(Arrays.asList("SENIOR","PROFESSIONAL"));
        params.put("KINGDEE_K3EE",ruleConfig);
        when(mockSystemProperty.getThirdPlatformRuleConfig()).thenReturn(params);
        when(mockRpcSaaSVipServiceAdapt.getVipCode("ouid")).thenReturn("SENIOR");

        // Configure RpcBillingServiceAdapt.queryInEffectOrderList(...).
        final OrderQueryResponse orderQueryResponse = new OrderQueryResponse();
        orderQueryResponse.setGid("gid");
        orderQueryResponse.setContractId("contractId");
        orderQueryResponse.setStatus(0);
        orderQueryResponse.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderQueryResponse.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderQueryResponse> orderQueryResponses = Arrays.asList(orderQueryResponse);
        when(mockRpcBillingServiceAdapt.queryInEffectOrderList("guid", Arrays.asList(0L)))
                .thenReturn(orderQueryResponses);

         bizLoginServiceImplUnderTest.rightsCheck(request);

    }

    @Test
    public void testRightsCheck_RpcBillingServiceAdaptReturnsNoItems() {
        // Setup
        final ThirdLoginRightsCheckRequest request = new ThirdLoginRightsCheckRequest();
        request.setTenantKey("tenantKey");
        request.setPlatform("platform");
        request.setTenantOuid("tenantOuid");
        request.setConditionAdd(false);

        final ThirdLoginRightsCheckResponse expectedResult = new ThirdLoginRightsCheckResponse();
        expectedResult.setEnable(false);

        final BizICUserOutput bizICUserOutput = new BizICUserOutput();
        final ICUserId id = new ICUserId();
        id.setOuid("ouid");
        id.setUuid("68a91d16-830b-4c57-ab90-8db5879c9835");
        id.setGuid("guid");
        bizICUserOutput.setId(id);
        when(mockRpcIcUserPlusServiceAdapt.getICUserByOuid("tenantOuid")).thenReturn(bizICUserOutput);

        when(mockSystemProperty.getThirdPlatformRuleConfig()).thenReturn(new HashMap<>());
        when(mockRpcSaaSVipServiceAdapt.getVipCode("ouid")).thenReturn("result");
        when(mockRpcBillingServiceAdapt.queryInEffectOrderList("guid", Arrays.asList(0L)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final ThirdLoginRightsCheckResponse result = bizLoginServiceImplUnderTest.rightsCheck(request);

    }

}
