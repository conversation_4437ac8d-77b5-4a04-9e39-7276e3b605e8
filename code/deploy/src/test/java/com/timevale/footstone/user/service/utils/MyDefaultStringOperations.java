package com.timevale.footstone.user.service.utils;


import com.timevale.framework.tedis.core.RedisOperations;
import com.timevale.framework.tedis.core.StringOperations;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/8/8 15:13
 */

public class MyDefaultStringOperations <K, V> implements StringOperations<K, V> {

    private Map<K,V> map = new HashMap<>();

    @Override
    public void set(K key, V value) {
        map.put(key,value);
    }

    @Override
    public void set(K key, V value, long timeout, TimeUnit unit) {
        map.put(key,value);
    }

    @Override
    public Boolean setIfAbsent(K key, V value) {
        return null;
    }

    @Override
    public void multiSet(Map<? extends K, ? extends V> map) {

    }

    @Override
    public Boolean multiSetIfAbsent(Map<? extends K, ? extends V> map) {
        return null;
    }

    @Override
    public V get(Object key) {
        return map.get(key);
    }

    @Override
    public V getAndSet(K key, V value) {
        return null;
    }

    @Override
    public List<V> multiGet(Collection<K> keys) {
        return null;
    }

    @Override
    public Long increment(K key, long delta) {
        return null;
    }

    @Override
    public Double increment(K key, double delta) {
        return null;
    }

    @Override
    public Integer append(K key, String value) {
        return null;
    }

    @Override
    public String get(K key, long start, long end) {
        return null;
    }

    @Override
    public void set(K key, V value, long offset) {

    }

    @Override
    public Long size(K key) {
        return null;
    }

    @Override
    public Boolean setBit(K key, long offset, boolean value) {
        return null;
    }

    @Override
    public Boolean getBit(K key, long offset) {
        return null;
    }

    @Override
    public RedisOperations<K, V> getOperations() {
        return null;
    }
}
