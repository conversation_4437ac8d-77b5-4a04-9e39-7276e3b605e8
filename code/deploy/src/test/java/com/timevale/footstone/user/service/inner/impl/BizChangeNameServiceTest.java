package com.timevale.footstone.user.service.inner.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.account.organization.service.api.RpcChangeNameService;
import com.timevale.account.organization.service.model.service.biz.output.changename.ChangeNameRecordOutput;
import com.timevale.footstone.user.service.inner.impl.biz.changename.AbstractChannelService;
import com.timevale.footstone.user.service.inner.impl.biz.changename.BizChangeNameServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.changename.enums.ChangeNameChannelEnum;
import com.timevale.footstone.user.service.inner.impl.biz.changename.impl.RealNameChannelChannelService;
import com.timevale.footstone.user.service.model.account.mods.changename.ChangeNameUrlContextInfo;
import com.timevale.footstone.user.service.model.account.request.changename.GetChangeNameUrlRequest;
import com.timevale.footstone.user.service.model.account.response.changename.ChangeOrgNameByIdResponse;
import com.timevale.footstone.user.service.model.account.response.changename.GetChangeNameUrlResponse;
import com.timevale.footstone.user.service.utils.ChangeNameConverter;
import com.timevale.unittest.footstone.v2.rest.ChangeNameFlowRestMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @DATE 2024/7/2 16:13
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = com.timevale.unittest.footstone.configuration.Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizChangeNameServiceTest {

    @Autowired
    BizChangeNameServiceImpl bizChangeNameService;

    @Autowired
    private RpcChangeNameService rpcChangeNameService;

    @Autowired
    private RealNameChannelChannelService realNameChannelChannelService;


    @Test
    public void testGetChangeNameUrlSign() {
        GetChangeNameUrlRequest request = new GetChangeNameUrlRequest();
        request.setBizAppId(ChangeNameFlowRestMockTest.appId);
        request.setChangeNameChannel(ChangeNameChannelEnum.SIGN.name());
        request.setExpectOrgName(ChangeNameFlowRestMockTest.expectOrgName + System.currentTimeMillis());
        request.setOrgCertNo(ChangeNameFlowRestMockTest.orgCertNo);
        request.setOutBizId(ChangeNameFlowRestMockTest.outBizId);
        request.setExtendMap(new HashMap<>());
        ChangeNameUrlContextInfo contextInfo = new ChangeNameUrlContextInfo();
        contextInfo.setReturnUrl("https://www.baidu.com");
        request.setChangeNameUrlContextInfo(contextInfo);
        try {

            GetChangeNameUrlResponse changeNameUrl = bizChangeNameService.getChangeNameUrl(request);
            String changeNameRecordId = changeNameUrl.getChangeNameRecordId();
        } catch (Exception e) {

            log.error("testGetChangeNameUrlSign", e);
        }

    }

    @Test
    public void testGetChangeNameUrlRealName() {

        String json = "{\n" +
                "    \"bizAppId\": \"**********\",\n" +
                "    \"expectOrgName\": \"esigntest芒草改名测试11234\",\n" +
                "    \"outBizId\": \"3440729317419001115\",\n" +
                "    \"orgCertNo\": \"9100000068388886FL\",\n" +
                "    \"changeNameChannel\": \"REALNAME\",\n" +
                "    \"changeNameUrlContextInfo\": {\n" +
                "        \"returnUrl\": \"https://www.baidu.com11&organChangeName=true\",\n" +
                "        \"colour\": \"#EE2C2C\"\n" +
                "    },\n" +
                "    \"extendMap\": {\n" +
                "        \"bizSource\": \"Sign\"\n" +
                "    }\n" +
                "}";
        try {
            GetChangeNameUrlResponse changeNameUrl = bizChangeNameService.getChangeNameUrl(JSON.parseObject(json, GetChangeNameUrlRequest.class));
            String changeNameRecordId = changeNameUrl.getChangeNameRecordId();
            //查询
            bizChangeNameService.getChangeNameInfo(changeNameRecordId);

            //修改
            ChangeOrgNameByIdResponse response = bizChangeNameService.changeOrgNameById(changeNameRecordId);
        } catch (Exception e) {
            log.error("testGetChangeNameUrlRealName", e);
        }
    }

    @Test
    public void statusTest() {
        ChangeNameConverter.toJson(new GetChangeNameUrlResponse());
        AbstractChannelService.parseJson("{}", GetChangeNameUrlResponse.class);

        ChangeNameConverter.convertChannelVerifyDTO(new GetChangeNameUrlRequest(), null);
        ChangeNameConverter.convertPostProcessBeforeDTO(new ChangeNameRecordOutput());
        ChangeNameConverter.convertSaveChangeNameRecordInput(new GetChangeNameUrlRequest(), new AbstractChannelService.UserAccountDTO(), "x");

    }


    @Test
    public void abstractChannelTest() {
        String url = realNameChannelChannelService.getUrl("xx", "**********");
        realNameChannelChannelService.getStringUserAccountDTOMap(Lists.newArrayList(ChangeNameFlowRestMockTest.orgId));
        realNameChannelChannelService.getRealOrgByCode(ChangeNameFlowRestMockTest.orgCertNo);


        ChangeNameRecordOutput changeNameRecordOutput = new ChangeNameRecordOutput();
        changeNameRecordOutput.setCertNo(ChangeNameFlowRestMockTest.orgCertNo);
        changeNameRecordOutput.setChangeOrgName(ChangeNameFlowRestMockTest.expectOrgName);
        changeNameRecordOutput.setOid(ChangeNameFlowRestMockTest.orgId);
        changeNameRecordOutput.setAppId(ChangeNameFlowRestMockTest.appId7488);
        AbstractChannelService.PostProcessBeforeDTO build = AbstractChannelService.PostProcessBeforeDTO.builder().build();
        build.setRecordOutput(changeNameRecordOutput);

        realNameChannelChannelService.innerPostProcessBeforeUpdateName(build);


        String id = realNameChannelChannelService.orgInfoAuthCache(ChangeNameFlowRestMockTest.expectOrgName + System.currentTimeMillis(),
                ChangeNameFlowRestMockTest.orgCertNo, "熊文", System.currentTimeMillis() + "");


    }

}
