package com.timevale.footstone.user.service.inner.impl.biz.adapt;

import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcEsAccountService;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.utils.MockUntils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 */
@RunWith(MockitoJUnitRunner.class)
public class RpcEsAccountServiceAdaptTest {


    @InjectMocks
    private RpcEsAccountServiceAdapt rpcEsAccountServiceAdapt;


    @Mock
    private RpcEsAccountService esAccountService;

    @Mock private IdAdapt idAdapt;

    @Mock private RpcIcUserPlusServiceAdapt icUserPlusService;

    @Before
    public void setUp(){
        PagerResult<EsOrgSearhOutput> pagerResult = new PagerResult<>();
        pagerResult.setItems(Lists.newArrayList(MockUntils.mockEsOrgSearhOutput()));
        when(esAccountService.getOrgsByEs(any())).thenReturn(RpcOutput.with(pagerResult));
    }

    @Test
    public void searchByOrgdsTest(){
        rpcEsAccountServiceAdapt.searchByOrgIds(Lists.newArrayList("orgId"));
    }
}
