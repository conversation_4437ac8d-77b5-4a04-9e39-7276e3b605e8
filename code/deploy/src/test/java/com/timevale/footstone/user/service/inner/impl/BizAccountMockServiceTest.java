package com.timevale.footstone.user.service.inner.impl;

import static com.timevale.footstone.user.service.enums.ResultEnum.UPDATE_NAME_ERROR;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.biz.rlnm.output.BizAccountRealnameOutput;
import com.timevale.account.service.model.service.mods.icuser.share.GlobalSysCredentials;
import com.timevale.easun.service.api.RpcIcUserPlusService;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.sender.output.BizApplyWillAssembleAuthOutput;
import com.timevale.easun.service.model.sender.output.BizApplyWillAuthOutput;
import com.timevale.easun.service.model.sender.output.BizSenderWillAssembleServiceOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.identity.common.service.response.scene.webpage.IdentityAuthUrlResponse;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.constants.CommonConstants;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.impl.biz.BizAccountServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.realname.RpcRealnameServiceAdapt;
import com.timevale.footstone.user.service.inner.model.OperatorTimesLimit;
import com.timevale.footstone.user.service.inner.model.Person2FactValidModel;
import com.timevale.footstone.user.service.model.account.response.UpdateNameResponse;
import com.timevale.footstone.user.service.model.account.response.UserRealNameExtendnfoResponse;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.footstone.user.service.utils.UUIDUtils;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import org.hibernate.validator.constraints.ModCheck;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @version 2024/11/25 17:27
 */
@RunWith(MockitoJUnitRunner.Silent.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@ContextConfiguration(classes = Application.class)
public class BizAccountMockServiceTest {


    @InjectMocks
    private BizAccountServiceImpl bizAccountService;


    @Mock
    private Tedis tedis;


    @Mock
    private RpcRealnameServiceAdapt realNameService;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;

    HttpServletRequest servletRequest;

    @Mock
    private CommonConfig commonConfig;



    @Mock
    private RpcSenderAuthService senderAuthService;

    @Mock private RpcIcUserPlusService plusService;



    @Before
    public void setUp(){
        servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR,"operator");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,UUID.randomUUID().toString().replace("-",""));
        servletRequest.setAttribute(HeaderConstants.HEADER_APP_ID,UUID.randomUUID().toString().replace("-",""));
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR,"operator");
        StringOperations<String, Map<String,Object>> stringOperations = new MyDefaultStringOperations();
        Map<String,Object> organMap = new HashMap<>();
        stringOperations.set("legal_check_result_memberOid",organMap);
        when(tedis.string()).thenReturn(stringOperations);
        ReflectionTestUtils.setField(tedis,"valueOps",stringOperations);
        TedisUtil.setTedis(tedis);
        ReflectionTestUtils.setField(bizAccountService,"findTimes",10);
        when(realNameService.getBaseRealnameInfo(any())).thenReturn(MockUntils.mockBizAccountRealnameOutput());
        when(icUserPlusService.getGlobalUserDetailByGuid(any())).thenReturn(Optional.ofNullable(MockUntils.mockGlobalSysCredentials()));
        when(icUserPlusService.getICUserByOuid(any())).thenReturn(MockUntils.mockBizICUserOutput());
        List<String> cardReglarList = new ArrayList<>();
        cardReglarList.add("^\\d{18}|^[0-9]{6}[\\*]{8}[0-9]{4}$|^\\d{10}$");
        when(commonConfig.getHmtReglarList()).thenReturn(cardReglarList);
        when(senderAuthService.willAssembleAuthApply(any())).thenReturn(RpcOutput.with(MockUntils.mockBizApplyWillAssembleAuthOutput()));
    }

//    @Test
    public void getLastTimesTest(){
        OperatorTimesLimit limit =  bizAccountService.getLastTimes(CommonConstants.USER_UPDATE_ACCOUNT_NAME_PREFIX,"371326*******3434",true,UPDATE_NAME_ERROR,servletRequest);
        bizAccountService.setRemainTimes(limit);
    }


    @Test
    public void getUserRealNameExtendnfoResponseTest(){
        UserRealNameExtendnfoResponse response = bizAccountService.getUserRealNameExtendnfoResponse("accountId");
        Assert.assertTrue(response!=null);
    }


    @Test
    public void updateNameTest(){
        UserRealNameExtendnfoResponse response = bizAccountService.getUserRealNameExtendnfoResponse("accountId");
        UpdateNameResponse updateNameResponse = bizAccountService.updateName(response,"改名测试","").getData();
        bizAccountService.updateName(response,"改名测试",updateNameResponse.getServiceId()).getData();
        when(commonConfig.getHmtReglarList()).thenReturn(null);
        bizAccountService.updateName(response,"改名测试",updateNameResponse.getServiceId()).getData();
        Person2FactValidModel validModel = new Person2FactValidModel();
        validModel.setCheckResult(false);
        when(realNameService.person2FactValid(any(), any())).thenReturn(validModel);
        bizAccountService.updateName(response,"改名测试",updateNameResponse.getServiceId()).getData();
    }
}
