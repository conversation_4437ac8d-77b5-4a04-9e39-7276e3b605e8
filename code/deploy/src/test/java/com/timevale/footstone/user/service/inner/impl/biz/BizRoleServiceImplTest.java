package com.timevale.footstone.user.service.inner.impl.biz;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.account.organization.service.api.RpcOrganizationPrivilegeService;
import com.timevale.easun.service.api.RpcIcOrgPlusService;
import com.timevale.footstone.user.model.base.OrgBaseModel;
import com.timevale.footstone.user.service.inner.impl.RoleMsgServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.SealPrivilegeServiceAdapter;
import com.timevale.footstone.user.service.model.role.model.CreateRoleModel;
import com.timevale.footstone.user.service.model.role.model.RoleSyncItemModel;
import com.timevale.footstone.user.service.model.role.request.CreateRoleRequest;
import com.timevale.footstone.user.service.model.role.request.RoleSyncToSubUnitsRequest;
import com.timevale.footstone.user.service.model.role.request.UpdateRoleRequest;
import com.timevale.footstone.user.service.mq.RocketMqClient;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2023/6/7 22:56
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class BizRoleServiceImplTest {

  @InjectMocks
  private BizRoleServiceImpl bizRoleService;

  @Mock
  private RoleMsgServiceImpl roleMsgService;

  @Mock 
  private RpcRoleServiceAdapt roleServiceAdapt;

  @Mock 
  private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;

  @Mock 
  private IdAdapt idAdapt;

  @Mock  private RpcInnerOrgServiceAdapt innerOrgService;

  @Mock  private RpcOrgPlusServiceAdapt rpcOrgPlusService;

  @Mock private RpcIcOrgPlusServiceAdapt orgPlusServiceAdapt;

  @Mock 
  private SealPrivilegeServiceAdapter sealPrivilegeServiceAdapter;

  @Mock 
  private RocketMqClient rocketMqClient;

  @Mock 
  private RpcIcOrgPlusService icOrgPlusService;

  @Mock
  RpcOrganizationPrivilegeService rpcOrganizationPrivilegeService;

  @Mock 
  private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

  @Mock 
  private RpcEsAccountServiceAdapt esAdapt;

  @Before
  public void  setUp(){
    OrgBaseModel baseModel = new OrgBaseModel();
    baseModel.setOrganId("organId");
    when(idAdapt.getIds(anyString())).thenReturn(baseModel);
    when(roleServiceAdapt.getPagedBuiltinRolesByOrgan(any(),any(),any(),any()))
            .thenReturn(MockUntils.mockPageBizRole());
    HttpServletRequest servletRequest = new MyTestHttpServletRequest();
    servletRequest.setAttribute("X-Tsign-Open-App-Id","**********");
    servletRequest.setAttribute("X-Tsign-operator","d6bb1aa228034ac29a467eb0bc11d0aa");
    servletRequest.setAttribute("X-Tsign-Open-Operator-Id","operatorId");
    RequestContext.put("httpServletRequest",servletRequest);
  }

  @Test
  public void syncRolesToSubUnitsTest() {
    RoleSyncToSubUnitsRequest request = new RoleSyncToSubUnitsRequest();
    request.setSubUnits(Lists.newArrayList("org1"));
    RoleSyncItemModel roleSyncItemModel = new RoleSyncItemModel();
    roleSyncItemModel.setRoleId("roleid");
    roleSyncItemModel.setName("name");
    request.setRoles(Lists.newArrayList(roleSyncItemModel));
    ReflectionTestUtils.setField(bizRoleService,"roleSyncBatchPartition",5);

    when(this.orgPlusServiceAdapt.isRootMultiOrgan(Mockito.anyString()))
        .thenReturn(true);
    
    this.bizRoleService.syncRolesToSubUnits("tenantOrgId","operatorId", request);
  }


  @Test
  public void deleteRoleTest(){
    bizRoleService.deleteRole("orgId","roleId");
  }


  @Test
  public void updateRole(){
    UpdateRoleRequest request = new UpdateRoleRequest();
    request.setName("name");
    request.setParentRoleId("parentRoleId");

    bizRoleService.updateRole("orgId","roleId",request);
  }

  @Test
  public void createRolesTest(){
    CreateRoleRequest request = new CreateRoleRequest();
    CreateRoleModel createRoleModel = new CreateRoleModel();
    createRoleModel.setName("创建角色");
    createRoleModel.setParentRoleId("parentId");
    createRoleModel.setDisc("des");
    request.setRoleModels(Lists.newArrayList(createRoleModel));
    bizRoleService.createRoles("orgId",request);
  }
  
}