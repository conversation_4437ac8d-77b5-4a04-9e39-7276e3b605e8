package com.timevale.footstone.user.service.inner.impl.biz.adapt;

import com.timevale.account.organization.service.api.RpcThirdOrgConnectorService;
import com.timevale.account.organization.service.model.service.newbiz.output.BizThirdOrgGetConnectorOutput;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.api.RpcThirdOrgDataSyncService;
import com.timevale.easun.service.model.account.output.BizThirdOrgSyncConfirmOutput;
import com.timevale.easun.service.model.account.output.ThirdContactInfoOutput;
import com.timevale.easun.service.model.organization.output.BizThirdOrgGetTaskOutput;
import com.timevale.easun.service.model.organization.output.BizThirdOrgLastTaskStatusOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcThirdOrgSyncServiceAdapt;
import com.timevale.footstone.user.service.model.account.request.ThirdOrgConfirmRequest;
import com.timevale.footstone.user.service.model.account.request.ThirdOrgContactRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/9/1 23:42
 */
@RunWith(MockitoJUnitRunner.class)
public class RpcThirdOrgSyncServiceAdaptTest {

    @InjectMocks
    private RpcThirdOrgSyncServiceAdapt rpcThirdOrgSyncServiceAdapt;

    @Mock
    private IdAdapt idAdapt;

    @Mock
    private RpcOrgPlusService rpcOrgPlusService;

    @Mock
    private RpcThirdOrgConnectorService rpcThirdOrgConnectorService;

    @Mock
    private RpcThirdOrgDataSyncService rpcThirdOrgDataSyncService;

    @Before
    public void setUp() {
        when(rpcThirdOrgDataSyncService.queryLastTaskStatus(any())).thenReturn(RpcOutput.with(new BizThirdOrgLastTaskStatusOutput()));
//        when(rpcThirdOrgDataSyncService.queryLastTaskOtherStatus(any())).thenReturn(RpcOutput.with(new BizThirdOrgLastTaskStatusOutput()));
        when(rpcOrgPlusService
                .getThirdOrgSyncConfirmInfo(any())).thenReturn(RpcOutput.with(new BizThirdOrgSyncConfirmOutput()));
        when(rpcOrgPlusService
                .queryThirdOrgSyncContactInfo(any())).thenReturn(RpcOutput.with(new ThirdContactInfoOutput()));
        when(rpcThirdOrgConnectorService.queryConnectorInfo(any())).thenReturn(RpcOutput.with(new BizThirdOrgGetConnectorOutput()));

        BizThirdOrgGetTaskOutput bizThirdOrgGetTaskOutput = new BizThirdOrgGetTaskOutput();
        bizThirdOrgGetTaskOutput.setPushStatus("RUNNING");
        when(rpcThirdOrgDataSyncService.queryLastTask(any())).thenReturn(RpcOutput.with(bizThirdOrgGetTaskOutput));

    }

    @Test
    public void queryLastTaskStatusTest() {
        rpcThirdOrgSyncServiceAdapt.queryLastTaskStatus("organId");
        rpcThirdOrgSyncServiceAdapt.queryLastTaskOtherStatus("organId");
        ThirdOrgConfirmRequest request = new ThirdOrgConfirmRequest();
        rpcThirdOrgSyncServiceAdapt.queryThirdOrgSyncConfirmInfo(request);
        ThirdOrgContactRequest contactRequest = new ThirdOrgContactRequest();
        rpcThirdOrgSyncServiceAdapt.queryThirdOrgSyncContactInfo(contactRequest);
        rpcThirdOrgSyncServiceAdapt.queryConnectorInfo("organI");
    }
}
