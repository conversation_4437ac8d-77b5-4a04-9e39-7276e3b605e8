package com.timevale.footstone.user.service.inner.impl.biz;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.identity.common.service.api.common.RpcQueryService;
import com.timevale.footstone.identity.common.service.api.scene.webpage.RpcWebIdentityAuthService;
import com.timevale.footstone.identity.common.service.response.evidance.IdentityInnerDetailResponse;
import com.timevale.footstone.user.service.inner.impl.BizProcessServiceImpl;
import com.timevale.footstone.user.service.model.organization.request.BizProcessQueryRequest;
import com.timevale.notice.service.api.RpcBizProcessService;
import com.timevale.notice.service.model.service.biz.output.BizProcessInfoOutput;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BizProcessServiceImplTest {

    @InjectMocks
    private BizProcessServiceImpl bizProcessService;

    @Mock
    private RpcBizProcessService rpcBizProcessService;

    @Mock
    private RpcQueryService rpcQueryService;

    @Mock
    private RpcWebIdentityAuthService rpcWebIdentityAuthService;


    @Test
    public void testQueryProcessDetail(){

        BizProcessQueryRequest request = new BizProcessQueryRequest();
        request.setOrgId("orgId");
        request.setBizScene("adminJoin");
        BizProcessInfoOutput bizProcessInfoOutput = new BizProcessInfoOutput();
        bizProcessInfoOutput.setFlowId("flowId");
        bizProcessInfoOutput.setStatus(0);
        RpcOutput<BizProcessInfoOutput> bizProcessInfoOutputRpcOutput = RpcOutput.with(bizProcessInfoOutput);
        when(rpcBizProcessService.getLatestBizProcessRecordInfo(any())).thenReturn(bizProcessInfoOutputRpcOutput);
        IdentityInnerDetailResponse response = new IdentityInnerDetailResponse();
        response.setStatus("INIT");
        BaseResult<IdentityInnerDetailResponse> baseResult = BaseResult.success(response);
        when(rpcQueryService.detailInner(anyString())).thenReturn(baseResult);
        bizProcessService.queryProcessDetail(request);

    }


//    @Test
    public void testRevokeBizProcess(){
        List<BizProcessInfoOutput> bizProcessInfoOutputs = Lists.newArrayList();
        BizProcessInfoOutput bizProcessInfoOutput = new BizProcessInfoOutput();
        bizProcessInfoOutput.setStatus(0);
        bizProcessInfoOutputs.add(bizProcessInfoOutput);
        RpcOutput<List<BizProcessInfoOutput>> rpcOutput = RpcOutput.with(bizProcessInfoOutputs);
//        when(rpcBizProcessService.getBizProcessRecordInfo(any())).thenReturn(rpcOutput);
        RpcOutput<Boolean> booleanRpcOutput = RpcOutput.with(true);
//        when(rpcBizProcessService.updateBizProcessStatus(any())).thenReturn(booleanRpcOutput);
        bizProcessService.revokeBizProcess("flowId");
    }

}
