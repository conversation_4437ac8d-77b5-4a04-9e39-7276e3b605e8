package com.timevale.footstone.user.service.inner.impl.biz;

import static com.timevale.footstone.user.service.constants.CommonConstants.MEMBER_LIMIT_COUNT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.timevale.easun.service.api.RpcDeptPlusService;
import com.timevale.easun.service.model.account.AdminInfo;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.organization.output.v3.BizOrgSummaryOutputV3;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.base.model.enums.CommonResultEnum;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.identity.common.service.api.scene.adapter.fromv1.V2IndividualAuthenticationService;
import com.timevale.footstone.identity.common.service.response.adapter.V2AuthenticationIdAndUrlRpcResponse;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizAccountService;
import com.timevale.footstone.user.service.inner.biz.BizAsyncInviteService;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.biz.v3.BizOrganizationServiceV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.FileSystemServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdaptV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizTInviteServiceV3Impl;
import com.timevale.footstone.user.service.model.ApiPageResult;
import com.timevale.footstone.user.service.model.account.response.UserRealNameBaseInfoResponse;
import com.timevale.footstone.user.service.model.dept.response.v3.OrganizationDataSyncResponseV3;
import com.timevale.footstone.user.service.model.invite.request.v3.AdminBatchOperate4InvitationReqV3;
import com.timevale.footstone.user.service.model.invite.request.v3.InviteJoinMemberReqV3;
import com.timevale.footstone.user.service.model.member.mods.AdminListOpenResponse;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.notice.service.api.RpcInviteJoinService;
import com.timevale.notice.service.api.RpcInviteJoinServiceV3;
import com.timevale.notice.service.api.RpcTInviteService;
import com.timevale.notice.service.model.service.biz.mods.TInviteContextData;
import com.timevale.notice.service.model.service.biz.output.BizInviteUrlOutput;
import com.timevale.notice.service.model.service.biz.output.ExistUnfinishedInviteTaskOutput;
import com.timevale.notice.service.model.service.biz.output.InviteJoinMember;
import com.timevale.notice.service.model.service.biz.output.InviteJoinUrlGenerateOutput;
import com.timevale.notice.service.model.service.biz.output.InviteJoinUrlGetOutput;
import com.timevale.notice.service.model.service.biz.output.InviteRecordInfoOutput;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipQueryOutput;
import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @date 2023/6/6 20:39
 */
@RunWith(MockitoJUnitRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizTInviteServiceV3Test {


    @InjectMocks
    private BizTInviteServiceV3Impl bizTInviteServiceV3;

    @Mock
    private BizAsyncInviteService asyncInviteService;

    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;

    @Mock
    private RpcDeptPlusServiceAdaptV3 deptPlusServiceAdaptV3;

    @Mock
    private RpcRoleServiceAdapt roleServiceAdapt;

    @Mock
    private BizAccountService accountService;

    @Mock
    private BizOrganService bizOrganService;

    @Mock
    private RpcDeptPlusService deptPlusService;

    @Mock
    private BizRoleService bizRoleService;

    @Mock
    private FileSystemServiceAdapt fileSystemServiceAdapt;

    @Mock
    private V2IndividualAuthenticationService v2IndividualAuthenticationService;

    @Mock
    private BizMemberService bizMemberService;

    @Mock
    private IdAdapt idAdapt;

    @Mock
    private RpcAccountPlusServiceAdapt accountPlusServiceAdapt;

    @Mock
    private RpcDeptPlusServiceAdapt rpcDeptPlusServiceAdapt;

    @Mock
    private RpcTInviteService inviteService;

    @Mock
    private RpcInviteJoinService inviteJoinService;

    @Mock
    private RpcInviteJoinServiceV3 inviteJoinServiceV3;


    @Mock
    private RpcSaaSVipServiceAdapt vipServiceAdapt;


    private String realNameBizRedirectUrl;


    private Integer inviteBatchUploadLimit;

    private String failedFileKey;

    @Mock
    private BizOrganizationServiceV3 bizOrganizationServiceV3;


    @Mock
    private AuditLogCommon auditLogCommon;

    @Mock
    private Tedis tedis;

//    @Mock
//    private BizOrganizationServiceV3 bizOrganizationServiceV3;



    @Before
    public void setUp(){
        ReflectionTestUtils.setField(bizTInviteServiceV3,"asyncInviteService",asyncInviteService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"icOrgPlusServiceAdapt",icOrgPlusServiceAdapt);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"icUserPlusServiceAdapt",icUserPlusServiceAdapt);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"deptPlusServiceAdaptV3",deptPlusServiceAdaptV3);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"roleServiceAdapt",roleServiceAdapt);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"accountService",accountService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"bizOrganService",bizOrganService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"deptPlusService",deptPlusService);
//        ReflectionTestUtils.setField(bizTInviteServiceV3,"bizRoleService",bizRoleService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"v2IndividualAuthenticationService",v2IndividualAuthenticationService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"bizMemberService",bizMemberService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"idAdapt",idAdapt);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"accountPlusServiceAdapt",accountPlusServiceAdapt);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"rpcDeptPlusServiceAdapt",rpcDeptPlusServiceAdapt);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"inviteService",inviteService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"inviteJoinService",inviteJoinService);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"inviteJoinServiceV3",inviteJoinServiceV3);
        when(idAdapt.orgIdToOrganId(any())).thenReturn("organId");
        when(deptPlusService.getChildren(any())).thenReturn(RpcOutput.with(MockUntils.getDeptChildren()));
        when(inviteJoinService.queryInviteJoinMembers(any())).thenReturn(RpcOutput.with(MockUntils.getInviteJoin()));
        PagerResult pagerResult =new PagerResult();
        List<EasunEsearchAccountOutput>  accountOutputs = new ArrayList<>();
        accountOutputs.add(MockUntils.getAccountOutPut());
        pagerResult.setItems(accountOutputs);
        pagerResult.setTotal(1);
        when(accountPlusServiceAdapt.getEsearchResult(any(),any())).thenReturn(pagerResult);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(MockUntils.getInviteJoinMember()));
//        when(icOrgPlusServiceAdapt.createMemberWithResult(any(),any(),any())).thenReturn(MockUntils.getBizICCreateMemberOutput());
        when(inviteService.getInviteContextData(any())).thenReturn(RpcOutput.with(MockUntils.getTInviteContextData()));
        ApiPageResult result = new ApiPageResult();
        AdminListOpenResponse adminListOpenResponse = MockUntils.getAdminListOpenResponse();
        List<AdminListOpenResponse> apiItems = new ArrayList<>();
        apiItems.add(adminListOpenResponse);
        result.setResult(apiItems);
        when(bizMemberService.listAdministrators(any(),any(), any())).thenReturn(result);
        when(icUserPlusServiceAdapt.getICUserByOuid(any())).thenReturn(MockUntils.mockBizICUserOutput());
        when(icOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(true);
        InviteJoinUrlGetOutput getOutput = new InviteJoinUrlGetOutput();
        when(inviteJoinServiceV3.getInviteJoinUrlV3(any())).thenReturn(RpcOutput.with(getOutput));
        ExistUnfinishedInviteTaskOutput existedInviteTask = new ExistUnfinishedInviteTaskOutput();
        existedInviteTask.setExisted(true);
        existedInviteTask.setInviteId("inviteId");
        when(inviteJoinServiceV3
                .existUnfinishedInviteTask(any())).thenReturn(RpcOutput.with(existedInviteTask));
        StringOperations<String,String> tedisString = new MyDefaultStringOperations();
//        when(tedis.string()).thenReturn(tedisString);
        TedisUtil.setTedis(tedis);
        BizOrgSummaryOutputV3 orgSummaryOutputV3 = new BizOrgSummaryOutputV3();
        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setOuid("ouid");
        orgSummaryOutputV3.setAdmin(adminInfo);
        orgSummaryOutputV3.setMemberCount(3);
        when(icOrgPlusServiceAdapt.getOrgSummary(any())).thenReturn(orgSummaryOutputV3);
        BizInviteUrlOutput urlOutput = new BizInviteUrlOutput();
        when(inviteJoinServiceV3.getNotifyAdminUrl(any())).thenReturn(RpcOutput.with(urlOutput));
        OrganizationDataSyncResponseV3 responseV3 = new OrganizationDataSyncResponseV3();
        responseV3.setIsOpenThirdSync(false);
        when(this.bizOrganizationServiceV3.getOrganHasDataSync(anyString())).thenReturn(responseV3);
        AccountVipQueryOutput queryOutput = new AccountVipQueryOutput();
        queryOutput.setVipCode("PROFESSIONAL");
        when(vipServiceAdapt.getVipInfo(anyString())).thenReturn(queryOutput);
        VipFunctionQueryOutput vipFunctionQueryOutput = new VipFunctionQueryOutput();
        Map<String, Object> limit = new HashMap<>();
        limit.put(MEMBER_LIMIT_COUNT, ********);
        vipFunctionQueryOutput.setLimit(limit);
        when(vipServiceAdapt.queryVipFunctionInfo(anyString())).thenReturn(vipFunctionQueryOutput);
    }


    @Test
    public void getInviteJoinListTest() {
        bizTInviteServiceV3.getInviteJoinList("orgId", 1, 0, 10, "name", 1);
    }




    @Test
    public void bachPassedJoinTest(){
        AdminBatchOperate4InvitationReqV3 reqV3 = new AdminBatchOperate4InvitationReqV3();
        List<InviteJoinMemberReqV3> reqV3s = new ArrayList<>();
        InviteJoinMemberReqV3 joinMemberReqV3 = new InviteJoinMemberReqV3();
        joinMemberReqV3.setInviteId("inviteId");
        joinMemberReqV3.setInviteeAccount("130xxxxxxx");
        reqV3s.add(joinMemberReqV3);
        reqV3.setInviteJoinMembers(reqV3s);
        bizTInviteServiceV3.bachPassedJoin("orgId","operatorId",reqV3);
    }

    @Test
    public void batchRejectJoinTest(){
        AdminBatchOperate4InvitationReqV3 reqV3 = new AdminBatchOperate4InvitationReqV3();
        List<InviteJoinMemberReqV3> reqV3s = new ArrayList<>();
        InviteJoinMemberReqV3 joinMemberReqV3 = new InviteJoinMemberReqV3();
        joinMemberReqV3.setInviteId("inviteId");
        joinMemberReqV3.setInviteeAccount("130xxxxxxx");
        reqV3s.add(joinMemberReqV3);
        reqV3.setInviteJoinMembers(reqV3s);
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setStatus(5);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        bizTInviteServiceV3.batchRejectJoin("orgId","operatorId",reqV3);
    }

//    @Test
//    public void batchUrgingJoinTest(){
//        AdminBatchOperate4InvitationReqV3 reqV3 = new AdminBatchOperate4InvitationReqV3();
//        List<InviteJoinMemberReqV3> reqV3s = new ArrayList<>();
//        InviteJoinMemberReqV3 joinMemberReqV3 = new InviteJoinMemberReqV3();
//        joinMemberReqV3.setInviteId("inviteId");
//        joinMemberReqV3.setInviteeAccount("130xxxxxxx");
//        reqV3s.add(joinMemberReqV3);
//        reqV3.setInviteJoinMembers(reqV3s);
//        bizTInviteServiceV3.batchUrgingJoin("orgId","operatorId",reqV3);
//        TedisUtil.set("footstone_user_api_urging_join_limit_count_inviteId","invite",5, TimeUnit.SECONDS);
//        bizTInviteServiceV3.batchUrgingJoin("orgId","operatorId",reqV3);
//    }


    @Test
    public void batchDeleteTest(){
        AdminBatchOperate4InvitationReqV3 reqV3 = new AdminBatchOperate4InvitationReqV3();
        List<InviteJoinMemberReqV3> reqV3s = new ArrayList<>();
        InviteJoinMemberReqV3 joinMemberReqV3 = new InviteJoinMemberReqV3();
        joinMemberReqV3.setInviteId("inviteId");
        joinMemberReqV3.setInviteeAccount("130xxxxxxx");
        reqV3s.add(joinMemberReqV3);
        reqV3.setInviteJoinMembers(reqV3s);
        bizTInviteServiceV3.batchDelete("orgId","operatorId",reqV3);
    }

    @Test
    public void batchReInviteTest(){
        AdminBatchOperate4InvitationReqV3 reqV3 = new AdminBatchOperate4InvitationReqV3();
        List<InviteJoinMemberReqV3> reqV3s = new ArrayList<>();
        InviteJoinMemberReqV3 joinMemberReqV3 = new InviteJoinMemberReqV3();
        joinMemberReqV3.setInviteId("inviteId");
        joinMemberReqV3.setInviteeAccount("130xxxxxxx");
        reqV3s.add(joinMemberReqV3);
        reqV3.setInviteJoinMembers(reqV3s);
        bizTInviteServiceV3.batchReInvite("orgId","operatorId",reqV3);
    }

    @Test
    public void getAdminContextDataTest(){
        when(idAdapt.getICUserBase("operatorId")).thenReturn(MockUntils.getBizICUserBaseOutput());
        bizTInviteServiceV3.getAdminContextData("operatorId","contextId");
    }

    @Test
    public void adminAcceptJoinTest(){
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setStatus(11);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        bizTInviteServiceV3.adminAcceptJoin("operatorId","contexId");
    }

    @Test
    public void adminRejectJoinTest(){
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setStatus(11);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        bizTInviteServiceV3.adminRejectJoin("operatorId","contextId");
    }

    @Test
    public void getMemberContextDataTest(){
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setType(0);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        bizTInviteServiceV3.getMemberContextData("operatorId","contextId");
        when(icOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(false);
        bizTInviteServiceV3.getMemberContextData("operatorId","contextId");
        joinMember.setStatus(0);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        bizTInviteServiceV3.getMemberContextData("operatorId","contextId");

    }

    @Test
    public void memberAcceptJoinTest(){
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setType(0);
        joinMember.setStatus(10);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        bizTInviteServiceV3.memberAcceptJoin("operatorId","contextId");
    }


    @Test
    public void memberRejectJoinTest(){
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setType(0);
        joinMember.setStatus(10);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        bizTInviteServiceV3.memberRejectJoin("operatorId","contextId");
    }


    @Test
    public void getScanContextDataTest(){
//        bizTInviteServiceV3.getMemberContextData("operatorId","contextId");
//        when(icOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(false);
//        bizTInviteServiceV3.getMemberContextData("operatorId","contextId");
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setStatus(0);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        try {
            bizTInviteServiceV3.getScanContextData("operatorId", "contextId");
        }catch (Exception e){
        }
        when(icOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(false);
        bizTInviteServiceV3.getScanContextData("operatorId", "contextId");

    }



    @Test
    public void inviteCheckTest(){
        bizTInviteServiceV3.inviteCheck("orgId",0);
        Map<String, Object> limit = new HashMap<>();
        limit.put(MEMBER_LIMIT_COUNT, 1);
        VipFunctionQueryOutput vipFunctionQueryOutput = new VipFunctionQueryOutput();
        vipFunctionQueryOutput.setLimit(limit);
        when(vipServiceAdapt.queryVipFunctionInfo(anyString())).thenReturn(vipFunctionQueryOutput);
        bizTInviteServiceV3.inviteCheck("orgId",0);
    }



    @Test
    public void scanApplyJoinTest(){
        InviteJoinMember joinMember = MockUntils.getInviteJoinMember();
        joinMember.setType(0);
        joinMember.setStatus(0);
        when( inviteJoinService.queryTargetInviteMember(any())).thenReturn(RpcOutput.with(joinMember));
        when(accountService.getUserRealnameBaseInfo(any())).thenReturn(MockUntils.getUserRealNameBaseInfoResponse());
        when(icOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(false);
        bizTInviteServiceV3.scanApplyJoin("operatorId","contextId");

    }

    @Test
    public void applyJoinOrgTest() {
        BaseResult<V2AuthenticationIdAndUrlRpcResponse> baseResult = new BaseResult<>();
        baseResult.setCode(CommonResultEnum.RPC_INVOKE_ERR.getCode());
        V2AuthenticationIdAndUrlRpcResponse urlRpcResponse = new V2AuthenticationIdAndUrlRpcResponse();
        urlRpcResponse.setUrl("url");
        baseResult.setData(urlRpcResponse);
//        when(v2IndividualAuthenticationService.createIndividualAuthenticationUrl(any())).thenReturn(baseResult);
        try {
            bizTInviteServiceV3.applyJoinOrg("operatorId", "contextId");
        } catch (Exception e) {
        }
        baseResult.setCode(CommonResultEnum.SUCCESS.getCode());
//        when(v2IndividualAuthenticationService.createIndividualAuthenticationUrl(any())).thenReturn(baseResult);
        try {
            bizTInviteServiceV3.applyJoinOrg("operatorId", "contextId");
        } catch (Exception e) {

        }
        UserRealNameBaseInfoResponse response =  MockUntils.getUserRealNameBaseInfoResponse();
        when(accountService.getUserRealnameBaseInfo(any())).thenReturn(response);
        baseResult.setCode(CommonResultEnum.RPC_INVOKE_ERR.getCode());
//        when(v2IndividualAuthenticationService.createIndivi\dualAuthenticationUrl(any())).thenReturn(baseResult);
        try {
            bizTInviteServiceV3.applyJoinOrg("operatorId", "contextId");
        } catch (Exception e) {

        }
        when(bizOrganService.getOrg(any())).thenReturn(MockUntils.getUserInfoResponse());
        bizTInviteServiceV3.applyJoinOrg("operatorId", "contextId");
    }

    @Test
    public void getUploadPersonsV3Test(){
        byte[] byte1 = {80,75,3,4,10,0,0,0,0,0,-121,78,-30,64,0,0,0,0,0,0,0,0,0,0,0,0,9,0,0,0,100,111,99,80,114,111,112,115,47,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,-69,55,-39,-81,48,1,0,0,52,2,0,0,16,0,0,0,100,111,99,80,114,111,112,115,47,97,112,112,46,120,109,108,-99,-111,-63,74,3,49,20,69,-9,-126,-1,16,-78,111,51,45,34,82,50,41,-126,-120,59,7,90,117,29,51,111,-38,-64,76,18,-110,-25,-48,-6,45,46,116,33,-8,7,110,-12,111,84,-4,12,51,19,-48,-87,-72,114,119,95,-18,-27,-66,-13,8,-97,111,-102,-102,-76,-32,-125,-74,38,-89,-109,113,70,9,24,101,75,109,86,57,-67,88,-98,-114,-114,40,9,40,77,41,107,107,32,-89,91,8,116,46,-10,-9,120,-31,-83,3,-113,26,2,-119,21,38,-28,116,-115,-24,102,-116,5,-75,-122,70,-122,113,-76,77,116,42,-21,27,-119,113,-12,43,102,-85,74,43,56,-79,-22,-90,1,-125,108,-102,101,-121,12,54,8,-90,-124,114,-28,-66,11,105,106,-100,-75,-8,-33,-46,-46,-86,-114,47,92,46,-73,46,2,11,126,-20,92,-83,-107,-60,120,-91,-72,42,22,-28,-13,-31,-23,-29,-2,-123,-77,-31,59,63,3,-39,-35,93,72,-19,-125,-32,45,-50,90,80,104,61,9,-6,54,94,62,-91,-28,90,6,-24,26,115,-38,74,-81,-91,-63,-40,-36,-59,-46,-48,-21,-38,5,-12,-30,-3,-7,-15,-19,-11,46,46,-31,44,-6,-23,-83,-105,-61,-24,80,-21,3,49,-23,3,81,-20,6,-69,-126,-60,17,-115,93,-62,-91,-58,26,-62,121,85,72,-113,127,0,79,-122,-64,61,67,-62,77,56,-117,53,0,-90,-99,67,-66,-2,-30,-72,-23,87,55,-5,-7,110,-15,5,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,75,-16,123,-12,66,1,0,0,91,2,0,0,17,0,0,0,100,111,99,80,114,111,112,115,47,99,111,114,101,46,120,109,108,-123,-110,95,75,-61,48,20,-59,-33,5,-65,67,-55,123,-101,-76,-35,-4,19,-38,14,84,-10,-28,64,-80,-94,-8,22,-110,-69,45,-84,73,75,-110,-39,-19,-37,-101,-74,91,-19,80,-16,49,-9,-100,-5,-69,-25,94,-110,45,14,-86,10,-66,-64,88,89,-21,28,-59,17,65,1,104,94,11,-87,55,57,122,43,-105,-31,29,10,-84,99,90,-80,-86,-42,-112,-93,35,88,-76,40,-82,-81,50,-34,80,94,27,120,49,117,3,-58,73,-80,-127,39,105,75,121,-109,-93,-83,115,13,-59,-40,-14,45,40,102,35,-17,-48,94,92,-41,70,49,-25,-97,102,-125,27,-58,119,108,3,56,33,-28,6,43,112,76,48,-57,112,7,12,-101,-111,-120,78,72,-63,71,100,-77,55,85,15,16,28,67,5,10,-76,-77,56,-114,98,-4,-29,117,96,-108,-3,-77,-95,87,38,78,37,-35,-79,-15,59,-99,-30,78,-39,-126,15,-30,-24,62,88,57,26,-37,-74,-115,-38,-76,-113,-31,-13,-57,-8,99,-11,-4,-38,-81,26,74,-35,-35,-118,3,42,50,-63,41,55,-64,92,109,-118,74,-18,-10,58,-61,-109,74,119,-67,-118,89,-73,-14,-121,94,75,16,15,71,111,82,-52,-101,126,11,-98,-44,7,31,112,32,2,31,-123,14,-63,-49,-54,123,-6,-8,84,46,81,-111,-112,36,13,-55,44,-116,-45,50,-98,-47,-124,80,66,62,-69,-71,23,-3,93,-76,-95,-96,78,-45,-1,37,-50,-61,36,41,-55,45,-99,-33,83,114,51,33,-98,1,69,-97,-5,-14,59,20,-33,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,-128,-10,123,-43,42,1,0,0,17,2,0,0,19,0,0,0,100,111,99,80,114,111,112,115,47,99,117,115,116,111,109,46,120,109,108,-91,-111,93,75,-61,48,20,-122,-17,5,-1,67,-56,125,-102,52,-3,-80,25,109,-57,-42,15,16,47,20,-100,-69,-107,-110,-90,91,-95,73,74,-109,78,-121,-8,-33,-51,-104,83,-68,-16,70,47,15,-25,-27,-31,57,-25,77,-105,-81,114,0,7,49,-103,94,-85,12,-6,30,-127,64,40,-82,-37,94,-19,50,-8,-76,-87,81,2,-127,-79,-115,106,-101,65,43,-111,-63,-93,48,112,-103,95,95,-91,15,-109,30,-59,100,123,97,-128,67,40,-109,-63,-67,-75,-29,2,99,-61,-9,66,54,-58,115,107,-27,54,-99,-98,100,99,-35,56,-19,-80,-18,-70,-98,-117,82,-13,89,10,101,49,37,36,-58,124,54,86,75,52,126,-31,-32,-103,-73,56,-40,-65,34,91,-51,79,118,102,-69,57,-114,78,55,79,63,-31,71,-48,73,-37,-73,25,124,43,-93,-94,44,35,18,33,90,-79,2,-7,-60,95,35,22,-80};
        byte[] byte2 = {27,68,18,66,-24,-102,22,53,91,85,-17,16,-116,-89,48,-123,64,53,-46,-99,126,91,108,29,-21,96,23,-61,-8,98,-20,-108,23,-76,-94,85,-20,-110,44,14,98,-106,20,-11,42,102,81,18,-121,117,-103,-60,85,88,85,-49,97,-112,-30,-17,120,-118,47,26,-1,20,10,46,66,119,-113,-9,-18,-50,118,-26,118,61,-9,67,-69,21,-45,15,63,74,34,-118,124,-33,115,-91,122,62,9,89,-12,-101,13,62,125,-21,-36,101,-2,1,80,75,3,4,10,0,0,0,0,0,-121,78,-30,64,0,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,120,108,47,80,75,3,4,10,0,0,0,0,0,-121,78,-30,64,0,0,0,0,0,0,0,0,0,0,0,0,14,0,0,0,120,108,47,119,111,114,107,115,104,101,101,116,115,47,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,92,117,45,-85,-105,11,0,0,-38,68,0,0,24,0,0,0,120,108,47,119,111,114,107,115,104,101,101,116,115,47,115,104,101,101,116,49,46,120,109,108,-115,-36,91,111,27,57,18,5,-32,-9,5,-10,63,8,122,31,89,-83,-69,2,-37,-125,81,21,-101,-69,-64,44,16,-20,-20,-19,85,-111,-37,-79,16,73,-19,-107,58,113,-78,-65,126,-55,38,37,86,-15,16,-127,17,76,-20,124,42,81,-89,47,-26,73,6,-112,-18,127,-3,126,60,12,-66,53,-25,-53,-66,61,61,12,-85,-47,120,56,104,78,-69,-10,105,127,-6,-4,48,-4,-25,63,-22,95,86,-61,-63,-91,-37,-98,-98,-74,-121,-10,-44,60,12,127,52,-105,-31,-81,-113,127,-2,-45,-3,91,123,-2,114,121,105,-102,110,-32,86,56,93,30,-122,47,93,-9,-6,-31,-18,-18,-78,123,105,-114,-37,-53,-88,125,109,78,-18,-111,-25,-10,124,-36,118,-18,-113,-25,-49,119,-105,-41,115,-77,125,-22,-97,116,60,-36,77,-58,-29,-59,-35,113,-69,63,13,-61,10,31,-50,-17,89,-93,125,126,-34,-17,26,110,119,95,-113,-51,-87,11,-117,-100,-101,-61,-74,115,-7,47,47,-5,-41,-53,117,-75,-17,79,-17,90,-17,-23,-68,125,115,-57,122,-51,35,34,114,120,-28,-74,94,53,-125,124,-57,-3,-18,-36,94,-38,-25,110,-76,107,-113,119,33,26,30,-27,-6,110,-83,-114,-13,-72,-125,-123,10,39,-21,-72,61,127,-7,-6,-6,-117,91,-8,-43,29,-36,-89,-3,97,-33,-3,-24,15,-9,26,-88,-23,-46,58,111,111,111,-93,-73,-41,-53,104,119,-118,41,-60,9,-86,-106,119,77,71,95,47,93,123,-28,109,-73,29,62,-34,-9,87,-32,-29,-7,-18,-15,-2,105,-17,-50,-94,-65,-12,-125,115,-13,-4,48,-4,-83,-6,-16,-97,-102,-85,-15,100,-24,30,-21,-89,-2,-75,111,-34,46,-30,-5,65,-73,-3,-12,71,115,104,118,93,-13,-28,110,-105,-31,-64,-33,6,-97,-38,-10,-117,31,-4,-85,-93,-79,91,-1,117,123,106,6,63,-2,120,117,-111,31,-122,-109,-31,-96,107,95,127,111,-98,59,106,14,7,-9,26,-45,-31,96,-69,-21,-10,-33,-102,-113,110,-20,97,-8,-87,-19,92,50,-1,120,127,-93,117,-114,-98,-49,-19,-1,-102,83,-97,-95,127,41,-105,-49,-25,-71,126,63,-16,-21,-21,39,-122,5,-61,11,108,102,110,-95,-1,-10,-57,-29,-66,117,79,-68,-69,29,-119,-4,-2,122,84,117,127,-125,126,60,15,-98,-102,-25,-19,-41,67,71,-19,-31,-33,-5,-89,-18,-27,97,-72,30,77,38,-53,-12,-53,-27,-114,35,127,111,-33,-2,-46,-20,63,-65,-72,-93,-85,-6,87,-40,-75,7,-73,-100,-5,125,112,-36,-5,-97,-94,-31,-32,-72,-3,30,78,79,88,106,94,-115,38,-85,-22,-10,-53,-99,-109,75,-9,-29,-32,14,-62,125,-73,-21,47,77,124,-51,-54,7,-66,45,-28,30,-19,23,114,95,-33,110,11,85,105,-99,-86,-1,-31,124,-57,66,46,123,-65,-112,-5,26,23,-102,-50,71,-85,-11,-8,-10,-53,69,126,87,34,119,110,-5,-123,-36,-41,-21,66,-117,-47,-14,-99,79,-98,-57,39,47,110,79,-98,-52,70,-43,45,-61,120,-3,-34,20,-18,5,-5,20,-18,-21,53,-59,116,84,-23,107,-11,-77,-61,-71,11,23,-84,-65,47,-4,-49,-60,-29,-3,-71,125,27,-72,-3,-62,7,-24,127,15,23,37,-36,27,61,-12,23,-69,114,-113,-121,71,110,-41,-33,61,-63,-35,-114,-2,73,31,42,119,-21,-17,-4,42,-65,-123,101,-36,-55,118,119,-56,-59,-23,-73,-57,-15,-3,-35,55,119,-9,-19,-36,127,-18,-91,110,-81,-25,46,-84,95,120,-14,-45,117,-105,-73,117,-35,-72,123,37,119,-18,111,-21,86,-73,117,-5,87,-34,-32,-60,68,79,16,78,76,-11,4,-29,-60,76,79,24,-100,-104,-21,-119};
        byte[] byte3 = {26,39,22,122,-62,-30,-60,-14,54,-95,-50,-111,59,-115,-73,83,-100,78,-123,-41,-121,-95,-69,-93,110,-89,98,117,123,122,56,21,56,-79,-42,19,-124,19,85,-70,78,-3,34,124,29,-15,-41,-80,90,77,-42,-13,-11,120,62,93,100,71,107,-62,-44,-94,-1,-39,-11,55,64,-99,-125,-67,66,-118,91,-91,75,-89,14,-41,-17,94,-41,59,42,29,-82,-41,-97,31,110,-104,112,63,17,-73,19,82,-27,-105,30,23,-87,-14,107,127,29,-23,-113,119,57,30,-49,87,-21,73,-75,76,87,-90,63,43,38,76,-119,-29,-51,-63,94,65,-92,73,55,-111,58,94,119,9,11,-57,-21,-43,31,-81,-33,13,-3,25,-35,-28,64,57,112,14,38,-128,72,-103,-125,21,-96,50,-71,13,-86,-112,-55,-85,-54,-108,3,-27,-64,57,-104,0,34,83,14,86,-128,-54,-28,55,89,-68,47,-68,-86,76,57,80,14,-100,-125,9,32,50,-27,96,5,-88,76,-66,126,48,-109,87,-107,41,7,-54,-127,115,48,1,68,-90,28,-84,0,-107,105,93,-52,-28,85,101,-54,-127,114,-32,28,76,0,-111,41,7,43,64,101,-86,-36,-33,-86,11,39,-86,103,-107,10,-124,64,-36,95,-49,-78,99,49,81,68,50,16,43,69,103,-13,101,-123,23,-47,87,-99,62,99,32,4,-62,32,38,-118,-52,22,86,78,98,-27,-116,-50,-26,75,-94,-112,45,116,-121,-40,29,-86,92,8,-124,65,76,-108,-108,-92,6,-79,82,116,54,-65,-91,23,-78,-123,-99,94,102,-53,-123,-86,92,24,-60,68,-111,-39,-62,-77,-110,88,57,-93,-77,-7,-19,-73,-112,45,-20,-54,50,91,46,-28,-2,66,-85,-81,59,-125,-104,40,41,73,13,98,-91,-24,108,126,-45,45,100,11,123,-79,-52,-106,11,85,-71,48,-120,-119,34,-77,-123,103,37,-79,114,70,103,-13,-101,111,33,91,-40,-109,101,-74,92,-88,-54,-123,65,76,-108,-108,-92,6,-79,82,116,54,-65,9,23,-78,-123,-67,89,102,-53,-123,-86,92,24,-60,68,-111,-39,-62,-77,-110,88,57,-93,-77,-7,-51,-72,-112,45,-20,-47,50,91,46,-44,-1,19,70,-18,-127,12,98,-94,-92,36,53,-120,-107,-94,-77,-7,77,-71,-112,45,-20,-43,50,91,46,84,-27,-62,32,38,-118,-52,22,-98,-107,-60,-54,25,-107,109,82,-18,-123,-98,-27,57,-39,-128,16,8,-125,-104,40,41,73,13,98,-91,-24,108,-27,94,-16,-1,84,-47,-67,0,66,32,12,98,-94,-56,108,-48,11,114,70,103,43,-9,-62,36,111,-127,13,8,-127,48,-120,-119,34,-77,-123,-107,-109,88,57,-93,-77,-7,109,26,-17,-73,73,-66,-25,111,64,8,-124,65,76,-108,-108,-92,6,-79,82,116,-74,114,47,76,-14,61,127,3,66,32,12,98,-94,-56,108,97,-27,36,86,-50,-24,108,126,-101,46,-100,-73,124,-49,-33,76,114,33,16,6,49,81,82,-110,26,-60,74,-47,-39,-54,-67,48,-55,-9,-4,13,8,-127,48,-120,-119,34,-77,-123,-107,-109,88,57,-93,-77,-7,109,-70,112,-34,-14,61,127,51,-55,-123,64,24,-60,68,73,73,106,16,43,69,103,43,-9,-62,36,111,-127,13,8,-127,48,-120,-119,34,-77,-123,-107,-109,88,57,-93,-77,-7,109,-70,112,-34,-14,61,127,51,-55,-123,64,24,-60,68,73,73,106,16,43,69,101,-101,-106,123,-95,103,-43,11,32,4,-62,32,38,-118,-56,6,98,-91,-24,108,-27,94,-104,66,47,-128,16,8,-125,-104,40,50,27,-12,-126,-100,-47,-39,-54,-67,48,-123,94,0,33,16,6,49,81,100,54,-24,5,57,-93,-77,-107,123,97,10,-67,0,66,32,12,98,-94,-56,108,97,-27,36,86,-50,-24,108,-27,94,-104,66,47,-128,16,8,-125,-104,40,41,73,13,98,-91,-24,108,-27,94,-104,-26,45,-80,1,33,16,6,49,81,100,-74,-80,114,18,43,103,116,-74,114,47,76,-95,23,64,8,-124,65,76,-108,-108,-92,6,-79,82,116,-74,114,47,76,-13,22,-40,-128,16,8,-125,-104,40,50,91,88,57,-119,-107,51,58,91,-71,23,-90,-48,11,32,4,-62,32,38,74,74,82,-125,88,41,58,91,-71,23,-90,121,11,108,64,8,-124,65,76,20,-103,45,-84,-100,-60,-54,25,-107,109,86,-18,-123,-98,85,47,-128,16,8,-125,-104,40,41,73,13,98,-91,-24,108,-27,94,-104,65,47,-128,16,8,-125,-104,40,50,27,-12,-126,-100,-47,-39,-54,-67,48,-125,94,0,33,16,6,49,81,100,54,-24,5,57,-93,-77,-107,123,97,6,-67,0,66,32,12,98,-94,-56,108,-48,11,114,70,103,43,-9,-62,12,122,1,-124,64,24,-60,68,-111,-39,-62,-54,73,-84,-100,-47,-39,-54,-67,48,-125,94,0,33,16,6,49,81,82,-110,26,-60,74,-47,-39,-54,-67,48};
        byte[] byte4 = {-125,94,0,33,16,6,49,81,100,-74,-80,114,18,43,103,116,-74,114,47,-52,-96,23,64,8,-124,65,76,-108,-108,-92,6,-79,82,116,-74,114,47,-52,-96,23,64,8,-124,65,76,20,-103,45,-84,-100,-60,-54,25,-99,-83,-36,11,51,-24,5,16,2,97,16,19,37,37,-87,65,-84,20,-107,109,94,-18,-123,-98,85,47,-128,16,8,-125,-104,40,34,27,-120,-107,-94,-77,-107,123,97,14,-67,0,66,32,12,98,-94,-56,108,-48,11,114,70,103,43,-9,-62,28,122,1,-124,64,24,-60,68,-111,-39,-96,23,-28,-116,-50,86,-18,-123,57,-12,2,8,-127,48,-120,-119,34,-77,65,47,-56,25,-99,-83,-36,11,115,-24,5,16,2,97,16,19,69,102,-125,94,-112,51,58,91,-71,23,-26,-48,11,32,4,-62,32,38,-118,-52,22,86,78,98,-27,-116,-50,86,-18,-123,57,-12,2,8,-127,48,-120,-119,-110,-110,-44,32,86,-118,-50,86,-18,-123,57,-12,2,8,-127,48,-120,-119,34,-77,-123,-107,-109,88,57,-93,-77,-107,123,97,14,-67,0,66,32,12,98,-94,-92,36,53,-120,-107,-94,-77,-107,123,97,14,-67,0,66,32,12,98,-94,-56,108,97,-27,36,86,-50,-88,108,-117,114,47,-12,-84,122,1,-124,64,24,-60,68,73,73,106,16,43,69,103,43,-9,-62,2,122,1,-124,64,24,-60,68,-111,-39,-96,23,-28,-116,-50,86,-18,-123,5,-12,2,8,-127,48,-120,-119,34,-77,65,47,-56,25,-99,-83,-36,11,11,-24,5,16,2,97,16,19,69,102,-125,94,-112,51,58,91,-71,23,22,-48,11,32,4,-62,32,38,-118,-52,6,-67,32,103,116,-74,114,47,44,-96,23,64,8,-124,65,76,20,-103,13,122,65,-50,-24,108,-27,94,88,64,47,-128,16,8,-125,-104,40,50,91,88,57,-119,-107,51,58,91,-71,23,22,-48,11,32,4,-62,32,38,74,74,82,-125,88,41,58,91,-71,23,22,-48,11,32,4,-62,32,38,-118,-52,22,86,78,98,-27,-116,-50,86,-18,-123,5,-12,2,8,-127,48,-120,-119,-110,-110,-44,32,86,-118,-54,-74,44,-9,66,-49,-86,23,64,8,-124,65,76,20,-111,13,-60,74,-47,-39,-54,-67,-80,-124,94,0,33,16,6,49,81,100,54,-24,5,57,-93,-77,-107,123,97,9,-67,0,66,32,12,98,-94,-56,108,-48,11,114,70,103,43,-9,-62,18,122,1,-124,64,24,-60,68,-111,-39,-96,23,-28,-116,-50,86,-18,-123,37,-12,2,8,-127,48,-120,-119,34,-77,65,47,-56,25,-99,-83,-36,11,75,-24,5,16,2,97,16,19,69,102,-125,94,-112,51,58,91,-71,23,-106,-48,11,32,4,-62,32,38,-118,-52,6,-67,32,103,116,-74,114,47,44,-95,23,64,8,-124,65,76,20,-103,45,-84,-100,-60,-54,25,-99,-83,-36,11,75,-24,5,16,2,97,16,19,37,37,-87,65,-84,20,-99,-83,-36,11,75,-24,5,16,2,97,16,19,69,102,11,43,39,-79,114,70,101,91,-107,123,-95,103,-43,11,32,4,-62,32,38,74,74,82,-125,88,41,58,91,-71,23,86,-48,11,32,4,-62,32,38,-118,-52,6,-67,32,103,116,-74,114,47,-84,-96,23,64,8,-124,65,76,20,-103,13,122,65,-50,-24,108,-27,94,88,65,47,-128,16,8,-125,-104,40,50,27,-12,-126,-100,-47,-39,-54,-67,-80,-126,94,0,33,16,6,49,81,100,54,-24,5,57,-93,-77,-107,123,97,5,-67,0,66,32,12,98,-94,-56,108,-48,11,114,70,103,43,-9,-62,10,122,1,-124,64,24,-60,68,-111,-39,-96,23,-28,-116,-50,86,-18,-123,21,-12,2,8,-127,48,-120,-119,34,-77,65,47,-56,25,-99,-83,-36,11,43,-24,5,16,2,97,16,19,69,102,11,43,39,-79,114,70,103,43,-9,-62,10,122,1,-124,64,24,-60,68,73,73,106,16,43,69,101,91,-105,123,-95,103,-43,11,32,4,-62,32,38,-118,-56,6,98,-91,-24,108,-27,94,-24,-33,75,-88,-77,-27,77,65,48,-61,32,38,-118,-52,6,-67,32,103,116,-74,114,47,-84,-95,23,64,8,-124,65,76,20,-103,13,122,65,-50,-24,108,-27,94,88,67,47,-128,16,8,-125,-104,40,50,27,-12,-126,-100,-47,-39,-54,-67,-80,-122,94,0,33,16,6,49,81,100,54,-24,5,57,-93,-77,-107,123,97,13,-67,0,66,32,12,98,-94,-56,108,-48,11,114,70,103,43,-9,-62,26,122,1,-124,64,24,-60,68,-111,-39,-96,23,-28,-116,-50,86,-18,-123,53,-12,2,8,-127,48,-120,-119,34,-77,65,47,-56,25,-99,-83,-36,11,107,-24,5,16,2,97,16,19,69,102,-125,94,-112,51,58,91,-71,23,-42,-48,11,32,4,-62,32,38,-118,-52,22,86,78,98,-27,-116,-54};
        byte[] byte5 = {86,-115,-53,-59,16,92,-19,-66,72,-18,-83,109,-2,-39,114,-54,-67,-73,45,39,-9,-26,-74,64,41,79,-115,-28,-34,-34,38,-90,-78,-116,-27,-126,-88,-58,121,31,108,-112,92,-58,124,-54,101,-52,-55,101,12,-92,50,-26,-28,50,10,-54,50,-106,-117,-62,127,18,-125,63,67,105,93,-105,49,39,-105,49,-89,-2,35,28,-12,19,93,-58,124,-54,-99,-57,-100,92,70,65,33,99,-8,-8,-124,-16,54,-7,99,115,-2,-36,-8,-113,89,-72,12,118,-19,-41,-109,123,-85,-79,127,-45,-5,77,-43,-89,72,-12,31,-67,112,123,-56,125,62,-62,-21,-10,115,-13,-73,-19,-7,-13,-2,116,25,28,-36,-25,61,-72,15,-117,24,-7,-1,-11,113,14,31,-91,16,-2,-32,62,43,-94,127,-65,125,-8,88,-120,-16,-42,123,-9,-95,33,-115,123,15,-16,120,-28,-122,-97,-37,-74,-69,-2,-63,-67,57,-8,-91,127,-84,-18,-47,127,-44,-61,-19,83,73,30,-1,15,80,75,3,4,10,0,0,0,0,0,-121,78,-30,64,0,0,0,0,0,0,0,0,0,0,0,0,9,0,0,0,120,108,47,116,104,101,109,101,47,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,76,29,-106,-48,-37,5,0,0,32,25,0,0,19,0,0,0,120,108,47,116,104,101,109,101,47,116,104,101,109,101,49,46,120,109,108,-19,89,77,111,27,55,16,-67,23,-24,127,88,-20,-67,-111,100,-21,35,50,34,7,-74,62,-30,38,118,18,68,74,-118,28,-87,93,106,-105,17,119,-71,32,41,59,-70,21,-55,-79,64,-127,-94,105,-47,75,-127,-34,122,40,-38,6,72,-128,94,-46,95,-29,54,69,-101,2,-7,11,29,114,87,43,82,-94,106,-57,-56,33,45,98,95,36,-18,-101,-31,-29,-52,-16,13,-71,-70,114,-11,97,66,-67,99,-52,5,97,105,-57,-81,93,-86,-6,30,78,3,22,-110,52,-22,-8,119,71,-125,-113,46,-5,-98,-112,40,13,17,101,41,-18,-8,115,44,-4,-85,-69,31,126,112,5,-19,-56,24,39,-40,3,-5,84,-20,-96,-114,31,75,-103,-19,84,42,34,-128,97,36,46,-79,12,-89,-16,108,-62,120,-126,36,124,-27,81,37,-28,-24,4,-4,38,-76,-78,85,-83,54,43,9,34,-87,-17,-91,40,1,-73,-73,38,19,18,96,127,119,-31,-74,79,-63,119,42,-123,26,8,40,31,42,-89,120,29,27,78,107,10,33,-26,-94,75,-71,119,-116,104,-57,-121,25,66,118,50,-62,15,-91,-17,81,36,36,60,-24,-8,85,-3,-25,87,118,-81,84,-48,78,97,68,-27,6,91,-61,110,-96,-1,10,-69,-62,32,-100,110,-23,57,121,52,46,39,-83,-41,27,-11,-26,94,-23,95,3,-88,92,-57,-11,91,-3,102,-65,89,-6,-45,0,20,4,-80,-46,-100,-117,-23,-77,-79,-33,-34,-17,53,10,-84,1,-54,63,58,124,-9,90,-67,-19,-102,-123,55,-4,111,-81,113,-34,107,-88,127,11,-81,65,-71,-1,-6,26,126,48,-24,66,20,45,-68,6,-27,-8,-58,26,-66,94,111,109,117,-21,22,94,-125,114,124,115,13,-33,-86,-18,-11,-22,45,11,-81,65,49,37,-23,116,13,93,109,52,-73,-69,-117,-43,-106,-112,9,-93,7,78,120,-69,81,31,-76,-74,10,-25,75,20,84,67,89,93,106,-118,9,75,-27,-90,90,75,-48,3,-58,7,0,80,64,-118,36,73,61,57,-49,-16,4,5,80,-65,93,68,-55,-104,19,-17,-112,68,-79,84,-45,-96,29,-116,-116,-25,-7,80,32,-42,-122,-44,-116,-98,8,56,-55,100,-57,-65,-98,33,-40,17,75,-81,-81,95,-4,-8,-6,-59,51,-17,-11,-117,-89,-89,-113,-98,-97,62,-6,-27,-12,-15,-29,-45,71,63,-25,-66,44,-61,3,-108,70,-90,-31,-85,-17,-65,-8,-5,-37,79,-67,-65,-98,125,-9,-22,-55,87,110,-68,48,-15,-65,-1,-12,-39,111,-65,126,-23,6,-62,62,90,50,122,-7,-11,-45,63,-98,63,125,-7,-51,-25,127,-2,-16,-60,1,-33,-29,104,108,-62,71,36,-63,-62,-69,-119,79,-68,59,44,-127,-75,-23,-64,-40,-52,-15,-104,-65,-103,-59,40,70,-60,-78,64,49,-8,118,-72,-18,-53,-40,2,-34,-100,35,-22,-62,-19,99,59,120,-9,56,72,-120,11,120,109,-10,-64,-30,58,-116,-7,76,18,-57,-52,55,-30,-60,2,30,49,70,-9,25,119,6,-32,-122,-102,-53,-120,-16,104,-106,70,-18,-55,-7,-52,-60,-35,65,-24,-40,53,119,23,-91,86,106,-5,-77,12,-76,-109,-72,92,118,99,108,-47,-68,77,81,42,81,-124,83,44,61,-11,-116,77,49,118,-84,-18,62,33,86,92,-113,72,-64,-103,96,19,-23,-35,39,-34,62,34,-50,-112,-116,-56,-40,42,-92,-91,-47,1,73,32,47,115,23,65,72,-75,21,-101};
        byte[] byte6= {-93,123,-34,62,-93,-82,85,-9,-16,-79,-115,-124,13,-127,-88,-125,-4,8,83,43,-116,-41,-48,76,-94,-60,-27,114,-124,18,106,6,-4,16,-55,-40,69,114,56,-25,-127,-119,-21,11,9,-103,-114,48,101,94,63,-60,66,-72,108,110,113,88,-81,-111,-12,27,32,31,-18,-76,31,-47,121,98,35,-71,36,83,-105,-49,67,-60,-104,-119,-20,-79,105,55,70,73,-26,-62,14,73,26,-101,-40,-113,-59,20,74,20,121,-73,-103,116,-63,-113,-104,-67,67,-44,119,-56,3,74,55,-90,-5,30,-63,86,-70,-49,22,-126,-69,-96,-100,38,-91,101,-127,-88,39,51,-18,-56,-27,53,-52,-84,-6,29,-50,-23,4,97,-83,50,32,-20,-106,94,39,36,61,83,-68,-13,25,-34,-53,118,-57,-33,-29,-60,-71,121,14,86,-60,122,19,-18,63,40,-47,61,52,75,111,99,-40,21,-21,45,-22,-67,66,-65,87,104,-1,127,-81,-48,-101,-10,-14,-37,-41,-27,-91,20,-125,74,-85,-61,96,126,-30,-42,-25,-17,100,-29,-15,123,66,40,29,-54,57,-59,-121,66,-97,-64,5,52,-96,112,0,-125,-54,78,95,58,113,121,29,-53,98,-8,-88,118,50,76,96,-31,34,-114,-76,-115,-57,-103,-4,-124,-56,120,24,-93,12,78,-17,53,95,57,-119,68,-31,58,18,94,-58,4,-36,26,-11,-80,-45,-73,-62,-45,89,114,-60,-62,-4,-42,89,-85,-87,27,102,46,30,2,-55,-27,120,-75,81,-114,-61,-115,65,-26,-24,102,107,121,-109,42,-35,107,-74,-111,-66,-15,46,8,40,-37,55,33,97,76,102,-109,-40,118,-112,104,45,6,85,-112,-12,-3,26,-126,-26,32,-95,87,-10,86,88,-76,29,44,46,43,-9,-117,84,-83,-79,0,106,101,86,-32,-124,-28,-63,-71,-86,-29,55,-22,96,2,70,112,109,66,20,-121,42,79,121,-86,23,-39,-43,-55,124,-101,-103,-34,20,76,-85,2,-86,-16,82,-93,-88,-128,101,-90,-37,-118,-21,-58,-27,-87,-43,-27,-91,118,-114,76,91,36,-116,114,-77,73,-24,-56,-24,30,38,98,20,-30,-94,58,-43,-24,121,104,-68,105,-82,-37,-53,-108,90,-12,84,40,-118,88,24,52,90,-105,-1,-115,-59,69,115,13,118,-85,-38,64,83,83,41,104,-22,-99,116,-4,-26,118,3,74,38,64,89,-57,-97,-64,-19,29,62,38,25,-44,-114,80,39,91,68,35,120,-7,21,72,-98,111,-8,-117,40,75,-58,-123,-20,33,17,-25,1,-41,-94,-109,-85,65,66,36,-26,30,37,73,-57,87,-53,47,-45,64,83,-83,33,-102,91,109,11,4,-31,-99,37,-41,6,89,121,-41,-56,65,-46,-19,36,-29,-55,4,7,-46,76,-69,49,-94,34,-99,127,5,-123,-49,-75,-62,-7,84,-101,95,28,-84,44,-39,12,-46,61,-116,-61,19,111,76,103,-4,14,-126,18,107,-76,106,42,-128,33,17,-16,-118,-89,-106,71,51,36,-16,86,-78,20,-78,101,-3,-83,52,-90,66,118,-51,-41,-126,-70,-122,-14,113,68,-77,24,21,29,-59,20,-13,28,-82,-91,-68,-92,-93,-65,-107,49,48,-66,21,107,-122,-128,26,33,41,26,-31,56,82,13,-42,12,-86,-43,77,-53,-82,-111,115,-40,-40,117,-49,54,82,-111,51,68,115,-39,51,45,85,81,93,-45,-83,98,-42,12,-117,54,-80,18,-53,-117,53,121,-125,-43,34,-60,-48,46,-51,14,-97,75,-9,-86,-28,-74,23,90,-73,114,78,40,-69,4,4,-68,-116,-97,-93,-21,-98,-93,33,24,-44,-106,-109,89,-44,20,-29,117,25,86,-102,93,-116,-38,-67,99,-79,-64,51,-88,-99,-89,73,24,-86,-33,92,-72,93,-119,91,-39,35,-100,-45,-63,-32,-123,58,63,-40,-83,86,45,12,77,22,-25,74,29,105,-3,-61,-123,-7,11,3,27,63,0,-15,-24,-63,-69,-36,25,-107,34,23,8,13,-38,-3,7,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,109,-26,-25,-66,3,11,0,0,19,84,0,0,13,0,0,0,120,108,47,115,116,121,108,101,115,46,120,109,108,-43,92,107,-113,-37,88,25,-2,-114,-60,127,-80,92,-63,-121,21,51,-119,47,-71,117,39,83,58,-103,-79,64,90,-48,74,45,18,18,-96,-54,-109,56,51,-106,-100,120,112,-100,50,-77,8,-87,-48,45,3,-117,-118,-124,-70,80,88,-83,-60,-78,-85,82,62,-48,1,22,4,-43,-78,-19,-2,-103,38,-99,-7,-60,95,-32,61,23,-5,-100,-109,28,-57,-18,52,118,-100,-114,-44,56,-50,121,-17,-49,121,-33,115,-77,-73,-82,29,15,60,-27,-74,19,-116,92,127,-40,86,-75,-51,-86,-86,56,-61,-82,-33,115,-121,7,109,-11,59,55,-83,-115,-90,-86,-116,66,123,-40,-77,61,127,-24,-76,-43,19,103,-92,94,-37,-2,-14,-105,-74,70,-31,-119,-25,-36,56,116,-100,80,1,22,-61,81,91,61,12};
        byte[] byte7=  {-61,-93,-85,-107,-54,-88,123,-24,12,-20,-47,-90,127,-28,12,-31,-105,-66,31,12,-20,16,-66,6,7,-107,-47,81,-32,-40,-67,17,34,26,120,21,-67,90,-83,87,6,-74,59,84,-73,-73,-122,-29,-127,53,8,71,74,-41,31,15,-61,-74,106,-58,-73,20,-14,-53,55,123,112,-45,84,21,-62,-83,-29,-9,64,-107,91,-54,87,127,56,-10,-61,55,-1,-9,-59,35,114,-15,-122,114,-27,107,87,-82,84,55,-85,-43,91,-54,-101,-110,95,-65,-65,-79,-8,119,-62,101,-125,124,92,-69,-122,-103,124,-3,-106,-94,86,34,-3,120,101,-12,108,-54,44,-44,68,-2,-93,-96,-58,98,37,-76,89,37,-88,11,48,-43,27,10,53,-104,126,123,5,-66,70,2,-33,-56,-75,49,107,118,67,-32,46,58,-81,66,-93,-69,-67,-43,-9,-121,44,-56,122,13,-94,-116,-18,108,111,-115,-34,81,110,-37,30,-32,79,67,-50,-18,-6,-98,31,40,33,-64,8,-94,-116,-17,12,-19,-127,67,90,76,-50,126,-11,-30,-39,3,-36,-22,-48,14,70,-128,62,66,104,-104,-24,30,-58,30,109,57,112,-121,126,-128,110,86,-120,12,-103,-92,-27,-15,-35,71,-46,5,43,114,-30,-83,35,-109,-106,-57,59,-42,-71,-70,89,67,-100,-119,-17,-125,-125,-3,-74,106,89,6,-2,39,10,-4,-122,-29,-35,118,66,-73,107,-29,-42,-110,24,8,-18,-34,-97,115,-117,32,-64,-70,-34,-40,-83,86,69,1,9,17,-58,-83,-46,-29,-101,36,-112,-94,-55,-56,36,75,-69,28,-102,46,-127,-37,-116,86,-59,81,-30,-6,71,20,35,-53,-80,26,-11,76,102,-67,-122,48,-22,-66,66,66,-59,25,102,88,121,27,70,-123,-75,58,85,-88,70,5,9,-77,44,16,-74,84,87,-114,23,119,51,36,-50,90,-86,43,83,4,54,65,96,115,-87,22,-18,-13,22,54,81,-96,-124,-34,-74,-28,126,-19,-14,-46,-26,-5,92,-61,66,127,-53,68,-117,96,30,-105,-120,-13,73,91,-126,52,-20,-70,60,-99,-71,32,121,65,-97,-45,-106,-37,19,4,-45,-26,35,103,-31,127,-53,-116,-36,2,-21,90,-99,122,109,-71,-42,45,16,86,120,41,-59,-50,77,29,-120,-68,70,-51,-95,-87,25,101,-26,-116,94,-52,80,-73,-15,-24,100,4,-29,78,-41,-13,-30,-23,6,-108,123,114,103,123,-21,-56,14,67,39,24,90,-16,-77,66,-81,111,-98,28,-63,56,116,8,-45,31,-124,-101,10,-94,76,109,125,16,-40,39,26,12,112,51,19,-116,124,-49,-19,33,45,14,58,-4,-24,23,-90,99,-95,-117,102,68,27,116,124,-74,79,127,119,-121,61,-25,-40,-127,89,81,29,15,124,43,-100,-34,89,117,-100,21,73,-3,109,-23,-24,15,105,-98,-93,44,-102,-41,96,70,71,-52,-85,110,54,90,-83,86,83,-85,55,-101,-51,-106,105,104,-40,-88,28,-27,71,-74,66,58,-24,116,114,-74,53,-106,-43,-23,-76,90,57,-53,-94,126,109,-84,-54,-81,84,126,125,-59,-14,57,92,-43,0,87,45,-93,-39,-86,-21,0,-81,42,-18,-111,57,-30,-118,-38,95,-112,24,-50,-51,-59,-102,-55,32,-35,-24,-20,21,5,-23,66,50,18,-25,82,3,-128,-45,-88,-43,-102,53,-83,-91,-101,90,65,61,23,-106,-37,-94,-116,88,108,72,41,114,91,76,-2,-86,-19,95,-119,124,-50,-2,98,43,82,-100,57,-30,-8,-81,-60,126,60,-75,-54,63,65,-30,-87,118,-2,98,106,12,-51,43,-23,77,92,29,90,73,52,-71,113,64,-79,-14,105,-127,-24,-44,-9,-84,-36,11,4,-107,117,-67,-122,-2,114,46,70,113,-31,-37,-37,105,-27,61,110,-100,-49,-56,43,-63,48,-41,-121,86,-110,17,-71,-118,-72,18,-7,121,79,69,-30,48,23,50,-66,-31,50,-62,107,-94,9,79,-124,97,38,-67,-17,7,61,-40,63,84,-24,-42,-99,86,-123,89,44,-71,-73,-67,-27,57,-3,16,-52,10,-36,-125,67,-12,25,-6,71,-56,72,63,12,-3,1,92,-12,92,-5,-64,31,-38,30,92,86,34,-118,-24,19,81,-62,-58,35,-20,49,-74,-43,-16,16,-17,17,-110,53,42,123,28,-6,116,87,-88,-126,26,81,-18,-87,109,-79,14,88,-123,-44,-90,-96,102,-92,101,106,91,98,-52,37,109,-95,-39,-124,45,39,-90,90,52,79,-111,98,-41,60,65,-102,117,-13,20,-53,-80,113,71,71,127,40,59,103,-75,-111,-93,-56,102,35,71,-112,-47,70,-114,98,25,54,26,-80,21,67,118,44,-78,-38,-56,81,100,-77,-111,35,-56,104,35,71,-111,-43,70,8,18,-42,6,62,65,8,-4,79,8,-93,-66,48,112,122,-18,120,0,-67,-100,116,72,-102,-65,-56,50,84,-50,34,-30,-79,-79,-119,86,-117,-52,-122,89,109,-104,53,-67,78,112,-107,85,118,100,71,-49,31,-17,123,78,108,7,-59};
        byte[] byte8 = {61,-25,47,73,122,73,-91,-103,15,99,42,-119,36,-112,-87,52,89,109,77,9,-91,92,-114,101,-63,-2,13,94,-34,-123,-76,-116,114,117,122,122,19,-27,68,30,22,18,-73,-128,-109,116,-101,-123,-26,-117,-44,-96,-91,3,42,81,-41,-15,-68,27,-88,100,124,-73,31,-105,35,-77,5,1,62,-18,115,39,55,96,73,21,29,5,64,39,74,-48,37,-84,-26,-46,75,82,122,-56,-105,-19,45,-37,115,15,-122,3,103,8,7,12,-100,0,-19,119,-61,-15,-124,46,124,117,2,-100,-60,-114,-5,51,108,77,124,32,36,-115,-81,98,31,29,121,39,22,-56,-57,-46,-55,55,80,-127,125,-37,-63,-27,-108,125,-65,30,-23,-63,110,-67,29,-8,-95,-45,13,-15,-119,29,84,109,95,85,85,-50,3,48,28,-120,60,0,-29,44,-34,3,68,-45,111,-113,7,-5,78,96,-31,51,60,76,-127,98,117,4,-59,34,29,97,117,-122,-23,8,14,-57,-2,-109,-21,-104,-125,-33,-56,1,-92,-75,8,-79,-119,79,6,-83,-123,-86,114,52,-62,73,32,46,-44,-48,102,65,-88,-117,-123,35,-46,44,-62,-93,6,24,100,-128,92,-71,-106,38,62,-73,-75,110,81,-121,37,-73,-40,-97,-80,96,82,34,127,114,-48,-44,-112,107,-27,-75,98,17,52,-83,-94,115,59,114,102,106,117,35,26,-105,-89,10,-63,118,-33,90,56,-105,-61,3,-70,-92,112,-128,5,18,-122,89,-128,-55,-126,76,37,-70,60,-121,18,-59,105,-56,119,43,-72,102,42,66,-101,5,42,-18,20,58,-2,-128,-126,30,-71,17,-44,-54,-86,99,-31,-67,-118,115,43,74,-7,-21,-90,50,32,116,-35,84,94,67,96,112,99,105,1,-53,-32,-3,5,-3,77,-60,114,-66,41,65,-29,-122,-46,37,85,-111,-53,90,58,-28,-46,-84,25,-95,28,89,11,64,91,-110,72,-13,110,44,-17,-104,10,-91,85,90,68,-123,88,-125,-58,11,-4,-104,111,31,-31,114,-91,-96,83,-31,-77,77,-82,-26,-24,-100,-93,116,97,14,12,57,103,101,-98,-30,-14,29,74,44,-27,-20,-86,92,71,-128,-99,-119,-110,42,-87,39,-115,41,-64,-83,11,-62,91,100,-23,64,-55,56,-22,-85,-126,31,-63,-63,37,81,17,-114,-24,-60,42,-62,-47,-53,-78,-58,-102,91,62,-48,-95,11,-107,-77,-37,112,125,-37,16,-62,13,95,22,-124,-69,-40,50,-52,-59,-37,40,-19,114,17,-25,-55,-46,22,98,78,71,-16,105,-23,17,-87,-81,-123,-110,-27,90,26,-124,126,27,-83,11,113,-35,70,47,109,-73,-31,-108,68,75,111,-91,-57,-28,90,100,-14,-46,58,-110,-117,54,26,-21,-106,62,-38,90,105,-121,23,-100,39,-75,-46,98,-110,83,-46,40,109,-102,-28,-118,-94,-66,22,-45,27,97,98,-72,-118,113,90,-123,-33,-128,39,-37,-15,-36,78,124,-13,82,27,-15,-54,113,-1,-78,59,-14,92,-55,-29,-58,-36,-77,-23,37,-30,79,-122,-75,100,-127,30,-102,-29,65,46,-103,96,69,-33,-72,117,7,77,-40,117,63,-12,3,-9,29,-40,116,65,71,4,-48,-103,17,53,-29,-111,1,78,65,116,-103,-80,-69,36,42,24,-115,-80,69,13,-78,29,81,-32,-28,37,77,56,-95,-119,40,47,47,-121,40,63,10,-20,-93,-101,-50,49,-100,-125,32,39,77,-26,14,84,112,-38,114,115,79,33,93,64,100,-78,107,-53,60,71,-126,-69,-118,112,-50,41,92,114,-91,94,-21,108,9,76,119,-78,97,58,-62,88,38,105,56,-55,64,90,-31,-114,-5,-120,-121,125,-30,36,-92,-96,-121,83,-37,-22,-28,-23,-45,-13,-57,-17,114,72,-39,31,-69,30,60,99,67,-46,10,28,92,-102,37,56,-1,-41,-29,-55,-45,-97,126,-81,-6,-125,-120,6,-94,-58,-47,52,-48,-7,-97,89,26,-67,-6,21,101,67,-103,124,-2,-39,-7,-33,-17,78,127,119,58,121,-14,-16,-30,-29,15,-49,127,-7,-87,2,-48,37,16,69,-64,101,-126,13,-14,8,123,116,96,-119,-86,122,-2,-4,-63,-28,-34,-93,-120,2,-115,-42,24,5,-116,-117,36,114,-119,-82,17,5,-102,96,50,10,124,34,111,86,-47,-55,-3,-69,47,-98,-35,-97,-4,-30,-25,23,31,-68,-49,-103,-120,-58,51,-116,18,63,-99,49,75,105,-90,-102,-120,10,16,99,98,-32,39,-90,102,-71,76,-2,115,22,105,-117,-122,39,-84,57,-84,-123,72,-20,-29,-43,-115,-24,80,49,102,116,-28,-15,-11,25,71,-42,83,117,-123,33,8,-57,-60,-108,59,-9,-33,-9,46,30,60,-97,-2,58,-114,8,94,65,100,-94,-91,49,124,-7,-121,-25,-32,-35,-23,-33,-34,-113,-44,-59,39,89,24,17,126,66,97,-34,43,-97,-98,-97,125,113,-15,-16,-20,-27,7,-17,-98,-49,73,21,-95,35,117,-21,-12,-97,127,-71,56,125,47,22,41,66,7,-44};
        byte[] byte9 = {-106,-72,54,-47,71,32,-114,64,22,109,-53,115,78,50,-92,-80,-104,126,116,122,-15,-15,-17,21,104,74,-119,68,40,-111,-25,-26,102,-19,61,127,-14,-25,-55,111,-34,-125,-114,50,-3,-16,-81,49,-95,8,31,-6,42,-96,-103,-64,18,105,49,-119,8,33,24,-107,75,-20,60,127,-4,9,-72,102,122,-25,-15,-116,56,17,70,53,41,-114,-88,113,113,-38,70,75,-116,-100,71,-76,69,30,-119,-35,-120,103,52,12,0,48,44,-105,104,-103,24,-115,88,54,-98,-85,51,54,48,112,-106,-80,-95,10,-77,-84,35,98,7,-42,-23,37,68,-119,-78,-29,-96,-30,-83,56,38,27,14,-62,73,-40,-96,-20,117,-6,89,20,27,93,4,15,104,47,35,57,-5,-45,-53,-77,-121,49,-119,8,29,88,21,-105,-112,76,63,-71,51,-3,-29,-93,-55,-3,-33,78,-18,-35,-99,126,-12,121,76,43,-94,7,20,-106,-48,38,102,105,32,-90,89,90,68,84,77,-38,115,36,105,-98,5,91,-124,-107,33,-43,-125,100,-107,121,27,68,116,-127,7,37,54,76,-1,113,58,-67,-13,-33,72,95,-68,56,-53,2,67,-34,56,48,-37,-37,38,-113,-98,-59,-19,-59,90,6,-101,73,18,17,23,119,126,-10,-30,-23,-109,-104,68,-124,16,-52,-123,36,36,-119,-98,-123,-112,18,-49,-126,39,-8,-82,99,74,37,75,60,27,-29,31,94,-46,-64,51,-128,69,-64,87,-47,-125,-79,17,65,6,14,-108,-80,73,-84,117,-116,-115,-120,55,88,127,-106,-80,73,-12,74,12,23,67,-60,27,121,-17,-44,108,-4,18,-75,97,108,102,80,39,77,-124,18,-25,-58,121,2,-118,53,-17,92,80,75,98,-114,-124,65,-100,33,-96,-126,-14,12,-32,-4,-87,-124,65,-94,63,24,27,17,-97,112,-88,90,-62,38,-47,31,-116,-115,-120,89,56,23,41,97,35,49,39,70,43,58,73,-55,37,122,83,-22,-48,68,61,24,27,17,-77,-90,-44,-83,-119,-23,-105,-79,17,49,107,74,-69,-96,-60,-100,56,-83,65,111,19,-52,-111,-10,-99,68,115,24,27,17,-83,53,105,-112,19,-51,97,108,68,-76,-42,112,-112,-39,-30,1,12,-19,67,27,-98,-52,-64,71,-7,-29,-79,61,-32,-85,-25,-12,-19,-79,23,-34,-116,127,108,-85,-20,-6,91,-8,-87,20,-120,59,109,-11,-74,123,-37,15,49,-117,-74,-54,-82,-33,66,79,-94,-47,-78,-115,-34,-77,2,-78,6,-63,24,-65,114,5,-51,40,-16,-85,87,-16,-125,32,-8,-91,51,-12,-91,89,21,-82,73,5,-73,-127,-74,48,117,125,107,4,79,-97,-63,-89,50,14,-36,-74,-6,-29,-67,-99,70,107,119,-49,-46,55,-102,-43,-99,-26,-122,105,56,-75,-115,86,109,103,119,-93,102,118,118,118,119,-83,86,85,-81,118,126,2,-23,16,-67,45,-13,-22,-79,102,-50,-67,49,115,-32,118,3,127,-28,-9,-61,-51,46,60,31,-25,-9,-5,110,-41,-103,127,103,102,-85,-46,-118,-34,-102,9,76,-82,-114,60,104,21,80,95,81,-37,111,-80,123,109,-107,-5,66,-84,71,125,-95,2,106,-109,-1,-79,17,-107,81,-4,54,-49,-19,-1,3,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,40,-47,-30,58,37,3,0,0,125,6,0,0,20,0,0,0,120,108,47,115,104,97,114,101,100,83,116,114,105,110,103,115,46,120,109,108,-75,85,93,79,-38,96,20,-66,95,-78,-1,-48,116,-55,-82,-90,5,117,102,113,-128,23,38,92,-17,98,-5,1,21,59,36,-127,-42,-75,-43,108,-69,-86,40,95,1,4,-123,77,5,-99,-110,-128,-112,-71,80,-99,26,88,65,-3,49,-10,-19,-37,94,-15,23,118,-38,-105,49,83,93,-68,113,4,72,-34,-25,-100,-9,124,60,-25,57,-83,111,-10,99,44,74,-83,112,-94,20,17,120,63,-19,29,-9,-48,20,-57,-121,-124,-123,8,31,-10,-45,-17,-34,6,-57,94,-47,-108,36,-77,-4,2,27,21,120,-50,79,127,-30,36,122,54,-16,-12,-119,79,-110,100,10,-18,-14,-110,-97,94,-108,-27,-91,25,-122,-111,66,-117,92,-116,-107,-58,-123,37,-114,7,-53,123,65,-116,-79,50,28,-59,48,35,45,-119,28,-69,32,45,114,-100,28,-117,50,19,30,-49,52,19,99,35,60,77,-123,-124,101,94,-122,-68,-45,52,-75,-52,71,62,44,115,115,67,-32,37,29,-16,73,-111,-128,79,-124,-17,27,-8,-101,103,-32,-4,-103,90,97,-93,-32,-19,-91,-31,36,6,5,94,38,0,106,103,-11,-53,-110,13,-122,22,89,81,-30,-122,-80,119,114,-54,-58,-100,-70,56,-30,25,-117,-16,-126,104,-125,-116,19,85,14,32,-75,-113,18,13,93,-45,-48,-26,-114,81,40,-30,-70,54,-24,87,124,-116,108,123,-4,-51,-2,31,50,-37,-28,-51,72,75,108,8,72};
        byte[] fileList = new byte[11972];
        int startIndex = 0;
        for (int i = 0;i<byte1.length;i++){
            fileList[startIndex] = byte1[i];
            startIndex ++;
        }
        for (int i = 0;i<byte2.length;i++){
            fileList[startIndex] = byte2[i];
            startIndex ++;
        }
        for (int i = 0;i<byte3.length;i++){
            fileList[startIndex] = byte3[i];
            startIndex ++;
        }
        for (int i = 0;i<byte4.length;i++){
            fileList[startIndex] = byte4[i];
            startIndex ++;
        }
        for (int i = 0;i<byte5.length;i++){
            fileList[startIndex] = byte5[i];
            startIndex ++;
        }
        for (int i = 0;i<byte6.length;i++){
            fileList[startIndex] = byte6[i];
            startIndex ++;
        }
        for (int i = 0;i<byte7.length;i++){
            fileList[startIndex] = byte7[i];
            startIndex ++;
        }
        for (int i = 0;i<byte8.length;i++){
            fileList[startIndex] = byte8[i];
            startIndex ++;
        }
        for (int i = 0;i<byte9.length;i++){
            fileList[startIndex] = byte9[i];
            startIndex ++;
        }
        for (int i = 0;i<getByte10().length;i++){
            fileList[startIndex] = getByte10()[i];
            startIndex ++;
        }
        for (int i =  0;i<getByte11().length;i++){
            fileList[startIndex] = getByte11()[i];
            startIndex ++;
        }
        when(fileSystemServiceAdapt.download(any())).thenReturn(fileList);
        ReflectionTestUtils.setField(bizTInviteServiceV3,"inviteBatchUploadLimit",100);
        bizTInviteServiceV3.getUploadPersonsV3("$bc93da85-7bb2-4663-80d2-28fd76062068$1222103801","tenantId");
    }

    private byte[] getByte10() {
        byte[] byte10 = {5, 118, 36, 78, 92, -31, -24, -64, -13, 103, 94, -49, 107, -17, -115, -78, 106, 100, 20, 116, -6, 13, -9, -42, 113, 47, 5, 21, -103, 106, -57, -68, 42, 65, -91, 40, -107, -60, -75, 54, -82, -84, -21, -3, 85, -67, 91, 65, -59, 60, 110, -98, 12, -6, 85, -29, -96, -88, 119, -65, -109, 62, -84, 61, -59, 60, 90, 37, -99, -95, 122, 5, 112, 18, 7, 110, 13, -6, 57, 67, 45, -116, 16, 107, -19, 18, 37, 119, -11, -82, 98, -42, 114, -125, 126, 90, -17, -106, 32, 38, -50, -92, -11, 94, -62, 40, -85, 70, 110, 24, -60, -40, -5, 65, -46, -39, 121, 29, -86, 32, -114, 117, -68, -125, -75, 38, 56, -93, -126, -86, -9, 26, 102, -89, 69, -78, -112, -68, -125, 126, -58, -23, 101, 98, -44, -117, -75, -42, -78, -74, 91, -48, 11, 41, -119, 28, -47, 105, -36, 14, -30, 20, -122, -53, 45, -58, -86, -108, 81, 95, -127, -24, 15, -116, 32, 36, 68, 5, -111, 18, -61, -13, 126, 58, 24, -100, -13, -40, -97, 71, -44, -60, 81, 28, -54, -76, -54, 10, 42, -73, -95, 76, -58, 108, 92, -93, -4, -74, -111, -34, 65, 57, -115, 65, -43, 42, -22, -20, -93, 116, 18, 44, 15, 20, -7, 56, 10, -123, -31, -126, 18, 116, 109, -29, 54, 111, 35, -58, -52, -20, -87, -15, 53, 101, 41, -37, -88, -48, -71, 81, 114, 47, 110, -108, 60, -31, -48, 97, 127, 18, -40, 55, -49, -113, -64, 102, -13, -18, -52, 73, -17, 106, 70, 38, 107, -20, 105, 104, 43, 103, -59, -37, -72, 125, 10, 108, -101, -87, 51, 116, -78, -119, 106, -57, 68, 13, -96, 15, 0, -1, -8, -41, -31, 104, 11, -59, -79, 58, 97, -89, -20, -80, -51, 45, 51, -13, 115, 52, 78, 114, 124, -72, 44, -24, -122, -72, 18, -27, -94, -21, -124, 85, -21, -23, -35, 13, 124, -42, -61, -67, 3, -108, 76, -104, -19, 43, 124, 105, 11, -100, 56, -40, -38, 76, -99, 59, 89, 65, -96, -90, -38, 32, 75, -86, 119, -13, 67, -107, -89, 15, -83, -35, 58, -88, 109, 52, 11, -58, 126, 108, -40, 63, 57, 112, 123, -119, -120, -35, 109, 35, 42, 116, -37, 80, -77, 4, -39, 25, 99, -25, 2, -106, -53, 109, 36, 124, -114, 17, 18, -1, 97, 36, -60, -70, -115, -88, -45, -128, 73, -72, 81, 66, -121, 27, -27, -92, 72, -104, -105, 57, 73, -58, -103, -90, -82, 109, -62, -20, -55, 118, -115, -74, -50, 125, -63, 42, -17, -102, -86, 10, -68, -111, -106, -68, 119, -20, -15, 95, -88, 88, 117, -93, 40, 127, -126, -113, 15, 113, -69, -122, -117, 73, 120, 2, -70, -51, -58, 69, -42, 84, -65, -112, -115, -122, -48, 94, -40, -43, -5, 25, 35, -114, 19, 119, -18, -73, 106, -58, -2, -11, 61, -31, 25, 120, 125, 4, 126, 3, 80, 75, 3, 4, 20, 0, 0, 0, 8, 0, -121, 78, -30, 64, -111, 84, -20, 45, 39, 1, 0, 0, -36, 1, 0, 0, 15, 0, 0, 0, 120, 108, 47, 119, 111, 114, 107, 98, 111, 111, 107, 46, 120, 109, 108, -115, 81, -53, 78, -61, 48, 16, -68, 35, -15, 15, -42, -34, -87, -109, -74, -127, 82, 53, -87, -124, 0, -47, 11, -86, 4, -108, -77, -119, 55, -115, 85, -65, 100, -69, 4, -2, -98, 77, -94, 82, -114, -100, 118, 103, 118, 61, -102, 29, -81, -42, 95, 70, -77, 79, 12, 81, 57, 91, 66, 62, -55, -128, -95, -83, -99, 84, 118, 95, -62, -37, -21, -29, -43, 2, 88, 76, -62, 74, -95, -99, -59, 18, -66, 49, -62, -70, -70, -68, 88, 117, 46, 28, 62, -100, 59, 48, 18, -80, -79, -124, 54, 37, -65, -28, 60, -42, 45, 26, 17, 39, -50, -93, -91, 73, -29, -126, 17, -119, 96, -40, -13, -24, 3, 10, 25, 91, -60, 100, 52, -97, 102, -39, 53, 55, 66, 89, 24, 21, -106, -31, 63, 26, -82, 105, 84, -115, -9, -82, 62, 26, -76, 105, 20, 9, -88, 69, 34, -5, -79, 85, 62, 66, -75, 106, -108, -58, -35, 120, 17, 19, -34, 63, 11, 67, -66, -65, 52, 48, 45, 98, 122, -112, 42, -95, 44, 97, 70, -48, 117, 120, 38, 10, 96, -31, -24, -17, -114, 74, -45, -12, 118, -106, 77, -127, 87, -65, 71, 110, 3, -127, -2, -38, -99, -62, 46, -98, -7, 30, -78, 78, 89, -23, -70, 119, 37, 83, 75, 9, 46, -118, 25, 101, 56, 114, 79, -88, -10, 109, 42, -31, 38, 39, -114, 20, -8, 31, -119, 33, 7, -110, 26, 42, -77, -125, -55, -105, 62, -101, -100, 2, -17, -21, -122, 124, 80, 31, -106, -118, -102, -80, -111, -7, -96, 112, 122, 86, 11, 93, 111, 3, -21, -53, -80, 56, -97, 23, -45, 98, -40, 56, 125, 76, -11, 3, 80, 75, 3, 4, 10, 0, 0, 0, 0, 0, -121, 78, -30, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 95, 114, 101, 108, 115, 47, 80, 75, 3, 4, 20, 0, 0, 0, 8, 0, -121, 78, -30, 64, 123, 56, 118, -68, -1, 0, 0, 0, -33, 2, 0, 0, 11, 0, 0, 0, 95, 114, 101, 108, 115, 47, 46, 114, 101, 108, 115, -83, -110, -49, 74, -60, 48, 16, -58, -17, -126, -17, 16, -26, -66, 77, 119, 21, 17, -39, 116, 47, 34, -20, 77, 100, 125, -128, -104, 76, -1, -48, 38, 19, -110, 89, -19, -66, -67, 65, 81, 44, -44, -70, 7, -113, -103, -7, -26, -101, -33, 124, 100, -69, 27, -35, 32, 94, 49, -90, -114, -68, -126, 117, 81, -126, 64, 111, -56, 118, -66, 81, -16, 124, 120, 88};
        return byte10;
    }

    private byte[] getByte11(){
        byte[] byte11 = {-35,-126,72,-84,-67,-43,3,121,84,112,-62,4,-69,-22,-14,98,-5,-124,-125,-26,60,-108,-38,46,36,-111,93,124,82,-48,50,-121,59,41,-109,105,-47,-23,84,80,64,-97,59,53,69,-89,57,63,99,35,-125,54,-67,110,80,110,-54,-14,70,-58,-97,30,80,77,60,-59,-34,42,-120,123,-69,6,113,56,-123,-68,-7,111,111,-86,-21,-50,-32,61,-103,-93,67,-49,51,43,-28,84,-111,-99,117,108,-112,21,-116,-125,124,-93,-40,-65,16,-11,69,6,6,57,-49,114,117,62,-53,-17,119,74,-121,-84,-83,102,45,13,69,92,-123,-104,83,-118,-36,-27,92,-65,113,44,-103,-57,92,78,31,-118,37,-96,-51,-7,64,-45,-45,-25,-62,-63,-111,-47,91,-76,-53,72,58,-124,37,-94,-21,-1,36,50,-57,-60,-28,-106,121,62,53,95,72,114,-14,45,-85,119,80,75,3,4,10,0,0,0,0,0,-121,78,-30,64,0,0,0,0,0,0,0,0,0,0,0,0,9,0,0,0,120,108,47,95,114,101,108,115,47,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,-27,-16,-94,24,-19,0,0,0,-70,2,0,0,26,0,0,0,120,108,47,95,114,101,108,115,47,119,111,114,107,98,111,111,107,46,120,109,108,46,114,101,108,115,-83,-110,-49,106,-61,48,12,-58,-17,-125,-67,-125,-47,125,113,-46,-115,49,70,-99,94,-58,-96,-41,-83,123,0,99,43,127,104,98,7,75,91,-101,-73,-97,-56,-95,89,-96,116,-105,92,12,-97,-124,-65,-17,39,-53,-37,-35,-71,-17,-44,15,38,106,99,48,80,100,57,40,12,46,-6,54,-44,6,-66,14,-17,15,47,-96,-120,109,-16,-74,-117,1,13,-116,72,-80,43,-17,-17,-74,31,-40,89,-106,75,-44,-76,3,41,113,9,100,-96,97,30,94,-75,38,-41,96,111,41,-117,3,6,-23,84,49,-11,-106,69,-90,90,15,-42,29,109,-115,122,-109,-25,-49,58,-3,-11,-128,114,-31,-87,-10,-34,64,-38,-5,39,80,-121,113,-112,-28,-1,-67,99,85,-75,14,-33,-94,-5,-18,49,-16,-107,8,77,-115,77,-24,63,57,-55,120,36,-58,54,-43,-56,6,22,-27,76,-120,65,95,-121,121,92,21,-122,-57,78,94,115,-90,-104,-12,-83,-8,-51,-102,-15,44,59,-62,57,125,-110,122,58,-117,91,12,-59,-102,12,-89,-104,-114,-44,32,-14,-52,113,41,-111,108,75,58,23,24,-67,-8,113,-27,47,80,75,3,4,20,0,0,0,8,0,-121,78,-30,64,-88,-15,90,115,103,1,0,0,13,5,0,0,19,0,0,0,91,67,111,110,116,101,110,116,95,84,121,112,101,115,93,46,120,109,108,-83,-108,-53,78,2,49,20,-122,-9,38,-66,-61,-92,91,51,83,112,97,-116,97,96,-31,101,-87,36,-30,3,-44,-10,-64,52,-12,-106,-98,-126,-16,-10,-98,41,96,2,65,-127,-116,-101,73,58,-19,-7,-65,-1,-4,-67,12,70,43,107,-118,37,68,-44,-34,-43,-84,95,-11,88,1,78,122,-91,-35,-84,102,31,-109,-105,-14,-98,21,-104,-124,83,-62,120,7,53,91,3,-78,-47,-16,-6,106,48,89,7,-64,-126,-86,29,-42,-84,73,41,60,112,-114,-78,1,43,-80,-14,1,28,-51,76,125,-76,34,-47,48,-50,120,16,114,46,102,-64,111,123,-67,59,46,-67,75,-32,82,-103,90,13,54,28,60,-63,84,44,76,42,-98,87,-12,123,-29,36,-126,65,86,60,110,22,-74,-84,-102,-119,16,-116,-106,34,-111,83,-66,116,-22,-128,82,110,9,21,85,-26,53,-40,-24,-128,55,100,-125,-15,-93,-124,118,-26,119,-64,-74,-18,-115,-94,-119,90,65,49,22,49,-67,10,75,54,-72,-14,114,28,125,64,78,-122,-86,-65,85,-114,-40,-12,-45,-87,-106,64,26,11,75,17,84,-48,-74,-84,64,-107,-127,36,33,38,13,63,-98,-1,100,75,31,-31,114,-8,46,-93,-74,-6,98,-30,2,-109,-73,-105,51,15,26,-106,89,-26,76,-8,-54,112,108,68,4,-11,-98,34,-99,72,-20,76,-57,16,65,40,108,0,-110,53,-43,-98,-10,-18,-88,28,-117,-67,-11,-111,-42,6,-2,-35,64,22,61,65,78,116,-87,-128,-25,111,-65,115,0,89,-26,4,-16,-53,-57,-7,-89,-9,-13,-50,-80,-61,-76,41,-11,-54,10,-19,-50,-32,-25,45,66,-38,125,-86,-23,-34,-11,-66,-111,-74,-65,44,-68,-13,-63,-13,99,54,-4,6,80,75,1,2,20,0,20,0,0,0,8,0,-121,78,-30,64,-88,-15,90,115,103,1,0,0,13,5,0,0,19,0,0,0,0,0,0,0,1,0,32,0,0,0,15,41,0,0,91,67,111,110,116,101,110,116,95,84,121,112,101,115,93,46,120,109,108,80,75,1,2,20,0,10,0,0,0,0,0,-121,78,-30};
        return byte11;
    }

    @Test
    public void getInviteJoinUrlTest(){
        InviteJoinUrlGetOutput urlGetOutput = new InviteJoinUrlGetOutput();
        when(inviteJoinServiceV3.getInviteJoinUrlV3(any())).thenReturn(RpcOutput.with(urlGetOutput));
        bizTInviteServiceV3.getInviteJoinUrl("orgId",null);
    }

    @Test
    public void testGetInviteLinkUrl() {
        when(inviteJoinServiceV3.getInviteLinkUrlV3(any())).thenReturn(RpcOutput.with("1"));
        bizTInviteServiceV3.getInviteLinkUrl("1");
    }

    @Test
    public void testGetInviteAdminJoinContextData(){
        InviteRecordInfoOutput inviteRecordInfoOutput = new InviteRecordInfoOutput();
        inviteRecordInfoOutput.setInviteId("1");
        inviteRecordInfoOutput.setContextId("contextId");
        RpcOutput<InviteRecordInfoOutput> rpcOutput = RpcOutput.with(inviteRecordInfoOutput);
        when(inviteJoinServiceV3.getInviteRecordInfo(any())).thenReturn(rpcOutput);
        TInviteContextData inviteContextData = new TInviteContextData();
        inviteContextData.setInviteId("1");
        RpcOutput<TInviteContextData> rpcOutput1 = RpcOutput.with(inviteContextData);
        when(inviteService.getInviteContextData(any())).thenReturn(rpcOutput1);
        bizTInviteServiceV3.getInviteAdminJoinContextData("operatorId","contextId");
    }

    @Test
    public void testcheckInviteAdminJoin(){
        InviteRecordInfoOutput inviteRecordInfoOutput = new InviteRecordInfoOutput();
        inviteRecordInfoOutput.setInviteId("1");
        inviteRecordInfoOutput.setContextId("contextId");
        RpcOutput<InviteRecordInfoOutput> rpcOutput = RpcOutput.with(inviteRecordInfoOutput);
        when(inviteJoinServiceV3.getInviteRecordInfo(any())).thenReturn(rpcOutput);
        TInviteContextData inviteContextData = new TInviteContextData();
        inviteContextData.setInviteId("1");
        RpcOutput<TInviteContextData> rpcOutput1 = RpcOutput.with(inviteContextData);
        when(inviteService.getInviteContextData(any())).thenReturn(rpcOutput1);
        bizTInviteServiceV3.checkInviteAdminJoin("operatorId","contextId");
    }

    @Test
    public void testGetInviteAdminJoinUrl(){
        InviteJoinUrlGenerateOutput inviteJoinUrlGenerateOutput = new InviteJoinUrlGenerateOutput();
        inviteJoinUrlGenerateOutput.setUrl("url");
        inviteJoinUrlGenerateOutput.setStatus(1);
        inviteJoinUrlGenerateOutput.setValidateAll(true);
        RpcOutput<InviteJoinUrlGenerateOutput> inviteUrlOutput = RpcOutput.with(inviteJoinUrlGenerateOutput);
        when(inviteJoinServiceV3.getInviteAdminJoinUrlV3(any())).thenReturn(inviteUrlOutput);
        bizTInviteServiceV3.getInviteAdminJoinUrl("orgId","invitorId","redirectUrl");

    }


}
