package com.timevale.footstone.user.service.rest;

import com.timevale.easun.service.api.RpcICUserExtendService;
import com.timevale.easun.service.model.account.output.BizICUserLoginOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.biz.BizAccountService;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoLoginConfigServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoThirdAuthServiceImpl;
import com.timevale.footstone.user.service.model.sso.SsoAccountDetailRequest;
import com.timevale.footstone.user.service.rest.inner.SsoThirdAuthLoginInnerRest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import static org.mockito.ArgumentMatchers.any;

/**
 * Author: liujiaxing
 * Date: 2023/8/16 5:29 下午
 * Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class SsoThirdAuthLoginInnerRestMockTest {

    @InjectMocks
    SsoThirdAuthLoginInnerRest ssoThirdAuthLoginInnerRest;

    @Mock
    private BizSsoThirdAuthServiceImpl thirdAuthService;

    @Mock
    private BizAccountService accountService;

    @Mock
    RpcICUserExtendService rpcICUserExtendService;

    @Mock
    private BizSsoLoginConfigServiceImpl ssoLoginConfigService;

    @Test
    public void accountDetail() {

        Mockito.when(accountService.getUserRealNameStatus(any())).thenReturn(true);

        Mockito.when(thirdAuthService.queryAccountJoinOrganFlag(any(),any(),any())).thenReturn(true);

        BizICUserLoginOutput output = new BizICUserLoginOutput();
        output.setMobile("111111");
        output.setEmail("<EMAIL>");
        RpcOutput<BizICUserLoginOutput> rpcOutput = RpcOutput.with(output);
        Mockito.when(rpcICUserExtendService.getContactInfoByOuid(new RpcInput<>(any()))).thenReturn(rpcOutput);

        SsoAccountDetailRequest ssoAccountDetailRequest = new SsoAccountDetailRequest();
        ssoAccountDetailRequest.setSsoProvider("dingding");

        ssoThirdAuthLoginInnerRest.accountDetail("test_oid",ssoAccountDetailRequest);
    }
}
