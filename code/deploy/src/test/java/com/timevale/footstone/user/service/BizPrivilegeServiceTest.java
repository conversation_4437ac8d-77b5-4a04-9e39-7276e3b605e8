package com.timevale.footstone.user.service;

import com.google.common.collect.Lists;
import com.timevale.easun.service.model.privilege.output.BizEasunGetPrivilegesOutput;
import com.timevale.footstone.user.service.inner.impl.biz.BizPrivilegeServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.privilege.service.model.service.mods.BizPrivilegeDetail;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2024-06-25
 */
@RunWith(MockitoJUnitRunner.class)
public class BizPrivilegeServiceTest {
    @InjectMocks
    private BizPrivilegeServiceImpl bizPrivilegeService;

    @Mock
    private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;


    @Mock
    private IdAdapt idAdapt;

    @Test
    public void checkUserPrivilegeTest() {
        BizEasunGetPrivilegesOutput output = new BizEasunGetPrivilegesOutput();
        BizPrivilegeDetail privilegeDetail = new BizPrivilegeDetail();
        privilegeDetail.setTargetClassKey("MEMBER");
        privilegeDetail.setOperationPermit("QUERY");
        output.setPrivilegeList(Lists.newArrayList(privilegeDetail));
        when(rolePrivilegePlusServiceAdapt.getPrivilegesByUser(any(), any(),anyBoolean())).thenReturn(output);
        boolean result =
                bizPrivilegeService.checkUserPrivilege(
                        "testoid", "testOrgOid", "MEMBER", Lists.newArrayList("QUERY"));
        Assert.assertTrue(result);
    }

    @Test
    public void checkUserPrivilegeNoAdminTest() {
        BizEasunGetPrivilegesOutput output = new BizEasunGetPrivilegesOutput();
        BizPrivilegeDetail privilegeDetail = new BizPrivilegeDetail();
        privilegeDetail.setTargetClassKey("MEMBER");
        privilegeDetail.setOperationPermit("QUERY");
        output.setPrivilegeList(Lists.newArrayList(privilegeDetail));
        when(rolePrivilegePlusServiceAdapt.getPrivilegesByUser(any(), any(),anyBoolean())).thenReturn(output);
        boolean result =
                bizPrivilegeService.checkUserPrivilege(
                        "testoid", "testOrgOid", "ADMIN", Lists.newArrayList("ALL"));
        Assert.assertFalse(result);
    }


}
