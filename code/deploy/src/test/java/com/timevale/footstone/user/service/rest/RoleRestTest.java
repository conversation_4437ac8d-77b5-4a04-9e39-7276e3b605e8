package com.timevale.footstone.user.service.rest;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.easun.service.model.role.output.RoleIsolationOutput;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.role.request.IsolateAdminRequest;
import com.timevale.footstone.user.service.model.role.response.IsolateRoleResponse;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.privilege.service.enums.BuiltinRoleType;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import javax.servlet.http.HttpServletRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RoleRestTest {

    // 步骤1：定义被测试对象和依赖
    @InjectMocks
    private RoleRest roleRest;

    @Mock
    private BizRoleService bizRoleService;
    
    @Mock
    private AuditLogCommon auditLogCommon;
    
    @Mock
    private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;

    // 步骤3：定义测试数据
    private static final String INPUT_ORG_ID = "org_123";
    private static final String INPUT_OPERATOR_ID = "op_456";
    private static final String INPUT_ROLE_ID = "role_789";
    private static final boolean EXPECTED_RESULT = true;
    private static final String EXPECTED_ROLE_TYPE = BuiltinRoleType.ADMIN;

    private static final IsolateAdminRequest VALID_REQUEST = new IsolateAdminRequest();
    static {
        VALID_REQUEST.setOrgId(INPUT_ORG_ID);
    }

    private final BizRoleDetail mockRoleDetail = new BizRoleDetail();
    {
        mockRoleDetail.setRoleId(INPUT_ROLE_ID);
    }

    // 步骤2：准备测试场景
    @Before
    public void setUp() {
        // 公共mock配置
        when(bizRoleService.getRoleByRoleKey(INPUT_ORG_ID, EXPECTED_ROLE_TYPE))
            .thenReturn(mockRoleDetail);
        
        // 隔离操作成功场景
        RoleIsolationOutput successOutput = new RoleIsolationOutput();
        successOutput.setResult(EXPECTED_RESULT);
        when(rolePrivilegePlusServiceAdapt.isolateAdmin(
            INPUT_ORG_ID, INPUT_OPERATOR_ID, EXPECTED_ROLE_TYPE))
            .thenReturn(successOutput);
        
        // 解除隔离成功场景
        RoleIsolationOutput unIsolateOutput = new RoleIsolationOutput();
        unIsolateOutput.setResult(EXPECTED_RESULT);
        when(rolePrivilegePlusServiceAdapt.unIsolateAdmin(
            INPUT_ORG_ID, INPUT_OPERATOR_ID, EXPECTED_ROLE_TYPE))
            .thenReturn(unIsolateOutput);
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_TENANT_ID,INPUT_ORG_ID);
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,INPUT_OPERATOR_ID);
        RequestContext.put("httpServletRequest",servletRequest);
    }

    // 步骤4：测试用例 - 正常隔离场景
    @Test
    public void testIsolateAdmins_WhenSuccess() {
        // 执行测试
        WrapperResponse<IsolateRoleResponse> response =
            roleRest.isolateAdmins(INPUT_ORG_ID);

        // 验证结果
        assertTrue("响应类型错误", response.getData() instanceof IsolateRoleResponse);
        assertEquals("隔离结果不匹配", 
            EXPECTED_RESULT, response.getData().isResult());

    }

    // 测试用例 - 角色不存在场景
    @Test(expected = RuntimeException.class)
    public void testIsolateAdmins_WhenRoleNotFound() {
        // 配置异常场景
        when(bizRoleService.getRoleByRoleKey(INPUT_ORG_ID, EXPECTED_ROLE_TYPE))
            .thenReturn(null);

        roleRest.isolateAdmins("orgId");
    }

    // 测试用例 - 正常解除隔离场景 
    @Test
    public void testUnIsolateAdmins_WhenSuccess() {
        WrapperResponse<IsolateRoleResponse> response = 
            roleRest.unIsolateAdmins(INPUT_ORG_ID);

        assertTrue("响应类型错误", response.getData() instanceof IsolateRoleResponse);
        assertEquals("解除隔离结果不匹配",
            EXPECTED_RESULT, response.getData().isResult());

    }

    // 步骤5：对象正确性验证
    @Test
    public void testDependencyInjection() {
        assertNotNull("BizRoleService注入失败", 
            ReflectionTestUtils.getField(roleRest, "bizRoleService"));
        assertNotNull("RpcRolePrivilegePlusServiceAdapt注入失败",
            ReflectionTestUtils.getField(roleRest, "rolePrivilegePlusServiceAdapt"));
    }
}
