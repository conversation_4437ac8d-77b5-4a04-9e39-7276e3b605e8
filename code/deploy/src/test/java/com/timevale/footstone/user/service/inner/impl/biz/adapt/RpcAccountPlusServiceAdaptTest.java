package com.timevale.footstone.user.service.inner.impl.biz.adapt;

import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcEsAccountService;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountPlusServiceAdapt;
import com.timevale.footstone.user.service.utils.MockUntils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/8/1 14:57
 */
@RunWith(MockitoJUnitRunner.class)
public class RpcAccountPlusServiceAdaptTest {

    @InjectMocks
    private RpcAccountPlusServiceAdapt rpcAccountPlusServiceAdapt;

    @Mock
    private RpcEsAccountService esAccountService;

    @Before
    public void setUp() {
        EasunEsearchAccountOutput accountOutput = MockUntils.getAccountOutPut();
        when(esAccountService.getEsAccount(any())).thenReturn(RpcOutput.with(MockUntils
                .mockPageResultEsSearchAccount(Lists.newArrayList(accountOutput))));
        when(esAccountService.getOrgsByEs(any())).thenReturn(RpcOutput.with(MockUntils
                .mockPageResultEsSearchAccount(Lists.newArrayList(MockUntils.mockEsOrgSearhOutput()))));
    }

    @Test
    public void getOrgsByCodeTest() {
        rpcAccountPlusServiceAdapt.getOrgsByCode("codeValue", "codeType", 0, 10
                , Lists.newArrayList("appId"));
    }

    @Test
    public void getAccountByIdCodeTest() {
        rpcAccountPlusServiceAdapt.getAccountByIdCode("codeValue", "codeType", 0, 10
                , Lists.newArrayList("appId"));
    }
}
