package com.timevale.footstone.user.service.inner.impl;

import com.timevale.footstone.user.service.inner.biz.BizAccountService;
import com.timevale.footstone.user.service.model.mods.response.OrganIdResponse;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2021/11/18
 */
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizMemberServiceV2ImplTest {

    @Mock
    private BizMemberServiceV2Impl memberServiceV2;
    @Mock
    private BizMemberServiceImpl memberService;
    @Mock
    private BizAccountService bizAccountService;
    @Test
    public void test_listMembersPageable() {
        memberServiceV2.listMembersPageable("e8316ad4578940febb4d68788f21276d", 0, 20);
    }

    @Test
    public void test_listAdministrators() {
        try {
            memberService.listAdministrators("e8316ad4578940febb4d68788f21276d", 0, 20);
        }catch (Exception e){

        }
    }
    @Test
    public void test1(){
        OrganIdResponse response = new OrganIdResponse();
        response.setOrganId("organId");
        when(bizAccountService.getMainOrg(any())).thenReturn(response);
        String mainOrgId = bizAccountService.getMainOrg("c266e7d68d984d899f6551124c14a8ca").getOrganId();

    }
}
