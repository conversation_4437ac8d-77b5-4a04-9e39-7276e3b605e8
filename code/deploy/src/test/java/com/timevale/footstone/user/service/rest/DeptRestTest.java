package com.timevale.footstone.user.service.rest;

import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizDeptService;
import com.timevale.footstone.user.service.model.dept.request.BatchCreateDeptRequest;
import com.timevale.footstone.user.service.model.dept.request.CreateDeptRequest;
import com.timevale.footstone.user.service.model.dept.request.UpdateDeptRequest;
import javax.servlet.http.HttpServletRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2023/3/7 16:23
 * @Description
 */
@RunWith(MockitoJUnitRunner.class)
public class DeptRestTest {

  @InjectMocks
  private DeptRest mockDeptRest;

  @Mock
  private BizDeptService service;

  @Mock
  private HttpServletRequest servletRequest;

  @Mock
  private AuditLogCommon auditLogCommon;

  @Test
  public void createDept() {
    this.mockDeptRest.createDept("mockOrg1", new CreateDeptRequest());
  }

  @Test
  public void updateDept() {
    this.mockDeptRest.updateDept("mockOrg1", "mockDept1", new UpdateDeptRequest());
  }

  @Test
  public void deleteDept() {
    this.mockDeptRest.deleteDept("mockOrg1", "mockDept1");
  }

  @Test
  public void batchCreate() {
    this.mockDeptRest.batchCreate("mockOrg1", new BatchCreateDeptRequest());
  }
}