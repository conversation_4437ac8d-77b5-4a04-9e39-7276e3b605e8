package com.timevale.footstone.user.service.inner.impl.biz;

import com.google.common.collect.Lists;
import com.timevale.account.organization.service.model.service.newbiz.output.BizGetOrganOutput;
import com.timevale.account.service.enums.AccountType;
import com.timevale.account.service.exception.Errors.AccIdcardNotExist;
import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.icuser.ICAccountUser;
import com.timevale.easun.service.exception.EasunErrors.OrganCreationError;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.esign.compontent.common.base.exception.PaasBaseRuntimeException;
import com.timevale.esign.platform.toolkit.utils.exceptions.BizException;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.CredentialValidatorUtil;
import com.timevale.footstone.user.service.configuration.IsolationConfig;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAuthenticationServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.model.account.request.OrgAddByUserIdRequest;
import com.timevale.footstone.user.service.model.account.request.OrgCreateByThirdPartyUserId;
import com.timevale.footstone.user.service.model.account.response.OrgRealNameBaseInfoResponse;
import com.timevale.footstone.user.service.model.account.response.PersonGetV1Response;
import com.timevale.footstone.user.service.model.organization.request.ChangeAdminUrlParam;
import com.timevale.footstone.user.service.model.organization.response.ChangeAdminUrlResponse;
import com.timevale.footstone.user.service.rest.OpenRest;
import com.timevale.footstone.user.service.utils.FormatCheckUtil;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.AssertUtil;
import com.timevale.open.platform.oauth.service.result.Response;
import com.timevale.open.platform.service.service.api.AppPropertyService;
import com.timevale.open.platform.service.service.utils.AuthUtils;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2021/3/11
 **/
@RunWith(MockitoJUnitRunner.Silent.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizOpenApiServiceImplTest {

  @InjectMocks
  private BizOpenApiServiceImpl openApiService;
  @Mock
  private RpcOrgPlusServiceAdapt rpcOrgPlusService;
  @Mock
  private RpcAuthenticationServiceAdapt authenticationService;
  @Mock
  private RpcIcUserPlusServiceAdapt icUserPlusService;
  @Mock
  private RpcInnerOrgServiceAdapt innerOrgServiceAdapt;

  @Mock
  private BizOrganServiceImpl organService;

  @Mock
  private IdAdapt idAdapt;

  @Mock
  private BizOpenApiServiceImpl bizOpenApiService;

  @InjectMocks
  private OpenRest rest;

  @InjectMocks
  private BizOrganServiceImpl bizOrganService;

  @Mock
  private IsolationConfig isolationConfig;

  @Mock
  private RpcIcOrgPlusServiceAdapt icOrgPlusService;

  @Mock SyncAccountServiceImpl syncAccountService;

  @Mock
  private AppPropertyService appPropertyService;


  @Mock
  private RpcAccountPlusServiceAdapt accountPlusServiceAdapt;

  String appId  = "7488";
  String appRequestHeader = "X-Tsign-Open-App-Id";

  @Before
  public void setUp(){
    when(icUserPlusService.getICUserByOuid(any())).thenReturn(MockUntils.mockBizICUserOutput());
  }


  @Test
  public void testGetUser() {
    when(icUserPlusService.getRealNameByOuid(any())).thenReturn(MockUntils.getRealNameOutput());
    final PersonGetV1Response user = openApiService.getUser(() -> "x");
  }

  @Test(expected = PaasBaseRuntimeException.class)
  public void test_createOrgByIdNo_when_happen_OrganCreationError() {
    BizICUserBaseOutput output = new BizICUserBaseOutput();
    AccountBaseDetail base = new AccountBaseDetail();
    base.setType(AccountType.PERSON);
    ICAccountUser accout = new ICAccountUser();
    accout.setAccountUid("1");
    output.setBase(base);
    output.setId(accout);
    when(rpcOrgPlusService.createICOrgAndCompany(any())).thenThrow(new OrganCreationError());
    when(authenticationService.checkProjThirdparty(any(), any())).thenThrow(new AccIdcardNotExist());
    when(icUserPlusService.getICUserBaseByOuid(any())).thenReturn(output);

    OrgAddByUserIdRequest request = new OrgAddByUserIdRequest();
    request.setThirdPartyUserId("1");
    openApiService.createOrgByUserId(request);
  }

    @Test
    public void createOrgByThirdPartyUserId_UnsupportedCredentialsType_ThrowsCredentialsTypeNotSupport() {
        OrgCreateByThirdPartyUserId request = new OrgCreateByThirdPartyUserId();
        request.setThirdPartyUserId("newUserId");
        request.setOrgLegalIdType("unsupportedType");

        when(authenticationService.checkProjThirdparty(any(), any())).thenThrow(new AccIdcardNotExist());
        try {
            openApiService.createOrgByThirdPartyUserId(request);
        }catch (BizException e) {
            Assert.assertEquals(e.getCode(), new FootstoneUserErrors.CredentialsTypeNotSupport().getCode());
        }
    }

    @Test
    public void createOrgByThirdPartyUserId_InvalidIdNumber_ThrowsParamFormatError() {
        OrgCreateByThirdPartyUserId request = new OrgCreateByThirdPartyUserId();
        request.setThirdPartyUserId("newUserId");
        request.setOrgLegalIdType(BuiltinCredentials.CRED_PSN_CH_IDCARD);
        request.setOrgLegalIdNumber("invalidNumber");
        when(authenticationService.checkProjThirdparty(any(), any())).thenThrow(new AccIdcardNotExist());
        try (MockedStatic<CredentialValidatorUtil> mockedStatic =
                     Mockito.mockStatic(CredentialValidatorUtil.class)) {
            mockedStatic.when(() -> CredentialValidatorUtil.validate(anyString(), anyString())).thenReturn(false);
        }
        try {
            openApiService.createOrgByThirdPartyUserId(request);
        }catch (BaseBizRuntimeException e) {
            Assert.assertEquals(e.getCode(), new FootstoneUserErrors.ParamFormatError("").getCode());
        }
    }


  @Test
  public void test_delete_org() {
    /*
      当创建组织时，发生异常(OrganCreationError), 需抛出异常`PaasBaseRuntimeException(OpenTreatyErrorCodeEnum.ACCOUNT_CREATE_FAIL, "创建组织失败")`
     */

    try {

      BizICUserBaseOutput output = new BizICUserBaseOutput();
      AccountBaseDetail base = new AccountBaseDetail();
      base.setType(AccountType.ORGANIZE);
      ICAccountUser accout = new ICAccountUser();
      accout.setAccountUid("1");
      accout.setOuid("2");
      output.setBase(base);
      output.setId(accout);
      OrgRealNameBaseInfoResponse response = new OrgRealNameBaseInfoResponse();
      response.setIsRealnameOrg(true);
      when(bizOpenApiService.assertAccountId(anyString(), any())).thenReturn("sdajg");
      when(organService.isActivateRealNameOrg(anyString())).thenReturn(true);
      BizGetOrganOutput organOutput = new BizGetOrganOutput();
      organOutput.setOrganGuid(UUID.randomUUID().toString().replace("-", ""));
      when(innerOrgServiceAdapt.getOrganByAccount(any())).thenReturn(organOutput);
      when(idAdapt.accountUid(any())).thenReturn("1");
//      when(isolationConfig.isolationSwitch(anyString())).thenReturn(true);
//      when(isolationConfig.isolationSwitch()).thenReturn(true);
      ReflectionTestUtils.setField(isolationConfig,"isolationSwitch",true);
      when(idAdapt.accountUid(any())).thenReturn("1");
//      when(icOrgPlusService.deleteOrg(any())).thenReturn()
//      bizOrganService.isActivateRealNameOrg("1");
      bizOrganService.isActivateRealNameOrgCheckActivate("1");
      String appId = "126844";
      openApiService.deleteOrg(() -> bizOpenApiService.assertAccountId("2", AccountType.ORGANIZE), appId);
      rest.cancelOrg("1");
    } catch (Exception e) {
      System.out.println("====" + e);
    }
  }

  @Test
  public void getByIdNumTest(){
    ReflectionTestUtils.setField(bizOpenApiService,"maxPageSize",10);
    Map<String, String> appProperties = new HashMap<>();
    appProperties.put("queryOtherAppUser","7488");
    when(appPropertyService.getAppPropertiesV2(any())).thenReturn(com.timevale.open.platform.service.service.model.response.Response.with(appProperties));
    openApiService.getByIdNumber("idtype","idNum",10,1,"appId",AccountType.PERSON);
  }


  @Test
  public void testGetChangeAdminUrl(){
    ChangeAdminUrlParam param = new ChangeAdminUrlParam();
    param.setOrgId("orgId");
    param.setFlowId("flowId");
    param.setRedirectUrl("redirectUrl");
    param.setInvitorId("invitorId");
    ReflectionTestUtils.setField(openApiService,"decorateName",true);
    ChangeAdminUrlResponse response = openApiService.getChangeAdminUrl(param);
    Assert.assertTrue(response!=null);
  }
}