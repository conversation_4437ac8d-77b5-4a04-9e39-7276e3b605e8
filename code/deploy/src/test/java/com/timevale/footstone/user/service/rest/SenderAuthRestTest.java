package com.timevale.footstone.user.service.rest;

import com.timevale.MyTestHttpServletRequest;
import com.timevale.easun.service.api.RpcSenderAuthService;
import com.timevale.easun.service.model.sender.output.BizApplyWillAssembleAuthOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.inner.impl.biz.conv.SenderAuthConv;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.senderauth.request.ResetPwdRequest;
import com.timevale.footstone.user.service.model.account.senderauth.response.SecurityPassResonse;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(MockitoJUnitRunner.Silent.class)
public class SenderAuthRestTest {

    private MockMvc mockMvc;

    @Mock
    private RpcSenderAuthService svc;

    @InjectMocks
    private SenderAuthRest senderAuthRest;

    HttpServletRequest servletRequest;

    @Before
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(senderAuthRest).build();
        servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR,"operator");
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,
                UUID.randomUUID().toString().replace("-",""));
        servletRequest.setAttribute(HeaderConstants.HEADER_APP_ID,UUID.randomUUID().toString().replace("-",""));
        servletRequest.setAttribute(HeaderConstants.HEADER_OPERATOR_ID,"operator");
        ReflectionTestUtils.setField(senderAuthRest,"servletRequest",servletRequest);

    }

    @Test
    public void setupPwd_ValidRequest_ReturnsEmptyWrapperResponse() throws Exception {
        ResetPwdRequest request = new ResetPwdRequest();
        request.setServiceId("testServiceId");
        request.setNewPassword("newPassword");

        // 创建一个空的 RpcOutput 对象来模拟返回值
        RpcOutput<?> rpcOutput = new RpcOutput<>();
        when(svc.senderResetPwdExecute(any(RpcInput.class))).thenReturn(rpcOutput);


        senderAuthRest.setupPwd( request);
    }

    @Test
    public void setupPwdServiceId_ValidRequest_ReturnsSecurityPassResponse() throws Exception {
        BizApplyWillAssembleAuthOutput output = new BizApplyWillAssembleAuthOutput();
        output.setServiceId("testServiceId");

        RpcOutput<BizApplyWillAssembleAuthOutput> rpcOutput = RpcOutput.with(output);
//        rpcOutput.setData(output);

        when(svc.assembleApplySecurityPassToken(any(RpcInput.class))).thenReturn(rpcOutput);

        senderAuthRest.securityPassToken("bizType");
    }
}
