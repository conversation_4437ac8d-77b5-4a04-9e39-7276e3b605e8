package com.timevale.footstone.user.service.inner.impl.biz;

import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcDeptPlusService;
import com.timevale.easun.service.api.RpcInviteService;
import com.timevale.easun.service.api.RpcTInvitePlusService;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.identity.common.service.api.scene.adapter.fromv1.V2IndividualAuthenticationService;
import com.timevale.footstone.identity.common.service.api.scene.webpage.RpcWebIdentityAuthService;
import com.timevale.footstone.user.service.async.TaskUtil;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.biz.BizAccountService;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.biz.BizOrganService;
import com.timevale.footstone.user.service.inner.biz.BizRealNameService;
import com.timevale.footstone.user.service.inner.biz.v3.BizMemberServiceV3;
import com.timevale.footstone.user.service.inner.biz.v3.BizOrganizationServiceV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.FileSystemServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.model.dept.response.v3.OrganizationDataSyncResponseV3;
import com.timevale.footstone.user.service.model.invite.mods.InviteJoinPerson;
import com.timevale.footstone.user.service.model.invite.request.BatchInviteJoinRequest;
import com.timevale.footstone.user.service.model.invite.request.ConfirmJoinRequest;
import com.timevale.footstone.user.service.mq.RocketMqClient;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.framework.tedis.core.RedisOperations;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.BaseUtil;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.notice.service.api.RpcInviteJoinService;
import com.timevale.notice.service.api.RpcInviteJoinServiceV3;
import com.timevale.notice.service.api.RpcTInviteScheduleService;
import com.timevale.notice.service.api.RpcTInviteService;
import com.timevale.notice.service.model.service.biz.output.ExistUnfinishedInviteTaskOutput;
import com.timevale.notificationmanager.service.model.SendMessageRequest;
import com.timevale.open.platform.service.service.api.AppPropertyService;
import com.timevale.open.platform.service.service.api.AppService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/8/8 10:22
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class BizTinviteServiceImplTest {

    @InjectMocks
    private BizTinviteServiceImpl bizTinviteService;

    @Mock
    private AuditLogCommon auditLogCommon;

    @Mock
    RpcTInvitePlusService tInvitePlusService;

    @Mock
    private RpcRolePrivilegePlusServiceAdapt rpcrolePrivilegePlusServiceAdapt;

    @Mock
    RpcTInviteService inviteService;

    @Mock
    RpcTInviteScheduleService scheduleService;

    @Mock
    RpcInviteJoinService inviteJoinService;

    @Mock
    RpcInviteJoinServiceV3 inviteJoinServiceV3;

    @Mock
    BizAccountService accountService;

    @Mock
    AppPropertyService appPropertyService;

    @Mock
    AppService appService;

    @Mock
    private IdAdapt idAdapt;

    @Mock
    private BizOrganService bizOrganService;

    @Mock
    BizMemberService memberService;

    @Mock
    private BizMemberServiceV3 bizMemberServiceV3;

    @Mock
    private FileSystemServiceAdapt fileSystemServiceAdapte;
    @Mock
    RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;
    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusServiceAdapt;
    @Mock
    RocketMqClient<SendMessageRequest> rocketMqClient;

    @Mock
    private Tedis tedis;

    @Mock
    private TedisUtil tedisUtil;

    @Mock
    RpcAccountPlusServiceAdapt accountPlusServiceAdapt;

    @Mock
    RpcDeptPlusService deptPlusService;

    @Mock
    V2IndividualAuthenticationService v2IndividualAuthenticationService;

    @Mock
    RpcWebIdentityAuthService webIdentityAuthService;
    @Mock
    BizAccountService bizAccountService;
    @Mock
    BizRealNameService bizRealNameService;
    @Mock
    SystemProperty systemProperty;

    @Mock
    private RpcInviteService rpcInviteService;




    String notificationTopic;


    String notificationTag;


    String defaultBizRedirectUrl;


    String realNameBizRedirectUrl;


    Integer inviteBatchUploadLimit;

    public static final String CustomDomainPrefix = "customDomainPrefix";

    @Mock
    private BizOrganizationServiceV3 bizOrganizationServiceV3;


    @Before
    public void setUp(){
        ExistUnfinishedInviteTaskOutput existedInviteTask = new ExistUnfinishedInviteTaskOutput();
        existedInviteTask.setExisted(true);
        existedInviteTask.setInviteId("inviteId");
//        when(inviteJoinServiceV3
//                .existUnfinishedInviteTask(any())).thenReturn(RpcOutput.with(existedInviteTask));
        StringOperations<String,String> tedisString = new MyDefaultStringOperations();
        when(tedis.string()).thenReturn(tedisString);
        TedisUtil.setTedis(tedis);
        PagerResult pagerResult = new PagerResult();
        pagerResult.setItems(Lists.newArrayList(MockUntils.getAccountOutPut()));
        pagerResult.setTotal(1);
        when(rpcInviteService.getThirdOrgSyncConfirmUrl(any())).thenReturn(RpcOutput.with("success"));
//        when(accountPlusServiceAdapt.getEsearchResult(anyList(),anyList()))
//                .thenReturn(pagerResult);
        OrganizationDataSyncResponseV3 responseV3 = new OrganizationDataSyncResponseV3();
        responseV3.setIsOpenThirdSync(false);
        when(this.bizOrganizationServiceV3.getOrganHasDataSync(anyString())).thenReturn(responseV3);
        when(inviteJoinServiceV3
                .existUnfinishedInviteTask(any())).thenReturn(RpcOutput.with(existedInviteTask));

    }


    @Test
    public void bachInviteJoinV3Test(){
//        when(bizMemberServiceV3.idcardExistInTheOrg(anyString(), anyString(), anyString())).thenReturn(false);
        BatchInviteJoinRequest request = new BatchInviteJoinRequest();
        List<InviteJoinPerson> invitedPersons = new ArrayList<>();
        InviteJoinPerson person = new InviteJoinPerson();
        person.setMobile("130********");
        Map<String,String> memberInfo = new HashMap<>();
        memberInfo.put("ownerOrgId","orgId");
        person.setMemberInfo(memberInfo);
        invitedPersons.add(person);
        request.setInvitedPersons(invitedPersons);
        bizTinviteService.bachInviteJoinV3(request,"orgId","operatorId");
    }

    @Test
    public void getThirdOrgSyncConfirmUrlTest(){
        bizTinviteService.getThirdOrgSyncConfirmUrl(new ConfirmJoinRequest());
    }
}
