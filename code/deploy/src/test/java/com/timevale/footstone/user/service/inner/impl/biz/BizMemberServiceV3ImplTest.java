package com.timevale.footstone.user.service.inner.impl.biz;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

import com.timevale.account.organization.service.exception.OrganErrors;
import com.timevale.account.service.model.service.biz.icuser.output.BizFatICUserOutput;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.account.service.model.service.mods.idcard.MultiIdcardContentCollection;
import com.timevale.besp.lowcode.integration.response.OrgResponse;
import com.timevale.easun.service.api.RpcEsAccountService;
import com.timevale.easun.service.model.IdcardTypeEnum;
import com.timevale.easun.service.model.account.output.BatchSpaceOutput;
import com.timevale.easun.service.model.account.output.EasunSpaceListItemOutput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdCardDetailOutput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.component.SaasTaskComponent;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.constants.InviteJoinMemberConstants;
import com.timevale.footstone.user.service.inner.audit.AuditLogCommon;
import com.timevale.footstone.user.service.inner.audit.organization.AuditLogExportMemberService;
import com.timevale.footstone.user.service.inner.biz.BizAccountService;
import com.timevale.footstone.user.service.inner.biz.BizMemberService;
import com.timevale.footstone.user.service.inner.biz.BizRoleService;
import com.timevale.footstone.user.service.inner.biz.BizThirdPartyOrgSyncService;
import com.timevale.footstone.user.service.inner.biz.v3.BizOrganizationServiceV3;
import com.timevale.footstone.user.service.inner.biz.v3.ThirdPlatformNameSearchService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.FileSystemServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcEsAccountServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcDeptPlusServiceAdaptV3;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrganSpaceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSGrayConfigAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRolePrivilegePlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizMemberServiceV3Impl;
import com.timevale.footstone.user.service.model.account.response.UserInfoResponse;
import com.timevale.footstone.user.service.model.dept.response.v3.OrganizationDataSyncResponseV3;
import com.timevale.footstone.user.service.model.invite.mods.InviteJoinPerson;
import com.timevale.footstone.user.service.model.member.mods.DeptSubListResponse;
import com.timevale.footstone.user.service.model.member.mods.MemberBaseResponse;
import com.timevale.footstone.user.service.model.member.request.ListByDeptParam;
import com.timevale.footstone.user.service.model.member.request.SearchMemberRequest;
import com.timevale.footstone.user.service.model.member.request.v3.CopyMembersFromUnitsRequest;
import com.timevale.footstone.user.service.model.member.request.v3.CopyToRelatedUnitsRequest;
import com.timevale.footstone.user.service.model.member.request.v3.DeleteMemberBatchRequest;
import com.timevale.footstone.user.service.model.member.request.v3.DeptAndOrgRequest;
import com.timevale.footstone.user.service.model.member.request.v3.GrantUsersRolesRequest;
import com.timevale.footstone.user.service.model.member.request.v3.MemberAndOrgRequest;
import com.timevale.footstone.user.service.model.member.request.v3.RevokeUsersRoleRequest;
import com.timevale.footstone.user.service.model.role.model.RoleModel;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @date 2023/6/6 22:13
 */

@RunWith(MockitoJUnitRunner.class)
public class BizMemberServiceV3ImplTest {

    @InjectMocks
    private BizMemberServiceV3Impl bizMemberServiceV3;


    @Mock
    private RpcOrganSpaceAdapt rpcOrganSpaceAdapt;

    @Mock
    private BizMemberService bizMemberService;

    @Mock
    private BizAccountService bizAccountService;

    @Mock
    private RpcEsAccountServiceAdapt esAccountServiceAdapt;

    @Mock
    private RpcDeptPlusServiceAdaptV3 deptPlusServiceAdaptV3;

    @Mock
    private RpcIcOrgPlusServiceAdapt orgPlusServiceAdapt;

    @Mock
    private BizOrganizationServiceV3 bizOrganizationServiceV3;

    @Mock
    private RpcRoleServiceAdapt roleServiceAdapt;

    @Mock
    private RpcDeptPlusServiceAdapt rpcDeptPlusServiceAdapt;

    @Mock
    private AuditLogExportMemberService auditLogExportMemberService;

    @Mock
    private RpcRolePrivilegePlusServiceAdapt rolePrivilegePlusServiceAdapt;

    @Mock
    private BizRoleService bizRoleService;

    @Mock
    private SaasTaskComponent saasTaskComponent;

    @Mock
    private FileSystemServiceAdapt fileSystemServiceAdapt;

    @Mock
    private RpcSaaSVipServiceAdapt rpcSaasVipServiceAdapt;

    @Mock
    private IdAdapt idAdapt;

    @Mock
    private RpcSaaSGrayConfigAdapt rpcSaaSGrayConfigAdapt;

    @Mock
    private ThirdPlatformNameSearchService thirdPlatformNameSearchService;

    @Value("${members.export.max.count:100}")
    int membersExportMaxCount;

    @Value("${members.export.page.size:50}")
    int membersExportPageSize;

    @Value("${members.revoke.role.max.count:20}")
    int membersRevokeRoleMaxCount;

    @Mock
    private CommonConfig config;


    @Mock
    private AuditLogCommon auditLogCommon;

    @Mock
    private BizThirdPartyOrgSyncService bizThirdPartyOrgSyncService;

    @Mock
    private RpcIcUserPlusServiceAdapt rpcIcUserPlusServiceAdapt;

    @Mock
    private RpcEsAccountService esService;

    @Mock
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;



    @Before
    public void setUp() {
        PagerResult<MemberDetail> pagerResult = new PagerResult<>();
        List<MemberDetail> items = new ArrayList<>();
        items.add(MockUntils.getMemberDetail());
        pagerResult.setTotal(1);
        when(esAccountServiceAdapt.getMemberListByDept(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(pagerResult);
        ReflectionTestUtils.setField(bizMemberServiceV3, "memberCopyTaskPartition", 10);
        ReflectionTestUtils.setField(bizMemberServiceV3, "roleBlackList", "SPACE_ADMIN,ADMIN,ORGAN_LEGAL");
        ReflectionTestUtils.setField(bizMemberServiceV3, "membersExportMaxCount", 10);
        ReflectionTestUtils.setField(bizMemberServiceV3, "membersExportPageSize", 1);
        PagerResult pagerResult1 = new PagerResult();
        List<BizRoleDetail> roles = new ArrayList<>();
        BizRoleDetail role = new BizRoleDetail();
        role.setRoleKey("MEMBER");
        role.setRoleId("role");
        roles.add(role);
        pagerResult1.setItems(roles);
        when(idAdapt.orgIdToOrganId(any())).thenReturn("organId");
        when(roleServiceAdapt.getPagedBuiltinRolesByOrgan(any(), any(), any(), any())).thenReturn(pagerResult1);
        OrganizationDataSyncResponseV3 responseV3 = new OrganizationDataSyncResponseV3();
        responseV3.setIsOpenThirdSync(false);
        when(this.bizOrganizationServiceV3.getOrganHasDataSync(anyString())).thenReturn(responseV3);


        BizFatICUserOutput bizFatICUserOutput = new BizFatICUserOutput();
        MultiIdcardContentCollection<ContentSecurity> idcards = new MultiIdcardContentCollection<ContentSecurity>();
        List<IdcardContentThirdparty<ContentSecurity>> idcardsList = new ArrayList<>();
        IdcardContentThirdparty<ContentSecurity> idcardContentThirdparty = new IdcardContentThirdparty<>();
        IdcardThirdparty idcardThirdparty = new IdcardThirdparty();
        idcardThirdparty.setThirdpartyUserType("userType");
        idcardThirdparty.setThirdpartyUserId("TK");
        idcardThirdparty.setThirdpartyKey("FEI_SHU_TENANT");
        idcardContentThirdparty.setDetail(idcardThirdparty);
        idcardsList.add(idcardContentThirdparty);
        idcards.addThirdparty(idcardContentThirdparty);
        bizFatICUserOutput.setIdcards(idcards);
//        when(rpcIcUserPlusServiceAdapt.getFatICUserByOuid("organOuid")).thenReturn(bizFatICUserOutput);
    }


    @Test
    public void memberListByKeywordV3Test() {
//    when(deptPlusServiceAdaptV3.getDeptFullNameByDeptId(any())).thenReturn("fullname");
        SearchMemberRequest request = new SearchMemberRequest();
        bizMemberServiceV3.memberListByKeywordV3("orgId", request);
    }

    @Test
    public void searchMembersSupportMultiOrg() {
        SearchMemberRequest request = new SearchMemberRequest();
        request.setKeyword("haha");
        when(this.esAccountServiceAdapt
                .searchMembersSupportMultiOrg(anyString(), anyString(),any(), anyBoolean(),
                        any(), any()))
                .thenReturn(MockUntils.getPagerResultMemberDetail());
        this.bizMemberServiceV3.searchMembersSupportMultiOrg("orgId", true, request,null);
    }


    @Test
    public void memberDeptUnionSearchTest() {
        when(orgPlusServiceAdapt.needMultiOrgData("orgId", true)).thenReturn(true);
        bizMemberServiceV3.memberDeptUnionSearch("orgId", "", "request", true, "");
    }

    @Test
    public void thirdPartyMemberDeptUnionSearchTest() {
//        when(orgPlusServiceAdapt.needMultiOrgData("orgId", true)).thenReturn(true);
        when(bizThirdPartyOrgSyncService.getThirdPartyUserId("orgId", "FEI_SHU")).thenReturn("tenantKey");
        PagerResult<EsAccountIdcardInfoOutput> pagerResult = new PagerResult<>();
        List<EsAccountIdcardInfoOutput> list = Lists.newArrayList();
        EsAccountIdcardInfoOutput esAccountIdcardInfoOutput = new EsAccountIdcardInfoOutput();
        EsAccountIdCardDetailOutput idcardInfo = new EsAccountIdCardDetailOutput();
        idcardInfo.setKey("key");
        idcardInfo.setValue("value");
        esAccountIdcardInfoOutput.setIdcardInfo(idcardInfo);
        list.add(esAccountIdcardInfoOutput);
        pagerResult.setItems(list);
        RpcOutput<PagerResult<EsAccountIdcardInfoOutput>> accountIdcardInfos = RpcOutput.with(pagerResult);
        when(esService.getAccountIdcardInfoByEs(any())).thenReturn(accountIdcardInfos);
        List<OrgResponse> responses = com.google.common.collect.Lists.newArrayList();
        OrgResponse response = new OrgResponse();
        response.setName("name");
        response.setParentId("ALL");
        response.setHasChild(true);
        responses.add(response);
        when(rpcTripartiteServiceAdapt.searchDeptList("tenantKey","value","FEI_SHU","request")).thenReturn(responses);
        bizMemberServiceV3.memberDeptUnionSearch("orgId", "operatorId", "request", true, "FEI_SHU");
    }

    @Test
    public void copyMembersFromUnitsRequest() {
        CopyMembersFromUnitsRequest request = new CopyMembersFromUnitsRequest();
        request.setTargetOrgId("targetOrgId");
        request.setTargetDeptIds(Lists.newArrayList());

        MemberAndOrgRequest memberAndOrgRequest = new MemberAndOrgRequest();
        memberAndOrgRequest.setFromMemberOid("fromMemberOid");
        memberAndOrgRequest.setFromOrgId("fromOrgId");
        memberAndOrgRequest.setFromOrgName("fromOrgName");
        memberAndOrgRequest.setMemberAccountUid("memberAccountUid");
        request.setMembers(Lists.newArrayList(memberAndOrgRequest));

        when(this.orgPlusServiceAdapt.isRootMultiOrgan(anyString())).thenReturn(true);

        this.bizMemberServiceV3.copyMembersFromUnitsRequest("orgId", "targetOrgId", request);
    }

    @Test
    public void copyToRelatedUnits() {
        CopyToRelatedUnitsRequest request = new CopyToRelatedUnitsRequest();
        request.setMemberOid("memberOid");
        request.setAssignRoleIds(Lists.newArrayList("roleId1"));
        DeptAndOrgRequest deptAndOrgRequest = new DeptAndOrgRequest();
        deptAndOrgRequest.setDeptId("deptId");
        deptAndOrgRequest.setOwnerOrgId("ownerOrgId");
        deptAndOrgRequest.setOwnerOrgName("ownerOrgName");
        request.setTargetDepts(Lists.newArrayList(deptAndOrgRequest));
        when(this.orgPlusServiceAdapt.isRootMultiOrgan(anyString())).thenReturn(true);

        when(this.orgPlusServiceAdapt
                .createMemberWithResult(anyString(), anyString(), any()))
                .thenReturn(MockUntils.getBizICCreateMemberOutput());
        when(this.roleServiceAdapt.getTheSameRoleIdsFromRoleIds(
                anyString(), anyList(), anyString()))
                .thenReturn(Lists.newArrayList("roleId1"));
        this.bizMemberServiceV3.copyToRelatedUnits("tenantOrgId", "memberOrgId", request);

        doThrow(new OrganErrors.AccRealnameMemberExistWith("222", "111"))
                .when(this.orgPlusServiceAdapt).createMemberWithResult(anyString(), anyString(), any());
        this.bizMemberServiceV3.copyToRelatedUnits("tenantOrgId", "memberOrgId", request);

    }

    @Test
    public void deleteMemberBatch() {
        DeptSubListResponse response = new DeptSubListResponse();
        MemberBaseResponse memberBaseResponse = new MemberBaseResponse();
        memberBaseResponse.setMemberOid("memberOid1");
        memberBaseResponse.setMemberGid("adminGid1");
        List<RoleModel> roleDetails = new ArrayList<>();
        RoleModel roleDetail = new RoleModel();
        roleDetail.setRoleKey("MEMBER");
        roleDetails.add(roleDetail);
        memberBaseResponse.setRoleModels(roleDetails);
        response.setMemberList(Lists.newArrayList(memberBaseResponse));

        when(rpcDeptPlusServiceAdapt.listByDeptIdsOrMemberIds(anyString(), any(), any()
                , any(), any()))
                .thenReturn(response);

        DeleteMemberBatchRequest request = new DeleteMemberBatchRequest();
        request.setDeleteAccountList(Lists.newArrayList("oid1"));
        BatchSpaceOutput spaceOutput = new BatchSpaceOutput();
        List<EasunSpaceListItemOutput> easunSpaceListItemOutputs = new ArrayList<>();
        EasunSpaceListItemOutput itemOutput = new EasunSpaceListItemOutput();
        List<MemberDetail> members = new ArrayList<>();
        MemberDetail memberDetail = new MemberDetail();
        memberDetail.setMemberOid("adminOid1");
        memberDetail.setMemberGid("adminGid1");
//    List<BizRoleDetail> roleDetails =new ArrayList<>();
//    BizRoleDetail roleDetail = new BizRoleDetail();
//    roleDetail.setRoleKey("MEMBER");
//    roleDetails.add(roleDetail);
//    memberDetail.setRoleDetails(roleDetails);
        members.add(memberDetail);
        itemOutput.setAdmins(members);
        when(rpcOrganSpaceAdapt.getSpaceListByOrgan(any())).thenReturn(spaceOutput);
        this.bizMemberServiceV3.deleteMemberBatch("orgId", request);
    }

    @Test
    public void revokeUsersRole() {
        RevokeUsersRoleRequest request = new RevokeUsersRoleRequest();
        request.setMemberIds(Lists.newArrayList("memberId"));
        request.setRoleId("roleId");
        ReflectionTestUtils.setField(bizMemberServiceV3, "membersRevokeRoleMaxCount", 20);
        this.bizMemberServiceV3.revokeUsersRole("orgId", request);
    }

    @Test
    public void grantUsersRoles() {
        GrantUsersRolesRequest request = new GrantUsersRolesRequest();
        request.setRoleIds(Lists.newArrayList("roleId"));
        this.bizMemberServiceV3.grantUsersRoles("orgId", request);
    }


    @Test
    public void getOrgIdAndNameResponseTest() {
        MockUntils.getOrgIdAndNameResponse();
    }

    @Test
    public void membersExportAsyncMultiOrg() {
        String orgId = "orgId";
        String createrOid = "createOid";
        ListByDeptParam request = new ListByDeptParam();
        when(this.orgPlusServiceAdapt.isRootMultiOrgan(any())).thenReturn(Boolean.TRUE);
        when(this.esAccountServiceAdapt.searchMembersSupportMultiOrg(any(), any(), any(), anyBoolean(), any(), any()))
                .thenReturn(MockUntils.getTwoPagerResultMemberDetail());
        when(bizAccountService.getUserInfo(any())).thenReturn(MockUntils.getUserInfoResponse());
        this.bizMemberServiceV3.execExportAllMultiOrganMembers(orgId, createrOid, request);
    }

    @Test
    public void checkAccountExistInRelaOrgs() {
        String loginAccount = "***********";
        String idCardType = IdcardTypeEnum.MOBILE.name();
        String orgId = "orgId1";

//    when(this.bizAccountService
//        .getByIdcard(any(), any(), any())).thenReturn(MockUntils.getPagerUserInfoResp());
//    when(this.idAdapt.orgIdToGuid(anyString())).thenReturn("orgGuid");
        this.bizMemberServiceV3.checkAccountExistInRelaOrgs(loginAccount, idCardType, orgId);
    }

    @Test
    public void membersExportAsync() {
        String loginAccount = "***********";
        String idCardType = IdcardTypeEnum.MOBILE.name();
        String orgId = "orgId1";
        ListByDeptParam request = new ListByDeptParam();

        DeptSubListResponse deptSubListResponse = new DeptSubListResponse();
        deptSubListResponse.setMemberCount(1);

        when(rpcDeptPlusServiceAdapt.listByDepts(anyString(), any(ListByDeptParam.class), any()))
                .thenReturn(deptSubListResponse);
        this.bizMemberServiceV3.membersExportAsync(orgId, orgId, "operatorId", request);
    }

    @Test
    public void createMemberByImportMemberInfo() {
        InviteJoinPerson inviteJoinPerson = new InviteJoinPerson();
        inviteJoinPerson.setEmail("<EMAIL>");
        Map<String, String> memberInfo = new HashMap<>();
        memberInfo.put(InviteJoinMemberConstants.INVITE_JOIN_MEMBER_INFO_KEY_MEMBER_NAME, "memberName");
        memberInfo.put(InviteJoinMemberConstants.INVITE_JOIN_MEMBER_INFO_KEY_EMPLOYEE_ID, "employeeId1");
        memberInfo.put(InviteJoinMemberConstants.INVITE_JOIN_MEMBER_INFO_KEY_DEPTIDS, "deptId1,deptId2");
        memberInfo.put(InviteJoinMemberConstants.INVITE_JOIN_MEMBER_INFO_KEY_ROLEIDS, "roleId1,roleId2");

        inviteJoinPerson.setMemberInfo(memberInfo);

        when(this.bizAccountService.getByIdcard(anyString(), anyString(), any()))
                .thenReturn(MockUntils.getPagerUserInfoResp());

//    when(this.orgPlusServiceAdapt.createMemberWithResult(anyString(), anyString(), any()))
//        .thenReturn(MockUntils.getBizICCreateMemberOutput());

        this.bizMemberServiceV3.createMemberByImportMemberInfo(inviteJoinPerson);
    }

    @Test
    public void testWriteAndUploadExcel() {
        when(bizAccountService.getUserInfo(any())).thenReturn(new UserInfoResponse());
        when(idAdapt.orgIdToGuid(anyString())).thenReturn("gid");
        when(rpcSaaSGrayConfigAdapt.inGrayGid(anyString(), anyString())).thenReturn(true);
        this.bizMemberServiceV3.writeAndUploadExcel(Lists.newArrayList(), "oid", "orgId");
    }

    @Test
    public void testWriteAndUploadFeiShuExcel() {
        when(bizAccountService.getUserInfo(any())).thenReturn(new UserInfoResponse());
        when(idAdapt.orgIdToGuid(anyString())).thenReturn("gid");
        when(rpcSaaSGrayConfigAdapt.inGrayGid(anyString(), anyString())).thenReturn(true);
        this.bizMemberServiceV3.writeAndUploadFeiShuExcel(Lists.newArrayList(), "oid", "orgId");
    }

    @Test
    public void getBlackRoleKeysFromRoleIdsTest() {
        when(idAdapt.orgIdToOrganId(anyString())).thenReturn("orgId");
//    when(roleServiceAdapt.getPagedBuiltinRolesByOrgan(anyString(), any(Integer.class), any(Integer.class), anyString()))
//        .thenReturn(MockUntils.mockPageBizRole());
        this.bizMemberServiceV3.getBlackRoleKeysFromRoleIds("orgId", Lists.newArrayList("roleId1"), false);
        this.bizMemberServiceV3.getBlackRoleKeysFromRoleIds(null, Lists.newArrayList("roleId1"), false);
    }

    /**
     * 测试命中了角色黑名单
     */
    @Test
    public void testHasBlackRoles() {
        when(idAdapt.orgIdToOrganId(anyString())).thenReturn("organId");
        when(roleServiceAdapt.getPagedBuiltinRolesByOrgan("organId", 0, 100, null))
                .thenReturn(MockUntils.mockPageBlackRoles());
        List<String> blackRoleKeysFromRoleIds = this.bizMemberServiceV3
                .getBlackRoleKeysFromRoleIds("orgId", Lists.newArrayList("roleId1"), false);
        AssertSupport.assertTrue(CollectionUtils.isNotEmpty(blackRoleKeysFromRoleIds), new RuntimeException("判断是否存在黑名单角色列表异常"));
    }

}
