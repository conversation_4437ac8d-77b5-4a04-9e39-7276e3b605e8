package com.timevale.footstone.user.service.inner.impl.biz.clientstrategy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.account.service.model.constants.BuiltinThirdparty;
import com.timevale.account.service.model.service.biz.acc.output.BizAccountBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.app.ProductApp;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentCollection;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.user.service.configuration.IsolationConfig;
import com.timevale.footstone.user.service.enums.AdminFlagEnum;
import com.timevale.footstone.user.service.enums.AuthStatusEnum;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcOrganizationPrivilegeServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.oem.RpcOAuthRecordServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.model.QueryUserOrganListParam;
import com.timevale.footstone.user.service.inner.property.SystemProperty;
import com.timevale.footstone.user.service.model.account.mods.UserInfoModel;
import com.timevale.footstone.user.service.model.organization.response.ListOrganAndRoleResponse;
import com.timevale.open.platform.oauth.enums.SubjectAuthStatusEnum;
import com.timevale.open.platform.oauth.service.model.response.SubjectAuthInfoResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @since 2025/4/02
 */
@RunWith(MockitoJUnitRunner.StrictStubs.class)
public class DefaultClientIdStrategyImplTest {
    @InjectMocks
    private DefaultClientIdStrategyImpl defaultClientIdStrategy;

    @Mock
    private RpcIcOrgPlusServiceAdapt icOrgPlusServiceAdapt;
    @Mock
    private IsolationConfig isolationConfig;
    @Mock
    private SystemProperty systemProperty;
    @Mock
    private RpcOAuthRecordServiceAdapt rpcOAuthRecordServiceAdapt;
    @Mock
    private RpcOrganizationPrivilegeServiceAdapt rpcOrganizationPrivilegeServiceAdapt;

    @Test
    public void testGetClientId() {
    }

    @Test
    public void testQueryUserOrganList() {
        List<BizOrgBaseInfoOutput> bizOrgBaseInfoOutputs = Lists.newArrayList();
        BizOrgBaseInfoOutput bizOrgBaseInfoOutput = new BizOrgBaseInfoOutput();
        BizICUserOutput account = new BizICUserOutput();
        ICUserId id = new ICUserId();
        id.setOuid("orgOid2222");
        account.setId(id);
        BizAccountBaseOutput base = new BizAccountBaseOutput();
        ProductApp projectApp = new ProductApp();
        projectApp.setProjectId("appId11111");
        base.setProjectApp(projectApp);
        account.setBase(base);
        account.setProperties(Lists.newArrayList());
        IdcardContentCollection<ContentSecurity> idcards = new IdcardContentCollection<ContentSecurity>();
        IdcardContentThirdparty<ContentSecurity> thirdparty = new IdcardContentThirdparty<ContentSecurity>();
        IdcardThirdparty detail = new IdcardThirdparty();
        detail.setThirdpartyKey("");
        thirdparty.setDetail(detail);
        idcards.setThirdparty(thirdparty);
        account.setIdcards(idcards);
        bizOrgBaseInfoOutput.setAccount(account);
        bizOrgBaseInfoOutput.setRoleDetailList(Lists.newArrayList());
        bizOrgBaseInfoOutput.setOrganGuid("guid11111111");
        bizOrgBaseInfoOutput.setOrganId("organId22222");
        bizOrgBaseInfoOutputs.add(bizOrgBaseInfoOutput);
        Mockito.when(icOrgPlusServiceAdapt.getIcUserOrganlist(any(), any())).thenReturn(new PagerResult<>(bizOrgBaseInfoOutputs, bizOrgBaseInfoOutputs.size()));
        Mockito.when(isolationConfig.isolationSwitch(any())).thenReturn(false);

        HashMap<String, Integer> authInfoMap = new HashMap<>();
        authInfoMap.put(bizOrgBaseInfoOutput.getOrganGuid(), SubjectAuthStatusEnum.REFUSED_APPROVAL.getCode());
        SubjectAuthInfoResponse build = SubjectAuthInfoResponse.builder().authInfoMap(authInfoMap).build();
        Mockito.when(rpcOAuthRecordServiceAdapt.getUserAuthRecordList(any(), any(), any())).thenReturn(build);
        HashMap<String, Boolean> roleGrantMap = new HashMap<>();
        roleGrantMap.put(bizOrgBaseInfoOutput.getOrganId(), true);

        Mockito.when(rpcOrganizationPrivilegeServiceAdapt.getUserGrantedByOrganIdList(any(), any(), any())).thenReturn(roleGrantMap);
        Mockito.when(systemProperty.getOemAuthUrl()).thenReturn("http://xxx.com/%s/%s");

        QueryUserOrganListParam param = QueryUserOrganListParam.builder().fromOemFlag(true).build();

        ListOrganAndRoleResponse response = defaultClientIdStrategy.queryUserOrganList(param);
        System.out.println(JSON.toJSONString(response));
        UserInfoModel userInfoModel = response.getBizUserInfoOutputs().get(0);
        Assert.assertEquals(AuthStatusEnum.NOT_AUTHORIZE.getCode(), userInfoModel.getAuthStatus());
        Assert.assertEquals(AdminFlagEnum.WITH_ADMIN.getCode(), userInfoModel.getHasAdminFlag());
    }

}