package com.timevale.footstone.user.service.inner.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.timevale.MyTestHttpServletRequest;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.constants.HeaderConstants;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.UserIpNotPermit;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoLoginConfigAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoConnectorManageServiceImpl;
import com.timevale.footstone.user.service.inner.impl.biz.sso.BizSsoLoginConfigServiceImpl;
import com.timevale.footstone.user.service.model.account.mods.UserInfoModel;
import com.timevale.footstone.user.service.model.sso.manage.SsoOrganConfigModel;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.token.service.model.output.SsoIpIsolationOutput;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.http.util.Asserts;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @version 2024/12/30 17:20
 */
@RunWith(MockitoJUnitRunner.Silent.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizSsoLoginConfigServiceImplTest {


    @InjectMocks
    private BizSsoLoginConfigServiceImpl bizSsoLoginConfigService;


    @Mock
    private RpcSsoLoginConfigAdapt ssoLoginConfigAdapt;



    @Mock
    private BizSsoConnectorManageServiceImpl ssoConnectorManageService;


    @Mock
    private CommonConfig config;


    @Before
    public void setUp(){
        when(ssoLoginConfigAdapt.getOrganSsoLoginConfig(anyString())).thenReturn(MockUntils.mockSsoLoginConfigsOutput());
        when(ssoConnectorManageService.getSsoOrgFreeDomains(anyString())).thenReturn(Lists.newArrayList(MockUntils.mockSsoOrganizationDomainModel()));
        Map<String, List<SsoIpIsolationOutput>> ipIsolation = new HashMap<>();
        SsoIpIsolationOutput zero2OneThousand = MockUntils.mockSsoIpIsolationOutput(0,1000);
        ipIsolation.put("orgId",Lists.newArrayList(zero2OneThousand));
        when(ssoLoginConfigAdapt.getIpIsolationList(anyList())).thenReturn(ipIsolation);
        when(config.getUserIpHeaderName()).thenReturn("X-Tsign-Webserver-Request-Ip");
    }


    @Test
    public void savaSsoOrganConfigTest(){
        SsoOrganConfigModel configModel = new SsoOrganConfigModel();
        JSONArray config = MockUntils.mockSsoConfigJsonArray();
        configModel.setConfig(config);
        bizSsoLoginConfigService.savaSsoOrganConfig(configModel,"orgId");
    }


    @Test
    public void getSsoConfigResponseTest(){
        bizSsoLoginConfigService.getSsoOrganConfig("orgId");
    }



    @Test
    public void filterIpIsolationOrgsTest(){
        List<UserInfoModel> userOrgans = new ArrayList<>();
        userOrgans.add(MockUntils.mockUserInfoModel());
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*******");
        ReflectionTestUtils.setField(bizSsoLoginConfigService,"servletRequest",servletRequest);
        bizSsoLoginConfigService.filterIpIsolationOrgs(userOrgans,"*******",UserInfoModel::getOrgId);
    }


    @Test
    public void checkUserOrganIsolationTest(){
        HttpServletRequest servletRequest = new MyTestHttpServletRequest();
        servletRequest.setAttribute(HeaderConstants.HEADER_TSIGN_WEBSERVER_REQUEST_IP,"*************");
        ReflectionTestUtils.setField(bizSsoLoginConfigService,"servletRequest",servletRequest);
        List<UserInfoModel> userOrgans = new ArrayList<>();
        userOrgans.add(MockUntils.mockUserInfoModel());
        try {
            bizSsoLoginConfigService.checkUserOrganIsolation("orgId");
        }catch (UserIpNotPermit permit){

        }

    }
}
