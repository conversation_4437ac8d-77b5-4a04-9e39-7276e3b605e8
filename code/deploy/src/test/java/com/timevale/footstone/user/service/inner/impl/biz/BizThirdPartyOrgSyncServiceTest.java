package com.timevale.footstone.user.service.inner.impl.biz;


import com.google.common.collect.Lists;
import com.timevale.account.organization.service.model.service.mods.BizFinalOrganInfo;
import com.timevale.account.service.model.service.biz.icuser.output.BizFatICUserOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.idcard.IdcardContentThirdparty;
import com.timevale.account.service.model.service.mods.idcard.IdcardThirdparty;
import com.timevale.account.service.model.service.mods.idcard.MultiIdcardContentCollection;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.besp.lowcode.integration.response.TenantInfoResponse;
import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import com.timevale.easun.service.model.account.output.BizUserInfoOutput;
import com.timevale.easun.service.model.organization.output.BizOrgMemberSummaryOutput;
import com.timevale.easun.service.model.organization.output.BizOrgSummaryInfoOutput;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.footstone.user.service.configuration.CommonConfig;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.IdAdapt;

import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcAccountPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.*;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcIdCardAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.third.RpcThirdOrgSyncServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import com.timevale.footstone.user.service.model.third.response.ThirdOrgSyncTaskResponseV3;
import com.timevale.footstone.user.service.model.thirdparty.request.ThirdPartySyncOperateRequest;
import com.timevale.footstone.user.service.model.thirdparty.response.OrgDataSyncPlatformStateResponse;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.timevale.account.organization.service.model.service.newbiz.output.BizBatchDeptFullNameOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.entity.Dept;
import com.timevale.footstone.user.service.constants.ThirdPartyConstant;
import com.timevale.footstone.user.service.model.thirdparty.request.GetFrontRenderThirdIdRequest;
import com.timevale.footstone.user.service.model.thirdparty.response.GetFrontRenderThirdIdResponse;

import java.util.*;

import static com.timevale.easun.service.constant.ThirdPartyConstant.SAAS_PLATFORM_CLIENT_MAPPING;
import static org.junit.Assert.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BizThirdPartyOrgSyncServiceTest {

    // 测试数据定义
    private static final String INPUT_ORG_OUID = "testOrganOuid"; // 测试用组织OUID
    private static final String INPUT_WECHAT_WORK_PLATFORM = ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(); // 企微平台
    private static final String INPUT_OTHER_PLATFORM = "OTHER_PLATFORM"; // 其他平台
    private static final String INPUT_TENANT_KEY = "testTenantKey"; // 测试用租户密钥
    private static final List<String> INPUT_ACCOUNT_IDS = Arrays.asList("acc1", "acc2"); // 测试用账户ID列表
    private static final List<String> EMPTY_LIST = Collections.emptyList(); // 空列表

    private static final Boolean EXPECTED_NEED_RENDER_FALSE = false; // 预期不需要前端渲染
    private static final Boolean EXPECTED_NEED_RENDER_TRUE = true; // 预期需要前端渲染

    @InjectMocks
    private BizThirdPartyOrgSyncServiceImpl bizThirdPartyOrgSyncService;
    @Mock
    private IdAdapt idAdapt;
    @Mock
    private RpcInnerOrgServiceAdapt rpcInnerOrgServiceAdapt;
    @Mock
    private RpcIcUserPlusServiceAdapt rpcIcUserPlusServiceAdapt;
    @Mock
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;
    @Mock
    private RpcAccountPlusServiceAdapt accountPlusServiceAdapt;
    @Mock
    private RpcIcOrgPlusServiceAdapt rpcIcOrgPlusServiceAdapt;
    @Mock
    private RpcThirdOrgSyncServiceAdapt rpcThirdOrgSyncServiceAdapt;
    @Mock
    private RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdapt;
    @Mock
    private RpcIdCardAdapt rpcIdCardAdapt;
    @Mock
    private CommonConfig config;
    @Mock
    private Tedis tedis;
    @Mock
    private RpcDeptPlusServiceAdaptV3 deptPlusServiceAdaptV3;
    @Mock
    private RpcDeptPlusServiceAdapt rpcDeptPlusServiceAdapt;
    @Mock
    private CommonConfig commonConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        when(idAdapt.orgIdToGuid(any())).thenReturn("gid");
        when(idAdapt.accountUid(any())).thenReturn("accountUid");
        when(idAdapt.orgIdToOrganId(any())).thenReturn("organId");

        BizFatICUserOutput bizFatICUserOutput = new BizFatICUserOutput();
        MultiIdcardContentCollection<ContentSecurity> idcards = new MultiIdcardContentCollection<ContentSecurity>();
        List<IdcardContentThirdparty<ContentSecurity>> idcardsList = new ArrayList<>();
        IdcardContentThirdparty<ContentSecurity> idcardContentThirdparty = new IdcardContentThirdparty<>();
        IdcardThirdparty idcardThirdparty = new IdcardThirdparty();
        idcardThirdparty.setThirdpartyUserType("userType");
        idcardThirdparty.setThirdpartyUserId("TK");
        idcardThirdparty.setThirdpartyKey("FEI_SHU_TENANT");
        idcardContentThirdparty.setDetail(idcardThirdparty);
        idcardsList.add(idcardContentThirdparty);
        idcards.addThirdparty(idcardContentThirdparty);
        bizFatICUserOutput.setIdcards(idcards);
        when(rpcIcUserPlusServiceAdapt.getFatICUserByOuid("organOuid")).thenReturn(bizFatICUserOutput);
        when(rpcTripartiteServiceAdapt.getThirdSyncEnableStatusByTenantKey("TK", "FEI_SHU")).thenReturn(true);
        TedisUtil.setTedis(tedis);
        ReflectionTestUtils.setField(bizThirdPartyOrgSyncService, "supportVerify", true);
        ReflectionTestUtils.setField(bizThirdPartyOrgSyncService, "syncOperateLimit", 5);

        // 设置支持的平台
        when(commonConfig.getOrgSyncSupportList()).thenReturn(Arrays.asList(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform(), ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform()));
        when(commonConfig.getOrgSyncSupportPlatformIconMap()).thenReturn(Collections.singletonMap(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), "http://example.com/icon.png"));

    }

    @Test
    public void getDataSyncStateTest() {
        BizFinalOrganInfo bizFinalOrganInfo = new BizFinalOrganInfo();
        bizFinalOrganInfo.setOrganId("organId");
        when(rpcInnerOrgServiceAdapt.getFinalOrganInfoByGuid("gid", 1)).thenReturn(bizFinalOrganInfo);
        bizThirdPartyOrgSyncService.getDataSyncState("organOuid", "FEI_SHU");
    }

    @Test
    public void getDataSyncStateTest1() {
        BizFinalOrganInfo bizFinalOrganInfo = new BizFinalOrganInfo();
        bizFinalOrganInfo.setOrganId("organId");
        when(rpcInnerOrgServiceAdapt.getFinalOrganInfoByGuid("gid", 1)).thenReturn(bizFinalOrganInfo);
        bizThirdPartyOrgSyncService.getDataSyncState("organOuid", ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
    }

    @Test
    public void getOrganizationInfoTest() {
        BizUserInfoOutput userInfo = new BizUserInfoOutput();
        userInfo.setRealName(true);
        BizICUserOutput icUserOutput = new BizICUserOutput();
        List<Property> properties = Lists.newArrayList();
        Property property = new Property();
        property.setType("INFO_NAME");
        property.setValue("NNNNNN");
        properties.add(property);
        icUserOutput.setProperties(properties);
        userInfo.setAccount(icUserOutput);
        when(accountPlusServiceAdapt.getUserInfo("accountUid")).thenReturn(userInfo);
        BizOrgSummaryInfoOutput orgSummary = new BizOrgSummaryInfoOutput();
        when(rpcIcOrgPlusServiceAdapt.getOrgSummaryForThirdParty("organOuid", "FEI_SHU")).thenReturn(orgSummary);
        TenantInfoResponse tenantInfo = new TenantInfoResponse();
        tenantInfo.setTenantName("飞书哒哒哒哒企业");
//        when(rpcTripartiteServiceAdapt.getTenantInfo("tk", "FEI_SHU")).thenReturn(tenantInfo);
//        when(rpcTripartiteServiceAdapt.checkTenantAuth("tk", "FEI_SHU")).thenReturn(true);
        bizThirdPartyOrgSyncService.getOrganizationInfo("organOuid", "FEI_SHU");
    }


    @Test
    public void getMemberSummaryTest() {
        BizOrgMemberSummaryOutput memberSummary = new BizOrgMemberSummaryOutput();
        memberSummary.setOrganOuid("organOuid");
        memberSummary.setMemberCount(10);
        memberSummary.setMemberPlatformBindCount(5);
        when(rpcIcOrgPlusServiceAdapt.getMemberSummary("organOuid", "FEI_SHU")).thenReturn(memberSummary);
        bizThirdPartyOrgSyncService.getMemberSummary("organOuid", "FEI_SHU");
    }

//    @Test
    public void syncOpenTest() {
        ThirdPartySyncOperateRequest request = new ThirdPartySyncOperateRequest();
        request.setPlatform("FEI_SHU");
        request.setDeptMappingRelations(Lists.newArrayList());
        when(rpcTripartiteServiceAdapt.syncOpen("organOuid", "TK", "FEI_SHU")).thenReturn(true);
        Mockito.doNothing().when(rpcThirdOrgSyncServiceAdapt).deptThirdMapping("organOuid", request);
        bizThirdPartyOrgSyncService.syncOpen("organOuid", request);
    }

    @Test
    public void syncCloseTest() {
        ThirdPartySyncOperateRequest request = new ThirdPartySyncOperateRequest();
        request.setPlatform("FEI_SHU");
        request.setDeptMappingRelations(Lists.newArrayList());
        when(rpcTripartiteServiceAdapt.syncClose("organOuid", "TK", "FEI_SHU")).thenReturn(true);
        Mockito.doNothing().when(rpcThirdOrgSyncServiceAdapt).updateDeptThirdPartyInfo("organOuid", "DEFAULT");
        bizThirdPartyOrgSyncService.syncClose("organOuid", "FEI_SHU");
    }

//    @Test
    public void syncTaskSubmitTest() {
//        when(rpcTripartiteServiceAdapt.getThirdSyncEnableStatusByTenantKey("TK","FEI_SHU")).thenReturn(true);
//        when(rpcTripartiteServiceAdapt.startTask("organOuud","TK","FEI_SHU")).thenReturn("TASK1");
        bizThirdPartyOrgSyncService.syncTaskSubmit("organOuid", "FEI_SHU");
    }

    @Test
    public void getSyncTaskStatusTest(){
        ThirdOrgSyncTaskResponseV3 taskResponseV3 = new ThirdOrgSyncTaskResponseV3();
        taskResponseV3.setStatus("FINISH");
        taskResponseV3.setPushStatus("FINISH");
        when(rpcThirdOrgSyncServiceAdapt.queryTaskInfo("TASK1")).thenReturn(taskResponseV3);
        bizThirdPartyOrgSyncService.getSyncTaskStatus("TASK1");
    }

    /**
     * TC01: openSyncPlatform不匹配
     * 输入: organOuid="testOrganOuid", platform不是企微
     * 预期输出: needFrontRender=false
     * 描述: 不是企微平台
     */
    @Test
    public void testGetFrontRenderThirdId_OpenSyncPlatformNotMatch() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_OUID)).thenReturn(INPUT_OTHER_PLATFORM);

        GetFrontRenderThirdIdRequest request = new GetFrontRenderThirdIdRequest();
        request.setAccountIds(INPUT_ACCOUNT_IDS);

        GetFrontRenderThirdIdResponse response = bizThirdPartyOrgSyncService.getFrontRenderThirdId(INPUT_ORG_OUID, request);
        assertNotNull(response);
        assertEquals(EXPECTED_NEED_RENDER_FALSE, response.getNeedFrontRender());
    }

    /**
     * TC02: tenantKey为空
     * 输入: organOuid="testOrganOuid", getTenantKey返回null
     * 预期输出: needFrontRender=false
     * 描述: 未绑定企微企业ID
     */
    @Test
    public void testGetFrontRenderThirdId_TenantKeyEmpty() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_OUID)).thenReturn(INPUT_WECHAT_WORK_PLATFORM);
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_OUID, INPUT_WECHAT_WORK_PLATFORM)).thenReturn(null);

        GetFrontRenderThirdIdRequest request = new GetFrontRenderThirdIdRequest();
        request.setAccountIds(INPUT_ACCOUNT_IDS);

        GetFrontRenderThirdIdResponse response = bizThirdPartyOrgSyncService.getFrontRenderThirdId(INPUT_ORG_OUID, request);
        assertNotNull(response);
        assertEquals(EXPECTED_NEED_RENDER_FALSE, response.getNeedFrontRender());
    }

    /**
     * TC03: 正常流程，无accountIds
     * 输入: organOuid="testOrganOuid", 请求中没有accountIds
     * 预期输出: needFrontRender=true, accountInfos空, departmentInfos空
     * 描述: 完整正常流程
     */
    @Test
    public void testGetFrontRenderThirdId_NormalFlow_NoAccountIds() {
        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_OUID)).thenReturn(INPUT_WECHAT_WORK_PLATFORM);
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_OUID, INPUT_WECHAT_WORK_PLATFORM)).thenReturn(INPUT_TENANT_KEY);
        when(idAdapt.orgIdToOrganId(INPUT_ORG_OUID)).thenReturn("testOrganId");
        when(rpcIdCardAdapt.batchGetThirdUserIdByOuids(Collections.emptyList(), SAAS_PLATFORM_CLIENT_MAPPING.get(INPUT_WECHAT_WORK_PLATFORM), INPUT_TENANT_KEY))
                .thenReturn(new HashMap<>());

        GetFrontRenderThirdIdRequest request = new GetFrontRenderThirdIdRequest();
        request.setDepartmentIds(Collections.emptyList());
        request.setAccountIds(Collections.emptyList());

        GetFrontRenderThirdIdResponse response = bizThirdPartyOrgSyncService.getFrontRenderThirdId(INPUT_ORG_OUID, request);
        assertNotNull(response);
        assertEquals(EXPECTED_NEED_RENDER_TRUE, response.getNeedFrontRender());
        Assert.assertTrue(response.getAccountInfos().isEmpty());
        Assert.assertTrue(response.getDepartmentInfos().isEmpty());
    }

    /**
     * TC04: 正常流程，有accountIds和departmentInfos
     * 输入: organOuid="testOrganOuid", 有效accountIds和departmentInfos
     * 预期输出: needFrontRender=true, 包含有效数据
     * 描述: 完整正常流程
     */
    @Test
    public void testGetFrontRenderThirdId_NormalFlow_WithAccountsAndDepartments() {
        Map<String, String> thirdUserIdMap = new HashMap<>();
        thirdUserIdMap.put("acc1", "third1");
        thirdUserIdMap.put("acc2", "third2");

        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_ORG_OUID)).thenReturn(INPUT_WECHAT_WORK_PLATFORM);
        when(rpcIdCardAdapt.getTenantKey(INPUT_ORG_OUID, INPUT_WECHAT_WORK_PLATFORM)).thenReturn(INPUT_TENANT_KEY);
        when(idAdapt.orgIdToOrganId(INPUT_ORG_OUID)).thenReturn("testOrganId");
        when(rpcIdCardAdapt.batchGetThirdUserIdByOuids(INPUT_ACCOUNT_IDS, SAAS_PLATFORM_CLIENT_MAPPING.get(INPUT_WECHAT_WORK_PLATFORM), INPUT_TENANT_KEY))
                .thenReturn(thirdUserIdMap);

        // 模拟getDepartmentIdBeans方法的行为
        GetFrontRenderThirdIdRequest request = new GetFrontRenderThirdIdRequest();
        request.setAccountIds(INPUT_ACCOUNT_IDS);
        request.setDepartmentIds(Arrays.asList("dept1", "dept2"));
        request.setNeedDepartmentPath(true);

        // 创建mock的部门信息
        Map<String, Dept> deptList = new HashMap<>();
        Dept dept1 = new Dept();
        dept1.setDeptId("dept1");
        dept1.setOrganId("testOrganId");
        dept1.setDeptThirdId("thirdDept1");
        dept1.setPath("path1");
        dept1.setSource(INPUT_WECHAT_WORK_PLATFORM);
        deptList.put(dept1.getDeptId(), dept1);

        Dept dept2 = new Dept();
        dept2.setDeptId("dept2");
        dept2.setOrganId("testOrganId");
        dept2.setDeptThirdId("thirdDept2");
        dept2.setPath("path2");
        dept2.setSource(INPUT_WECHAT_WORK_PLATFORM);
        deptList.put(dept2.getDeptId(), dept2);

        // 创建mock的路径映射
        List<BizBatchDeptFullNameOutput.BizDeptPath> pathList = new ArrayList<>();
        BizBatchDeptFullNameOutput.BizDeptPath path1 = new BizBatchDeptFullNameOutput.BizDeptPath();
        path1.setDeptFullName("path1");
        path1.setDeptThirdIdPath(Lists.newArrayList("thirdPath1"));
        pathList.add(path1);

        BizBatchDeptFullNameOutput.BizDeptPath path2 = new BizBatchDeptFullNameOutput.BizDeptPath();
        path2.setDeptFullName("path2");
        path2.setDeptThirdIdPath(Lists.newArrayList("thirdPath2"));
        pathList.add(path2);

        when(deptPlusServiceAdaptV3.batchGetDeptInfo(any())).thenReturn(deptList);

        GetFrontRenderThirdIdResponse response = bizThirdPartyOrgSyncService.getFrontRenderThirdId(INPUT_ORG_OUID, request);
        assertNotNull(response);
        assertEquals(EXPECTED_NEED_RENDER_TRUE, response.getNeedFrontRender());
        assertNotNull(response.getAccountInfos());
        assertEquals(2, response.getAccountInfos().size());
        assertNotNull(response.getDepartmentInfos());
        assertEquals(2, response.getDepartmentInfos().size());
    }

    // SC01: platform is blank
    @Test
    public void testGetDataSyncPlatformState_WebMode_ReturnsWebResponse() throws Exception {
        final String INPUT_organOuid = "org123";
        final String INPUT_platform = "";

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.getDataSyncPlatformState(INPUT_organOuid, INPUT_platform);
        assertNotNull(response);
    }

    // SC02: platform is supported (e.g., dingtalk)
    @Test
    public void testGetDataSyncPlatformState_ThirdPlatformMode_ReturnsThirdPlatformResponse() throws Exception {
        final String INPUT_organOuid = "org123";
        final String INPUT_platform = ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform();

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.getDataSyncPlatformState(INPUT_organOuid, INPUT_platform);
        assertNotNull(response);
    }

    // SC03: platform not in support list
    @Test
    public void testGetDataSyncPlatformState_UnsupportedPlatform_ThrowsException() {
        final String INPUT_organOuid = "org123";
        final String INPUT_platform = "unknown";

        OrgDataSyncPlatformStateResponse dataSyncPlatformState = bizThirdPartyOrgSyncService.getDataSyncPlatformState(INPUT_organOuid, INPUT_platform);
        Assert.assertFalse(dataSyncPlatformState.getShowSyncOrgBar());
    }




    // SC01: 没有绑定任何平台租户ID
    @Test
    public void testBuildOrgDataSyncPlatformStateInWeb_NoBinding_ReturnsNoSyncBar() {
        final String INPUT_organOuid = "org123";
        final Map<String, Boolean> INPUT_platformBindTenantKeyMap = new HashMap<>();

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.buildOrgDataSyncPlatformStateInWeb(INPUT_organOuid, INPUT_platformBindTenantKeyMap);

        assertFalse(response.getShowSyncOrgBar());
    }

    // SC02: 绑定企微平台但未开启同步
    @Test
    public void testBuildOrgDataSyncPlatformStateInWeb_BoundButNotOpen_ReturnsSyncButtons() {
        final String INPUT_organOuid = "org123";
        final Map<String, Boolean> INPUT_platformBindTenantKeyMap = new HashMap<>();
        INPUT_platformBindTenantKeyMap.put(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), true);
        INPUT_platformBindTenantKeyMap.put(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform(), false);

        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_organOuid)).thenReturn(null);

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.buildOrgDataSyncPlatformStateInWeb(INPUT_organOuid, INPUT_platformBindTenantKeyMap);

        assertTrue(response.getShowSyncOrgBar());
        assertNull(response.getOpenedSyncPlatform());
        assertEquals(OrgDataSyncPlatformStateResponse.CAN_SYNC_THIRD_PLATFORM_ORG, response.getSyncDescribeCode());
        assertEquals(Collections.singletonList(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform()), response.getOpenSyncButtonPlatforms());
    }

    // SC03: 开启了企微同步
    @Test
    public void testBuildOrgDataSyncPlatformStateInWeb_OpenedDingTalkSync_ShowRefreshAndClose() {
        final String INPUT_organOuid = "org123";
        final Map<String, Boolean> INPUT_platformBindTenantKeyMap = new HashMap<>();
        INPUT_platformBindTenantKeyMap.put(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), true);
        INPUT_platformBindTenantKeyMap.put(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform(), true);

        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_organOuid)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());
        ThirdOrgSyncTaskResponseV3 thirdOrgSyncTaskResponseV3 = new ThirdOrgSyncTaskResponseV3();
        thirdOrgSyncTaskResponseV3.setUpdateTime(new Date());
        when(rpcThirdOrgSyncServiceAdapt.queryLastTaskWithPlatform(INPUT_organOuid, ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform())).thenReturn(thirdOrgSyncTaskResponseV3);

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.buildOrgDataSyncPlatformStateInWeb(INPUT_organOuid, INPUT_platformBindTenantKeyMap);

        assertTrue(response.getShowSyncOrgBar());
        assertEquals(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), response.getOpenedSyncPlatform());
        assertNotNull(response.getLastSyncTime());
        assertTrue(response.getShowRefreshButton());
        assertTrue(response.getShowCloseSyncButton());
        assertEquals(OrgDataSyncPlatformStateResponse.OPENED_SYNC_ORG, response.getSyncDescribeCode());
        assertEquals("http://example.com/icon.png", response.getPlatformIconUrl());
    }









    // SC01: 未绑定当前平台且无同步开启
    @Test
    public void testBuildOrgDataSyncPlatformStateInThirdPlatform_NotBoundAndNotOpen_ReturnsNoSyncBar() {
        final String INPUT_organOuid = "org123";
        final String INPUT_platform = ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform();
        final Map<String, Boolean> INPUT_platformBindTenantKeyMap = Collections.singletonMap(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), false);
 
        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.buildOrgDataSyncPlatformStateInThirdPlatform(
                INPUT_organOuid, INPUT_platform, INPUT_platformBindTenantKeyMap);

        assertNotNull(response);
        assertFalse(response.getShowSyncOrgBar());
    }

    // SC02: 绑定企微但未开启同步
    @Test
    public void testBuildOrgDataSyncPlatformStateInThirdPlatform_BoundButNotOpen_ReturnsSyncButtons() {
        final String INPUT_organOuid = "org123";
        final String INPUT_platform =ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform();
        final Map<String, Boolean> INPUT_platformBindTenantKeyMap = Collections.singletonMap(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), true);

        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_organOuid)).thenReturn(null);

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.buildOrgDataSyncPlatformStateInThirdPlatform(
                INPUT_organOuid, INPUT_platform, INPUT_platformBindTenantKeyMap);

        assertNotNull(response);
        assertTrue(response.getShowSyncOrgBar());
        assertNull(response.getOpenedSyncPlatform());
        assertEquals(OrgDataSyncPlatformStateResponse.CAN_SYNC_CURRENT_THIRD_PLATFORM_ORG, response.getSyncDescribeCode());
        assertEquals(Collections.singletonList(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform()), response.getOpenSyncButtonPlatforms());
        assertEquals("http://example.com/icon.png", response.getPlatformIconUrl());
    }

    // SC03: 开启了企微同步
    @Test
    public void testBuildOrgDataSyncPlatformStateInThirdPlatform_OpenedDingTalkSync_ShowRefreshClose() {
        final String INPUT_organOuid = "org123";
        final String INPUT_platform = ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform();
        final Map<String, Boolean> INPUT_platformBindTenantKeyMap = Collections.singletonMap(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), true);

        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_organOuid)).thenReturn(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform());

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.buildOrgDataSyncPlatformStateInThirdPlatform(
                INPUT_organOuid, INPUT_platform, INPUT_platformBindTenantKeyMap);

        assertNotNull(response);
        assertTrue(response.getShowSyncOrgBar());
        assertEquals(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), response.getOpenedSyncPlatform());
        assertTrue(response.getShowRefreshButton());
        assertTrue(response.getShowCloseSyncButton());
        assertEquals(OrgDataSyncPlatformStateResponse.OPENED_SYNC_ORG, response.getSyncDescribeCode());
        assertEquals("http://example.com/icon.png", response.getPlatformIconUrl());
    }

    // SC04: 开启了其他平台同步（如飞书）
    @Test
    public void testBuildOrgDataSyncPlatformStateInThirdPlatform_OtherPlatformSynced_ShowOtherSyncMessage() {
        final String INPUT_organOuid = "org123";
        final String INPUT_platform = ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform();
        final Map<String, Boolean> INPUT_platformBindTenantKeyMap = new HashMap<>();
        INPUT_platformBindTenantKeyMap.put(ThirdPartyPlatFormEnum.WECHAT_WORK.getPlatform(), true);
        INPUT_platformBindTenantKeyMap.put(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform(),  true);

        when(rpcThirdOrgConnectorServiceAdapt.getOpenSyncPlatform(INPUT_organOuid)).thenReturn(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform());

        OrgDataSyncPlatformStateResponse response = bizThirdPartyOrgSyncService.buildOrgDataSyncPlatformStateInThirdPlatform(
                INPUT_organOuid, INPUT_platform, INPUT_platformBindTenantKeyMap);

        assertNotNull(response);
        assertTrue(response.getShowSyncOrgBar());
        assertEquals(ThirdPartyPlatFormEnum.FEI_SHU.getPlatform(), response.getOpenedSyncPlatform());
        assertFalse(response.getShowRefreshButton());
        assertFalse(response.getShowCloseSyncButton());
        assertEquals(OrgDataSyncPlatformStateResponse.OPENED_OTHER_THIRD_PLATFORM_SYNC_ORG, response.getSyncDescribeCode());
        assertNull(response.getPlatformIconUrl());
    }

}
