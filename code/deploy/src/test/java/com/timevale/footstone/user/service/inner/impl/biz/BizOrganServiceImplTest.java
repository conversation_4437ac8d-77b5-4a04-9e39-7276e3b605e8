package com.timevale.footstone.user.service.inner.impl.biz;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.timevale.footstone.user.service.inner.biz.UserOrganListService;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcInnerOrgServiceAdapt;
import com.timevale.footstone.user.service.model.organization.request.OrganSortRequest;
import com.timevale.footstone.user.service.utils.MockUntils;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @version 2025/1/15 10:26
 */

@RunWith(MockitoJUnitRunner.class)
//@ContextConfiguration(classes = Application.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizOrganServiceImplTest {


    @InjectMocks
    private BizOrganServiceImpl bizOrganService;


    @Mock
    private RpcInnerOrgServiceAdapt innerOrgServiceAdapt;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;

    @Mock
    private UserOrganListService userOrganListService;

    @Before
    public void setUp(){
        when(innerOrgServiceAdapt.getOrganByOuid(any())).thenReturn(MockUntils.mockBizGetOrganOutput());
        when(icUserPlusService.getICUserByAccount(any())).thenReturn(MockUntils.mockBizICUserOutput());
        ReflectionTestUtils.setField(bizOrganService,"subItem",100);
        when(icUserPlusService.getICUserByOuid(any())).thenReturn(MockUntils.mockBizICUserOutput());
//        when()
    }


    @Test
    public void orgCreatorNotRealNameTest(){
        bizOrganService.orgCreatorNotRealName("");
        bizOrganService.orgCreatorNotRealName("orgId");
    }


    @Test
    public void organSortTest(){
        List<OrganSortRequest> orgsList = new ArrayList<>();
        orgsList.add(MockUntils.mockOrganSortRequest());
        OrganSortRequest sort2 = MockUntils.mockOrganSortRequest();
        sort2.setOrgId("orgId2");
        orgsList.add(sort2);
        OrganSortRequest sort3 = MockUntils.mockOrganSortRequest();
        sort2.setOrgId("orgId3");
        orgsList.add(sort3);
        String accountId = "accountId";
        bizOrganService.organSort(orgsList,accountId);
    }
}
