package com.timevale.footstone.user.service.inner.impl.biz.adapt;


import com.google.common.collect.Lists;
import com.timevale.besp.lowcode.integration.common.enums.PlatformEnum;
import com.timevale.besp.lowcode.integration.request.CheckAdminRequest;
import com.timevale.besp.lowcode.integration.response.*;
import com.timevale.besp.lowcode.integration.third.*;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.thirdparty.RpcTripartiteServiceAdapt;
import com.timevale.footstone.user.service.model.thirdplatform.request.AppConfigAddRequest;
import com.timevale.footstone.user.service.model.thirdplatform.request.AppConfigDelRequest;
import com.timevale.saas.auth.api.facade.api.RpcDingAccountService;
import com.timevale.saas.auth.api.facade.api.RpcSubCorpDepartmentService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RpcTripartiteServiceAdaptTest {

    @InjectMocks
    private RpcTripartiteServiceAdapt rpcTripartiteServiceAdapt;

    @Mock
    private ThirdConfigRpcService thirdConfigRpcService;
    @Mock
    private ThirdTenantRpcService thirdTenantRpcService;
    @Mock
    private ThirdOrgRpcService thirdOrgRpcService;
    @Mock
    private ThirdOrgSyncRpcService thirdOrgSyncRpcService;
    @Mock
    private ThirdConfigBaseRpcService thirdConfigBaseRpcService;
    @Mock
    private ThirdUserBaseRpcService thirdUserBaseRpcService;
    @Mock
    private RpcSubCorpDepartmentService rpcSubCorpDepartmentService;
    @Mock
    private RpcDingAccountService rpcDingAccountService;

    @Before
    public void setUp() {
        List<ThirdConfigResponse> responses = Lists.newArrayList();
        ThirdConfigResponse configResponse = new ThirdConfigResponse();
        configResponse.setConfigType("SYNC_ORG_ALL");
        configResponse.setEnable(true);
        responses.add(configResponse);
        when(thirdConfigRpcService.getConfigs(any())).thenReturn(responses);
    }


    @Test
    public void getTenantThirdConfigTest() {
        rpcTripartiteServiceAdapt.getTenantThirdConfig("TK", "FEISHU");
    }

    @Test
    public void getTenantInfoTest() {
        TenantInfoResponse tenantInfo = new TenantInfoResponse();
        tenantInfo.setTenantName("测试");
        when(thirdTenantRpcService.getTenantInfo(any())).thenReturn(tenantInfo);
        rpcTripartiteServiceAdapt.getTenantInfo("TK", "FEI_SHU");
    }

    @Test
    public void checkTenantAuthTest() {
        TenantCheckAuthResponse response = new TenantCheckAuthResponse();
        response.setStatus("AUTHORIZED");
        when(thirdTenantRpcService.checkAuth(any())).thenReturn(response);
        rpcTripartiteServiceAdapt.checkTenantAuth("TK", "FEI_SHU");
    }

    @Test
    public void syncOpenTest() {
        ThirdConfigUpdateResponse response = new ThirdConfigUpdateResponse();
        response.setResult(true);
        when(thirdConfigRpcService.updateConfig(any())).thenReturn(response);
        rpcTripartiteServiceAdapt.syncOpen("organOuid", "TK", "FEI_SHU");
    }

    @Test
    public void syncCloseTest() {
        ThirdConfigUpdateResponse response = new ThirdConfigUpdateResponse();
        response.setResult(true);
        when(thirdConfigRpcService.updateConfig(any())).thenReturn(response);
        rpcTripartiteServiceAdapt.syncClose("organOuid", "TK", "FEI_SHU");
    }

    @Test
    public void startTaskTest() {
        OrgSyncStartResponse orgSyncStartResponse = new OrgSyncStartResponse();
        orgSyncStartResponse.setTaskCode("taskCode");
        when(thirdOrgSyncRpcService.startTask(any())).thenReturn(orgSyncStartResponse);
        rpcTripartiteServiceAdapt.startTask("organOuid", "TK", "FEI_SHU");
    }

    @Test
    public void getDeptSubListTest() {
        List<OrgResponse> responses = Lists.newArrayList();
        OrgResponse response = new OrgResponse();
        response.setName("name");
        response.setParentId("ALL");
        response.setHasChild(true);
        responses.add(response);
        when(thirdOrgRpcService.childList(any())).thenReturn(responses);
        rpcTripartiteServiceAdapt.getDeptSubList("organOuid", "FEI_SHU", "ALL");
    }

    @Test
    public void searchDeptListTest() {
        List<OrgResponse> responses = Lists.newArrayList();
        OrgResponse response = new OrgResponse();
        response.setName("name");
        response.setParentId("ALL");
        response.setHasChild(true);
        responses.add(response);
        when(thirdOrgRpcService.queryOrg(any())).thenReturn(responses);
        rpcTripartiteServiceAdapt.searchDeptList("tenantKey", "thirdUserId", "FEI_SHU", "keyword");
    }

    @Test
    public void getThirdSyncEnableStatusByTenantKeyTest() {
        rpcTripartiteServiceAdapt.getThirdSyncEnableStatusByTenantKey("TK", "FEI_SHU");
    }

    @Test
    public void getThirdSyncEnableStatusOrganOuidTest() {
        List<TenantConfigResponse> responseList = Lists.newArrayList();
        TenantConfigResponse tenantConfigResponse = new TenantConfigResponse();
        List<ThirdConfigResponse> responses = Lists.newArrayList();
        ThirdConfigResponse configResponse = new ThirdConfigResponse();
        configResponse.setConfigType("SYNC_ORG_ALL");
        configResponse.setEnable(true);
        responses.add(configResponse);
        tenantConfigResponse.setConfigs(responses);
        responseList.add(tenantConfigResponse);
        when(thirdConfigBaseRpcService.getTenantConfigs(any())).thenReturn(responseList);
        rpcTripartiteServiceAdapt.getThirdSyncEnableStatusOrganOuid("organOuid", "FEI_SHU");
    }

    @Test
    public void getTenantInfoListTest(){
        when(thirdTenantRpcService.getTenantInfoList(any())).thenReturn(Lists.newArrayList());
        rpcTripartiteServiceAdapt.getTenantInfoList("KINGDEE_K3EE", Arrays.asList("tenantKeys"));
    }

    @Test
    public void getTenantChildOrgTest(){
        when(thirdTenantRpcService.getChildOrgList(any())).thenReturn(Lists.newArrayList());
        rpcTripartiteServiceAdapt.getTenantChildOrg("KINGDEE_K3EE","tenantKey");
    }

    @Test
    public void saveOpenConfigTest(){
        AppConfigAddRequest request = new AppConfigAddRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setNotifyUrl("notifyUrl");
        request.setTenantAppId("tenantAppId");
        request.setTenantAppSecret("tenantAppSecret");
        when(thirdTenantRpcService.saveOpenConfig(any())).thenReturn(any());
        rpcTripartiteServiceAdapt.saveOpenConfig(request,"t","c","m");
    }

    @Test
    public void delAppConfigTest(){
        AppConfigDelRequest request = new AppConfigDelRequest();
        request.setClientId("clientId");
        request.setTenantKey("tenantKey");
        request.setAppConfigId(1L);
        when(thirdTenantRpcService.deleteOpenConfig(any())).thenReturn(any());
        rpcTripartiteServiceAdapt.delAppConfig(request,"t","o");
    }

    @Test
    public void getUserDingSubOrgList(){
        rpcTripartiteServiceAdapt.getUserDingSubOrgList(ClientEnum.DING_TALK.getClientId(),"t","o");
    }

    @Test
    public void getUserDingInfo(){
        rpcTripartiteServiceAdapt.getUserDingInfo("t","o");
    }
    @Test
    public void isAdmin(){
        ArgumentCaptor<CheckAdminRequest> argument = ArgumentCaptor.forClass(CheckAdminRequest.class);
        rpcTripartiteServiceAdapt.isAdmin(ClientEnum.DING_TALK.getClientId(),"o","u");
        Mockito.verify(thirdUserBaseRpcService).isAdmin(argument.capture());
        CheckAdminRequest value = argument.getValue();
        Assert.assertEquals((PlatformEnum.DING_TALK.getPlatform()), value.getPlatform());
    }

    @Test
    public void isAdmin2(){
        ArgumentCaptor<CheckAdminRequest> argument = ArgumentCaptor.forClass(CheckAdminRequest.class);
        rpcTripartiteServiceAdapt.isAdmin(ClientEnum.FEI_SHU.getClientId(),"o","u");
        Mockito.verify(thirdUserBaseRpcService).isAdmin(argument.capture());
        CheckAdminRequest value = argument.getValue();
        Assert.assertEquals((PlatformEnum.FEI_SHU.getPlatform()), value.getPlatform());
    }

    @Test
    public void isAdmin3(){
        ArgumentCaptor<CheckAdminRequest> argument = ArgumentCaptor.forClass(CheckAdminRequest.class);
        rpcTripartiteServiceAdapt.isAdmin(ClientEnum.WE_WORK.getClientId(),"o","u");
        Mockito.verify(thirdUserBaseRpcService).isAdmin(argument.capture());
        CheckAdminRequest value = argument.getValue();
        Assert.assertEquals((PlatformEnum.WECHAT_WORK.getPlatform()), value.getPlatform());
    }

}
