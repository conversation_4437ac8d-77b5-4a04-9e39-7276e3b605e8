package com.timevale.footstone.user.service.inner.impl.biz.adapt;
import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcIcOrgPlusService;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.model.account.input.OrgICUserCreateRequest;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.easun.service.model.esearch.input.EsGetMembersByOidInput;
import com.timevale.easun.service.model.organization.OrganAccountDetail;
import com.timevale.easun.service.model.organization.output.BizOrgMemberSummaryOutput;
import com.timevale.easun.service.model.organization.output.BizOrgSummaryInfoOutput;
import com.timevale.easun.service.model.organization.output.v3.BizCheckLegalOutput;
import com.timevale.easun.service.model.organization.output.v3.BizOrgBaseInfoOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizSubOrgListOutputV3;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RpcIcOrgPlusServiceAdaptTest {

    @Mock
    private RpcIcOrgPlusService icOrgPlusService;

    @InjectMocks
    private RpcIcOrgPlusServiceAdapt adapt;

    @Test
    public void getMemberOrganListTest(){
        PagerResult<OrganAccountDetail> pagerResult = new PagerResult<>();
        when(icOrgPlusService.getMemberOrganList(any())).thenReturn(RpcOutput.with(pagerResult));
        EsGetMembersByOidInput getMembersByOidInput = new EsGetMembersByOidInput();
        getMembersByOidInput.setMemberOid("accountId");
        getMembersByOidInput.setOffset(0);
        getMembersByOidInput.setSize(100);
        getMembersByOidInput.setRealName(true);
        adapt.getMemberOrganList(getMembersByOidInput);
    }

    @Test
    public void batchCheckOrganLegalTest(){
        when(icOrgPlusService.batchCheckOrganLegal(any())).thenReturn(RpcOutput.with(new BizCheckLegalOutput()));
        adapt.batchCheckOrganLegal("memberOid",Lists.newArrayList("orgId1"));
        adapt.transferLegal("orgId","memberOid");
    }


    @Test
    public void getAllSubOrg() {

        BizSubOrgListOutputV3 bizSubOrgListOutputV3 = new BizSubOrgListOutputV3();

        BizOrgBaseInfoOutputV3 bizOrgBaseInfoOutputV3 = new BizOrgBaseInfoOutputV3();
        bizOrgBaseInfoOutputV3.setOuid("test_oid");
        bizOrgBaseInfoOutputV3.setGuid("test_gid");
        bizOrgBaseInfoOutputV3.setUuid("");
        bizOrgBaseInfoOutputV3.setAccountUid("test_a_id");
        bizOrgBaseInfoOutputV3.setOrgName("test_name");

        bizSubOrgListOutputV3.setSubList(Lists.newArrayList(bizOrgBaseInfoOutputV3));

        RpcOutput<BizSubOrgListOutputV3> rpcOutput = RpcOutput.with(bizSubOrgListOutputV3);


        Mockito.when(icOrgPlusService.getAllSubOrgansByRootOrgId(any())).thenReturn(rpcOutput);

        String s = "test";
        List<BizOrgBaseInfoOutputV3> allSubOrg = adapt.getAllSubOrg(s);
    }

    @Test
    public void getAllSubOrgWithEmpty() {

        BizSubOrgListOutputV3 bizSubOrgListOutputV3 = new BizSubOrgListOutputV3();

        RpcOutput<BizSubOrgListOutputV3> rpcOutput = RpcOutput.with(bizSubOrgListOutputV3);


        Mockito.when(icOrgPlusService.getAllSubOrgansByRootOrgId(any())).thenReturn(rpcOutput);

        String s = "test";
        List<BizOrgBaseInfoOutputV3> allSubOrg = adapt.getAllSubOrg(s);
    }


    @Test
    public void getOrgSummaryForThirdPartyTest(){
        BizOrgSummaryInfoOutput bizOrgSummaryInfoOutput = new BizOrgSummaryInfoOutput();
        when(icOrgPlusService.getOrgSummaryInfo(any())).thenReturn(RpcOutput.with(bizOrgSummaryInfoOutput));
        adapt.getOrgSummaryForThirdParty("orgId","FEI_SHU");
    }

    @Test
    public void getMemberSummaryTest(){
        BizOrgMemberSummaryOutput bizOrgMemberSummaryOutput = new BizOrgMemberSummaryOutput();
        when(icOrgPlusService.getOrgMemberSummary(any())).thenReturn(RpcOutput.with(bizOrgMemberSummaryOutput));
        adapt.getMemberSummary("orgId","FEI_SHU");
    }

}
