package com.timevale.footstone.user.service.inner.impl.biz.adapt;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.timevale.account.ashman.service.api.RpcUserAshmanService;
import com.timevale.account.ashman.service.model.constants.OperationType;
import com.timevale.account.ashman2.facade.api.RpcAccountAshman2Service;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.core.AshmanConfig;
import com.timevale.unittest.footstone.configuration.Application;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @since 2021/8/31
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RpcUserAshmanServiceAdaptTest {

    @InjectMocks private RpcUserAshmanServiceAdapt userAshmanServiceAdapt;

    @Mock private AshmanConfig config;
    @Mock private RpcUserAshmanService svc;
    @Mock private RpcAccountAshman2Service ashman2Service;

    @Before
    public void setUp() throws Exception {
        when(svc.execute(any())).thenReturn(RpcOutput.with(null));
        when(ashman2Service.exec(any())).thenReturn(RpcOutput.with(null));
    }

    @Test
    public void test_when_version_1() {
        when(config.getVersion()).thenReturn(1);
        userAshmanServiceAdapt.executeForMember(OperationType.INSERT, "1", "uid");
    }

    @Test
    public void test_when_version_2() {
        when(config.getVersion()).thenReturn(2);
        userAshmanServiceAdapt.executeForMember(OperationType.UPDATE, "1", "uid");
    }

    @Test
    public void test_when_version_3() {
        when(config.getVersion()).thenReturn(3);
        userAshmanServiceAdapt.executeForMember(OperationType.DELETE, "1", "uid");
    }
}
