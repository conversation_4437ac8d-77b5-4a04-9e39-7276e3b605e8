package com.timevale.footstone.user.service.inner.impl;

import com.google.common.collect.Lists;
import com.timevale.easun.service.api.RpcIcOrgPlusService;
import com.timevale.easun.service.model.organization.ICOrg.output.BizICCreateMemberOutput;
import com.timevale.easun.service.model.organization.output.v3.LegalCheckOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.account.RpcIcUserPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcIcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcOrgPlusServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.organ.RpcSaaSVipServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.adapt.privlege.RpcRoleServiceAdapt;
import com.timevale.footstone.user.service.inner.impl.biz.v3.BizOrganizationServiceV3Impl;
import com.timevale.footstone.user.service.model.dept.request.v3.BatchCheckLegalRequest;
import com.timevale.footstone.user.service.utils.MockUntils;
import com.timevale.footstone.user.service.utils.MyDefaultStringOperations;
import com.timevale.framework.tedis.core.StringOperations;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.privilege.service.model.service.biz.output.BizGetRolesByUsersOutput;
import com.timevale.privilege.service.model.service.mods.BizRoleDetail;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BizOrganizationServiceV3Test {

    @InjectMocks
    private BizOrganizationServiceV3Impl bizOrganizationServiceV3;

    @Mock
    private RpcIcOrgPlusServiceAdapt rpcIcOrgPlusServiceAdapt;

    @Mock
    private RpcSaaSVipServiceAdapt rpcSaasVipServiceAdapt;

//    @Mock
//    private RpcThirdOrgConnectorServiceAdapt rpcThirdOrgConnectorServiceAdapt;

    @Mock
    private RpcRoleServiceAdapt rpcRoleServiceAdapt;

    @Mock
    private RpcOrgPlusServiceAdapt rpcOrgPlusServiceAdapt;

    @Mock
    private RpcIcUserPlusServiceAdapt icUserPlusService;


    @Mock
    private RpcIcOrgPlusService icOrgPlusService;

    private static final String LEGAL_CHECK_CACHE_PREFIX = "legal_check_result_";

    @Value("${batch.check.organ.legal.chche.time:30}")
    private int legalCheckCacheTime;


    @Mock
    private Tedis tedis;

    @Before
    public void setUp(){
        StringOperations<String, Map<String,Boolean>> stringOperations = new MyDefaultStringOperations();
        Map<String,Boolean> organMap = new HashMap<>();
        organMap.put("orgId1",true);
        stringOperations.set("legal_check_result_memberOid",organMap);
        when(tedis.string()).thenReturn(stringOperations);
        ReflectionTestUtils.setField(tedis,"valueOps",stringOperations);
        TedisUtil.setTedis(tedis);
//        when(icUserPlusService.getRealNameByOuid(any())).thenReturn(MockUntils.getRealNameOutput());
        when( icOrgPlusService.createMember(any())).thenReturn(RpcOutput.with(new BizICCreateMemberOutput()));
        BizGetRolesByUsersOutput output = new BizGetRolesByUsersOutput();
        List<BizRoleDetail> roles = new ArrayList<>();
        BizRoleDetail roleDetail= new BizRoleDetail();
        roles.add(roleDetail);
        output.setRoles(roles);
        when(rpcRoleServiceAdapt.getUserRoleListByOrgan(any(),any())).thenReturn(output);
        ReflectionTestUtils.setField(bizOrganizationServiceV3,"legalCheckCacheTime",100);

    }

    @Test
    public void batchAddMemberAndGrantLegalTest(){
        BatchCheckLegalRequest request = new BatchCheckLegalRequest();
        request.setOrganOids(Lists.newArrayList("orgId1"));
        request.setMemberOid("memberOid");
        bizOrganizationServiceV3.batchAddMemberAndGrantLegal(request);
        when(rpcIcOrgPlusServiceAdapt.checkMemberOrCreator(any(), any())).thenReturn(true);

        bizOrganizationServiceV3.batchAddMemberAndGrantLegal(request);
    }

    @Test
    public void batchCheckOrganLegalTest(){
        Map<String, LegalCheckOutput> organMap = new HashMap<>();
        LegalCheckOutput output = new LegalCheckOutput();
        output.setOrganLegal(true);
        output.setHasOrganLegalRole(true);
        output.setOrganLegalFreeze(false);
        organMap.put("orgId1",output);
        when(rpcIcOrgPlusServiceAdapt.batchCheckOrganLegal(any(),any())).thenReturn(organMap);
        BatchCheckLegalRequest request = new BatchCheckLegalRequest();
        request.setOrganOids(Lists.newArrayList("orgId1"));
        request.setMemberOid("memberOid");
        bizOrganizationServiceV3.batchCheckOrganLegal(request);
    }
}