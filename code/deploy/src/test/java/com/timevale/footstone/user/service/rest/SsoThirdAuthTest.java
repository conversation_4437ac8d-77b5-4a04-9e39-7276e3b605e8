package com.timevale.footstone.user.service.rest;

import com.timevale.footstone.user.service.inner.impl.biz.adapt.sso.RpcSsoAuthServiceAdapt;
import com.timevale.footstone.user.service.model.sso.*;
import com.timevale.footstone.user.service.model.organization.response.ListOrganAndRoleResponse;
import com.timevale.footstone.user.service.model.sso.SsoAccountAuthorizeResponse;
import com.timevale.footstone.user.service.model.sso.SsoAccountBindAccountCheckRequest;
import com.timevale.footstone.user.service.model.sso.SsoAccountBindAccountSendRequest;
import com.timevale.footstone.user.service.model.sso.SsoAccountLoginDirectRequest;
import com.timevale.footstone.user.service.model.sso.SsoAccountLoginDirectResponse;
import com.timevale.footstone.user.service.model.sso.SsoAccountLoginFullyDomainRequest;
import com.timevale.footstone.user.service.model.sso.SsoAccountRefOrganThirdInfoResponse;
import com.timevale.footstone.user.service.model.sso.SsoBindAccountCheckResponse;
import com.timevale.footstone.user.service.model.sso.SsoBindAccountSendResponse;
import com.timevale.footstone.user.service.model.sso.SsoOrganThirdInfoGetRequest;
import com.timevale.footstone.user.service.model.sso.SsoOrganThirdInfoUpdateRequest;
import com.timevale.footstone.user.service.model.sso.SsoOrganThirdInfoUpdateResponse;
import com.timevale.footstone.user.service.model.sso.SsoOrganThirdUpdateInfoModel;
import com.timevale.footstone.user.service.rest.inner.SsoThirdAuthLoginInnerRest;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.token.service.model.input.SsoAuthAccessCredentialsInitInput;
import com.timevale.unittest.footstone.configuration.Application;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @since 2023/5/26 10:09
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Application.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class SsoThirdAuthTest {

    @Autowired
    private SsoThirdAuthRest rest;

    @Autowired
    private AccountRest accountRest;

    @Autowired
    private SsoThirdAuthLoginInnerRest loginInnerRest;
    @Autowired
    private RpcSsoAuthServiceAdapt authServiceAdapt;


    //@Test
    public void loginDomain(){
        SsoAccountLoginDirectRequest request = new SsoAccountLoginDirectRequest();
        request.setDomain("dingding");
        SsoAccountLoginDirectResponse directResponse = rest.loginDomain(request).getData();
        Assert.assertEquals(directResponse.getFullyDomain(), "testsso.tsign.cn");
        Assert.assertEquals(directResponse.getIfExistDomain(), Boolean.TRUE);

        request.setDomain("junit");

        directResponse = rest.loginDomain(request).getData();
        Assert.assertNull(directResponse.getFullyDomain());
        Assert.assertEquals(directResponse.getIfExistDomain(), Boolean.FALSE);


        SsoAccountLoginFullyDomainRequest fullyDomainRequest = new SsoAccountLoginFullyDomainRequest();
        fullyDomainRequest.setFullyDomain("testsso.tsign.cn");
        directResponse = rest.loginFullyDomain(fullyDomainRequest).getData();
        Assert.assertEquals(directResponse.getDomain(), "dingding");
        Assert.assertEquals(directResponse.getIfExistDomain(), Boolean.TRUE);

        fullyDomainRequest.setFullyDomain("junit.tsign.cn");
        directResponse = rest.loginFullyDomain(fullyDomainRequest).getData();
        Assert.assertNull(directResponse.getDomain());
        Assert.assertEquals(directResponse.getIfExistDomain(), Boolean.FALSE);
    }

//    @Test
//    public void accountRefOrganThirdInfo(){
//        MockHttpServletRequest request = new MockHttpServletRequest();
//        request.addHeader("X-Tsign-Open-App-Id","**********");
//        request.addHeader("X-Tsign-operator","d6bb1aa228034ac29a467eb0bc11d0aa");
//        RequestContext.put("httpServletRequest",request);
//
//
//        SsoOrganThirdInfoGetRequest getRequest = new SsoOrganThirdInfoGetRequest();
//        getRequest.setSourceKey("G643e6c32e4b02a8f9ee71669");
//        SsoAccountRefOrganThirdInfoResponse thirdInfoResponse = rest.accountRefOrganThirdInfo(getRequest).getData();
//        Assert.assertEquals(thirdInfoResponse.getSource(), "dingding");
//        Assert.assertEquals(thirdInfoResponse.getOrganList().size() > 0, true);
//
//        try{
//            getRequest.setSourceKey("GXXXXX");
//            thirdInfoResponse = rest.accountRefOrganThirdInfo(getRequest,null).getData();
//            Assert.assertEquals(Boolean.TRUE, Boolean.FALSE);
//        }catch (FootstoneUserErrors.IllegalRequest e){
//            Assert.assertEquals(Boolean.TRUE, Boolean.TRUE);
//        }
//
//        SsoOrganThirdInfoUpdateRequest updateRequest = new SsoOrganThirdInfoUpdateRequest();
//        updateRequest.setOrganId("xxxxxxxx");
//        updateRequest.setSource("dingding");
//        List<SsoOrganThirdUpdateInfoModel> thirdOrganList = new ArrayList<>();
//        SsoOrganThirdUpdateInfoModel updateInfoModel = new SsoOrganThirdUpdateInfoModel();
//        updateInfoModel.setThirdOrganId(UUIDHelper.genUUID("junit"));
//        updateInfoModel.setThirdOrganName("junit");
//        thirdOrganList.add(updateInfoModel);
//        updateRequest.setThirdOrganList(thirdOrganList);
//        SsoOrganThirdInfoUpdateResponse updateResponse = rest.organThirdUpdateInfo(updateRequest).getData();
//        Assert.assertEquals(updateResponse.getUpdateStatus(), Boolean.TRUE);
//    }

//    @Test
//    public void accountAuthorize(){
//        try {
//            MockHttpServletRequest request = new MockHttpServletRequest();
//            request.addHeader("X-Tsign-Open-App-Id","**********");
//            request.addHeader("X-Tsign-operator","d6bb1aa228034ac29a467eb0bc11d0aa");
//            request.addHeader("X-Tsign-Open-Access-Code", UUIDHelper.genUUID("junit"));
//            RequestContext.put("httpServletRequest",request);
//
//            try{
//                SsoAccountAuthorizeResponse authorizeResponse = loginInnerRest.accountAuthorize().getData();
//                Assert.assertEquals(Boolean.TRUE, Boolean.FALSE);
//            }catch (FootstoneUserErrors.IllegalRequest e){
//                Assert.assertEquals(Boolean.TRUE, Boolean.TRUE);
//            }
//
//            SsoAuthAccessCredentialsInitInput initInput = new SsoAuthAccessCredentialsInitInput();
//            initInput.setProvider("dingding");
//            initInput.setThirdOrganId("junit_third_id");
//            initInput.setThirdOrganName("junit_third_name");
//            initInput.setThirdUserId("5gkTUh4GxUVqKbo8txBdIwiEiE");
//            initInput.setThirdUserMobile("**********");
//            initInput.setThirdUserName("user_name");
//            String accessCode = authServiceAdapt.initAuthAccessCredentials(initInput).getAccessCode();
//            Assert.assertNotNull(accessCode);
//
//            String accountId =  "5121f65e93484712ae04f869f77ded6e";
//            request = new MockHttpServletRequest();
//            request.addHeader("X-Tsign-Open-App-Id","**********");
//            request.addHeader("X-Tsign-operator", accountId);
//            request.addHeader("X-Tsign-Open-Access-Code",accessCode);
//
//            RequestContext.put("httpServletRequest",request);
//
//            RequestContext.put(HeaderConstants.HEADER_APP_ID,"**********");
//            SsoAccountAuthorizeResponse authorizeResponse = loginInnerRest.accountAuthorize().getData();
//            Assert.assertEquals(authorizeResponse.getLoginStatus(), true);
//            request.addHeader(HeaderUtil.AuthenticationHeaders.HTTP_HEADER_X_TSIGN_TOKEN.header(), authorizeResponse.getToken());
//
//            ReflectionTestUtils.setField(accountRest, "servletRequest", request);
//            ListOrganAndRoleResponse roleResponse = accountRest.listSaasOrgAndRole( accountId, null).getData();
//            Assert.assertTrue(roleResponse.getBizUserInfoOutputs().size() > 0 );
//        }catch (Exception e){
//
//        }
//
//    }

    @Test
    public void accountsBindMobileCheck(){
        SsoAuthAccessCredentialsInitInput initInput = new SsoAuthAccessCredentialsInitInput();
        initInput.setProvider("junit");
        initInput.setThirdOrganId("junit_third_id");
        initInput.setThirdOrganName("junit_third_name");
        initInput.setThirdUserId("user_id");
        initInput.setThirdUserMobile("**********");
        initInput.setThirdUserName("user_name");
        initInput.setThirdUserEmail("<EMAIL>");
        String accessCode = authServiceAdapt.initAuthAccessCredentials(initInput).getAccessCode();
        Assert.assertNotNull(accessCode);

        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("X-Tsign-Open-App-Id","**********");
        request.addHeader("X-Tsign-operator","d6bb1aa228034ac29a467eb0bc11d0aa");
        request.addHeader("X-Tsign-Open-Access-Code",accessCode);
        RequestContext.put("httpServletRequest",request);

        SsoAccountBindAccountSendRequest sendRequest = new SsoAccountBindAccountSendRequest();
        sendRequest.setCredentials("***********");
        sendRequest.setType("MOBILE");
        SsoBindAccountSendResponse sendResponse = loginInnerRest.accountsBindAccountSend(sendRequest).getData();
        Assert.assertEquals(sendResponse.getAuthCodeId().length() > 0,true );

        SsoAccountBindAccountCheckRequest checkRequest = new SsoAccountBindAccountCheckRequest();
        checkRequest.setAuthCodeId(sendResponse.getAuthCodeId());
        checkRequest.setCode("123456");
        checkRequest.setEndpoint("ESIGN_APP");
        SsoBindAccountCheckResponse checkResponse = loginInnerRest.accountsBindAccountCheck(checkRequest).getData();
        Assert.assertEquals(checkResponse.getCheckResult(), true);
    }

    @Test
    public void accountsBindEmailCheck(){
        SsoAuthAccessCredentialsInitInput initInput = new SsoAuthAccessCredentialsInitInput();
        initInput.setProvider("junit");
        initInput.setThirdOrganId("junit_third_id");
        initInput.setThirdOrganName("junit_third_name");
        initInput.setThirdUserId("user_email");
        initInput.setThirdUserMobile("**********");
        initInput.setThirdUserName("user_name");
        initInput.setThirdUserEmail("<EMAIL>");
        String accessCode = authServiceAdapt.initAuthAccessCredentials(initInput).getAccessCode();
        Assert.assertNotNull(accessCode);

        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("X-Tsign-Open-App-Id","**********");
        request.addHeader("X-Tsign-operator","d6bb1aa228034ac29a467eb0bc11d0aa");
        request.addHeader("X-Tsign-Open-Access-Code",accessCode);
        RequestContext.put("httpServletRequest",request);

        SsoAccountBindAccountSendRequest sendRequest = new SsoAccountBindAccountSendRequest();
        sendRequest.setCredentials("<EMAIL>");
        sendRequest.setType("EMAIL");
        SsoBindAccountSendResponse sendResponse = loginInnerRest.accountsBindAccountSend(sendRequest).getData();
        Assert.assertEquals(sendResponse.getAuthCodeId().length() > 0,true );

        SsoAccountBindAccountCheckRequest checkRequest = new SsoAccountBindAccountCheckRequest();
        checkRequest.setAuthCodeId(sendResponse.getAuthCodeId());
        checkRequest.setCode("123456");
        SsoBindAccountCheckResponse checkResponse = loginInnerRest.accountsBindAccountCheck(checkRequest).getData();
        Assert.assertEquals(checkResponse.getCheckResult(), true);
    }

}
