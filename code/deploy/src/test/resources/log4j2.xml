<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j2:configuration>
<!-- log4j2 配置文件 -->
<configuration xmlns:xi="http://www.w3.org/2001/XInclude" name="log4j2Conf" status="WARN" monitorInterval="60">
    <properties>
        <property name="log.path" value="./logs"/>
        <property name="log.encoding" value="UTF-8"/>
        <property name="log.level" value="INFO"/>
        <property name="appName" value="footstone-user"/>
    </properties>

    <xi:include href="log4j2_appenders.xml"/>

    <Loggers>
        <!-- 方法调用日志 -->
        <Logger name="com.timevale.footstone.user.service.aop" level="${log.level}"
                additivity="false">
            <appender-ref ref="INVOKE-APPENDER"/>
            <appender-ref ref="SYS-ERROR-APPENDER"/>
            <appender-ref ref="Console"/>
        </Logger>

        <!-- DAL统一日志 -->
        <Logger name="com.timevale.footstone.user.common.dal" level="${log.level}"
                additivity="false">
            <appender-ref ref="DAL-APPENDER"/>
            <appender-ref ref="SYS-ERROR-APPENDER"/>
            <appender-ref ref="Console"/>
        </Logger>

        <Logger name="org.apache.ibatis" level="${log.level}"
                additivity="false">
            <appender-ref ref="DAL-APPENDER"/>
            <appender-ref ref="SYS-ERROR-APPENDER"/>
            <appender-ref ref="Console"/>
        </Logger>

        <!-- 数据访问层 - SQL -->
        <Logger name="com.alibaba.druid" level="${log.level}"
                additivity="false">
            <appender-ref ref="DAL-APPENDER"/>
            <appender-ref ref="SYS-ERROR-APPENDER"/>
            <appender-ref ref="Console"/>
        </Logger>

        <!-- 业务服务层日志 -->
        <Logger name="com.timevale.footstone.user.service" level="${log.level}"
                additivity="false">
            <appender-ref ref="SYS-BIZ-SERVICE-APPENDER"/>
            <appender-ref ref="SYS-ERROR-APPENDER"/>
            <appender-ref ref="Console"/>
        </Logger>

        <Root level="${log.level}">
            <appender-ref ref="DEFAULT-APPENDER"/>
            <appender-ref ref="SYS-ERROR-APPENDER"/>
            <appender-ref ref="Console"/>
        </Root>
    </Loggers>
</configuration>