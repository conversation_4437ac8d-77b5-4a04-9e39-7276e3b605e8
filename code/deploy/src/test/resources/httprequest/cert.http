# For a quick start check out our HTTP Requests collection (Tools|HTTP Client|Open HTTP Requests Collection) or
# paste cURL into the file and request will be converted to HTTP Request format.
#
# Following HTTP Request Live Templates are available:
# * 'gtrp' and 'gtr' create a GET request with or without query parameters;
# * 'ptr' and 'ptrp' create a POST request with a simple or parameter-like body;
# * 'mptr' and 'fptr' create a POST request to submit a form with a text or file field (multipart/form-data);

### 证书申领（纯制证）
 POST {{host}}/v1/certs/apply
Accept: application/json
X-Tsign-Open-App-Id:{{appId}}
Content-Type: application/json

{
	"commonModel": {
		"address": "杭州",
		"agentName": "云杰",
		"email": "<EMAIL>",
		"mobile": "18812345882"
	},
	"configModel": {
		"algorithm": "RSA"
	},
	"csr": "MIICVjCCAT4CAQAwEzERMA8GA1UEAwwIdGltZXZhbGUwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCBqjTOMTlpJfIKvM+EkWTbYUQ3bn9uorxp/mVi0EVQYzMa3qrsT7id5/DWGtk+/3tZizPKz9QGhnaszzE9OAeIjpmMfNSjCcAgzOY5k1aIuwCGF2vutNqjCztkAVB5pC7FJDeRZtJoXqQ04PkjwgOqIjRcmGoJ2BTOfiFjIMoZVvAKqwILHPEJRXPHtVsFs/I8RRL8zTOW4pMcIkUQWXpN1D4yu9TpvO4fn61w6q7873CFyKqg8uP0zWHTwyHGGkye1Sl/SH6daYFgm66ZWCeVqTrwhHATvWR8CrMvdZ88JYgawMFCEzguNacR6OhRwmqne+EjemaHPiOp5oK/ufc7AgMBAAEwDQYJKoZIhvcNAQEEBQADggEBAGwUMptRdzjfmR/7leWqFfA+k2X5fJ1laF7VkSxxinMH7T/3KdgJ1ChUeAFPCdZopU0WNmXPHWE1bCOoOi+1kOu4rYNnN/0YGXzBFb2y9fcTArsRN1KnkhtBNMDRImds3DeQdOVkBhhqZ0eQMpwALPf/RkIbz8SrwLHtxlMcOk8DVW4qfKiyD3RvcvYSK78ktVvFuzeMjwoQik35VwCnoMeeBPS3Vo80uGYSOy6Y3XXW38d/egczLC0rYkm18Qq3nO3UiyImODKgq5T0UbueC90AfXElGOJa6HmLc6jt5wb2VTQ4TAMV8XW7doQzOTB9Fbq1YCW9E3TG4rQHq3x+GR8=\n",
    "issuer": "WISDOMCA",
  "userModel": {
		"certName": "云杰小公司",
		"licenseNumber": "911232123123123121",
		"licenseType": 1
	}
}

### 证书变更
POST {{host}}/v2/certs/{{certId}}/update
Accept: application/json
X-Tsign-Open-App-Id:{{appId}}
Content-Type: application/json

{
  "certName": "云杰小公司-变更",
  "commonModel": {
    "address": "",
    "agentIdNo": "",
    "agentName": "",
    "email": "",
    "phone": ""
  },
  "csr": "MIICVjCCAT4CAQAwEzERMA8GA1UEAwwIdGltZXZhbGUwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCBqjTOMTlpJfIKvM+EkWTbYUQ3bn9uorxp/mVi0EVQYzMa3qrsT7id5/DWGtk+/3tZizPKz9QGhnaszzE9OAeIjpmMfNSjCcAgzOY5k1aIuwCGF2vutNqjCztkAVB5pC7FJDeRZtJoXqQ04PkjwgOqIjRcmGoJ2BTOfiFjIMoZVvAKqwILHPEJRXPHtVsFs/I8RRL8zTOW4pMcIkUQWXpN1D4yu9TpvO4fn61w6q7873CFyKqg8uP0zWHTwyHGGkye1Sl/SH6daYFgm66ZWCeVqTrwhHATvWR8CrMvdZ88JYgawMFCEzguNacR6OhRwmqne+EjemaHPiOp5oK/ufc7AgMBAAEwDQYJKoZIhvcNAQEEBQADggEBAGwUMptRdzjfmR/7leWqFfA+k2X5fJ1laF7VkSxxinMH7T/3KdgJ1ChUeAFPCdZopU0WNmXPHWE1bCOoOi+1kOu4rYNnN/0YGXzBFb2y9fcTArsRN1KnkhtBNMDRImds3DeQdOVkBhhqZ0eQMpwALPf/RkIbz8SrwLHtxlMcOk8DVW4qfKiyD3RvcvYSK78ktVvFuzeMjwoQik35VwCnoMeeBPS3Vo80uGYSOy6Y3XXW38d/egczLC0rYkm18Qq3nO3UiyImODKgq5T0UbueC90AfXElGOJa6HmLc6jt5wb2VTQ4TAMV8XW7doQzOTB9Fbq1YCW9E3TG4rQHq3x+GR8=\n"
}

### 证书延期
POST {{host}}/v2/certs/{{certId}}/postpone
Accept: application/json
X-Tsign-Open-App-Id:{{appId}}
Content-Type: application/json

{
  "csr": "",
  "validDuration": "1D"
}

###
## 证书下载
GET {{host}}/v2/certs/{{certId}}/download
Accept: application/json
X-Tsign-Open-App-Id:{{appId}}