package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.footstone.user.service.enums.AccountDeleteStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountDeleteModel {

    private String ouid ;

    private String loginAccount;

    private String bizId;

    private String remark ;

    private AccountDeleteStatusEnum deleteStatus;
}
