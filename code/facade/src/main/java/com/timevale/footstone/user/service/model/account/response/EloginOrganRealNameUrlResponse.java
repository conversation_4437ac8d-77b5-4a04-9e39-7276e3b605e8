package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> on 2019-12-23 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EloginOrganRealNameUrlResponse {

    @ApiModelProperty(value = "e签宝企业账号id")
    private String orgId;

    @ApiModelProperty("实名流程id")
    private String flowId;

    @ApiModelProperty(value = "实名url短链")
    private String shortLink;

    @ApiModelProperty(value = "实名url长链")
    private String realNameUrl;
}
