package com.timevale.footstone.user.service.model.thirdlogin.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-05-09 保存用户三方登录信息响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SaveThirdLoginInfoResponse extends ToString {
    /** 用户三方登录信息上下文id */
    private String loginContextId;
}
