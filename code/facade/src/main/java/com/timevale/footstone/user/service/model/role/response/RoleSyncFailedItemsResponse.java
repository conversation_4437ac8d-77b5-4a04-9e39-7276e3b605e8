package com.timevale.footstone.user.service.model.role.response;

import com.timevale.footstone.user.service.model.role.model.RoleSyncFailedItemModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/17 15:50
 */
@Data
public class RoleSyncFailedItemsResponse extends ToString {

  @ApiModelProperty(value = "同步失败列表")
  private List<RoleSyncFailedItemModel> failedItems;

  @ApiModelProperty(value = "同步失败数量")
  private Integer total;
}
