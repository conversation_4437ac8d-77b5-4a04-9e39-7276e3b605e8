package com.timevale.footstone.user.service.model.thirdparty.response;

import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 三方组织同步开启状态信息,适用于web端和飞书企微端展示不同的信息 响应类
 *
 * <AUTHOR>
 * @since 2025/5/9 17:19
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgDataSyncPlatformStateResponse {

    /**
     * 当前可直接同步第三方服务商下的组织架构
     */
    public final static String CAN_SYNC_THIRD_PLATFORM_ORG = "CAN_SYNC_THIRD_PLATFORM_ORG";
    /**
     * 当前已开启同步%s通讯录
     */
    public final static String OPENED_SYNC_ORG = "OPENED_SYNC_ORG";
    /**
     * 当前组织架构可与%s的组织架构同步
     */
    public final static String CAN_SYNC_CURRENT_THIRD_PLATFORM_ORG = "CAN_SYNC_CURRENT_THIRD_PLATFORM_ORG";
    /**
     * 当前已同步第三方组织架构，如需调整组织架构请前往e签宝官网
     */
    public final static String OPENED_OTHER_THIRD_PLATFORM_SYNC_ORG = "OPENED_OTHER_THIRD_PLATFORM_SYNC_ORG";

    /**
     * 是否显示组织通讯录同步的bar
     */
    private Boolean showSyncOrgBar;
    /**
     * 显示哪些平台的开启同步按钮
     * FEI_SHU:展示飞书同步按钮
     * WE_CHAT_WORK:展示企微同步按钮
     * 空值： 不展示
     * {@link  ThirdPartyPlatFormEnum#getPlatform()}
     */
    private List<String> openSyncButtonPlatforms;
    /**
     * 是否显示刷新按钮
     */
    private Boolean showRefreshButton;
    /**
     * 是否显示关闭按钮
     */
    private Boolean showCloseSyncButton;
    /**
     * 当前开启同步的平台
     * FEI_SHU:飞书
     * WE_CHAT_WORK:企微
     * {@link  ThirdPartyPlatFormEnum#getPlatform()}
     */
    private String openedSyncPlatform;
    /**
     * 平台的icon， 是个图片url
     */
    private String platformIconUrl;
    /**
     * 同步描述文本
     * {@link #CAN_SYNC_THIRD_PLATFORM_ORG}
     * {@link #OPENED_SYNC_ORG}
     * {@link #CAN_SYNC_CURRENT_THIRD_PLATFORM_ORG}
     * {@link #OPENED_OTHER_THIRD_PLATFORM_SYNC_ORG}
     */
    private String syncDescribeCode;
    /**
     * 最后一次同步时间
     */
    private Long lastSyncTime;

}
