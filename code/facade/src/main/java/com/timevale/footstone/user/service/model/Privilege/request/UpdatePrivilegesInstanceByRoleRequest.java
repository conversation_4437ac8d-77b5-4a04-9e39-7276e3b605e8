package com.timevale.footstone.user.service.model.Privilege.request;

import com.timevale.footstone.user.service.model.Privilege.mods.UpdatePrivilegeInstanceModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class UpdatePrivilegesInstanceByRoleRequest extends ToString {

    @ApiModelProperty(value = "要授予的业务权限列表", required = true)
    private List<UpdatePrivilegeInstanceModel> grantPrivilegeList;

    @ApiModelProperty(value = "要取消的业务权限列表", required = true)
    private List<UpdatePrivilegeInstanceModel> revokePrivilegeList;
}
