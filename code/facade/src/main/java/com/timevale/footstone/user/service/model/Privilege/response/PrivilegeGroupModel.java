package com.timevale.footstone.user.service.model.Privilege.response;

import com.timevale.footstone.user.service.model.Privilege.mods.BizPrivilegeModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17 11:19
 */
@Data
public class PrivilegeGroupModel extends ToString {

    @ApiModelProperty(value = "权限资源列表")
    private List<PrivilegeResourceResponse> bizPrivilegeResource;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "分组key")
    private String groupKey;

    @ApiModelProperty(value = "分组权重")
    private Integer groupWeight;
}
