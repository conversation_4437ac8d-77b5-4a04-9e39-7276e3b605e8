package com.timevale.footstone.user.service.model.account.senderauth.response;

import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2018-12-24 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WillAuthApplyResponse extends ServiceIdModel {

    @ApiModelProperty(value = "刷脸url")
    private String faceUrl;

    @ApiModelProperty(value = "刷脸Token，for小程序")
    private String faceToken;
}
