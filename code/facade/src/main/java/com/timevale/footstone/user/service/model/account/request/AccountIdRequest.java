package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-20 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccountIdRequest extends ToString {

    @ApiModelProperty(value = "账号id", required = true)
    private String accountId;
}
