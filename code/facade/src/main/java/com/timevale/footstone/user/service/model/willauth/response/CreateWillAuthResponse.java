package com.timevale.footstone.user.service.model.willauth.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
public class CreateWillAuthResponse {

    @ApiModelProperty(value = "业务Id")
    private String bizId;

    @ApiModelProperty(value = "业务跳转地址")
    private String url;

    @ApiModelProperty(value = "短链")
    private String shortUrl;

    @ApiModelProperty(value = "意愿流程ID")
    private String willFlowId;
}
