package com.timevale.footstone.user.service.model.account.idcard.response;

import io.swagger.annotations.ApiModelProperty;

public class AccountCheckExistsResponse {
    public AccountCheckExistsResponse() {
    }

    public AccountCheckExistsResponse(Boolean accountExist) {
        this.accountExist = accountExist;
    }

    @ApiModelProperty(value = "账号是否存在 返回true or false")
    private Boolean accountExist;

    public Boolean getAccountExist() {
        return accountExist;
    }

    public void setAccountExist(Boolean accountExist) {
        this.accountExist = accountExist;
    }
}
