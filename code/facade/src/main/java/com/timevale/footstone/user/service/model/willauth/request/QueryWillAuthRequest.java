package com.timevale.footstone.user.service.model.willauth.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryWillAuthRequest {


    @ApiModelProperty(value = "业务Id")
    private String bizId;

    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @ApiModelProperty(value = "发起人账号id")
    private String ouid;
}
