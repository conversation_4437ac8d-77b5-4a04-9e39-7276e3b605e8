package com.timevale.footstone.user.service.model.account.idcard.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

/** <AUTHOR> on 2019-02-14 */
public class CheckMobileEmailRequest extends ToString {

    @ApiModelProperty(value = "手机号/邮箱", required = true)
    private String principal;

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }
}
