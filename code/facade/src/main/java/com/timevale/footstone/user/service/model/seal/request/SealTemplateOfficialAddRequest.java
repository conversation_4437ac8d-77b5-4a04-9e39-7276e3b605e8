/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealTemplateOfficialAddRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.seal.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/219:49]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.footstone.user.service.model.seal.model.SealTemplateOfficialModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 类名：SealTemplateOfficialAddRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/219:49]创建类 by flh
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SealTemplateOfficialAddRequest extends SealTemplateOfficialModel {

    @ApiModelProperty(value = "是否保存印章，默认保存")
    private boolean saveFlag = true;

    @ApiModelProperty(value = "是否返回印章下载地址，默认true")
    private boolean downloadFlag = true;

    @ApiModelProperty(value = "是否校验印章样式，默认false")
    private boolean checkFlag;
}
