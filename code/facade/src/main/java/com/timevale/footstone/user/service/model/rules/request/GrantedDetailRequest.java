package com.timevale.footstone.user.service.model.rules.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2020-12-14 11:09
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrantedDetailRequest extends ToString implements Serializable {

    private static final long serialVersionUID = 600137145922296260L;
    @ApiModelProperty(name = "resourceId", value = "资源ID", required = true)
    public String resourceId;

    @ApiModelProperty(name = "ruleGrantStatus", value = "授权状态")
    public String ruleGrantStatus;

    @ApiModelProperty(name = "offset", value = "数据下标")
    public Integer offset;

    @ApiModelProperty(name = "size", value = "返回数据条数")
    public Integer size;

    @ApiModelProperty(name = "type", value = "授权类型 1-企业内部 2-企业外部")
    public Integer type;
}
