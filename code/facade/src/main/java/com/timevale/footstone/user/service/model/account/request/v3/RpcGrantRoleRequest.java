package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.footstone.user.service.model.ReqHandler;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 业务授权请求参数
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
public class RpcGrantRoleRequest extends ToString implements ReqHandler {

    @ApiModelProperty(value = "授权ID")
    private String orgId;

    private String personId;

    private String roleKey;

    @Override
    public ReqHandler validate() {
        return this;
    }

    @Override
    public ReqHandler format() {
        return this;
    }
}