package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.model.account.mods.RealnameServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ValidBankFourCodeRequest extends RealnameServiceIdModel {

    @ApiModelProperty(value = "验证码", required = true)
    private String code;
}
