package com.timevale.footstone.user.service.model.thirdparty.response;

import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据部门id列表和成员id列表，获取需要前端组件渲染的数据 响应类
 *
 * <AUTHOR>
 * @since 2025/5/8 17:45
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GetFrontRenderThirdIdResponse {

    /**
     * 是否需要前端渲染
     */
    private Boolean needFrontRender;
    /**
     * 需要用哪个平台的渲染组件渲染数据
     * WE_CHAT_WORK : 企微
     * {@link  ThirdPartyPlatFormEnum#getPlatform()}
     */
    private String platform;
    /**
     * 三方企业id
     */
    private String tenantKey;
    /**
     * e签宝部门id列表
     */
    private List<DepartmentIdBean> departmentInfos;
    /**
     * e签宝部门id列表
     */
    private List<AccountIdBean> accountInfos;

    /**
     * 三方部门id信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepartmentIdBean {
        /**
         * e签宝部门id
         */
        private String departmentId;
        /**
         * 三方部门id
         */
        private String thirdDepartmentId;
        /**
         * 三方部门id路径
         */
        private List<String> thirdDepartmentIdPath;
    }

    /**
     * 三方用户id信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountIdBean {
        /**
         * e签宝用户oid
         */
        private String accountId;
        /**
         * 三方用户id
         */
        private String thirdUserId;


    }
}
