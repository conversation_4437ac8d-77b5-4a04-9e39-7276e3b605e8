package com.timevale.footstone.user.service.model.account.idcard.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class BatchGetByThirdIdsRequest extends ToString {

    @ApiModelProperty(value = "第三方类型，支付宝：ALI_PAY、微信平台：WE_CHAT、钉钉平台：DING_TALK", required = true)
    private String key;

    @ApiModelProperty(value = "第三方用户id", required = true)
    private List<String> userIds;

}
