package com.timevale.footstone.user.service.model.seal.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class SealWhiteListModel extends ToString {

    @ApiModelProperty(value = "白名单id", required = true)
    private Long id;

    @ApiModelProperty(value = "原因", required = true)
    private Integer reason;

    @ApiModelProperty(value = "企业名", required = true)
    private String orgName;

    @ApiModelProperty(value = "企业gid", required = true)
    private String orgGid;

    @ApiModelProperty(value = "创建人", required = true)
    private String creator;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;
}
