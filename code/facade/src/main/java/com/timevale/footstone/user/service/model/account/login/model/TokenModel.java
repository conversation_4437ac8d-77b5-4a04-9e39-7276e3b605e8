package com.timevale.footstone.user.service.model.account.login.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TokenModel extends ToString {

    @ApiModelProperty(value = "登录成功的令牌", required = true)
    private String token;
}
