package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-03-20 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OriginAuthApplyRequest extends ToString {

    @ApiModelProperty(value = "业务类型，与preServiceId二选一。设置/修改签署密码：RESET_SIGN_PWD", required = true)
    private String bizType;

    @ApiModelProperty(value = "上次流程事务id，与bizType二选一")
    private String preServiceId;

    @ApiModelProperty(value = "原密码", required = true)
    private String password;

    @ApiModelProperty(value = "原密码的编码")
    private String encrypter;
}
