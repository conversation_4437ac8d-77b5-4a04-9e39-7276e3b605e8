package com.timevale.footstone.user.service.model.account.login.request;

import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 沙箱登录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SandBoxLoginRequest extends ToString {

    @ApiModelProperty(value = "当前用户ouid", required = true)
    private String ouid;

    @ApiModelProperty(value = "授权码", required = true)
    private String accessKey;

    @ApiModelProperty(value = "登录相关配置")
    private LoginParams loginParams = new LoginParams();
}
