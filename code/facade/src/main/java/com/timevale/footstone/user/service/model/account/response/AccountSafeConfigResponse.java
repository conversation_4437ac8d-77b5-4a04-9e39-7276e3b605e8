package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @author: sicheng
 * @since: 2020-06-05 15:58
 **/
@AllArgsConstructor
@Data
public class AccountSafeConfigResponse {

    @ApiModelProperty(value = "0-登录二次验证，1-异地登录提醒", required = true)
    private Integer category;

    @ApiModelProperty(value = "true/false代表阀值开关",required = true)
    private Boolean isOpen;

}
