/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyWithoutAccountResponse.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.response <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1619:31]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：CertApplyWithoutAccountResponse.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1619:31]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class CertApplyWithoutAccountResponse {

    @ApiModelProperty(value = "证书id")
    private String certId;

    @ApiModelProperty(value = "申领成功的证书通道名称")
    private String caRoute;

    @ApiModelProperty(value = "签名证书base64")
    private String signCert;

    @ApiModelProperty(value = "算法，RSA,SM2")
    private String algorithm;

    /** 加密证书 */
    @ApiModelProperty(value = "加密证书")
    private String encCert;

    /** 签名证书SN */
    @ApiModelProperty(value = "签名证书SN")
    private String signSn;

    /** 证书开始时间 */
    @ApiModelProperty(value = "证书开始时间")
    private long startDate;

    /** 证书结束时间 */
    @ApiModelProperty(value = "证书结束时间")
    private long endDate;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息")
    private String ext;
}