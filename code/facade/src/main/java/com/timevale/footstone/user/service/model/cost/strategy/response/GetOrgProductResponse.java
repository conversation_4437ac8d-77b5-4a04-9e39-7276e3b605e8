package com.timevale.footstone.user.service.model.cost.strategy.response;

import com.timevale.footstone.user.service.model.cost.strategy.model.OrgProductModel;
import com.timevale.footstone.user.service.model.cost.strategy.model.SaaSProductModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/14 17:09
 */
@Data
public class GetOrgProductResponse {

    @ApiModelProperty(value = "saas产品列表")
    private SaaSProductModel saas;

    @ApiModelProperty(value = "其他产品列表")
    private List<OrgProductModel> other;
}
