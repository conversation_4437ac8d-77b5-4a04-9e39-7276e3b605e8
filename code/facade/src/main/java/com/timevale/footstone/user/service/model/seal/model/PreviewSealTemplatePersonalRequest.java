/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：PreviewSealTemplatePersonalRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.seal.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/10/1417:31]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：PreviewSealTemplatePersonalRequest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/10/1417:31]创建类 by flh
 * <AUTHOR>
 */
@Data
public class PreviewSealTemplatePersonalRequest extends SealTemplatePersonalModel {

    @ApiModelProperty(value = "是否下载印章，默认true")
    private boolean downloadFlag = true;

    @ApiModelProperty(value = "是否校验，默认false")
    private boolean checkFlag;

    @ApiModelProperty(value = "印章名称，优先使用此名称，不传默认使用账号名称")
    private String sealName;
}