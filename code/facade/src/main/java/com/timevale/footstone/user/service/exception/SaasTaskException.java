package com.timevale.footstone.user.service.exception;

import com.timevale.esign.platform.toolkit.utils.exceptions.BizException;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
public interface SaasTaskException extends ErrorsBase {

  class MembersExportTaskExist extends BizException {

    public MembersExportTaskExist() {
      super("请等待上一个导出人员任务结束");
    }
  }
}
