package com.timevale.footstone.user.service.model.cost.strategy.response;

import com.timevale.footstone.user.service.model.cost.strategy.model.CostStrategyModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/12 15:23
 */
@Data
public class CostStrategieResponse {

    @ApiModelProperty(value = "扣费策略列表")
    private List<CostStrategyModel> strategies;
}
