package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.footstone.user.service.enums.CustomBizSceneEnum;
import com.timevale.footstone.user.service.model.ReqHandler;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025/4/14 21:08
 */
@ApiModel
@Data
public abstract class ApplyBizAuthBaseRequest extends ToString  implements ReqHandler {

    @ApiModelProperty(value = "业务AppId")
    @NotBlank
    private String appId;

  @ApiModelProperty(
      value =
          "是否强制用户重复实名认证，默认false。\n"
              + "\n"
              + "true - 允许重复实名认证\n"
              + "\n"
              + "false - 不允许重复实名认证\n"
              + "\n")
  private boolean repeatableRealName;



    /**
     * 业务场景
     * @see CustomBizSceneEnum
     */
    @ApiModelProperty(value = "业务场景")
    @NotBlank
    private String customBizScene;

    @ApiModelProperty(value = "业务自定义编码,长度最大190")
    private String customBizNum;


    @ApiModelProperty(value = "业务上下文")
    private ApplyBizContext bizContext;



    @ApiModelProperty(value = "交互配置")
    private ApplyInteractiveConfig interactiveConfig;

    @Override
    public ReqHandler validate() {
        return this;
    }

    @Override
    public ReqHandler format() {
        if(interactiveConfig == null){
            interactiveConfig = new ApplyInteractiveConfig();
        }

        if(bizContext == null){
            bizContext = new ApplyBizContext();
        }
        return this;
    }


    public String getWebLang() {
        return interactiveConfig == null ? null : interactiveConfig.getLang();
    }

    public String getLoginContextId() {
        return interactiveConfig == null ? null : interactiveConfig.getLoginContextId();
    }
}
