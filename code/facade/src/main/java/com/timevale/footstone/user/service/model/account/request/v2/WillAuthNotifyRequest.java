package com.timevale.footstone.user.service.model.account.request.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ Id: WillAuthNotifyRequest.java, v0.1 2021年10月29日 08:42 WangYuWu $
 */
@Data
public class WillAuthNotifyRequest {

    /**
     * 实名认证的流程ID
     */
    @ApiModelProperty(value = "意愿任务业务ID")
    private String      bizId;

    /**
     * e签宝账号OID
     */
    @ApiModelProperty(value = "个人账户id")
    private String      accountId;

    /**
     * 固定为：WILL_FINISH
     */
    @ApiModelProperty(value = "标记该通知的业务类型")
    private String      action;

    /**
     * 意愿子任务ID
     */
    @ApiModelProperty(value = "意愿子任务ID")
    private String      willAuthId;

    /**
     * 意愿方式
     */
    @ApiModelProperty(value = "意愿方式")
    private String      willAuthType;

    /**
     * 是否成功. true - 成功; false - 失败
     */
    @ApiModelProperty(value = "是否成功")
    private Boolean     success;
}
