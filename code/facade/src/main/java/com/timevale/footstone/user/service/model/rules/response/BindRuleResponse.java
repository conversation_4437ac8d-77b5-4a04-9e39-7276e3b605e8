package com.timevale.footstone.user.service.model.rules.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2019/12/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BindRuleResponse {

    @ApiModelProperty(value = "授权id", required = true)
    private String ruleGrantId;

    @ApiModelProperty(value = "授权签署流程id")
    private String flowId;

    @ApiModelProperty(value = "授权签署url")
    private String signUrl;
}
