/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyCommonModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1615:22]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：CertApplyCommonModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1615:22]创建类 by flh
 * <AUTHOR>
 */
@Data
public class CertApplyCommonModel {

    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "联系电话")
    private String phone;
    @ApiModelProperty(value = "代理人名称")
    private String agentName;
    @ApiModelProperty(value = "代理人证件号")
    private String agentIdNo;
    /** 组织 */
    @ApiModelProperty(value = "组织,CFCA-SINGE必填参数")
    private String organization;

    /** 国家 */
    @ApiModelProperty(value = "国家")
    private String country;

    /** 省 */
    @ApiModelProperty(value = "省")
    private String province;

    /** 市 */
    @ApiModelProperty(value = "市")
    private String city;

    /** 部门 */
    @ApiModelProperty(value = "部门")
    private String department;
}