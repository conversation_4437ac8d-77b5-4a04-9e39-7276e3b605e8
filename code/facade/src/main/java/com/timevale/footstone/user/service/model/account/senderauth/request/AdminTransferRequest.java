package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-02-21 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AdminTransferRequest extends ServiceIdModel {

    @ApiModelProperty(value = "组织ID", required = true)
    private String orgId;

    @ApiModelProperty(value = "新管理员的账号id，要求已经是组织成员并且已实名", required = true)
    private String newAdminAccountId;

    @ApiModelProperty(value = "被转授者的用户accountUid")
    private String switchedAccountId;

    @ApiModelProperty(value = "被转授者的用户Oid")
    private String switchedAccountOid;
}
