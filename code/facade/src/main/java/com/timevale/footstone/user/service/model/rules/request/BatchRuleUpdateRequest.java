package com.timevale.footstone.user.service.model.rules.request;

import com.timevale.footstone.user.service.model.rules.request.bean.UrlBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年01月16日 15:44:00
 */
@Data
public class BatchRuleUpdateRequest extends UrlBaseRequest {

    @ApiModelProperty(value = "授权资源列表", required = true)
    List<BatchRuleItem> ruleGrantedList;

    @ApiModelProperty(value = "资源id", required = true)
    private String resourceId;

    @ApiModelProperty(value = "授权类型  1-企业内授权  2-企业外授权", required = true)
    private Integer type;
}
