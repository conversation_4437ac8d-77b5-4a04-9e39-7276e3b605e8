package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.footstone.user.service.model.account.mods.*;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-10 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgCreateRequest
        extends AccountCreateRequest<OrgIdcardCollection, OrgProperties, OrgGlobalCredentials> {

    @ApiModelProperty(value = "组织创建者oid")
    private String creater;

    /**
     * 校验证件号和名称<br>
     *
     * <ul>
     * 说明：
     * <li>只要有证件号，名称就不能为空
     * </ul>
     */
    public void validateVitalParams() {
        OrgCredentials credentials = this.getCredentials();
        String name = null;

        if(null != credentials) {
            if(credentials.hasValue() &&
                    (null == this.getProperties() || StringUtils.isBlank(name = this.getProperties().getName()))) {
                // 有证件号却没有名称的抛出异常提示
                AssertSupport.assertNotnull(name, new ErrorsBase.MissingArgumentsWith("name"));
            }
        }
    }
}
