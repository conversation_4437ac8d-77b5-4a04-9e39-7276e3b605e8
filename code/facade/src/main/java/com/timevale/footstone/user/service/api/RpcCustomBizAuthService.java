package com.timevale.footstone.user.service.api;

import com.timevale.footstone.user.service.ServiceName;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.v3.RpcApplyOrgBizAuthRequest;
import com.timevale.footstone.user.service.model.account.request.v3.RpcApplyPersonBizAuthRequest;
import com.timevale.footstone.user.service.model.account.request.v3.RpcBizAuthQueryRequest;
import com.timevale.footstone.user.service.model.account.request.v3.RpcGrantRoleRequest;
import com.timevale.footstone.user.service.model.account.response.v3.RpcApplyBizAuthResponse;
import com.timevale.footstone.user.service.model.account.response.v3.RpcQueryOrgBizAuthResponse;
import com.timevale.footstone.user.service.model.account.response.v3.RpcQueryPersonBizAuthResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 *
 * 业务授权
 * <AUTHOR>
 * @version 2025/04/14 14:03
 */
@RestClient(serviceId = ServiceName.name)
public interface RpcCustomBizAuthService {


    /**
     * 企业业务申请
     * @param request
     * @return
     */
    WrapperResponse<RpcApplyBizAuthResponse> applyOrgBizAuth(
            RpcApplyOrgBizAuthRequest request);


    /**
     * 个人业务申请
     * @param request
     * @return
     */
    WrapperResponse<RpcApplyBizAuthResponse> applyPersonBizAuth(
            RpcApplyPersonBizAuthRequest request);


    /**
     * 授权个人业务查询
     * @param request
     * @return
     */
    WrapperResponse<RpcQueryPersonBizAuthResponse> queryPersonBizAuth(
            RpcBizAuthQueryRequest request);


    /**
     * 授权企业业务查询
     * @param request
     * @return
     */
    WrapperResponse<RpcQueryOrgBizAuthResponse> queryOrgBizAuth(
            RpcBizAuthQueryRequest request);

    @Deprecated
    void grantUserRole(RpcGrantRoleRequest request);

}
