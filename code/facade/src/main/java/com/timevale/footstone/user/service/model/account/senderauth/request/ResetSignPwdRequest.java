package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResetSignPwdRequest extends ServiceIdModel {

    @ApiModelProperty(value = "签署密码", required = true)
    private String password;

    @ApiModelProperty(value = "密码的编码方式，默认MD5")
    private String encrypter;
}
