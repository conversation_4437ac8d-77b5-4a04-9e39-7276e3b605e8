package com.timevale.footstone.user.service.model.Privilege.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class PrivilegeModel extends ToString {


    @ApiModelProperty(value = "权限操作名称")
    private String targetClassKeyName;

    @ApiModelProperty(value = "权限操作资源")
    private String targetClassKey;

    @ApiModelProperty(value = "权限操作类型")
    private String operationPermit;

    @ApiModelProperty(value = "权限操作类型名称")
    private String operationPermitName;

    @ApiModelProperty(value = "权限操作类型描述")
    private String operationDisc;

    @ApiModelProperty(value = "操作权重")
    private Integer weight;
}
