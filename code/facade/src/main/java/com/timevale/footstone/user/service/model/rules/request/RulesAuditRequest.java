package com.timevale.footstone.user.service.model.rules.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @Date 2021/3/11 6:02 下午
 * 单独模块方便后续拆出
 */
@Data
@NoArgsConstructor
public class RulesAuditRequest extends ToString {

    @ApiModelProperty(value = "审核id", required = true)
    private Long auditId;

    @ApiModelProperty(value = "1:通过  2:拒绝", required = true)
    private Integer status;

    @ApiModelProperty(value = "审核人", required = true)
    private String auditUser;

    @ApiModelProperty(value = "原因", required = false)
    private String reason;
}
