package com.timevale.footstone.user.service.model.dept.mods;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/27
 */
@Data
public class CreateDeptFailedModel {

    public static final String PATH_TOO_LONG = "当前创建的部门深度超过最大级别，无法创建";

    public static final String DEPT_NAME_ERROR = "部门名称只支持字母、数字、汉字、括号、-、_字符。且长度不能超过50";
    public static final String DEPT_NAME_NOT_ALLOWED = "不允许部门名称为所有部门或未分配部门";

    private String deptNames;

    private String errorReason;

    public CreateDeptFailedModel() {
    }

    public CreateDeptFailedModel(String deptNames, String errorReason) {
        this.deptNames = deptNames;
        this.errorReason = errorReason;
    }


    public static CreateDeptFailedModel bulidPathTooLong(String deptNames) {
        return new CreateDeptFailedModel(deptNames,PATH_TOO_LONG);
    }

    public static CreateDeptFailedModel bulidNameError(String deptNames) {
        return new CreateDeptFailedModel(deptNames,DEPT_NAME_ERROR);
    }

    public static CreateDeptFailedModel bulidNameNotAllowed(String deptNames) {
        return new CreateDeptFailedModel(deptNames, DEPT_NAME_NOT_ALLOWED);
    }

}
