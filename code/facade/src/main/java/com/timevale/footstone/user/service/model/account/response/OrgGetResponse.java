package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.model.service.biz.helper.acc.BizICUserOutputHelper;
import com.timevale.account.service.model.service.biz.helper.acc.impl.IdcardCollectionHelper;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.idcard.IdcardProjThirdparty;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.footstone.user.service.model.account.mods.OrgModel;
import com.timevale.mandarin.base.util.CollectionUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import static com.timevale.account.service.model.constants.BuiltinProperty.*;

/** <AUTHOR> on 2019-01-21 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgGetResponse extends OrgModel {

    @ApiModelProperty("账号Id")
    private String orgId;

    @ApiModelProperty(value = "证件号", required = true)
    private String idNumber;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty(value = "第三方平台的用户id")
    private String thirdPartyUserId;

    @ApiModelProperty("第三方平台的用户类型")
    private String thirdPartyUserType;

    @ApiModelProperty(value = "实名状态")
    private boolean status;

    @ApiModelProperty(value = "实名流程id")
    private String realnameFlowid;

    @ApiModelProperty(value = "实名证据点")
    private String realnameEvidencePointId;

    public OrgGetResponse() {
    }

    public OrgGetResponse(BizICUserOutput in) {
        BizICUserOutputHelper helper = new BizICUserOutputHelper(in);

        setOrgId(in.getId().getOuid());
        setName(helper.getPropertyValue(INFO_NAME));
        setOrgLegalIdNumber(helper.getPropertyValue(INFO_ORG_LEGAL_IDNO));
        setOrgLegalName(helper.getPropertyValue(INFO_ORG_LEGAL_NAME));
        setRealnameEvidencePointId(helper.getPropertyValue(REALNAME_EVIDENCE_POINT_ID));
        List<Property> creds = in.getCredentials();
        if (CollectionUtils.isNotEmpty(creds)) {
            // 只能显示一个
            Property cred = creds.get(0);
            setIdNumber(cred.getValue());
            setIdType(cred.getType());
        }

        IdcardCollectionHelper idcardsHelper = helper.getIdcardsHelper();

        IdcardProjThirdparty ptd = idcardsHelper.getProjThirdparty();
        if (ptd != null) {
            setThirdPartyUserId(ptd.getThirdpartyUserId());
            setThirdPartyUserType(ptd.getThirdpartyUserType());
        }
    }
}
