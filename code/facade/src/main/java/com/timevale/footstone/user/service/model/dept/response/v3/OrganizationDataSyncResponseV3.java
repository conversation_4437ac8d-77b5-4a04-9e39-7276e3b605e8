package com.timevale.footstone.user.service.model.dept.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/8/10 17:22
 */

@Data
public class OrganizationDataSyncResponseV3 extends ToString {

    @ApiModelProperty("企业是否开通了三方组织架构同步")
    private Boolean isOpenThirdSync;

    @ApiModelProperty("三方组织架构对应的平台-code")
    private String platform;


    @ApiModelProperty("三方组织架构对应的平台-描述")
    private String platformName;
}
