package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.model.rules.request.bean.UrlBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-02-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetOrganPageUrlRequest extends UrlBaseRequest {

    @ApiModelProperty(value = "企业id", required = true)
    @NotBlank(message = "企业oid不能为空")
    private String orgId;
}
