package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/10
 */
@Data
public class AccountDeleteByAuthCodeRequest {

    @ApiModelProperty(value = "业务Id", required = true)
    private String serviceId;

    @ApiModelProperty(value = "验证码", required = true)
    private String authCode;

    public void valid() {
        AssertSupport.assertNotnull(serviceId, new ErrorsBase.MissingArgumentsWith("serviceId"));
        AssertSupport.assertNotnull(authCode, new ErrorsBase.MissingArgumentsWith("authCode"));
    }
}
