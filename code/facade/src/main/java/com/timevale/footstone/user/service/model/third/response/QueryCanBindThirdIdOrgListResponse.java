package com.timevale.footstone.user.service.model.third.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 获取当前用户可直接绑定三方企业id的企业列表 响应类
 *
 * <AUTHOR>
 * @since 2025/3/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryCanBindThirdIdOrgListResponse extends ToString {
    /**
     * 可绑定的org信息列表
     */
    private List<CanBindOrgInfo> orgList;

    /**
     * 可绑定的org信息
     */
    @Data
    public static class CanBindOrgInfo {
        @ApiModelProperty("企业oid")
        private String orgOid;
        @ApiModelProperty("企业gid")
        private String orgGid;
        @ApiModelProperty("企业名称")
        private String orgName;
    }
}
