package com.timevale.footstone.user.service.model.thirdparty.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrgDataSyncStateResponse extends ToString {

    @ApiModelProperty("是否展示同步/停用按钮")
    private Boolean displaySync = false;

    @ApiModelProperty("组织同步开启状态")
    private Boolean syncEnabledStatus = false;
}
