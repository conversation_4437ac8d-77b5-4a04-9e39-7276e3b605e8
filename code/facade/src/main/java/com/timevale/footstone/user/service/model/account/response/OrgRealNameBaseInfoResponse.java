package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.biz.rlnm.output.BizAccountRealnameOutput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrgRealNameBaseInfoResponse extends UserRealNameBaseInfoResponse {

    @ApiModelProperty("是否实名组织")
    private Boolean isRealnameOrg;


    public OrgRealNameBaseInfoResponse init(
            BizAccountRealnameOutput in, String ouid, String organGuid, String type, String code, Boolean isolationWhteList) {
        super.init(in, ouid, type, code);
        setIsRealnameOrg(organGuid != null && RealnameStatus.ACCEPT.equals(in.getStatus()));
        // 如果在老的隔离白名单内，则status和账号实名状态保持一致（为的是解决天印实名组织问题），否则和原来一样（与是否实名组织保持一致）
        // 看不懂不要骂我，有问题找无忌（***********）或李慢（***********），<EMAIL>， <EMAIL>
        setStatus(isolationWhteList ? RealnameStatus.ACCEPT.equals(in.getStatus()) : getIsRealnameOrg());
        return this;
    }

}
