package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-12-23 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EloginConfigRequest extends ToString {

    @ApiModelProperty(value = "第三方key", required = true)
    private String thirdKey;

    @ApiModelProperty(value = "配置内容", required = true)
    private String content;

    public void valid() {
        AssertSupport.assertNotnull(thirdKey, new ErrorsBase.MissingArgumentsWith("thirdKey"));
        AssertSupport.assertNotnull(content, new ErrorsBase.MissingArgumentsWith("content"));
    }
}
