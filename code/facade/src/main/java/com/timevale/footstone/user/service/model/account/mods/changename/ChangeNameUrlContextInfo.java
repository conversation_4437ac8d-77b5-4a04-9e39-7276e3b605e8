package com.timevale.footstone.user.service.model.account.mods.changename;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 更名流程配置信息
 *
 * <AUTHOR>
 * @DATE 2024/6/20 10:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChangeNameUrlContextInfo extends ToString {
    public static final String defaultReturnCountdown = "5";
    
    @ApiModelProperty(value = "完成后的重定向地址")
    private String returnUrl;
    @ApiModelProperty(value = "倒计时 默认5s")
    private String returnCountdown = defaultReturnCountdown;
    @ApiModelProperty(value = "颜色")
    private String colour;
}