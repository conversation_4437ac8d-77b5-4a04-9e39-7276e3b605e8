package com.timevale.footstone.user.service.model.role.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class CreateRoleResponse {

    public CreateRoleResponse() {
    }

    public CreateRoleResponse(List<String> roleId) {
        this.roleId = roleId;
    }

    @ApiModelProperty(value = "角色ID")
    private List<String> roleId;
}
