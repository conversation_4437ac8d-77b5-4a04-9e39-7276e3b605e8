package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class SetSignPwdRequest extends ToString {

    @ApiModelProperty(value = "加密后的密文")
    private String password;

    @ApiModelProperty(value = "加密方式 (md5,sha等)")
    private String encrypter;
}
