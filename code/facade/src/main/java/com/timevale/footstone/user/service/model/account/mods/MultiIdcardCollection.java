package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> on 2019-01-10
 */
@Data
public class MultiIdcardCollection {

    @ApiModelProperty(value = "的第三方用户ID")
    private List<IdcardThirdpartyDetail> thirdparties;

    @ApiModelProperty(value = "唯一的第三方用户ID（第三方平台拥有appId）")
    private IdcardProjThirdpartyDetail projThirdparty;

}
