/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyWithoutAccountRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.request <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1614:39]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.footstone.user.service.model.cert.model.CertApplyCommonModel;
import com.timevale.footstone.user.service.model.cert.model.CertApplyConfigModel;
import com.timevale.footstone.user.service.model.cert.model.CertApplyUserModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 类名：CertApplyWithoutAccountRequest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1614:39]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class CertApplyWithoutAccountRequest extends ToString {

    @ApiModelProperty(required = true, value = "证书请求p10")
    @NotBlank(message = "证书请求p10不能为空")
    private String csr;

    @ApiModelProperty(required = true, value = "指定CA通道，暂只支持CA枚举值:智慧CA-WISDOMCA(个人/企业证书);ZJCA-SINGLE(浙江CA单证书,支持修改,个人/企业证书);", example = "ZJCA-SINGLE")
    private String issuer;

    @ApiModelProperty(required = true, value = "制证用户信息")
    @NotNull(message = "用户信息不能为空")
    @Valid
    private CertApplyUserModel userModel;

    @ApiModelProperty(required = true, value = "制证非常用信息")
    private CertApplyCommonModel commonModel;

    @ApiModelProperty(required = true, value = "制证配置信息")
    private CertApplyConfigModel configModel;

    @ApiModelProperty(value = "业务方类型")
    /**
     * 1 - 代表天印
     * 后续有别的添加在考虑枚举值
     */
    private Integer bizType;
}