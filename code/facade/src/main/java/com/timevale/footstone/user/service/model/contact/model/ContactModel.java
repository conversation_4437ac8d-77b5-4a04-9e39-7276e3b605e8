/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：ContactModel.java <br>
 * 包：com.timevale.footstone.user.service.model.contact.model <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:01]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.contact.model;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.mods.ro.BizResourceOwner;
import com.timevale.footstone.user.service.model.mods.request.AccountIds;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：ContactModel.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:01]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class ContactModel {

    @ApiModelProperty(value = "用户标识，手机号或邮箱")
    private String uniqueId;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "企业名称")
    private String affliation;

    @ApiModelProperty(value = "备注信息")
    private String comment;

    @ApiModelProperty(value = "联系人id")
    private String contactId;

    @ApiModelProperty(value = "联系人账号的id")
    private AccountIds accountIds;

    @ApiModelProperty(value = "实名状态")
    private RealnameStatus realnameStatus;

    @ApiModelProperty(value = "实名后的名称")
    private String realName;

    @ApiModelProperty(value = "账号rid")
    private String rid;
}
