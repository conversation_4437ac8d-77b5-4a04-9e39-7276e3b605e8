package com.timevale.footstone.user.service.model.account.mods;

import com.google.common.collect.Lists;
import com.timevale.account.service.model.service.biz.acc.output.BizAccountTOutput;
import com.timevale.account.service.model.service.biz.builder.acc.impl.abs.AbstractBizAccountBuilder;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.account.service.model.constants.BuiltinCredentials.*;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_ORG_LEGAL_NAME;

@Data
public class OrgCredentials implements AccountPropConv {

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("组织机构代码")
    private String orgCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("工商注册号")
    private String regCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("统一社会信用代码")
    private String usccCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("税务登记证号")
    private String taxCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("工商登记证")
    private String businessRegistCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("法人代码证")
    private String legalPersonCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("事业单位法人证书")
    private String entLegalPersonCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("社会团体登记证书")
    private String socialRegCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("民办非企业登记证书")
    private String privateNonEntRegCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("外国企业常驻代表机构登记证")
    private String foreignEntRegCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("政府批文")
    private String govApprovalCode;

    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("自定义")
    private String customCode;
    @ModelFieldDecoration(DecorationType.ORGAN_CRED)
    @ApiModelProperty("未知证件类型")
    private String unknown;

    /**
     * 利用反射获取OrgCredentials.class的属性数组
     *
     * @return OrgCredentials类的属性数组
     */
    private Field[] loadOrgCredentialsFields() {
        Field[] fields = OrgCredentials.class.getDeclaredFields();
        Arrays.stream(fields)
                .forEach(
                        f -> {
                            if (!f.isAccessible()) {
                                f.setAccessible(true);
                            }
                        });
        return fields;
    }

    /**
     * 判断OrgCredentials对象的属性是否有值<br>
     *
     * <ul>
     * 说明：
     * <li>只要有一个属性有值，返回true
     * <li>所有属性都没有值，返回false
     * </ul>
     *
     * @return 属性是否有值
     */
    public boolean hasValue() {
        for (Field field : loadOrgCredentialsFields()) {
            try {
                String value = (String) field.get(this);
                if(StringUtils.isNotBlank(value)){
                    return true;
                }
            } catch (Exception cause) {
                return false;
            }
        }
        return false;
    }

    @Override
    public void conv(AbstractBizAccountBuilder builder) {
        builder.buildCredentials()
                .add(CRED_ORG_CODE, getOrgCode())
                .add(CRED_ORG_REGCODE, getRegCode())
                .add(CRED_ORG_USCC, getUsccCode())
                .add(CRED_ORG_TAX_REGISTTATION_CODE, getTaxCode())
                .add(CRED_ORG_BUSINESS_REGISTTATION_CODE, getBusinessRegistCode())
                .add(CRED_ORG_LEGAL_PERSON_CODE, getLegalPersonCode())
                .add(CRED_ORG_ENT_LEGAL_PERSON_CODE, getEntLegalPersonCode())
                .add(CRED_ORG_SOCIAL_REG_CODE, getSocialRegCode())
                .add(CRED_ORG_PRIVATE_NON_ENT_REG_CODE, getPrivateNonEntRegCode())
                .add(CRED_ORG_FOREIGN_ENT_REG_CODE, getForeignEntRegCode())
                .add(CRED_ORG_GOV_APPROVAL, getGovApprovalCode())
                .add(CODE_ORG_CUSTOM, getCustomCode())
                .add(CRED_ORG_UNKNOWN, getUnknown());
    }

    @Override
    public List<Property> conv() {
        if(hasValue()) {
            Property property = new Property();
            if(StringUtils.isNotBlank(getUsccCode())) {
                property.setType(CRED_ORG_USCC);
                property.setValue(getUsccCode());
            } else if(StringUtils.isNotBlank(getRegCode())) {
                property.setType(CRED_ORG_REGCODE);
                property.setValue(getRegCode());
            } else {
                throw new FootstoneUserErrors.CredentialsTypeNotSupport();
            }
            return Lists.newArrayList(property);
        }
        return Lists.newArrayList();
    }

    @Override
    public OrgCredentials init(BizAccountTOutput<ContentSecurity> icuser) {
        Map<String, String> propGroup =
                icuser.getCredentials().stream()
                        .filter(p -> p.getValue() != null)
                        .collect(
                                Collectors.toMap(
                                        Property::getType, Property::getValue, (k1, k2) -> k1));

        setOrgCode(propGroup.get(INFO_ORG_LEGAL_NAME));
        setRegCode(propGroup.get(CRED_ORG_REGCODE));
        setUsccCode(propGroup.get(CRED_ORG_USCC));
        setTaxCode(propGroup.get(CRED_ORG_TAX_REGISTTATION_CODE));
        setBusinessRegistCode(propGroup.get(CRED_ORG_BUSINESS_REGISTTATION_CODE));
        setLegalPersonCode(propGroup.get(CRED_ORG_LEGAL_PERSON_CODE));
        setEntLegalPersonCode(propGroup.get(CRED_ORG_ENT_LEGAL_PERSON_CODE));
        setSocialRegCode(propGroup.get(CRED_ORG_SOCIAL_REG_CODE));
        setPrivateNonEntRegCode(propGroup.get(CRED_ORG_PRIVATE_NON_ENT_REG_CODE));
        setForeignEntRegCode(propGroup.get(CRED_ORG_FOREIGN_ENT_REG_CODE));
        setGovApprovalCode(propGroup.get(CRED_ORG_GOV_APPROVAL));
        setCustomCode(propGroup.get(CODE_ORG_CUSTOM));
        setUnknown(propGroup.get(CRED_ORG_UNKNOWN));
        return this;
    }
}
