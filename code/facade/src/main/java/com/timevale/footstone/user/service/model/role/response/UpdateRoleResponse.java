package com.timevale.footstone.user.service.model.role.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UpdateRoleResponse extends ToString {

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色描述")
    private String disc;

    @ApiModelProperty(value = "父角色ID")
    private String parentRoleId;
}
