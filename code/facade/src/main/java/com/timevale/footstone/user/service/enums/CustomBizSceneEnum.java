package com.timevale.footstone.user.service.enums;

/**
 * 业务场景：
 *
 * <p>COMMON = 通用实名
 *
 * <p>CERT_APPLY_ESHIELD-天印证书申请
 * CERT_APPLY_TIANYIN-e签盾证书申请
 * CERT_APPLY_TCLOUD-云平台证书申请
 *
 * <AUTHOR>
 * @since 2025/4/16 15:58
 */
public enum CustomBizSceneEnum {

    COMMON("COMMON", "通用认证"),
    CERT_APPLY_TIANYIN("CERT_APPLY_TIANYIN", "天印证书申请"),
    CERT_APPLY_ESHIELD("CERT_APPLY_ESHIELD", "e签盾证书申请"),
    CERT_APPLY_TCLOUD("CERT_APPLY_TCLOUD", "云平台证书申请"),
    ;

    private String code;
    private String desc;
    CustomBizSceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomBizSceneEnum codeOf(String code) {
        for (CustomBizSceneEnum value : CustomBizSceneEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return COMMON;
    }
}
