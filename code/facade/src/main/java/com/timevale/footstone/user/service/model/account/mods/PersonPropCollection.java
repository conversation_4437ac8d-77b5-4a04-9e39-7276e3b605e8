package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** <AUTHOR> on 2019-01-14 */
@Data
public class PersonPropCollection {

    @ApiModelProperty("个人属性")
    private PersonProperties properties;

    @ApiModelProperty("个人证件")
    private PersonCredentials credentials;

    @ApiModelProperty("个人联系方式")
    private CommonContacts contacts;

    public PersonPropCollection init(BizICUserOutput in) {
        setContacts(new CommonContacts().init(in));
        setCredentials(new PersonCredentials().init(in));
        setProperties(new PersonProperties().init(in));
        return this;
    }
}
