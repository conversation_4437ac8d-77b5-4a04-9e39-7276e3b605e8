/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealTemplatePersonalAddRequest <br>
 * 包：com.timevale.footstone.user.service.model.seal.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/219:48]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.footstone.user.service.model.seal.model.SealTemplatePersonalModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：SealTemplatePersonalAddRequest <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/219:48]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class SealTemplatePersonalAddRequest extends SealTemplatePersonalModel {

    @ApiModelProperty(value = "是否保存印章，默认保存")
    private boolean saveFlag = true;

    @ApiModelProperty(value = "是否下载印章，默认true")
    private boolean downloadFlag = true;

    @ApiModelProperty(value = "是否校验，默认false")
    private boolean checkFlag;
}
