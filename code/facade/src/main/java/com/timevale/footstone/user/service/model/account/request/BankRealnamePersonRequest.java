package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BankRealnamePersonRequest extends ToString {

    @ApiModelProperty(value = "银行卡号", required = true)
    private String cardno;

    @ApiModelProperty(value = "银行预留手机号", required = true)
    private String mobile;

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "身份证号码", required = true)
    private String idno;
}
