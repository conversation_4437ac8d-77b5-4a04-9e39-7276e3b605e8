package com.timevale.footstone.user.service.model.account.request.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ Id: AuthorizedSubject.java, v0.1 2021年07月14日 14:08 WangYuWu $
 */
@Data
public class AuthorizedSubject {

    /**
     * 被授权主体名字
     */
    @ApiModelProperty(value = "被授权主体名字")
    private String name;

    /**
     * 被授权主体证件号
     */
    @ApiModelProperty(value = "被授权主体证件号码")
    private String certNo;

    /**
     * 被授权主体证件类型
     */
    @ApiModelProperty(value = "被授权主体证件类型")
    private String certType;

    @ApiModelProperty("法定代表人姓名")
    private String legalRepName;


    @ApiModelProperty("法定代表人证件类型")
    private String legalRepCertType;


    @ApiModelProperty("法定代表人证件号")
    private String legalRepCertNo;


    @ApiModelProperty("组织机构类型")
    private Integer organizationType;
}
