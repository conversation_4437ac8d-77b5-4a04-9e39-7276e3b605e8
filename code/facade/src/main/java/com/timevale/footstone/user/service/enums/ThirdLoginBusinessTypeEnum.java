package com.timevale.footstone.user.service.enums;

public enum ThirdLoginBusinessTypeEnum {

    ENTERPRISE_CONSOLE("ENTERPRISE_CONSOLE", "企业控制台"),
    ENTERPRISE_BIND_CONFIG("ENTERPRISE_BIND_CONFIG", " 企业关联设置"),
    TEMPLATES_MANAGE("TEMPLATES_MANAGE", "模版管理"),
    CONTRACT_MANAGE("CONTRACT_MANAGE", "合同管理"),

    FILE_FLOW_START("FILE_FLOW_START", "文件签署发起页"),

    TEMPLATE_FLOW_START("TEMPLATE_FLOW_START", "模版签署发起页"),

    FLOW_FILL("FLOW_FILL", "填写/签署任务页"),
    FLOW_VIEW("FLOW_VIEW", "签署结果查看页");

    ThirdLoginBusinessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ThirdLoginBusinessTypeEnum getInstance(String code) {
        for (ThirdLoginBusinessTypeEnum businessTypeEnum : ThirdLoginBusinessTypeEnum.values()) {
            if (businessTypeEnum.getCode().equals(code)) {
                return businessTypeEnum;
            }
        }

        return null;
    }

    String code;
    String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
