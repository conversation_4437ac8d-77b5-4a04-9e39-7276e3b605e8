package com.timevale.footstone.user.service.model.seal.response;

import com.timevale.footstone.user.service.model.seal.model.SealWhiteListTypeModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealWhiteListTypeResponse extends ToString {

   @ApiModelProperty(value = "白名单类型枚举列表")
   private List<SealWhiteListTypeModel> types;
}
