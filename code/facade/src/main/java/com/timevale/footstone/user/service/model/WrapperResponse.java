package com.timevale.footstone.user.service.model;

import com.timevale.footstone.base.model.enums.CommonResultEnum;
import com.timevale.footstone.base.model.enums.IResultEnum;
import com.timevale.footstone.base.model.response.BaseResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
public class WrapperResponse<T> extends BaseResult<T> {

    public WrapperResponse() {}

    public WrapperResponse(T data) {
        this.setData(data);
    }

    public WrapperResponse(int code, String message, T data) {
        this.setCode(code);
        this.setData(data);
        this.setMessage(message);
    }

    public WrapperResponse(IResultEnum resultEnum, T data) {
        this.setCode(resultEnum.getCode());
        this.setMessage(resultEnum.getMsg());
        this.setData(data);
    }

    public static <T> WrapperResponse<T> with(T data) {
        return new WrapperResponse<>(CommonResultEnum.SUCCESS, data);
    }

    public static <T> WrapperResponse<T> with(IResultEnum data) {
        return new WrapperResponse<>(data, null);
    }

    public static <T> WrapperResponse<T> empty() {
        return new WrapperResponse<>(CommonResultEnum.SUCCESS, null);
    }

    public static <T> WrapperResponse<T> emptyFail(T data) {
        return new WrapperResponse<>(CommonResultEnum.PARTIAL_FAILURE, data);
    }
}
