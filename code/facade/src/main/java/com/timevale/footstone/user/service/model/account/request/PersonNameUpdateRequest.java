package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PersonNameUpdateRequest extends ToString {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "意愿serviceId")
    private String serviceId;

    public void valid() {
        AssertSupport.assertNotnull(name, new ErrorsBase.MissingArgumentsWith("name"));
    }
}
