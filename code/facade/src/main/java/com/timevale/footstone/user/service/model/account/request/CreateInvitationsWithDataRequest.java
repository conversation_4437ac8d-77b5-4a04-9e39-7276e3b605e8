package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2019年08月29日 17:48:00
 */
@Data
public class CreateInvitationsWithDataRequest extends InvitationBaseRequest {

    @ApiModelProperty(value = "被邀请人姓名")
    @Size(max = 64,message = "名字长度不超过64")
    private String name;

    @ApiModelProperty(value = "被邀请人手机号")
    @Pattern(regexp = "^1\\d{10}$|^$",message = "手机格式")
    private String mobile;

    @ApiModelProperty(value = "被邀请人邮件")
    @Email
    private String email;

    @Pattern(regexp = "^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|x|X){1}$|^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$",message = "身份证号格式错误")
    @ApiModelProperty(value = "被邀请人证件号")
    private String idNumber;

    @ApiModelProperty(value = "被邀请人证件类型")
    private String inviteeCertType;

    @Size(max = 64,message = "企业名称长度不超过64")
    @ApiModelProperty(value = "企业名称")
    private String organName;

    @ApiModelProperty(value = "企业证件号")
    private String organCertNo;

    @ApiModelProperty(value = "企业证件类型")
    private String organCertType;

    @ApiModelProperty(value = "企业法人证件号")
    private String legalNo;

    @Size(max = 64,message = "企业法人姓名长度不超过64")
    @ApiModelProperty(value = "企业法人姓名")
    private String legalName;

    @ApiModelProperty(value = "企业法人证件类型")
    private String legalCertType;

    @ApiModelProperty(value = "邀请类型 邀请个人实名PERSON,邀请企业ORGAN",required = true)
    @NotEmpty(message = "inviteType 不能为空")
    private String inviteType;
}
