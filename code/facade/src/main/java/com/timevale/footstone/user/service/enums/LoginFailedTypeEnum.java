package com.timevale.footstone.user.service.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年08月03日 14:11:00
 */
public enum LoginFailedTypeEnum {
    PASSWORD_INCORRECT(1,"密码错误"),
    CODE_AUTH_INCORRECT(2,"验证码错误"),
    ACCOUNT_LOCKED(3,"账号锁定登录"),
    ACCOUNT_FREZZE(4,"账号冻结登录"),
    PASSOWRD_NOT_SET(5,"账号未设置密码，通过密码登录");


    int value;

    String reason;

    public static Map<Integer,String> failTypeMap = new HashMap();

    static{
        Arrays.stream(LoginFailedTypeEnum.values()).forEach( i-> {
            failTypeMap.put(i.getValue(),i.getReson());
        });
    }

    private LoginFailedTypeEnum(int value,String reason){
        this.value = value;
        this.reason = reason;
    }

    public int getValue(){
        return this.value;
    }

    public String getReson(){
        return this.reason;
    }

    public static String getReason(int value){
        return failTypeMap.get(value);
    }
}
