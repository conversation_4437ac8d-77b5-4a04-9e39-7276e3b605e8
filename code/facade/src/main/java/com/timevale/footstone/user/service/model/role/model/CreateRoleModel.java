package com.timevale.footstone.user.service.model.role.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-26
 */
@Data
public class CreateRoleModel extends ToString {

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色描述")
    private String disc;

    @ApiModelProperty(value = "父角色ID")
    private String parentRoleId;
}
