/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：app-parent <br>
 * 文件名：AddressModel.java <br>
 * 包：com.timevale.account.webserver.model.contact <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/2323:30]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.address.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：AddressModel.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/2323:30]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AddressModel {

    @ApiModelProperty(value = "收货人")
    private String contact;

    @ApiModelProperty(value = "联系方式")
    private String contactNumber;

    @ApiModelProperty(value = "省")
    private AddressTagModel province;

    @ApiModelProperty(value = "市")
    private AddressTagModel city;

    @ApiModelProperty(value = "区")
    private AddressTagModel area;

    @ApiModelProperty(value = "收货地址详情")
    private String addressDetail;

    @ApiModelProperty(value = "完整收货地址")
    private String addressAll;

    @ApiModelProperty(value = "是否为默认收货地址")
    private boolean defaultFlag;

    @ApiModelProperty(value = "收货地址id")
    private String addressId;
}
