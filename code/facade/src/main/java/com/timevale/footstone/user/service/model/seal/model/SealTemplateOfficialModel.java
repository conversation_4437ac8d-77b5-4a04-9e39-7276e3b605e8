/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealTemplateOfficialModel.java <br>
 * 包：com.timevale.footstone.user.service.model.seal.model <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/711:25]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：SealTemplateOfficialModel.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/711:25]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealTemplateOfficialModel extends SealCommonModel {

    @ApiModelProperty(value = "模板类型，TEMPLATE_ROUND-圆章，TEMPLATE_OVAL-椭圆章", required = true)
    private String type;

    @ApiModelProperty(value = "中心图案类型，STAR-五角星，NONE-无图案", required = true)
    private String central;

    @ApiModelProperty(value = "横向文")
    private String htext;

    @ApiModelProperty(value = "下弦文")
    private String qtext;

    @ApiModelProperty(value = "印章颜色，RED-红色，BLUE-蓝色，BLACK-黑色", required = true)
    private String color;

    @ApiModelProperty(value = "印章业务类型，COMMON-无业务类型(其他)，CANCELLATION-作废章, PUBLIC-公章，CONTRACT-合同专用章, FINANCE-财务章, INVOICE-发票专用章")
    private String sealBizType;

    @ApiModelProperty(value = "印章是否做透明处理，默认true")
    private boolean transparentFlag = true;

    @ApiModelProperty(value = "印章样式，NONE-无，OLD-印章做旧(随机样式，如果需指定做旧样式，指定1-12样式id，如OLD_1)，默认NONE")
    private String style = "NONE";
}
