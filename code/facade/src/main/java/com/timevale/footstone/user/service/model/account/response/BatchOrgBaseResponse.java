package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.OrgModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
@ApiModel(value = "机构账号批量创建的单个响应")
public class BatchOrgBaseResponse<T extends OrgModel> {
    @ApiModelProperty(value = "企业id")
    private String orgId;

    @ApiModelProperty(value = "创建者id")
    private String creator;


    @ApiModelProperty(value = "创建者的信息")
    private T orgInfo;

    public BatchOrgBaseResponse(String orgId, String creator, T orgInfo) {
        this.orgId = orgId;
        this.creator = creator;
        this.orgInfo = orgInfo;
    }
}
