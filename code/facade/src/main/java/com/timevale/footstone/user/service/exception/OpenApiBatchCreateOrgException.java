package com.timevale.footstone.user.service.exception;

import com.timevale.footstone.user.service.model.account.response.BatchOrgBaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
public class OpenApiBatchCreateOrgException extends BaseDataBizException {

    private List<BatchOrgBaseResponse> data;

    public OpenApiBatchCreateOrgException(String message, List<BatchOrgBaseResponse> accountIds) {
        super(message);
        this.data = accountIds;
    }

    @Override
    public Object getData() {
        return data;
    }
}
