package com.timevale.footstone.user.service.model.cost.strategy.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/12 15:44
 */
@Data
public class UpdateCostStrategyRequest extends ToString {

    @ApiModelProperty(value = "策略key", required = true)
    private Integer strategyKey;
}
