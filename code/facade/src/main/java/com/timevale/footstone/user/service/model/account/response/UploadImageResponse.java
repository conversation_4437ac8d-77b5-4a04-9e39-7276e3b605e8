package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-21
 */
@Data
public class UploadImageResponse {

    public UploadImageResponse() {
    }

    public UploadImageResponse(String url) {
        this.url = url;
    }

    @ApiModelProperty(value = "图片链接")
    private String url;
}
