package com.timevale.footstone.user.service.model.account.request.v2;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/9 21:04
 */
@Data
public class SsoUserSilentLoginRequest extends ToString {

    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty("用户OID")
    private String accountId;

}
