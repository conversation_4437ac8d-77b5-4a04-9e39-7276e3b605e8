/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：AddressTagModel.java <br>
 * 包：com.timevale.footstone.user.model.address.model <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/2414:44]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.address.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：AddressTagModel.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/2414:44]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AddressTagModel extends ToString {

    @ApiModelProperty("地址代码")
    private String addressCode;

    @ApiModelProperty("地址")
    private String address;
}
