package com.timevale.footstone.user.service.enums;

import com.timevale.mandarin.base.enums.BaseResultCodeEnum;
import com.timevale.mandarin.base.exception.BaseRuntimeException;

/**
 * 功能说明：TODO
 *
 * @return <br>
 *     修改历史：<br>
 *     1.[2018年06月13日上午10:09] 创建方法 by chenxing
 */
public enum BillTypeEnum {
    COMMON(1, "通用套餐"),
    PROJECTSPECIAL(2, "项目专用套餐"),
    DEVELOPERSPECIAL(3, "开发者专用套餐");
    private int type;
    private String description;

    BillTypeEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static BillTypeEnum getBillTypeEnumByType(int type) {
        for (BillTypeEnum billTypeEnum : BillTypeEnum.values()) {
            if (billTypeEnum.getType() == type) {
                return billTypeEnum;
            }
        }

        throw new BaseRuntimeException(BaseResultCodeEnum.ILLEGAL_ARGUMENT);
    }
}
