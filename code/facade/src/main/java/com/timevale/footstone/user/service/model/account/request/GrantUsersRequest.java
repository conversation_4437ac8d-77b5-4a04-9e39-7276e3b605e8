package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GrantUsersRequest extends ToString {

    @ApiModelProperty(value = "角色ID列表", required = true)
    private List<String> roleIds = new ArrayList<>();

    @ApiModelProperty(value = "成员ID", required = true)
    @NotEmpty(message = "成员ID不能为空")
    private String memberId;

}
