package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** */
@Data
@ApiModel(value = "个人账号基础响应")
public class AccountBaseResponse {

    /** 账号id */
    @ApiModelProperty(value = "账号id")
    private String accountId;

    public AccountBaseResponse(String accountId) {
        this.accountId = accountId;
    }
}
