package com.timevale.footstone.user.service.model.role.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class DeleteRolesRequest extends ToString {

    @ApiModelProperty(value = "组织ID", required = true)
    private String organId;

    @ApiModelProperty(value = "需要删除的角色id", required = true)
    private List<String> roles;
}
