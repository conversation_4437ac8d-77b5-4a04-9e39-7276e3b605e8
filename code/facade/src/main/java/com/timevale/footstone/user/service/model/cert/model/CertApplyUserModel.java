/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyUserModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1615:10]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 类名：CertApplyUserModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1615:10]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class CertApplyUserModel {

    @NotEmpty(message = "证书名称不能为空")
    @ApiModelProperty(required = true, value = "证书名称")
    private String certName;

    /**
     * 默认个人身份证
     */
    @NotNull(message = "证件类型不能为空")
    @ApiModelProperty(required = true, value = "证件类型")
    private Integer licenseType;

    @NotEmpty(message = "证件号码不能为空")
    @ApiModelProperty(required = true, value = "证件号码")
    private String licenseNumber;

    @ApiModelProperty(value = "用户自定义参数")
    private String ou;
}