package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CreateOrganResponse extends ToString {

    public CreateOrganResponse() {
    }

    public CreateOrganResponse(String orgId) {
        this.orgId = orgId;
    }

    @ApiModelProperty(value = "组织ID")
    private String orgId;
}
