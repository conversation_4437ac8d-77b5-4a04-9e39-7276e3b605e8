/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：LegalAuthApplyRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.seal.request <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1214:21]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.esign.compontent.common.base.exception.OpenTreatyErrorCodeEnum;
import com.timevale.esign.compontent.common.base.exception.PaasBaseRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 类名：LegalAuthApplyRequest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1214:21]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class EnterpriseFourVerifyRequest extends ToString {

    /** 企业名称 */
    @ApiModelProperty(value = "组织名称", required = true)
    private String name;

    /** 统一社会信用代码 */
    @ApiModelProperty(value = "企业证件号", required = true)
    @NotBlank(message = "企业证件号不能为空")
    private String orgCode;

    /** 法定代表人姓名 */
    @ApiModelProperty(value = "法定代表人姓名", required = true)
    @NotBlank(message = "法定代表人姓名不能为空")
    private String legalRepName;

    /** 法人身份证 */
    @ApiModelProperty(value = "法定代表人证件号", required = true)
    @NotBlank(message = "法定代表人证件号不能为空")
    private String legalRepCertNo;

  @ApiModelProperty(value = "授权法人证件类型, 大陆身份证")
  private String legalCertType = BuiltinCredentials.CRED_PSN_CH_IDCARD;

    /**
     * 判断入参请求是否正常
     */
    public Boolean requestValid() {
        Boolean result = Boolean.TRUE;
        if (StringUtils.isBlank(this.getLegalRepCertNo()) || StringUtils.isBlank(this.getLegalCertType())) {
            result = Boolean.FALSE;
        }
        return result;
    }
}