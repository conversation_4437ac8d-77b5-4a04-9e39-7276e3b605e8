package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/14 21:08
 */
@ApiModel
@Data
public  class ApplyInteractiveConfig extends ToString {



    @ApiModelProperty(value = "完成后跳转页面， ,长度最大1024")
    private String redirectUrl;

    @ApiModelProperty(value = "自定义域名appId")
    private String dnsAppId;


    @ApiModelProperty(value = "当前语言环境：zh-cn.us-en")
    private String lang;


    @ApiModelProperty(value = "登陆态上下文ID")
    private String loginContextId;


}
