package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.enums.LocalConfigLang;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2019-01-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateLocalConfigRequest extends ToString {

    @ApiModelProperty(value = "语言类型：zh-CN=简体中文，en-US=英语(美国)，默认简体中文")
    private String lang;

    public void valid(){
        if(LocalConfigLang.validBad(lang)){
            throw new FootstoneUserErrors.InvalidParameter();
        }
    }
}
