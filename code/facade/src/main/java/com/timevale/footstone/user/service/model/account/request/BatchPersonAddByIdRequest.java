package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class BatchPersonAddByIdRequest extends ToString {

    @ApiModelProperty(value = "批量创建账号的账号体列表", required = true)
    private List<PersonAddByIdRequest> accountInfoList;
}
