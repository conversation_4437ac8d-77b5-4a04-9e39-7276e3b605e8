package com.timevale.footstone.user.service.model.rules.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/5
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddRulesResponse {

    @ApiModelProperty(value = "授权id列表", required = true)
    private List<String> ruleGrantIds;

    @ApiModelProperty(value = "授权签署流程id")
    private String flowId;

    @ApiModelProperty(value = "授权签署url")
    private String signUrl;
}
