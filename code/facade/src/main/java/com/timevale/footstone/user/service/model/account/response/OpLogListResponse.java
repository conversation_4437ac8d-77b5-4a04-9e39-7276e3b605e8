package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.OpLogModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("操作日志列表")
@AllArgsConstructor
@NoArgsConstructor
public class OpLogListResponse {
    @ApiModelProperty(value = "分页查询加速因子")
    private String cursor;

    @ApiModelProperty(value = "结果列表")
    private List<OpLogModel> list;
    @ApiModelProperty(value = "当前页码")
    private int page;
    @ApiModelProperty(value = "总数量")
    private int total;
}
