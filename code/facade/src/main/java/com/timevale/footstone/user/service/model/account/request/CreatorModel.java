package com.timevale.footstone.user.service.model.account.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class CreatorModel<T, O> {
    @ApiModelProperty(value = "企业账号详情", required = true)
    private T orgInfo;
    

    @ApiModelProperty("个人账号详情,和creator,thirdPartyPersonInfo是互斥参数，如果同时传递 以personInfo为准")
    private O creatorInfo;
}
