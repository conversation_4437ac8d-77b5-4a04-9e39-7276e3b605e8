package com.timevale.footstone.user.service.model.realname.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** <AUTHOR> on 2019-01-05 */
@Data
public class PersonRealnameRequest extends AuthConfigParamRequest{

    @ApiModelProperty(value = "实名后跳转地址", required = true)
    private String redirectUrl;

    @ApiModelProperty(value = "实名后通知地址")
    private String notifyUrl;

    @ApiModelProperty(value = "是否显示实名结果页面,默认true")
    private Boolean showResultPage;

    @ApiModelProperty("实名方式, 2:个人实名认证:信息比对+短信验证码, 3:个人刷脸认证:腾讯微众/芝麻信用")
    private Integer type;

    @ApiModelProperty(value = "业务上下文ID")
    private String contextId;

    @ApiModelProperty(value = "业务APP-ID")
    private String bizAppId;

    @ApiModelProperty(value = "业务上下文")
    private Map<String, String> context;

    @ApiModelProperty(value = "是否重复实名")
    private Boolean repeatIdentity;

    @ApiModelProperty(value = "个人信息中允许编辑的属性")
    private List<String> indivEditableInfo;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "证件号")
    private String certNo;

    @ApiModelProperty(value = "证件类型")
    private String certType;

    @ApiModelProperty(value = "手机号")
    private String mobileNo;

    @ApiModelProperty(value = "银行卡号")
    private String bankCardNo;

    public Map<String, String> getContext() {
        if (this.context == null) {
            this.context = new HashMap<>();
        }
        return this.context;
    }
}
