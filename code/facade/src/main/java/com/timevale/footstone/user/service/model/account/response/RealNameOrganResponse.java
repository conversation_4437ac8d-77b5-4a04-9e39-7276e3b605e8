package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.organization.service.enums.ActivateEnum;
import com.timevale.account.service.enums.RealnameStatus;
import java.util.Objects;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version 2024/6/26 13:55
 */
@Data
public class RealNameOrganResponse {

    public RealNameOrganResponse() {
    }

    public RealNameOrganResponse(String oid, String gid, String accountUid,
            RealnameStatus status, String organGuid, Integer activate) {
        this.oid = oid;
        this.gid = gid;
        this.accountUid = accountUid;
        this.status = status;
        this.organGuid = organGuid;
        this.activate = activate;
    }

    private String oid;

    private String gid;

    private String accountUid;

    private RealnameStatus status;

    /**
     * 组织表下，企业gid
     */
    private String organGuid;

    /**
     * 是否激活态
     */
    private Integer activate;


    public boolean isRealName() {
        return Objects.equals(status, RealnameStatus.ACCEPT);
    }


    public boolean isRealNameOrg() {
        return StringUtils.isNotBlank(organGuid)
                && Objects.equals(status, RealnameStatus.ACCEPT);
    }


    public boolean isActiveRealNameOrg() {
        return StringUtils.isNotBlank(organGuid)
                && Objects.equals(activate, ActivateEnum.ACTIVATED.getCode())
                && Objects.equals(status, RealnameStatus.ACCEPT);
    }


    public boolean isResetIdentityRealNameOrg() {
        return !isRealName() && StringUtils.isNotBlank(getOrganGuid())
                && Objects.equals(getActivate(), ActivateEnum.ACTIVATED.getCode());
    }


    public boolean isUnRealNameOrg() {
        return !isRealName() && StringUtils.isBlank(getOrganGuid());
    }


}
