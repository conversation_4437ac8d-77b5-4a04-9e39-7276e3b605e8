package com.timevale.footstone.user.service.model.rules.request;

import com.timevale.footstone.user.service.model.rules.request.bean.UrlBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class BindRuleRequest extends UrlBaseRequest {

    @ApiModelProperty(value = "资源id", required = true)
    private String resourceId;

    @ApiModelProperty(value = "资源类型", required = true)
    private String resourceType;

    @ApiModelProperty(value = "模板key", required = true)
    private String templateKey;

    @ApiModelProperty(value = "角色类型", required = true)
    private String roleKey;

    @Deprecated
    @ApiModelProperty(value = "自动落章")
    private Boolean autoFall;

    @ApiModelProperty(value = "落章方式，0-手动落章，1-免意愿且自动落章，2-免意愿且手动落章， 默认手动落章")
    private Integer fallType;

    @ApiModelProperty(value = "授权范围(模板id/ALL/HR/FINANCE)", required = true)
    private String scope;

    @ApiModelProperty(value = "通知配值(默认开启)", required = false)
    private Boolean notifySetting = true;

    @ApiModelProperty(value = "授权人", required = true)
    private String granter;

    @ApiModelProperty(value = "授权人名称", required = true)
    private String granterName;

    @ApiModelProperty(value = "授权人证件号", required = true)
    private String granterCode;

    @ApiModelProperty(value = "被授权对象", required = true)
    private String grantedUser;

    @ApiModelProperty(value = "被授权对象证件号", required = false)
    private String grantedUserCode;

    @ApiModelProperty(value = "被授权对象", required = false)
    private String grantedUserName;

    @ApiModelProperty(value = "生效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "生效时间不能为空")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "失效时间不能为空")
    private Long expireTime;

    @ApiModelProperty(value = "失效原因", required = true)
    private String expireReason;

    @ApiModelProperty(value = "授权签署回调地址", required = false)
    private String grantRedirectUrl;

    @ApiModelProperty(value = "授权类型 ，1-企业内，2-企业间，默认1", required = true)
    private Integer grantType;

    @ApiModelProperty(value = "文件fileKey")
    private String fileKey;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

}
