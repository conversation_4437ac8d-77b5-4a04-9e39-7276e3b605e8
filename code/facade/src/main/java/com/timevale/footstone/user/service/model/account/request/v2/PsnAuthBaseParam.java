package com.timevale.footstone.user.service.model.account.request.v2;

import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PsnAuthBaseParam extends ToString {

    @ApiModelProperty(" 个人注册手机号")
    protected String mobile;

    @ApiModelProperty(" 个人注册邮箱")
    protected String email;

    public void valid() {
        if (StringUtils.isBlank(mobile) && StringUtils.isBlank(email)) {
            throw new ErrorsBase.MissingArguments();
        }
    }
}
