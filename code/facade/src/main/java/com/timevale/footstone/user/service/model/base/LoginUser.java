package com.timevale.footstone.user.service.model.base;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.mandarin.weaver.utils.RequestContext;
import org.apache.commons.lang.StringUtils;


/**
 * 通用的登录人信息Model
 * 前提是走的webserver转发来的
 * 异步线程中使用需要提前初始化
 */
public class LoginUser extends ToString {

    /**
     * 登录人OID
     */
    private String accountId;

    /**
     * 登录人当前企业空间OID
     */
    private String mainOrgId;

    public String getAccountId() {
        if (StringUtils.isBlank(accountId)) {
            String loginAccountId = RequestContext.getRequest().getHeader("X-Tsign-operator");
            this.setAccountId(loginAccountId);
        }
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getMainOrgId() {
        if (StringUtils.isBlank(mainOrgId)) {
            String curTenantId = RequestContext.getRequest().getHeader("X-Tsign-Open-Tenant-Id");
            this.setMainOrgId(curTenantId);
        }
        return mainOrgId;
    }

    public void setMainOrgId(String mainOrgId) {
        this.mainOrgId = mainOrgId;
    }
}
