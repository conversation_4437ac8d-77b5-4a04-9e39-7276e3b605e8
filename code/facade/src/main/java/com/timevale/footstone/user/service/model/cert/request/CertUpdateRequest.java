/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyWithoutAccountRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.request <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1614:39]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 */
@Data
public class CertUpdateRequest extends ToString {

    @ApiModelProperty(required = true,value = "证书请求p10")
    @NotBlank(message = "证书请求p10不能为空")
    private String csr;

    //@ApiModelProperty(value = "制证非常用信息")
    //private CertApplyCommonModel commonModel;

    @NotEmpty(message = "证书名称不能为空")
    @ApiModelProperty(required = true,value = "证书名称")
    private String certName;

//    @NotEmpty(message = "证件号不能为空")
//    @ApiModelProperty(required = true,value = "证件号")
//    private String licenseNumber;
}