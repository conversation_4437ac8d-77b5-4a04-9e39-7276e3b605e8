package com.timevale.footstone.user.service.exception;

import com.timevale.footstone.user.service.exception.model.PureOrgIdModel;

/**
 * <AUTHOR> on 2019-08-01
 */
public class OpenApiThirdIdExistForOrgException extends BaseDataBizException {

    private PureOrgIdModel data;

    public OpenApiThirdIdExistForOrgException(String orgId) {
        super("thirdPartyUserId已创建机构账号，无法重复创建");
        this.data = new PureOrgIdModel(orgId);
    }

    @Override
    public PureOrgIdModel getData() {
        return data;
    }
}
