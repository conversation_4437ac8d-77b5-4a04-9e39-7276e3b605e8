package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: sicheng
 * @since: 2020-06-05 09:38
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AccountWarnRuleRequest extends ToString {

    @ApiModelProperty(value = "1-常用登录地", required = true)
    private Integer category;

    @ApiModelProperty(value = "category 为 1,地址文本", required = true)
    private String content;

    @ApiModelProperty(value = "true/false代表阀值开关",required = true)
    private Boolean isOpen;

}
