package com.timevale.footstone.user.service.model.rules.request.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-03-01
 */
@Data
public class UrlBaseRequest extends ToString {

    @ApiModelProperty(value = "是否获取h5端地址", allowableValues = "false,true")
    private boolean h5;

    @ApiModelProperty("登录token")
    private String token;
}
