package com.timevale.footstone.user.service.api;

import com.timevale.footstone.user.service.ServiceName;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.response.QrUidApplyResponse;
import com.timevale.footstone.user.service.model.account.response.QrUidResultResponse;
import com.timevale.mandarin.common.annotation.RestClient;

@RestClient(serviceId = ServiceName.name)
public interface RpcQrLoginService {

    WrapperResponse<QrUidApplyResponse> apply();

    WrapperResponse<QrUidResultResponse> result(String qrUid);
}
