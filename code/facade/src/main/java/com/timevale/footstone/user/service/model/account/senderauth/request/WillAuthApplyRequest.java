package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-02-21 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WillAuthApplyRequest extends ToString {

    @ApiModelProperty(value = "业务类型, ADMIN_TRANSFER - 管理员转授，RESET_SIGN_PWD - 修改签署密码", required = true)
    private String bizType;

    @ApiModelProperty(value = "认证后回调地址", required = true)
    private String callBackUrl;

    @ApiModelProperty(value = "意愿认证类型 1-腾讯人脸 2-芝麻信息 3-芝麻支付宝小程序 12-微信小程序", required = true)
    private Integer faceAuthMode;
}
