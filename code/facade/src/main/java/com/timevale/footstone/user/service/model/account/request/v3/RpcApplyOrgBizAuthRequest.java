package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.footstone.user.service.model.ReqHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业业务授权请求参数
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
public class RpcApplyOrgBizAuthRequest extends ApplyBizAuthBaseRequest implements ReqHandler {


    @ApiModelProperty(
            value =
                    "是否强制激活太实名组织更名拦截，默认false。\n"
                            + "\n"
                            + "true - 拦截实名认证\n"
                            + "\n"
                            + "false - 不拦截实名认证\n"
                            + "\n")
    private boolean organChangeNameHalt;

    @ApiModelProperty(value = "认证配置信息")
    private ApplyBizOrgAuthConfig authConfig;

    @ApiModelProperty(value = "组织主体信息")
    private ApplyBizOrgSubject orgSubject;

    @Override
    public ReqHandler validate() {
        super.validate();
        return this;
    }

    @Override
    public ReqHandler format() {
        super.format();
        return this;
    }
}