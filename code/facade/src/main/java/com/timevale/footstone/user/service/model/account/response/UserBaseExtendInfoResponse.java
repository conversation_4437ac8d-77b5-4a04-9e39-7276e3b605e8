package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年12月30日 17:33:00
 */
@Data
public class UserBaseExtendInfoResponse extends UserBaseInfoResponse{

    @ApiModelProperty("姓名")
    private String name;

    public void extendFill(BizICUserOutput output){
        name = output.getProperties().stream().filter( i-> StringUtils.equals(i.getType(), BuiltinProperty.INFO_NAME))
                .findFirst().orElse(new Property()).getValue();
    }
}
