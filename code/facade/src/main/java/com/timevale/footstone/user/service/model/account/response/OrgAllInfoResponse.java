package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.OrgPropCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** <AUTHOR> on 2019-01-04 */
@Data
public class OrgAllInfoResponse {

    @ApiModelProperty("基础信息")
    private UserBaseInfoResponse base;

    @ApiModelProperty("属性信息")
    private OrgPropCollection prop;

    @ApiModelProperty("企业实名信息摘要")
    private OrgRealNameBaseInfoResponse baseRealname;

    @ApiModelProperty("登录信息")
    private OrgLoginInfoResponse loginInfo;

    @ApiModelProperty("组织创建者ID")
    private String orgCreater;
}
