package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2019年08月29日 17:50:00
 */
@Data
public class InvitationBaseRequest extends ToString {
    @ApiModelProperty(value = "重定向地址")
    private String redirectURL;
    @ApiModelProperty(value = "回调接口地址")
    private String callbackURL;
    @ApiModelProperty(value = "业务方id")
    private String bizId;
    @ApiModelProperty(value = "通知方式 sms短信， email:邮件")
    private String notifyType;
}
