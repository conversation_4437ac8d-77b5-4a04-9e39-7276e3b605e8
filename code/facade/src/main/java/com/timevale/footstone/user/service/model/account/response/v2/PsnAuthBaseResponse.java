package com.timevale.footstone.user.service.model.account.response.v2;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PsnAuthBaseResponse extends ToString {

    @ApiModelProperty(" 个人账号OID")
    protected String accountId;

    @ApiModelProperty(" 个人注册手机号")
    protected String mobile;

    @ApiModelProperty(" 个人注册邮箱")
    protected String email;
}
