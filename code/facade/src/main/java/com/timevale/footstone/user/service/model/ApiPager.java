package com.timevale.footstone.user.service.model;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
public class ApiPager {

    public ApiPager() {
        this.offset = 0;
        this.size = 20;
    }

    public ApiPager(Integer offset, Integer size) {
        this.offset = offset;
        this.size = size;
    }

    @ApiModelProperty("分页起始位置")
    private Integer offset;

    @ApiModelProperty("单页数量")
    private Integer size;

    public Integer getOffset() {
        if (offset == null) {
            offset = 0;
        }
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getSize() {
        if (size == null) {
            size = 20;
        }
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
