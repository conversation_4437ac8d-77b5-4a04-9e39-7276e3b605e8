package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.biz.helper.acc.BizICUserOutputHelper;
import com.timevale.account.service.model.service.biz.helper.acc.impl.IdcardCollectionHelper;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.idcard.IdcardProjThirdparty;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.footstone.user.service.model.account.mods.PersonModel;
import com.timevale.mandarin.base.util.CollectionUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import static com.timevale.account.service.model.constants.BuiltinContacts.EMAIL;
import static com.timevale.account.service.model.constants.BuiltinContacts.MOBILE;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_BANK_NUM;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_PSN_REALNAME_MOBILE;

/** <AUTHOR> on 2019-01-19 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonGetResponse extends PersonModel {

    @ApiModelProperty("账号Id")
    private String accountId;

    @ApiModelProperty(
            value = "证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String idType;

    @ApiModelProperty(value = "证件号")
    private String idNumber;

    @ApiModelProperty(value = "第三方平台的用户id")
    private String thirdPartyUserId;

    @ApiModelProperty(value = "第三方平台的用户类型")
    private String thirdPartyUserType;

    @ApiModelProperty(value = "实名状态")
    private boolean status;

    @ApiModelProperty(value = "账号实名方式")
    private String authType;

    @ApiModelProperty(value = "实名流程id")
    private String realnameFlowid;

    public PersonGetResponse() {
    }

    public PersonGetResponse(BizAccountRealNameOutput realNameOutput) {

        BizICUserOutputHelper helper = new BizICUserOutputHelper(realNameOutput.getAccount());

        setAccountId(realNameOutput.getAccount().getId().getOuid());
        setName(helper.getPropertyValue(INFO_NAME));
        setCardNo(helper.getPropertyValue(INFO_BANK_NUM));
        setMobile(helper.getContactsValue(MOBILE));
        setEmail(helper.getContactsValue(EMAIL));
        setRealnameMobile(helper.getPropertyValue(INFO_PSN_REALNAME_MOBILE));

        List<Property> creds = realNameOutput.getAccount().getCredentials();
        if (CollectionUtils.isNotEmpty(creds)) {
            // 只能显示一个
            Property cred = creds.get(0);
            setIdNumber(cred.getValue());
            setIdType(cred.getType());
        }

        IdcardCollectionHelper idcardsHelper = helper.getIdcardsHelper();
        IdcardProjThirdparty ptd = idcardsHelper.getProjThirdparty();
        if (ptd != null) {
            setThirdPartyUserId(ptd.getThirdpartyUserId());
            setThirdPartyUserType(ptd.getThirdpartyUserType());
        }

        status = RealnameStatus.ACCEPT.equals(realNameOutput.getStatus()) ? true:false;
    }
}
