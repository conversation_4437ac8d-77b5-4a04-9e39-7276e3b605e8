package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.footstone.user.service.model.account.response.UserBaseInfoResponse;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年12月30日 18:16:00
 */
@Data
public class OrganBaseExtendResponse extends UserBaseInfoResponse {
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "证件号")
    private String code;

    @ApiModelProperty("证件类型")
    private String codeType;

    @ApiModelProperty("法人")
    private String legalName;

    @ApiModelProperty("组织来源")
    private String organSource;

    public void initExtend(BizICUserOutput output){
        name = output.getProperties().stream().filter( i-> StringUtils.equals(i.getType(), BuiltinProperty.INFO_NAME))
                .findFirst().orElse(new Property()).getValue();
        legalName = output.getProperties().stream().filter( i-> StringUtils.equals(i.getType(), BuiltinProperty.INFO_ORG_LEGAL_NAME))
                .findFirst().orElse(new Property()).getValue();
        codeType = output.getCredentials().stream().findFirst().orElse(new Property()).getType();
        code = output.getCredentials().stream().findFirst().orElse(new Property()).getValue();

    }
}
