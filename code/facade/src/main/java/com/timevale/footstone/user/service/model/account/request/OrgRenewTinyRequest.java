package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.service.biz.builder.icuser.BizICUserUpdateInputBuilder;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;

/** <AUTHOR> on 2019-01-21 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgRenewTinyRequest extends ToString {

    @ApiModelProperty(value = "企业名称")
    private String name;

    @ApiModelProperty(value = "企业法人名称")
    private String orgLegalName;

    @ApiModelProperty(value = "企业法人证件号")
    private String orgLegalIdNumber;
    public BizICUserUpdateInputBuilder conv() {
        BizICUserUpdateInputBuilder builder = BizICUserUpdateInputBuilder.builder();
        builder.buildProperties().add(INFO_NAME, getName());
        return builder;
    }
}
