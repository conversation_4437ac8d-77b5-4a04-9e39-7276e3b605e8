package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/14 21:08
 */
@ApiModel
@Data
public  class ApplyBizContextCertParticipator extends ToString {



  /**
   * @see  com.timevale.component.identity.constant.IdentityCertOperationTypeEnum.code()
   */
  @ApiModelProperty(value = "证书操作类型 " +
          "APPLY=申请 " +
          "DELAY=延期 " +
          "UPDATE=更新 " +
          "DEFAULT= 默认系统适配")
  private String userCertOperationType;



  /**
   * 证书协议类型 caCert-普通ca证书协议 caStandardCert-标准CA证书协议
   * @see com.timevale.component.identity.constant.IdentityAgreementShowTypeEnum.code()
   */
  private String userCertAgreementType;


  /**
   * 通道
   *
   */
  private String userCertCaRoute;
}
