package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinContacts;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.builder.acc.BizICUserCreateInputBuilder;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2024/10/30 16:02
 */
@Data
public class PersonAddByIdV1Request extends PersonAddByIdRequest {

    /**
     * 目前只有v1接口支持区号
     * 区号
     * 只支持输入 852-香港地区  853-澳门地区  否则报格式错误
     * 当传入该参数时，mobile参数格式要求放开到上限20位纯数字
     */
    @ApiModelProperty(value = "联系人区号 852-香港地区  853-澳门地区 ")
    private String areaCode;
    @ApiModelProperty(value = "实名证据点")
    private String realnameEvidencePointId;

    @Override
    public BizICUserCreateInputBuilder conv(BizICUserCreateInputBuilder in) {
        super.conv(in);
        // 2024/10/30  联系人区号
        if (StringUtils.isNotBlank(getAreaCode())) {
            in.buildContacts().add(BuiltinContacts.MOBILE_AREA, getAreaCode());
        }
        if (StringUtils.isNotBlank(getRealnameEvidencePointId())) {
            in.buildProperties().add(BuiltinProperty.REALNAME_EVIDENCE_POINT_ID, getRealnameEvidencePointId());
        }
        return in;
    }


    @Override
    public void valid() {
        super.valid();
        //校验区号
        checkAreaCodeAllow(getAreaCode(), getMobile());
    }
}
