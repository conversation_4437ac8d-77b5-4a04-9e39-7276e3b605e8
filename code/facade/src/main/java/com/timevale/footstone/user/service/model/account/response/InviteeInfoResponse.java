package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.enums.AccountType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@ApiModel("用户信息")
@Data
public class InviteeInfoResponse {
    private String name;
    private String mobile;
    private String email;
    private String idType;
    private String idNumber;
    private String orgName;
    private String orgIdType;
    private String orgIdNumber;
    private String orgLegalName;
    private String orgLegalIdNumber;

    private String inviteeOid;

    private String orgId;

    private AccountType type;

    public Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>();
        map.put("name", name);
        map.put("mobile", mobile);
        map.put("email", email);
        map.put("idType", idType);
        map.put("idNumber", idNumber);

        map.put("orgName", orgName);
        map.put("orgIdType", orgIdType);
        map.put("orgIdNumber", orgIdNumber);
        map.put("legalName", orgLegalName);
        map.put("legalNo", orgLegalIdNumber);
        map.put("inviteeOid", inviteeOid);
        map.put("orgId", orgId);
        return map;
    }
}
