package com.timevale.footstone.user.service.model.dept.request;

import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/24
 */

public class CreateDeptRequest {

    @ApiModelProperty(value = "部门名称")
    @NotEmpty(message = "部门名称不能为空")
    private String deptName;

    @ApiModelProperty(value = "上级部门id")
    @NotEmpty(message = "上级部门id不能为空")
    private String parentDeptId;

    @ApiModelProperty(value = "部门描述")
    private String desc;

    @ApiModelProperty(value = "负责人账号")
    private List<String> mainMemberIds;

    public String getDeptName() {
        return StringUtils.trim(deptName);
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getParentDeptId() {
        return parentDeptId;
    }

    public void setParentDeptId(String parentDeptId) {
        this.parentDeptId = parentDeptId;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<String> getMainMemberIds() {
        return mainMemberIds;
    }

    public void setMainMemberIds(List<String> mainMemberIds) {
        this.mainMemberIds = mainMemberIds;
    }
}
