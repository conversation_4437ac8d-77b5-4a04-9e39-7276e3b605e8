package com.timevale.footstone.user.service.model.role.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/20
 */
@Data
public class CheckAdminResponse {

    @ApiModelProperty("企业账号id")
    private String orgId;

    @ApiModelProperty("企业名称")
    private String orgName;

    @ApiModelProperty("成员id")
    private String memberId;

    @ApiModelProperty("成员oid")
    private String memberOid;


}
