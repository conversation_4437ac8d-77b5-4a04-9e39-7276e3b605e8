package com.timevale.footstone.user.service.model.realname.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020-11-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealNameUrlResponse extends ToString {

    @ApiModelProperty("实名地址")
    private String url;

    @ApiModelProperty("实名流程ID")
    private String flowId;
}
