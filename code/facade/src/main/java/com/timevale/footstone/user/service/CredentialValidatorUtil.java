package com.timevale.footstone.user.service;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.account.service.model.service.mods.idcard.IdcardInfo;
import com.timevale.account.service.utils.IDCardValidatorUtils;
import com.timevale.mandarin.common.lang.ValidateUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年07月28日 18:34:00
 */
public class CredentialValidatorUtil {

    public static Map<String, Function<String,Boolean>> validatorMap = new HashMap();
    static {
        // 香港通行证
        validatorMap.put(BuiltinCredentials.CRED_PSN_CH_HONGKONG,(value) ->{
            return ValidateUtils.HKPassValid( value);
        });

        // 澳门通行证
        validatorMap.put(BuiltinCredentials.CRED_PSN_CH_MACAO,(value) ->{
            return ValidateUtils.MACPassValid( value);
        });

        // 台湾通行证
        validatorMap.put(BuiltinCredentials.CRED_PSN_CH_TWCARD,(value) ->{
            return ValidateUtils.TWPassValid(value);
        });

        // 身份证
        validatorMap.put(BuiltinCredentials.CRED_PSN_CH_IDCARD,(value) ->{
            IdcardInfo idcardInfo = new IdcardInfo(BuiltinCredentials.CRED_PSN_CH_IDCARD, value);
            return IDCardValidatorUtils.validateCredential(idcardInfo);
        });

        // 外籍护照
        validatorMap.put(BuiltinCredentials.CRED_PSN_FOREIGN,(value) ->{
            return ValidateUtils.ForeignPassValid( value);
        });

        //护照
        validatorMap.put(BuiltinCredentials.CRED_PSN_PASSPORT,(value) ->{
            return ValidateUtils.ForeignPassValid( value);
        });



    }

    public static boolean validate(String type,String value){
        return validatorMap.get(type).apply(value);
    }
}
