package com.timevale.footstone.user.service.model.account.request.v2;

import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgAuthInfoParam extends OrgAuthInfoBaseParam {

    @ApiModelProperty("企业账号OID")
    private String orgId;

    public void valid() {
        if (StringUtils.isBlank(orgId) && StringUtils.isBlank(name)) {
            throw new ErrorsBase.MissingArguments();
        }
    }

}
