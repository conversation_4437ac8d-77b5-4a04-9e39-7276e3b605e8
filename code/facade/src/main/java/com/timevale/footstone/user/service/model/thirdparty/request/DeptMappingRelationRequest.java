package com.timevale.footstone.user.service.model.thirdparty.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DeptMappingRelationRequest {

    @ApiModelProperty("部门id")
    @NotBlank(message = "三方平台不能为空")
    private String deptId;

    @ApiModelProperty("三方部门id")
    @NotBlank(message = "三方平台不能为空")
    private String deptThirdId;
}
