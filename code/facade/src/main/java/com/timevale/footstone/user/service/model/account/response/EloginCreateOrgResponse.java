package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/** <AUTHOR> on 2019-12-23 */
@Data
@AllArgsConstructor
public class EloginCreateOrgResponse {

    @ApiModelProperty(value = "e签宝企业账号id")
    private String orgId;

    @ApiModelProperty(value = "实名url")
    private String realnameUrl;
}
