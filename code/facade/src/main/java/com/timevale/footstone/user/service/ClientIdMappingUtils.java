package com.timevale.footstone.user.service;

import com.timevale.account.service.enums.AccountType;
import com.timevale.account.service.model.constants.BuiltinThirdparty;
import com.timevale.account.service.utils.ThirdPartyUtils;
import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.user.service.enums.EquipMappingEnum;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.mandarin.base.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-05-10
 */
public abstract class ClientIdMappingUtils {


    public static ClientEnum getClientFromBuiltinThirdParty(String builtinThirdParty, AccountType accountType) {
        AssertSupport.assertNotnull(
                builtinThirdParty, new FootstoneUserErrors.CustomBizException("builtinThirdParty不能为空"));
        //目前先先只支持企业类型的
        if (AccountType.ORGANIZE.equals(accountType)) {
            switch (builtinThirdParty) {
                case BuiltinThirdparty.FEI_SHU_TENANT:
                    return ClientEnum.FEI_SHU;
                case BuiltinThirdparty.WE_WORK_TENANT:
                    return ClientEnum.WE_WORK;
                default:
                    return null;
            }
        }
        return null;
    }


    /**
     * 根据clientId获取对应的BuiltinThirdParty
     *
     * @param clientId
     * @param accountType 对应clientId如果存在个人及租户场景，需要传入accountType，用于获取对应BuiltinThirdparty
     * @param tenantKey   租户隔离场景下获取builtinThirdParty需要传tenantKey
     * @return
     */
    public static String getBuiltinThirdParty(String clientId, AccountType accountType, String tenantKey) {
        AssertSupport.assertTrue(
                StringUtils.isNotBlank(clientId), new FootstoneUserErrors.CustomBizException("clientId不能为空"));
        ClientEnum clientEnum = ClientEnum.valueOfType(clientId);
        return getBuiltinThirdParty(clientEnum, accountType, tenantKey);

    }


    /**
     * 根据clientId获取对应的BuiltinThirdParty
     *
     * @param clientId
     * @param accountType 对应clientId如果存在个人及租户场景，需要传入accountType，用于获取对应BuiltinThirdparty
     * @param tenantKey   租户隔离场景下获取builtinThirdParty需要传tenantKey
     * @return
     */
    public static String getBuiltinThirdParty(
            ClientEnum clientId, AccountType accountType, String tenantKey) {
        AssertSupport.assertNotnull(
                clientId, new FootstoneUserErrors.CustomBizException("clientId不能为空"));
        switch (clientId) {
            case FEI_SHU:
                if (Objects.equals(accountType, AccountType.ORGANIZE)) {
                    return BuiltinThirdparty.FEI_SHU_TENANT;
                } else if (Objects.equals(accountType, AccountType.PERSON)) {
                    return StringUtils.isBlank(tenantKey) ? BuiltinThirdparty.FEI_SHU_USER : BuiltinThirdparty.FEI_SHU_USER + "_" + tenantKey;
                } else {
                    throw new FootstoneUserErrors.CustomBizException("accountType为空或暂不支持: " + accountType);
                }
            case WE_WORK:
                if (Objects.equals(accountType, AccountType.ORGANIZE)) {
                    return BuiltinThirdparty.WE_WORK_TENANT;
                } else if (Objects.equals(accountType, AccountType.PERSON)) {
                    return StringUtils.isBlank(tenantKey) ? BuiltinThirdparty.WE_WORK_USER : BuiltinThirdparty.WE_WORK_USER + "_" + tenantKey;
                } else {
                    throw new FootstoneUserErrors.CustomBizException("accountType为空或暂不支持: " + accountType);
                }
            case KINGDEE_K3EE:
                if (Objects.equals(accountType, AccountType.ORGANIZE)) {
                    return StringUtils.isBlank(tenantKey) ? BuiltinThirdparty.KINGDEE_K3EE_TENANT : BuiltinThirdparty.KINGDEE_K3EE_TENANT + "_" + tenantKey;
                } else if (Objects.equals(accountType, AccountType.PERSON)) {
                    return StringUtils.isBlank(tenantKey) ? BuiltinThirdparty.KINGDEE_K3EE_USER : BuiltinThirdparty.KINGDEE_K3EE_USER + "_" + tenantKey;
                } else {
                    throw new FootstoneUserErrors.CustomBizException("accountType为空或暂不支持: " + accountType);
                }
            case DING_TALK:
                //钉签端不区分企业端或个人端，都是DING_TALK
                return BuiltinThirdparty.DING_TALK;
            default:
                //统配抖音端的其他产品
                if (StringUtils.contains(clientId.name(), "DOUYIN_")) {
                    return BuiltinThirdparty.DOU_YIN;
                } else {
                    return null;
                }
        }
    }

    /**
     * 根据clientId获取EquipMappingEnum
     *
     * @param clientId
     * @return
     */
    public static EquipMappingEnum getEquipMappingEnum(ClientEnum clientId,String agentType) {
        AssertSupport.assertNotnull(
                clientId, new FootstoneUserErrors.CustomBizException("clientId不能为空"));
        /**
         * 兼容方案 不传agentType 按 clientId 匹配（默认证） ，否则 clientId + agentType
         */
        switch (clientId) {
            case FEI_SHU:
                if(StringUtils.isBlank(agentType)){
                    return EquipMappingEnum.FEI_SHU_PC;
                }
                if("h5".equals(agentType)){
                    return EquipMappingEnum.FEI_SHU_MOBILE;
                }
            case WE_WORK:
                if(StringUtils.isBlank(agentType)){
                    return EquipMappingEnum.WE_WORK_PC;
                }
                if("h5".equals(agentType)){
                    return EquipMappingEnum.WE_WORK_MOBILE;
                }
            case DOUYIN_LOAN:
                return EquipMappingEnum.DOUYIN_LOAN;
            case KINGDEE_K3EE:
                return EquipMappingEnum.KINGDEE_K3EE_PC;
            case DING_TALK:
                if (StringUtils.isBlank(agentType)) {
                    return EquipMappingEnum.DING_TALK_EXTERNAL;
                }
                if ("h5".equals(agentType)) {
                    return EquipMappingEnum.DING_TALK_INNER;
                } 
            default:
                throw new FootstoneUserErrors.CustomBizException("不支持的clientId: " + clientId + ",agentType：" + agentType);
        }
    }
}
