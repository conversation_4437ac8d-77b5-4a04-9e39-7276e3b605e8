package com.timevale.footstone.user.service.model.account.login.response;

import com.timevale.footstone.user.service.model.account.login.model.TokenModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LoginSuccessResponse extends TokenModel {

    private Boolean createStatus;

    private String ouid;
}
