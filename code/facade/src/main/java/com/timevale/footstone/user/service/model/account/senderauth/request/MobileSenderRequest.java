package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.senderauth.GeetestRobotAuthVerifyModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> on 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileSenderRequest extends ToString {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String principal;

    @ApiModelProperty(
            value = "业务类型，首次流程填写",
            allowableValues =
                    "重设密码 - RESET_PWD,"
                            + "注册 - REGISTER, 登录 - LOGIN, 注册并登录 - REGISTER_LOGIN, 原登录凭证验证 - MODIFY_IDCARD_ORIGIN,"
                            + " 新登录凭证验证 - MODIFY_IDCARD_TARGET, 设置登录凭证 - SET_IDCARD, 设置签署密码 - RESET_SIGN_PWD")
    private String bizType;

    @ApiModelProperty(value = "上次流程事务id，非首次流程填写")
    private String preServiceId;

    @ApiModelProperty(value = "是否进行极验人机校验", example = "false")
    private boolean geetestAuth;

    @ApiModelProperty(value = "人机校验数据")
    private GeetestRobotAuthVerifyModel model;

    @ApiModelProperty(value = "sessinId ")
    private String  sessinId;
}
