package com.timevale.footstone.user.service.model.seal.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:jianyang
 *
 * @since 2020-12-18 14:47
 */
@Data
public class SealModelOpen {

  @ApiModelProperty(value = "印章id")
  private String sealId;

  @ApiModelProperty(value = "印章宽度")
  private Integer width;

  @ApiModelProperty(value = "印章高度")
  private Integer height;

  @ApiModelProperty(value = "印章别名")
  private String alias;

  @ApiModelProperty(value = "印章下载地址")
  private String url;

  @ApiModelProperty(value = "印章业务类型")
  private String sealBizType;

  @JsonIgnore
  @ApiModelProperty(value = "印章fileKey")
  private String fileKey;

  @ApiModelProperty(value = "生效时间")
  private Long effectiveTime;

  @ApiModelProperty(value = "失效时间")
  private Long expireTime;

  @ApiModelProperty(value = "授权企业名称")
  private String organiztionName;

  @ApiModelProperty(value = "授权企业证件号")
  private String idNumber;

  @ApiModelProperty(value = "授权企业证件类型")
  private String idType;
}
