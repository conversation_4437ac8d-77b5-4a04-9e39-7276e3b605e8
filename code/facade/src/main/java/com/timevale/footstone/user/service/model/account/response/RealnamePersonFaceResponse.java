package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.RealnameServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RealnamePersonFaceResponse extends RealnameServiceIdModel {

    @ApiModelProperty("芝麻信用认证URL")
    private String url;

    @ApiModelProperty("刷脸认证值")
    private String faceValue;
}
