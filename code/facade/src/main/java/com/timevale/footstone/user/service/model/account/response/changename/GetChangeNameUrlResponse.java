package com.timevale.footstone.user.service.model.account.response.changename;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2024/6/20 10:47
 */
@Data
public class GetChangeNameUrlResponse extends ToString {

    @ApiModelProperty(value = "更名地址")
    private String url;

    @ApiModelProperty(value = "更名记录ID")
    private String changeNameRecordId;
}
