package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ThirdOrgContactRequest extends ToString {

    @ApiModelProperty(value = "三方成员编号")
    private String  thirdUserId;

    @ApiModelProperty(value = "三方成员来源")
    private String thirdSource;

    @ApiModelProperty(value = "确认信息")
    @NotBlank(message = "确认信息不能为空")
    private String confirmInfo;

}
