package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountEloginUndoBindRequest extends ToString {

    @ApiModelProperty(value = "解绑key", required = true)
    private String undoKey;

    @ApiModelProperty(value = "验证码", required = true)
    private String code;

    public void valid() {
        AssertSupport.assertNotnull(undoKey, new ErrorsBase.MissingArgumentsWith("undoKey"));
        AssertSupport.assertNotnull(code, new ErrorsBase.MissingArgumentsWith("code"));
    }

}
