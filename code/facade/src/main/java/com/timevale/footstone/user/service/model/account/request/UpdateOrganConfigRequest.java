package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.model.account.mods.NotifyConfigModel;
import com.timevale.footstone.user.service.model.account.mods.OrganCategoryModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-11-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateOrganConfigRequest extends ToString {

    @ApiModelProperty("企业配置")
    private List<OrganCategoryModel> configs;
}
