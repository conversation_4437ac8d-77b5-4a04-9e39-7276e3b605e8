package com.timevale.footstone.user.service.model.account.idcard.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-02-14 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UnbindThirdPartyRequest extends ToString {

    @ApiModelProperty(value = "第三方类型，支付宝：ALI_PAY、微信平台：WE_CHAT、钉钉平台：DING_TALK", required = true)
    private String key;

    public void valid() {
        AssertSupport.assertNotnull(key, new ErrorsBase.MissingArgumentsWith("key"));
    }
}
