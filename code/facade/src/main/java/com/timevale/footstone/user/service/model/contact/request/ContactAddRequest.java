/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：ContactAddRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.contact.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/1921:08]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.contact.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 类名：ContactAddRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/1921:08]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class ContactAddRequest extends ToString {

    @ApiModelProperty(value = "用户标识，手机号或邮箱")
    @NotBlank(message = "联系方式不能为空")
    @Length(max = 50, message = "联系方式长度不能超过50")
    private String uniqueId;

    @ApiModelProperty(value = "用户名称")
    @NotBlank(message = "联系人姓名不能为空")
    @Length(max = 100, message = "联系人姓名长度不能超过100")
    private String name;

    @ApiModelProperty(value = "所属企业")
    @Length(max = 100, message = "所属企业名字长度不能超过100")
    private String affliation;

    @ApiModelProperty(value = "备注信息")
    @Length(max = 200, message = "备注长度不能超过200")
    private String comment;
}
