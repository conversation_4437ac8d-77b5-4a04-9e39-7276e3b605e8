package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.PersonPropCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** <AUTHOR> on 2019-01-04 */
@Data
public class UserAllInfoResponse {

    @ApiModelProperty("基础信息")
    private UserBaseInfoResponse base;

    @ApiModelProperty("属性信息")
    private PersonPropCollection prop;

    @ApiModelProperty("个人实名信息摘要")
    private UserRealNameBaseInfoResponse baseRealname;

    @ApiModelProperty("登录信息")
    private UserLoginInfoResponse loginInfo;
}
