package com.timevale.footstone.user.service.model.role.request;

import com.timevale.footstone.user.service.model.role.model.CreateRoleModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class CreateRoleRequest extends ToString {
    @ApiModelProperty(value = "角色实体")
    public List<CreateRoleModel> roleModels;
}
