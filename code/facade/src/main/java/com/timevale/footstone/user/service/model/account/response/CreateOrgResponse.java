package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateOrgResponse {

    @ApiModelProperty(value = "企业oid")
    private String orgId;

    @ApiModelProperty(value = "是否新创建")
    private Boolean createStatus;

    @ApiModelProperty(value = "操作码 0-企业已实名，用户已加入企业 1-企业已实名，用户未加入企业，存在管理员 2-企业已实名，用户未加入企业，不存在管理员 3-企业未实名，用户加入该企业 4-企业不存在，新创建企业")
    private String operateCode;
}
