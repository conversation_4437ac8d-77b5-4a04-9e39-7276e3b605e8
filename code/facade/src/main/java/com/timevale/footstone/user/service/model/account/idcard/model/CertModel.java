package com.timevale.footstone.user.service.model.account.idcard.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CertModel extends ToString {

    @ApiModelProperty(value = "证书内容Base64", required = true)
    private String certBase64;
}
