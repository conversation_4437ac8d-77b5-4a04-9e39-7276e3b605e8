package com.timevale.footstone.user.service.api;

import com.timevale.footstone.user.service.ServiceName;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.v2.SsoUserAuthRegisterRequest;
import com.timevale.footstone.user.service.model.account.response.SsoUserAuthRegisterResponse;
import com.timevale.mandarin.common.annotation.RestClient;

@RestClient(serviceId = ServiceName.name)
public interface RpcSsoLoginService {



  /**
   * 三方用户授权并注册
   *
   * @return
   */
  WrapperResponse<SsoUserAuthRegisterResponse> thirdUserAuthAndRegister(SsoUserAuthRegisterRequest request);


}
