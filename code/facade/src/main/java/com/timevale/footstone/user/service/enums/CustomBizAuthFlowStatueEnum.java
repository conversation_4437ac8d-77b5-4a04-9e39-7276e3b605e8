package com.timevale.footstone.user.service.enums;

/**
 * 业务授权场景状态
 *
 * <AUTHOR>
 * @since 2025/4/16 15:58
 */
public enum CustomBizAuthFlowStatueEnum {

    INIT("INIT", 0,false,"初始化"),
    ING("ING", 0,false,"进行中"),
    SUCCESS("SUCCESS", 1,false,"成功"),
    FAIL("FAIL", 2,false,"失败"),
    EXPIRED("EXPIRED", 2,false,"过期"),
    TERMINATE("TERMINATE", 2,false,"终止"),
    ;

    private String code;
    private int status;
    private boolean completed;
    private String desc;

    CustomBizAuthFlowStatueEnum(String code, int status, boolean completed, String desc) {
        this.code = code;
        this.status = status;
        this.completed = completed;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    public boolean isCompleted() {
        return completed;
    }

    public String getDesc() {
        return desc;
    }


    public boolean isSuccess() {
        return this == SUCCESS;
    }

    public static CustomBizAuthFlowStatueEnum codeOf(String code) {
        for (CustomBizAuthFlowStatueEnum value : CustomBizAuthFlowStatueEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return ING;
    }
}
