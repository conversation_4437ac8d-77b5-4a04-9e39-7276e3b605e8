package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.service.biz.acc.output.BizAccountTOutput;
import com.timevale.account.service.model.service.biz.builder.acc.impl.abs.AbstractBizAccountBuilder;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.prop.Property;

import java.util.List;

/**
 * <AUTHOR> on 2019-01-12
 */
public interface AccountPropConv {

    /** 用于创建账号 */
    void conv(AbstractBizAccountBuilder builder);

    /** 用于更新 */
    List<Property> conv();

    /** 用于查询 */
    AccountPropConv init(BizAccountTOutput<ContentSecurity> icuser);
}
