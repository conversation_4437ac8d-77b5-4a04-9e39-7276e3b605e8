package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountEloginOCreateRequest extends ToString {

    @ApiModelProperty(value = "创建人的第三方id", required = true)
    private String userId;

    @ApiModelProperty(value = "企业名称", required = true)
    private String name;

    @ApiModelProperty(value = "实名后重定向地址")
    private String redirectUrl;

    public void valid() {
        AssertSupport.assertNotnull(userId, new ErrorsBase.MissingArgumentsWith("userId"));
        AssertSupport.assertNotnull(name, new ErrorsBase.MissingArgumentsWith("name"));
    }

}
