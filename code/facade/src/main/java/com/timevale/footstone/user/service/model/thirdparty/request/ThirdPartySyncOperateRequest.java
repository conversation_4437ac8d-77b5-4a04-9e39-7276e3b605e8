package com.timevale.footstone.user.service.model.thirdparty.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;


@Data
public class ThirdPartySyncOperateRequest  {

    @ApiModelProperty("三方平台")
    @NotBlank(message = "三方平台不能为空")
    private String platform;

    @ApiModelProperty("部门映射关系")
    private List<DeptMappingRelationRequest> deptMappingRelations;

}
