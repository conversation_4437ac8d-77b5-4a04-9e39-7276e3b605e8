package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.model.Validater;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.lang.ValidateUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 账号身份信息绑定/换绑页面请求参数
 */
@EqualsAndHashCode
@Data
public class AccountBindUrlRequest extends ToString implements Validater {

    private static final int PARAM_MAX_LENGTH = 256;
    private static final String TYPE_EMAIL = "email";
    private static final String TYPE_MOBILE = "mobile";

    @ApiModelProperty(value = "用户登录凭证：手机号或邮箱 注：传入后会展示到页面上且禁止编辑", required = true)
    private String account;

    @ApiModelProperty(value = "绑定账号登录凭证类型，默认手机号mobile。如果该登录凭证已存在，则进行换绑操作，如果不存在则进行绑定操作 mobile-手机号,email-邮箱")
    private String changeType;

    @ApiModelProperty(value = "是否隐藏顶部通栏，true-是，false-否，默认false")
    private boolean hideTopBar;

    @ApiModelProperty(value = "重定向地址")
    private String redirectUrl;

    @ApiModelProperty(value = "自定义业务编码")
    private String customBizNum;

    @Override
    public void valid() {
        // 用户手机号或邮箱必传
        AssertSupport.assertTrue(StringUtils.isNotBlank(account),
                new FootstoneUserErrors.ParamBlankError("account"));
        // 用户手机号或邮箱格式校验
        if (!ValidateUtils.mobileValid(account)
                && !ValidateUtils.emailValid(account)) {
            throw new FootstoneUserErrors.ParamFormatError("account");
        }
        // 登录凭证类型校验
        if (StringUtils.isBlank(changeType)) {
            changeType = TYPE_MOBILE;
        }
        if (!TYPE_EMAIL.equals(changeType) && !TYPE_MOBILE.equals(changeType)) {
            throw new FootstoneUserErrors.InvalidParamFormat("changeType", changeType);
        }
        // 重定向地址长度校验
        AssertSupport.assertTrue(StringUtils.length(redirectUrl) <= PARAM_MAX_LENGTH,
                new FootstoneUserErrors.ParamLengthTooLong("redirectUrl", PARAM_MAX_LENGTH));
        // 自定义业务编码长度校验
        AssertSupport.assertTrue(StringUtils.length(customBizNum) <= PARAM_MAX_LENGTH,
                new FootstoneUserErrors.ParamLengthTooLong("customBizNum", PARAM_MAX_LENGTH));
    }
}