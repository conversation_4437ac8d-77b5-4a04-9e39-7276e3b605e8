package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.will.common.service.enums.WillAuthTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 意愿组合认证申请请求
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WillAssembleAuthApplyRequest extends ToString {

  @ApiModelProperty(value = "业务类型, ADMIN_TRANSFER - 管理员转授，RESET_SIGN_PWD - 修改签署密码", required = true)
  private String bizType;

  @ApiModelProperty(value = "认证后回调地址", required = true)
  private String callBackUrl;

  @ApiModelProperty(value = "客户端类型，H5, PC 默认H5")
  private String clientType;


  /**
   * @see WillAuthTypeEnum
   */
  @ApiModelProperty(value = "指定意愿类型")
  private List<Integer> willAuthTypes;
}
