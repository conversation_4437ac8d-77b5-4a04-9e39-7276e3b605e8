package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** <AUTHOR> on 2019-01-14 */
@Data
public class OrgPropCollection {

    @ApiModelProperty("企业属性")
    private OrgProperties properties;

    @ApiModelProperty("企业证件")
    private OrgCredentials credentials;

    @ApiModelProperty("企业联系方式")
    private CommonContacts contacts;

    public OrgPropCollection init(BizICUserOutput in) {
        setContacts(new CommonContacts().init(in));
        setCredentials(new OrgCredentials().init(in));
        setProperties(new OrgProperties().init(in));
        return this;
    }
}
