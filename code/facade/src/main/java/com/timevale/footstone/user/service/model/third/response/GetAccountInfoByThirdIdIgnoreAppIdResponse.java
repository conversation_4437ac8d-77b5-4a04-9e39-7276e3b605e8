package com.timevale.footstone.user.service.model.third.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetAccountInfoByThirdIdIgnoreAppIdResponse {
    private String ouid;
    private String guid;
    private String accountType;
    private Boolean realName;
}
