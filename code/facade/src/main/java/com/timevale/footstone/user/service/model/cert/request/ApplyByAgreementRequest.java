package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 类名：ApplyByAgreementRequest
 * 功能说明：
 *
 * <AUTHOR>
 * @DATE 2023/3/17 13:40
 */
@Data
public class ApplyByAgreementRequest extends ToString {
    /**
     * 协议id
     */
    @ApiModelProperty(required = true, value = "协议id")
    @NotEmpty(message = "协议id不能为空")
    private String certAgreementId;

    /**
     * 对应的appId
     */
    @ApiModelProperty(required = true,value = "应用id")
    private String appId;


}
