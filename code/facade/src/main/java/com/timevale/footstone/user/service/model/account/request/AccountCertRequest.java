package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountCertRequest extends ToString {


    @ApiModelProperty(value = "证件类型 详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********", required = true)
    private String type;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号", required = true)
    private String value;

}
