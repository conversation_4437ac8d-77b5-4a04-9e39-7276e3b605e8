package com.timevale.footstone.user.service.model.thirdlogin.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.timevale.mandarin.common.result.ToString;


/**
 * <AUTHOR>
 * @since 2024-05-09 获取用户三方登录信息请求
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GetThirdLoginInfoRequest extends ToString{

    /** 用户三方登录信息上下文id */
    private String loginContextId;

}
