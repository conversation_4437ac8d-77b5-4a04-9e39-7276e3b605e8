package com.timevale.footstone.user.service.model.third.response;

import com.timevale.footstone.user.service.model.third.model.ThirdPartyTenantInfoModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-05-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetThirdPartyTenantInfoResponse {
    private List<ThirdPartyTenantInfoModel> thirdPartyTenantInfoList;
}
