package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SenderAuthVerifyRequest extends ServiceIdModel {

    @ApiModelProperty(value = "验证码", required = true)
    private String credentials;

    @ApiModelProperty(value = "登录参数")
    private LoginParams loginParams;

    public String getCredentials() {
        return credentials;
    }

    public void setCredentials(String credentials) {
        this.credentials = credentials;
    }

    public LoginParams getLoginParams() {
        if (loginParams == null) {
            return new LoginParams();
        }
        return loginParams;
    }

    public void setLoginParams(LoginParams loginParams) {
        this.loginParams = loginParams;
    }
}
