package com.timevale.footstone.user.service.model.billing.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShowCaseResponse extends ToString {

    @ApiModelProperty("商品橱窗id")
    private String showCaseId;
}
