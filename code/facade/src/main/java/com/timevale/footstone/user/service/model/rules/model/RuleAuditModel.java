package com.timevale.footstone.user.service.model.rules.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/3/15 3:13 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RuleAuditModel {

    @ApiModelProperty(value = "记录id")
    private Long auditId;

    @ApiModelProperty(value = "授权人")
    private String granter;

    @ApiModelProperty(value = "授权人证件号")
    private String granterCode;

    @ApiModelProperty(value = "授权人证件号")
    private String granterName;

    @ApiModelProperty(value = "被授权方名称")
    private String grantedUserName;

    @ApiModelProperty(value = "被授权方证件号")
    private String grantedUserCode;

    @ApiModelProperty(value = "授权范围(模板id/ALL/HR/FINANCE)")
    private String scope;

    @ApiModelProperty(value = "授权范围名称")
    private String scopeName;

    @ApiModelProperty(value = "生效时间")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间")
    private Long expireTime;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "修改时间")
    private Long modifyTime;

    @ApiModelProperty(value = "审核人")
    private String auditUser;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "授权文件fileKey")
    private String fileKey;

    @ApiModelProperty(value = "授权文件名")
    private String fileName;

    @ApiModelProperty(value = "文件下载地址")
    private String downloadUrl;

}
