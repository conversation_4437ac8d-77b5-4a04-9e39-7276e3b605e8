package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-11-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetOrganConfigRequest extends ToString {

    @ApiModelProperty(value = "业务类型")
    private Set<String> category;
}
