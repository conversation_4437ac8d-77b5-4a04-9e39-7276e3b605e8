package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.model.account.mods.NotifyConfigModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> on 2019-01-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateNotifyConfigRequest extends ToString {

    @ApiModelProperty("通知配置")
    private List<NotifyConfigModel> configs;

}
