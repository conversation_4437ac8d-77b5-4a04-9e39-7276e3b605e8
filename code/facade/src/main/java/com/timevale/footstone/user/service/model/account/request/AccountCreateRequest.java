package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.model.account.mods.CommonContacts;
import com.timevale.footstone.user.service.model.account.mods.IdcardCollection;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.Map;

/** <AUTHOR> on 2019-01-10 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccountCreateRequest<T extends IdcardCollection, P, C> extends ToString {

    @ApiModelProperty(value = "账号登录凭证")
    private T idcards;

    @ApiModelProperty(value = "账号属性信息")
    private P properties;

    @ApiModelProperty(value = "账号证件信息")
    private C credentials;

    @ApiModelProperty(value = "账号联系信息")
    private CommonContacts contacts;

    @ApiModelProperty(
            value = "账号来源参数，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=8784839")
    private Map<String, String> source;

    public Map<String, String> getSource() {
        return source == null ? Collections.emptyMap() : source;
    }
}
