/*
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyConfigModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1615:25]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.model;

import com.timevale.openca.common.service.enums.CertType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：CertApplyConfigModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1615:25]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class CertApplyConfigModel {

    @ApiModelProperty(value = "配置appId，不传默认从http头信息获取")
    private String configId;
    @ApiModelProperty(value = "证书有效期，默认一年,枚举值:ONEDAY,ONEMONTH,ONEYEAR,TWOYEAR,THREEYEAR")
    private String certTime;
    @ApiModelProperty(value = "算法，默认RSA,枚举值:RSA,SM2")
    private String algorithm;

    @ApiModelProperty(value = "单双证,SINGLE(单证),DOUBLE(双证书)")
    private CertType certType;
}