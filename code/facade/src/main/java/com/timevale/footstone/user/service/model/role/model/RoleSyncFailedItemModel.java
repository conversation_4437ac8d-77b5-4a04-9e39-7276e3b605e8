package com.timevale.footstone.user.service.model.role.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/17 15:52
 */
@Data
public class RoleSyncFailedItemModel extends ToString {

  @ApiModelProperty(value = "目标企业organId")
  private String targetOrganId;

  @ApiModelProperty(value = "角色ID")
  private String sourceRoleId;

  @ApiModelProperty(value = "角色名称")
  private String sourceRoleName;

  @ApiModelProperty(value = "失败原因")
  private String cause;

}
