package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.footstone.user.service.model.cert.model.CertBaseInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/23
 */
@Data
public class CreateOrgAndLegalResponse {

    @ApiModelProperty("企业id")
    private String orgId;

    @ApiModelProperty("法人id")
    private String legalOid;

    @ApiModelProperty(value = "法人证书信息")
    private CertBaseInfo legalCert;

    @ApiModelProperty(value = "企业法人信息")
    private CertBaseInfo orgCert;
}
