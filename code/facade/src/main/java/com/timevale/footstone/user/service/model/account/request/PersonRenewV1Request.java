package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinContacts;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.builder.icuser.BizICUserUpdateInputBuilder;
import com.timevale.footstone.user.service.model.Validater;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

/**
 * v1接口 更新签署账号接口支持传入区号
 *
 * <AUTHOR>
 * @DATE 2024/10/30 15:22
 */
@Data
public class PersonRenewV1Request extends PersonRenewRequest implements Validater {

    /**
     * 区号
     * 只支持输入 852-香港地区  853-澳门地区  否则报格式错误
     * 当传入该参数时，mobile参数格式要求放开到上限20位纯数字
     */
    @ApiModelProperty(value = "联系人区号 852-香港地区  853-澳门地区 ")
    private String areaCode;

    @ApiModelProperty(value = "实名证据点")
    private String realnameEvidencePointId;

    @Override
    public void valid() {
        //校验区号
        checkAreaCodeAllow(getAreaCode(), getMobile());
    }

    @Override
    public BizICUserUpdateInputBuilder conv(BizICUserUpdateInputBuilder in) {
        super.conv(in);
        // 2024/10/30  更新区号 兼容港澳手机号码更新成大陆手机号码的时候更新区号为空字符串 .     getAreaCode() = null 底层不会更新区号字段   ; getAreaCode() = '' 底层会更新区号字段为空字符串
        if (!StringUtils.isAllBlank(getAreaCode(), getMobile())) {
            in.buildContacts().add(BuiltinContacts.MOBILE_AREA, StringUtils.defaultString(getAreaCode()));
        }
        if (StringUtils.isNotBlank(getRealnameEvidencePointId())) {
            in.buildProperties().add(BuiltinProperty.REALNAME_EVIDENCE_POINT_ID, getRealnameEvidencePointId());
        }
        return in;
    }
}
