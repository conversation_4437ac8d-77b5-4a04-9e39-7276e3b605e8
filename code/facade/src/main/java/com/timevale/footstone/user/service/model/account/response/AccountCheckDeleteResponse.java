package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.role.response.CheckAdminResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/25
 */
@Data
public class AccountCheckDeleteResponse {

    @ApiModelProperty("校验结果")
    private Boolean result;

    @ApiModelProperty("是管理员的企业")
    private List<CheckAdminResponse> orgAdminList;

    @ApiModelProperty("仍有文件待处理的企业列表")
    private List<CheckDocResponse> docOrgList;
}
