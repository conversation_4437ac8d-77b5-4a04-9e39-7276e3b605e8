package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.service.biz.acc.output.BizAccountTOutput;
import com.timevale.account.service.model.service.biz.builder.acc.impl.abs.AbstractBizAccountBuilder;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.account.service.model.constants.BuiltinContacts.EMAIL;
import static com.timevale.account.service.model.constants.BuiltinContacts.MOBILE;

/** <AUTHOR> on 2019-01-14 */
@EqualsAndHashCode(callSuper = false)
@Data
public class CommonContacts extends ToString implements AccountPropConv {

    @ModelFieldDecoration(DecorationType.MOBILE)
    @ApiModelProperty(value = "联系手机号")
    private String mobile;

    @ModelFieldDecoration(DecorationType.EMAIL)
    @ApiModelProperty(value = "联系邮箱")
    private String email;

    @Override
    public void conv(AbstractBizAccountBuilder builder) {
        builder.buildContacts().add(MOBILE, getMobile()).add(EMAIL, getEmail());
    }

    @Override
    public List<Property> conv() {
        List<Property> list = new ArrayList<>();
        list.add(new Property(MOBILE, getMobile()));
        list.add(new Property(EMAIL, getEmail()));
        return list;
    }

    @Override
    public CommonContacts init(BizAccountTOutput<ContentSecurity> icuser) {
        Map<String, String> propGroup =
                icuser.getContacts()
                        .stream()
                        .filter(p -> p.getValue() != null)
                        .collect(
                                Collectors.toMap(
                                        Property::getType, Property::getValue, (k1, k2) -> k1));

        setMobile(propGroup.get(MOBILE));
        setEmail(propGroup.get(EMAIL));
        return this;
    }
}
