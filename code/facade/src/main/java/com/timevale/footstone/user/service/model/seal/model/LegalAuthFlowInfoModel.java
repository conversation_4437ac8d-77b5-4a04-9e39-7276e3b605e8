/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：LegalAuthFlowInfoModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.seal.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3020:48]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：LegalAuthFlowInfoModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3020:48]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LegalAuthFlowInfoModel {

    @ApiModelProperty(value = "授权签署流程id")
    private String flowId;

    @ApiModelProperty(value = "授权签署文档filekey")
    private String docFilekey;

    @ApiModelProperty(value = "授权签署文档下载地址")
    private String docUrl;

    @ApiModelProperty(value = "授权失败原因")
    private String reason;
}