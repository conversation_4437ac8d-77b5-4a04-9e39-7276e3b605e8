package com.timevale.footstone.user.service.model.Privilege.response;

import com.timevale.footstone.user.service.model.BizMapProperty;
import com.timevale.footstone.user.service.model.Privilege.mods.BizMenuPrivilege;
import com.timevale.footstone.user.service.model.Privilege.mods.BizPrivilegeOwner;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class ListAllAndRolePrivilegesResponse extends ToString {

    @ApiModelProperty(value = "业务权限列表")
    private List<BizMapProperty<BizMenuPrivilege, List<BizPrivilegeOwner>>> bizPrivilegeList;
}
