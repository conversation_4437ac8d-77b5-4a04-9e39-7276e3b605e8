package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> on 2019-01-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifyConfigDefaultModel {

    @ApiModelProperty(value = "业务类型：" +
            "EMAIL_ALL," +
            "MOBILE_ALL," +
            "支持扩展")
    private String category;

    @ApiModelProperty(value = "是否开启")
    private Boolean open;


    @ApiModelProperty(value = "业务配置")
    private List<NotifyChildConfigModel> scenes;
}
