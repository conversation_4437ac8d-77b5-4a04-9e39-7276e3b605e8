package com.timevale.footstone.user.service.model.role.request;

import com.timevale.footstone.user.service.model.role.model.RoleSyncItemModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/16 15:41
 */
@Data
public class RoleSyncToSubUnitsRequest extends ToString {

  @ApiModelProperty(value = "需同步的下级分支机构列表")
  @NotEmpty(message = "分支机构OID列表为空")
  @Size(min = 1,max = 50,message = "超出分支机构数量限制:50")
  private List<String> subUnits;

  @ApiModelProperty(value = "需同步的角色ID列表")
  @NotEmpty(message = "需同步的角色ID列表为空")
  @Size(min = 1,max = 10,message = "超出角色数量限制:10")
  private List<RoleSyncItemModel> roles;

}
