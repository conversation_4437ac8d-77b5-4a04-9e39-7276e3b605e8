package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年08月03日 14:20:00
 */
@Data
public class LoginHistoryInfoResponse extends ToString {
    @ApiModelProperty(value = "总数")
    private int total;

    @ApiModelProperty(value = "登录历史列表")
    List<LoginHistoryItem> list;

}
