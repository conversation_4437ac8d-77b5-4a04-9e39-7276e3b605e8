package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "获取页面地址返回数据")
public class GetOrganizationPageUrlResponse {

    @ApiModelProperty(value = "页面地址")
    private String url;
}
