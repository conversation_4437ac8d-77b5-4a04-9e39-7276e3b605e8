package com.timevale.footstone.user.service.model.account.mods;

import com.google.common.collect.Maps;
import com.timevale.easun.service.model.account.input.LoginSettings;
import com.timevale.footstone.user.service.enums.EquipMappingEnum;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LoginParams extends ToString {

    /** 登录端信息(不填则认为每次登录的都是不同设备) */
    @ApiModelProperty(
            value =
                    "登录端信息 可用值:PC(pc端默认浏览器),H5,ALIPAY(支付宝客户端),DING_TALK_INNER(钉钉内部网站登录),DING_TALK_EXTERNAL(外部第三方网站钉钉登录),ESIGN_APP(e签包app客户端)",
            allowableValues =
                    "PC,H5,ALIPAY(支付宝客户端),DING_TALK_INNER(钉钉内部网站登录),DING_TALK_EXTERNAL( 外部第三方网站钉钉登录),ESIGN_APP(e签包app客户端)")
    private String endpoint;

    /** 登录失效时间 单位：秒(不填则使用默认的登录失效时间) */
    @ApiModelProperty(value = "登录失效时间 单位：秒(不填则使用默认的登录失效时间)")
    private Integer expireTime;

    @ApiModelProperty(value = "环境参数，用于安全风控埋点")
    private Map<String, Object> env;

    @ApiModelProperty(value = "请求的时间，不需要传")
    private Long startTime;

    @ApiModelProperty(value = "是否需要校验长时间未登录人的身份")
    private boolean needCheck;
    public LoginParams() {
        this.startTime = System.currentTimeMillis();
    }

    public LoginParams(String endpoint, Integer expireTime) {
        this.startTime = System.currentTimeMillis();
        this.endpoint = endpoint;
        this.expireTime = expireTime;
    }

    public Map<String, Object> getEnv() {
        if (this.env == null) {
            this.env = Maps.newHashMap();
        }
        return this.env;
    }

    public LoginSettings adapt() {
        return new LoginSettings(
                StringUtils.isEmpty(endpoint)
                        ? UUID.randomUUID().toString()
                        : EquipMappingEnum.fromString(endpoint).getEquipId(),
                StringUtils.isEmpty(endpoint)
                        ? UUID.randomUUID().toString()
                        : EquipMappingEnum.fromString(endpoint).getSourceCode(),
                this.expireTime);
    }

    public void setStartTime(Long startTime) {
        this.startTime = System.currentTimeMillis();
    }

    public Long loginCost() {
        if (this.startTime != null) {
            return System.currentTimeMillis() - this.startTime;
        }
        return null;
    }
}
