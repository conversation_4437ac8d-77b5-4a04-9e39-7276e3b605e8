package com.timevale.footstone.user.service.api;

import com.timevale.footstone.user.service.ServiceName;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.account.request.v3.AccountBindUrlRequest;
import com.timevale.footstone.user.service.model.account.request.v3.AccountUnbindUrlRequest;
import com.timevale.footstone.user.service.model.account.request.v3.RpcAccountBindUrlRequest;
import com.timevale.footstone.user.service.model.account.request.v3.RpcAccountUnbindUrlRequest;
import com.timevale.footstone.user.service.model.account.response.v3.AccountBindUrlResponse;
import com.timevale.footstone.user.service.model.account.response.v3.AccountUnbindUrlResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 *
 * 账号登陆绑定相关能力
 * <AUTHOR>
 * @version 2024/11/22 14:03
 */
@RestClient(serviceId = ServiceName.name)
public interface RpcBindLoginService {


    /**
     * 获取账号身份信息解绑页面
     * @param request
     * @return
     */
    WrapperResponse<AccountUnbindUrlResponse> getAccountUnbindUrl(RpcAccountUnbindUrlRequest request);


    /**
     * 获取账号身份信息绑定/换绑页面
     * @param request
     * @return
     */
    WrapperResponse<AccountBindUrlResponse> getAccountBindUrl(RpcAccountBindUrlRequest request);
}
