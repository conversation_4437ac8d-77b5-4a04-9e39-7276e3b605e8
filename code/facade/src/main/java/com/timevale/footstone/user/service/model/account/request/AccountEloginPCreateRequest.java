package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountEloginPCreateRequest extends ToString {

    @ApiModelProperty(value = "第三方id", required = true)
    private String userId;

    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

    public void valid() {
        AssertSupport.assertNotnull(userId, new ErrorsBase.MissingArgumentsWith("userId"));
        AssertSupport.assertNotnull(mobile, new ErrorsBase.MissingArgumentsWith("mobile"));
    }

}
