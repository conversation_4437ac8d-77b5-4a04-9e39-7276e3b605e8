package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/** <AUTHOR> on 2019-01-04 */
@Data
public class UserAllInfosResponse {

    @ApiModelProperty("账号信息")
    private List<UserAllInfoResponse> accounts;

    public UserAllInfosResponse() {
    }

    public UserAllInfosResponse(List<UserAllInfoResponse> accounts) {
        this.accounts = accounts;
    }
}
