package com.timevale.footstone.user.service.model.third.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 判断能否绑定三方企业id 响应类
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CanBindOrgThirdPartyResponse extends ToString {

    /**
     * 当前用户可立即绑定企业
     */
    public final static String CAN_BIND = "CAN_BIND";
    /**
     * 邀请管理同绑定企业
     */
    public final static String INVITE_ADMIN = "INVITE_ADMIN";
    /**
     * 不能绑定,e签宝企业已绑定其他三方企业id
     */
    public final static String ORG_HAS_OTHER_TENANT_KEY = "ORG_HAS_OTHER_TENANT_KEY";
    /**
     * 走认证流程
     */
    public final static String DO_REAL_NAME = "DO_REAL_NAME";

    /**
     * {@link #CAN_BIND} 当前用户可立即绑定企业
     * <p>
     * {@link #INVITE_ADMIN} 邀请管理同绑定企业
     * <p>
     * {@link #ORG_HAS_OTHER_TENANT_KEY}  不能绑定,e签宝企业已绑定其他三方企业id
     * <p>
     * {@link #DO_REAL_NAME} 走认证流程
     */
    @ApiModelProperty(value = "能否绑定该企业")
    private String action;

    @ApiModelProperty(value = "企业oid\n不管能否绑定都会返回")
    private String orgOid;

    @ApiModelProperty(value = "脱敏的管理员名称\naction=INVITE_ADMIN时，返回该字段")
    private String adminName;


}
