package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.senderauth.GeetestRobotAuthVerifyModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> on 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmailSenderRequest extends ToString {

    @ApiModelProperty(value = "邮箱", required = true)
    @NotBlank(message = "邮箱不能为空")
    private String principal;

    @ApiModelProperty(
            value = "业务类型，与preServiceId二选一",
            allowableValues =
                    "重设密码 - RESET_PWD,"
                            + "注册 - REGIST, 登录 - LOGIN, 修改登录凭证 - MODIFY_IDCARD, 设置登录凭证 - SET_IDCARD")
    private String bizType;

    @ApiModelProperty(value = "上次流程事务id，与bizType二选一")
    private String preServiceId;

    @ApiModelProperty(value = "是否进行极验人机校验", example = "false")
    private boolean geetestAuth;

    @ApiModelProperty(value = "人机校验数据")
    private GeetestRobotAuthVerifyModel model;

    @ApiModelProperty(value = "sessinId ")
    private String  sessinId;
}
