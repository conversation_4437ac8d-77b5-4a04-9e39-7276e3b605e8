package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.CredentialValidatorUtil;
import com.timevale.footstone.user.service.model.account.mods.CommonContacts;
import com.timevale.footstone.user.service.model.account.mods.OrgCredentials;
import com.timevale.footstone.user.service.model.account.mods.OrgProperties;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-02 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgUpdateRequest extends ToString {

    @ApiModelProperty(value = "账号属性")
    private OrgProperties properties;

    @ApiModelProperty(value = "账号联系方式")
    private CommonContacts contacts;

    @ApiModelProperty(value = "证件属性")
    private OrgCredentials credentials;

    public void valid(){
        if(properties!=null &&
            properties.getLegalIdno()!=null &&
            properties.getLegalCertType()!=null){
            CredentialValidatorUtil.validate(properties.getLegalCertType(),properties.getLegalIdno());
        }
    }
}
