package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/23
 */
@Data
public class SealReceiveRequest {

    @ApiModelProperty(value = "用户id", required = true)
    private String accountId;
    public void valid() {
        AssertSupport.assertTrue(StringUtils.isNotBlank(accountId), new ErrorsBase.MissingArgumentsWith("accountId"));
    }
}
