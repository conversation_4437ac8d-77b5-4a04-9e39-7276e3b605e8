package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinContacts;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AccGetBackIdcardUpdateRquest {
    @NotNull
    @ApiModelProperty(value = "登录凭证对应的oid")
    private String oid;
    @NotNull
    @ApiModelProperty(value = "登录凭证对应的证件类型")
    private String certType;
    @NotNull
    @ApiModelProperty(value = "登录凭证对应的证件号")
    private String certNum;
    /**
     * @see BuiltinContacts
     */
    @NotNull
    @ApiModelProperty(value = "新登录凭证类型")
    private String idCardType;
    @NotNull
    @ApiModelProperty(value = "新登录凭证号")
    private String idCard;
    @NotNull
    @ApiModelProperty(value = "验证流程id")
    private String serviceId;
    @NotNull
    @ApiModelProperty(value = "验证码")
    private String authCode;
}
