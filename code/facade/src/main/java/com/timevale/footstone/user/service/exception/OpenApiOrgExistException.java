package com.timevale.footstone.user.service.exception;

import com.timevale.footstone.user.service.exception.model.OrgIdModel;

/** <AUTHOR> on 2019-04-24 */
public class OpenApiOrgExistException extends BaseDataBizException {

    private OrgIdModel data;

    public OpenApiOrgExistException(String accountId) {
        super("机构已存在");
        this.data = new OrgIdModel(accountId, accountId);
    }

    @Override
    public Object getData() {
        return data;
    }
}
