package com.timevale.footstone.user.service.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年07月27日 18:08:00
 *
 * @Desc 允许的法人证件类型
 */
public enum  LegalCertTypeEnum {
    CRED_PSN_FOREIGN,
    CRED_PSN_CH_IDCARD,
    CRED_PSN_CH_HONGKONG,
    CRED_PSN_CH_MACAO ,
    CRED_PSN_PASSPORT ,
    CRED_PSN_CH_TWCARD;

    public static final List<String> validTypes;

    static {
        validTypes = Arrays.stream(LegalCertTypeEnum.values()).map(i ->  i.name()).collect(Collectors.toList());
    }

    public static boolean validType(String type){
        return validTypes.contains(type);
    }
}
