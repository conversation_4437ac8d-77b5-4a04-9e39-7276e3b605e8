package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.base.exception.BaseIllegalArgumentException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

@Data
public class PostponeRequestV2 extends PostponeRequest {
    @ApiModelProperty(required = true,value = "证书请求p10")
    @NotBlank(message = "证书请求p10不能为空")
    private String csr;

    public void valid() {
       super.valid();
        if (StringUtils.isBlank(csr)){
            throw new BaseIllegalArgumentException("证书请求p10不能为空");
        }
    }
}
