package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR> on 2020-02-20
 */
@Data
@AllArgsConstructor
public class EloginCreateInfoModel {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否已实名，用于企业")
    private boolean realname;
}
