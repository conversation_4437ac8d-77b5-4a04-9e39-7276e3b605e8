package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class RecordedSealRequest extends ToString {

    @ApiModelProperty(value = "印章id列表", required = true)
    @NotEmpty(message = "印章id列表不能为空")
    private Set<String> sealIds;

    @ApiModelProperty(value = "备案省份")
    private String province;

    @ApiModelProperty(value = "备案城市")
    private String city;
}
