package com.timevale.footstone.user.service.model.third.response;

import com.timevale.easun.service.enums.ThirdPartyPlatFormEnum;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

@Data
public class ThirdOrgSyncTaskResponseV3 extends ToString {

    private String taskCode;
    private String organId;
    private String type;
    /**
     * {@link ThirdPartyPlatFormEnum#getPlatform()}
     */
    private String source;
    private String syncType;
    private String syncMode;
    private String status;
    private String pushStatus;
    /**
     * 最后更新时间，可以认为是完成时间
     */
    private Date updateTime;
}
