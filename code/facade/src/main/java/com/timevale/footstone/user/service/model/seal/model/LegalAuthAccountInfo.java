/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：LegalAuthAccountInfoModel <br/>
 * 包：com.timevale.footstone.user.service.model.seal.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3020:32]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：LegalAuthAccountInfoModel <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3020:32]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LegalAuthAccountInfo {

    @ApiModelProperty(value = "被授权企业主体名称")
    private String orgName;

    @ApiModelProperty(value = "被授权企业主体证件号")
    private String orgNumber;

    @ApiModelProperty(value = "授权法人名称")
    private String legalName;

    @ApiModelProperty(value = "授权法人证件号")
    private String legalNumber;

    @ApiModelProperty(value = "授权法人证件号")
    private String legalCertType;

    @ApiModelProperty(value = "授权法人账号oid")
    private String legalAccount;

    @ApiModelProperty(value = "法人信息是否完整")
    private boolean legalInfoCheck;
}