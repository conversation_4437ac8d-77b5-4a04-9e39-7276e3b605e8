package com.timevale.footstone.user.service.model.bizauth;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业授权记录
 * <AUTHOR>
 * @since 2023/4/25 10:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomBizAuthApplyResponse extends ToString {

    @ApiModelProperty("申请地址")
    private String applyAuthUrl;



}
