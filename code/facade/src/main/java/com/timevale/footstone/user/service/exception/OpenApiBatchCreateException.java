package com.timevale.footstone.user.service.exception;

import com.timevale.footstone.user.service.model.account.response.AccountBaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
public class OpenApiBatchCreateException extends BaseDataBizException {

    private List<AccountBaseResponse> data;

    public OpenApiBatchCreateException(String message, List<AccountBaseResponse> accountIds) {
        super(message);
        this.data = accountIds;
    }

    @Override
    public Object getData() {
        return data;
    }
}
