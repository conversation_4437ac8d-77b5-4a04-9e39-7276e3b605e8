package com.timevale.footstone.user.service.model.willauth.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
@AllArgsConstructor
public class QueryWillAuthResponse {


    @ApiModelProperty(value = "意愿结果状态, 0:进行中 1:成功 2:失败")
    private Integer status;

    @ApiModelProperty(value = "发起意愿账号id")
    private String ouid;
}
