package com.timevale.footstone.user.service.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021-07-15 19:42
 **/
@Data
public class RedisUtilRequest extends ToString {

    @ApiModelProperty(value = "key")
    private String key;

    @ApiModelProperty(value = "value")
    private String value;

    @ApiModelProperty(value = "expireTime")
    private Long expireTime;

    @ApiModelProperty(value = "timeUnit")
    private TimeUnit timeUnit;
}
