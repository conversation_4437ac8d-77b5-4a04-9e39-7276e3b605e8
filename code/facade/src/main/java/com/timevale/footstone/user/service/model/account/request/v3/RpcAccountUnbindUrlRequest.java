package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.model.BizAppIdModel;
import com.timevale.footstone.user.service.model.ReqHandler;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 账号身份信息解绑页面请求参数
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
public class RpcAccountUnbindUrlRequest extends AccountUnbindUrlRequest implements ReqHandler {

    @ApiModelProperty(value = "应用配置信息")
    private BizAppIdModel appIdModel;

    @Override
    public ReqHandler validate() {
        AssertSupport.assertTrue((appIdModel != null && StringUtils.isNotBlank(appIdModel.getAppId())),
                new FootstoneUserErrors.ParamBlankError("appId"));
        return this;
    }

    @Override
    public ReqHandler format() {
        return this;
    }
}