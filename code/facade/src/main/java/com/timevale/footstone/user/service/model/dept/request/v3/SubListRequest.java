package com.timevale.footstone.user.service.model.dept.request.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024/12/24 14:18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SubListRequest extends ToString {


    @ApiModelProperty(value = "orgId")
    private String orgId;


    @ApiModelProperty(value = "父部门ID，根部门为1（默认值）只支持查询下一级部门，不支持多级")
    private String deptId;


    @ApiModelProperty(value = "数据枚举: all(默认值), dept, member")
    private String item;


    @ApiModelProperty(value = "是否只获取单独主体数据")
    private boolean singleOrg;


    @ApiModelProperty(value = "请求类型  1- 组件 2-钉签标准签")
    private Integer type;


    @ApiModelProperty(value = "组织树平台  ESIGN - e签宝 FEI_SHU-飞书")
    private String platform;

    @ApiModelProperty(value = "是否展示部门负责人  true-展示  false-不展示")
    private boolean showDeptLeader;


}
