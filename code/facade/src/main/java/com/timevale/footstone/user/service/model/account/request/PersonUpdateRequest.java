package com.timevale.footstone.user.service.model.account.request;

import com.timevale.footstone.user.service.model.account.mods.CommonContacts;
import com.timevale.footstone.user.service.model.account.mods.PersonCredentials;
import com.timevale.footstone.user.service.model.account.mods.PersonProperties;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-02 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonUpdateRequest extends ToString {

    @ApiModelProperty(value = "账号属性")
    private PersonProperties properties;

    @ApiModelProperty(value = "账号联系方式")
    private CommonContacts contacts;

    @ApiModelProperty(value = "证件属性")
    private PersonCredentials credentials;
}
