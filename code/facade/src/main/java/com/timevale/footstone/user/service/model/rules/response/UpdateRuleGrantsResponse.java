package com.timevale.footstone.user.service.model.rules.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/7
 */
@Data
public class UpdateRuleGrantsResponse extends ToString {

    @ApiModelProperty(value = "流程id")
    private String flowId;
    @ApiModelProperty(value = "授权签署url")
    private String signUrl;

    @ApiModelProperty(value = "授权成功数量")
    private Integer successCount;

    @ApiModelProperty(value = "授权失败数量")
    private Integer errorCount;
}
