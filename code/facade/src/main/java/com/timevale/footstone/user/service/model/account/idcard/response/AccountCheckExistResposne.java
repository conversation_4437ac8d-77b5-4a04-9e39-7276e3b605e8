package com.timevale.footstone.user.service.model.account.idcard.response;

import io.swagger.annotations.ApiModelProperty;

/** <AUTHOR> on 2019-02-14 */
public class AccountCheckExistResposne {

    @ApiModelProperty(value = "账号id，如果为null则不存在")
    private String accountId;

    public AccountCheckExistResposne() {
    }

    public AccountCheckExistResposne(String accountId) {
        this.accountId = accountId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }
}
