package com.timevale.footstone.user.service.model.account.idcard.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> on 2018-12-20
 */
@Data
public class CertInfoModel {

    @ApiModelProperty("证书id")
    private String certId;

    @ApiModelProperty("证书内容base64")
    private String certBase64;

    @ApiModelProperty("生效时间")
    private Date startDate;

    @ApiModelProperty("失效时间")
    private Date endDate;

    @ApiModelProperty("序列号")
    private String sn;

    @ApiModelProperty("名称")
    private String certName;

    @ApiModelProperty("发证机构")
    private String issuer;

    @ApiModelProperty("是否有效")
    private Boolean enable;
}
