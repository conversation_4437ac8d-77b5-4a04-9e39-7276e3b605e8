/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：LegalAuthFileInfoModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.seal.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1214:03]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：LegalAuthFileInfoModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1214:03]创建类 by flh
 * <AUTHOR>
 */
@Data
public class LegalAuthFileInfoModel {

    @ApiModelProperty(value = "授权文档信心")
    private FileModel authDoc;

    @ApiModelProperty(value = "身份证正面照")
    private FileModel authIDFront;

    @ApiModelProperty(value = "身份证发面照")
    private FileModel authIDReverse;

    @ApiModelProperty(value = "审核原因")
    private String reason;
}