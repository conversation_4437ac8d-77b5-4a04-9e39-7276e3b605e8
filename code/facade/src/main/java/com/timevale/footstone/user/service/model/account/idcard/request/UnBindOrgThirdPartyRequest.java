package com.timevale.footstone.user.service.model.account.idcard.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UnBindOrgThirdPartyRequest {
    @ApiModelProperty("解绑的端")
    @NotBlank(message = "解绑的端信息不能为空")
    private String unbindClient;
}
