package com.timevale.footstone.user.service.model.bizauth;

import com.timevale.footstone.user.service.enums.CustomBizAuthFlowStatueEnum;
import com.timevale.footstone.user.service.enums.CustomBizSubjectTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：CustomBizAuthCallBackMsg 功能说明：
 *
 * <p>通用业务授权 mq消息通知
 * mq.custom_biz_auth.topic = custom_biz_auth_result
 * mq.custom_biz_auth.group = custom_biz_auth_result_group
 * mq.custom_biz_auth.person.tag = person
 * mq.custom_biz_auth.org.tag = org
 *
 * <AUTHOR> @DATE 2025/4/24 13:40
 */
@Data
public class CustomBizAuthCallBackMsg extends ToString {

  /**
   * 授权主体类型
   * @see CustomBizSubjectTypeEnum.name()
   * */
  private String bizAuthSubjectType;






  /**
   * 企业场景：企业账号ID
   *  个人场景：账号ID
   */
  private String subjectId;

  /**
   * 企业场景：办理人ID
   * 个人场景：办理人自己
   */
  private String agentId;



  /** 申请业务方ID */
  private String customBizNum;

  /** 申请业务方场景 */
  private String customBizScene;


  /** 授权流程ID */
  private String bizAuthFlowId;

  /**
   *
   * 授权流程状态
   * @see CustomBizAuthFlowStatueEnum.code()
   * */
  private String bizAuthFlowStatus;


  /** 授权完成时间 */
  private long bizAuthFinishTime;

  /** 授权申请时间 */
  private long bizAuthApplyTime;
}
