package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.NotifyConfigDefaultModel;
import com.timevale.footstone.user.service.model.account.mods.NotifyConfigModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2019-01-08
 */
@Data
public class NotifyConfigGetResponse {

    @ApiModelProperty("用户通知配置")
    private List<NotifyConfigModel> configs;

    @ApiModelProperty("通知默认配置")
    private Map<String, NotifyConfigDefaultModel> defaultConfigs;
}
