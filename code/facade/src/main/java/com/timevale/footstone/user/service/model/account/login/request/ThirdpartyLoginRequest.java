package com.timevale.footstone.user.service.model.account.login.request;

import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.Map;

/** <AUTHOR> on 2018-12-24 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ThirdpartyLoginRequest extends ToString {

    @ApiModelProperty(
            value = "第三方平台key",
            required = true,
            allowableValues = "ALI_PAY, WE_CHAT, DING_TALK")
    private String thirdpartyKey;

    @ApiModelProperty(value = "第三方平台用户ID", required = true)
    private String thirdpartyUserId;

    @ApiModelProperty(value = "第三方平台用户类型")
    private String thirdpartyUserType;

    @Deprecated
    @ApiModelProperty(value = "已废弃，登录应用的设备ID")
    private String equipId;

    @ApiModelProperty(value = "登录参数设置")
    private LoginParams loginParams = new LoginParams();

    @ApiModelProperty(
            value = "账号来源参数，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=8784839")
    private Map<String, String> source;

    @ApiModelProperty(value = "若登录账号不存在，是否需要创建，默认为 true")
    private Boolean needCreate;

    public Map<String, String> getSource() {
        return source == null ? Collections.emptyMap() : source;
    }

    public boolean getNeedCreate() {
        return needCreate == null ? true : needCreate;
    }
}
