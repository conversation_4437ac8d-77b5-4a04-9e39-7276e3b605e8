package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.PersonModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class ErrorAccountBasePlusResponse extends AccountBasePlusResponse {

    @ApiModelProperty(value = "失败原因", required = true)
    private String failedReason;

    public ErrorAccountBasePlusResponse(String accountId, String failedReason) {
        super(accountId);
        this.failedReason = failedReason;
    }

    public ErrorAccountBasePlusResponse(String accountId, PersonModel personModel, String failedReason) {
        super(accountId, personModel);
        this.failedReason = failedReason;
    }


}
