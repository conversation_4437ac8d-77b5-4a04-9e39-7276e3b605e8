package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResetPwdRequest extends ServiceIdModel {

    @ApiModelProperty(value = "新密码", required = true)
    private String newPassword;

    @ApiModelProperty(value = "密码的编码方式，默认MD5")
    private String encrypter;
}
