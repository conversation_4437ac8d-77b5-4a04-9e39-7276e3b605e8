package com.timevale.footstone.user.service.model.account.senderauth;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServiceIdModel extends ToString {

    @ApiModelProperty(value = "本次事务的id", required = true)
    private String serviceId;
}
