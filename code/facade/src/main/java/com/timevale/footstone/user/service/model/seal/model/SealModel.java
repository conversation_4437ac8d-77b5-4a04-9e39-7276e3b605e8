/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealModel.java <br>
 * 包：com.timevale.footstone.user.service.model.seal.model <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/220:37]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：SealModel.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/220:37]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealModel {

    @ApiModelProperty(value = "印章id")
    private String sealId;

    @ApiModelProperty(value = "印章fileKey")
    private String fileKey;

    @ApiModelProperty(value = "印章宽度")
    private Integer width;

    @ApiModelProperty(value = "印章高度")
    private Integer height;

    @ApiModelProperty(value = "印章别名")
    private String alias;

    /**
     * 1-公章模板章，2-个人模板章
     */
    @ApiModelProperty(value = "印章类型，1-公章，2-个人模板章，3-自定义印章，4-手绘章")
    private Integer sealType;

    @ApiModelProperty(value = "默认印章标识")
    private boolean defaultFlag;

    @ApiModelProperty(value = "印章下载地址")
    private String url;

    @ApiModelProperty(value = "印章状态，1-有效印章，2-待审核，3-审核不通过")
    private Short status;

    @ApiModelProperty(value = "审核描述")
    private String reason;

    @ApiModelProperty(value = "印章创建时间")
    private Long createDate;

    @ApiModelProperty(value = "印章业务类型")
    private String sealBizType;

    @ApiModelProperty(value = "印章业务类型说明")
    private String sealBizTypeDesc;

    @ApiModelProperty(value = "印章业务场景")
    private Integer sealBizScene;

    @ApiModelProperty(value = "印章来源平台")
    private String sealPlatform;

    @ApiModelProperty(value = "是否有用印权限")
    private boolean sealUserFlag;

    @ApiModelProperty(value = "授权条数")
    private Integer ruleGrantCount;

    @ApiModelProperty(value = "企业内授权数量")
    private Integer ruleGrantPsnCount;

    @ApiModelProperty(value = "企业外授权数量")
    private Integer ruleGrantOrgCount;

    @ApiModelProperty(value = "是否可编辑, 值为false时，不可编辑，不可删除")
    private boolean sealEditFlag = true;
}
