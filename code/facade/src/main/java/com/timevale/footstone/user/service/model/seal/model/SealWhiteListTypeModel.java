package com.timevale.footstone.user.service.model.seal.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SealWhiteListTypeModel extends ToString {

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "描述")
    private String desc;
}
