package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * sso三方授权结果
 *
 * <AUTHOR>
 * @since 2023/5/10 14:52
 */
@Data
public class SsoUserAuthRegisterResponse extends ToString {

  @ApiModelProperty("授权临时Code")
  private String authAccessCode;

  @ApiModelProperty("授权临时Code过期时间。毫秒")
  private Long authAccessExpired;

}
