package com.timevale.footstone.user.service.model.account.request.v2;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.footstone.user.service.model.account.request.SyncAccountRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ Id: DigitalGovAccountRequest.java, v0.1 2021年04月19日 13:52 WangYuWu $
 */
@Data
public class DigitalGovAccountRequest extends SyncAccountRequest {

    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

    public void valid() {
        super.valid();
        AssertSupport.assertNotnull(mobile, new ErrorsBase.MissingArgumentsWith("mobile"));
    }
}
