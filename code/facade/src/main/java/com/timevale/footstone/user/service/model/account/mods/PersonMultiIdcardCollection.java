package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-10 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonMultiIdcardCollection extends MultiIdcardCollection {

    @ModelFieldDecoration(DecorationType.MOBILE)
    @ApiModelProperty(value = "手机号登录")
    private String mobile;

    @ModelFieldDecoration(DecorationType.EMAIL)
    @ApiModelProperty(value = "邮箱登录")
    private String email;

    @ApiModelProperty(value = "个人证件登录")
    private IdcardCredentialsDetail person;

}
