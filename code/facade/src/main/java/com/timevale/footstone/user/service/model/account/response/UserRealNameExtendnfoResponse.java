package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.biz.rlnm.output.BizAccountRealnameOutput;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2024/11/18 16:25
 */
@Data
public class UserRealNameExtendnfoResponse extends UserRealNameBaseInfoResponse{

    private String name;


    public UserRealNameExtendnfoResponse init(
            BizAccountRealnameOutput in, String ouid, String type, String code,String name) {
        setOuid(ouid);
        setGuid(in.getGuid());
        setStatus(RealnameStatus.ACCEPT.equals(in.getStatus()));
        setRealnameTime(in.getModifyTime());
        setServiceId(in.getServiceId());
        setType(type);
        setCode(code);
        setName(name);
        return this;
    }
}
