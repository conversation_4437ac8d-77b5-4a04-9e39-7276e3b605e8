package com.timevale.footstone.user.service.model.account.senderauth.response;

import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SenderWillAssembleCheckResponse extends ServiceIdModel {

    @ApiModelProperty(value = "意愿结果：INIT=初始化，APPLY=申请成功，ACCEPT=认证成功，REJECT=认证失败")
    private String result;

}
