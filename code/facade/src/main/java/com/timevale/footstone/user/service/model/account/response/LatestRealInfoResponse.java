package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年05月07日 19:48:00
 */
@Data
public class LatestRealInfoResponse {
    @ApiModelProperty("个人gid 或者实名组织gid")
    String gid;

    @ApiModelProperty("个人账号姓名，或者组织名称")
    String name;

    @ApiModelProperty("个人PERSON，组织ORGAN")
    String type;
}
