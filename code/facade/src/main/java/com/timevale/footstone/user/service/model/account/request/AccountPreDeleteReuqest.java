package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.footstone.user.service.model.account.login.model.TokenModel;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
public class AccountPreDeleteReuqest extends TokenModel {

    @ApiModelProperty(value = "注销原因", required = true)
    private String remark;

    @ApiModelProperty(value = "客户端类型", required = false)
    private String clientType;

    public void valid() {
        AssertSupport.assertNotnull(remark, new ErrorsBase.MissingArgumentsWith("remark"));
        AssertSupport.assertTrue(StringUtils.length(remark) <= 200, new ErrorsBase.InvalidParameterRange("remark"));
    }
}
