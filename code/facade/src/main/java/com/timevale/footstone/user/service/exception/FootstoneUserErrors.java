package com.timevale.footstone.user.service.exception;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.timevale.esign.platform.toolkit.utils.exceptions.BizException;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;

/**
 * <AUTHOR> on 2019-01-19
 */
public interface FootstoneUserErrors extends ErrorsBase {

    class InvalidParamFormat extends BizException {
        public InvalidParamFormat(String param, String message) {
            super(String.format("参数格式异常: %s, 请确认: %s", param, message));
        }
    }

    class IdcardExist extends BizException {
        public IdcardExist(String src) {
            super(String.format("该%s已绑定其他e签宝账号，无法换绑", src));
        }
    }

    class CustomBizException extends BizException {
        public CustomBizException(String src) {
            super(src);
        }
    }

    class IllegalRequest extends BizException {
        public IllegalRequest() {
            super("非法请求，请提供正确的请求凭证");
        }
    }

    class IdAuthTimeout extends BizException {
        public IdAuthTimeout() {
            super("身份核验已过期");
        }
    }

    class FsAccountNotExist extends BizException {
        public FsAccountNotExist(String message) {
            super(message);
        }
    }

    class NoOper extends BizException {
        public NoOper() {
            super("无法获取当前操作人");
        }
    }

    class OpLogQueryFailed extends BizException {
        public OpLogQueryFailed() {
            super("操作日志查询失败");
        }
    }

    class FsNotSupportOrgan extends BizException {
        public FsNotSupportOrgan() {
            super("不支持删除企业账号");
        }
    }

    class FsAccountTypeNotMatch extends BizException {
        public FsAccountTypeNotMatch() {
            super("账号类型不匹配");
        }
    }

    class FsNumberMaxDeptInvalide extends BizException {
        public FsNumberMaxDeptInvalide(String number) {
            super(String.format("超出创建部门的最大限制 最多允许批量创建%s个部门", number));
        }
    }

    class FsOnlyIsolationOrgan extends BizException {
        public FsOnlyIsolationOrgan(String uid) {
            super(String.format("仅支持隔离的实名组织, input orgUid = %s", uid));
        }
    }

    class FsNumberMaxInvalide extends BizException {
        public FsNumberMaxInvalide(String number) {
            super(String.format("超出创建账号的最大限制 最多允许批量创建%s个账号", number));
        }
    }

    class FsIdNumberMaxCreateLimit extends BizException {
        public FsIdNumberMaxCreateLimit(String number) {
            super(String.format("超出创建账号的最大限制 最多允许该证件号创建%s个账号", number));
        }
    }

    class IlegalEncrtye extends BizException {
        public IlegalEncrtye(String number) {
            super(String.format("不允许的编码规则,目前只支持md5编码", number));
        }
    }

    class AppIdInvalide extends BizException {
        public AppIdInvalide(String appId, String projectId) {
            super(String.format("appId鉴权失败: 调用者appId:%s, 操作的appId:%s", appId, projectId));
        }
    }

    class EloginConfigDisableError extends BizException {
        public EloginConfigDisableError(String appId) {
            super(String.format("appId=%s免登配置未开启", appId));
        }
    }

    class SilentRealNameConfigDisableError extends BizException {
        public SilentRealNameConfigDisableError(String appId) {
            super(String.format("appId=%s静默实名配置未开启", appId));
        }
    }

    class SyncThirdOrgInfoConfigDisableError extends BizException {
        public SyncThirdOrgInfoConfigDisableError(String appId) {
            super(String.format("appId=%s同步三方企业信息配置未开启", appId));
        }
    }

    class PlatformCancelOrgDisableError extends BizException {
        public PlatformCancelOrgDisableError(String orgName, String platformName) {
            super(String.format("您的企业%s非由%s创建，暂不支持该方式注销，请手动注销", orgName, platformName));
        }
    }

    class SyncThirdSealToSaasDisableError extends BizException {
        public SyncThirdSealToSaasDisableError(String appId) {
            super(String.format("appId(%s)未创建或启用印章平台，不支持同步印章", appId));
        }
    }

    class UpdateThirdSealToSaasDisableError extends BizException {
        public UpdateThirdSealToSaasDisableError() {
            super(String.format("当前印章id非三方平台印章id，请确认后推送"));
        }
    }

    class DecryptConfigError extends BizException {
        public DecryptConfigError(String appId) {
            super(String.format("解密失败，确认appId=%s及相关配置是否正确", appId));
        }
    }

    class DecryptError extends BizException {
        public DecryptError(String msg) {
            super(String.format("解密失败，确认加密方式及内容是否正确，%s", msg));
        }
    }

    class ValidTimeoutExceed extends BizException {
        public ValidTimeoutExceed(long timeout) {
            super(String.format("有效期过长，超出限制，max = %s", timeout));
        }
    }

    class StartTimeExceed extends BizException {
        public StartTimeExceed(long timeout) {
            super(String.format("发起时间晚于当前时间，startTime = %s", timeout));
        }
    }

    class ValidTimeNotEnough extends BizException {
        public ValidTimeNotEnough(long timeout) {
            super(String.format("有效时间已过期，validTime = %s", timeout));
        }
    }

    class ThirdIdAlreadyBind extends BizException {
        public ThirdIdAlreadyBind(String userId) {
            super(String.format("该第三方id已绑定e签宝账号，userId = %s", userId));
        }
    }

    class InvalidShortLink extends BizException {
        public InvalidShortLink(String url) {
            super(String.format("短链转长链失败，%s", url));
        }
    }

    class InvalidLongLink extends BizException {
        public InvalidLongLink(String url) {
            super(String.format("长链转短链失败，%s", url));
        }
    }

    class MaxTimeInvalid extends BizException {
        public MaxTimeInvalid() {
            super("印章授权有效期最多不能超过1年");
        }
    }

    class MaxTimeInvalidExternal extends BizException {
        public MaxTimeInvalidExternal() {
            super("印章授权有效期最多不能超过3年");
        }
    }

    class EloginThirdIdBinded extends BizException {
        public EloginThirdIdBinded() {
            super("该第三方账号已经绑定！");
        }
    }

    class EloginThirdIdNotBinded extends BizException {
        public EloginThirdIdNotBinded() {
            super("该第三方账号未绑定e签宝账号！");
        }
    }

    class EloginCreatorNotRealname extends BizException {
        public EloginCreatorNotRealname() {
            super("创建人未实名！");
        }
    }

    class EloginOrganNameExist extends BizException {
        private final String ouid;

        public EloginOrganNameExist(String ouid) {
            super("已有同名的实名企业已存在！");
            this.ouid = ouid;
        }

        public String getOuid() {
            return ouid;
        }
    }

    class EloginEsignAccountBinded extends BizException {
        public EloginEsignAccountBinded() {
            super("该e签宝账号已经绑定！");
        }
    }

    class EloginKeyExpired extends BizException {
        public EloginKeyExpired() {
            super("已失效！");
        }
    }

    class EloginAlreadUnbind extends BizException {
        public EloginAlreadUnbind() {
            super("已解绑，无需重复操作！");
        }
    }

    class EloginCodeNotMatch extends BizException {
        public EloginCodeNotMatch() {
            super("验证码不符！");
        }
    }

    class SignPwdAuthFail extends BizException {
        public SignPwdAuthFail() {
            super("签署密码错误！");
        }
    }

    class EloginGetAppidConifgFail extends BizException {
        public EloginGetAppidConifgFail() {
            super("免登陆开关未开启或未传入解决方案！");
        }
    }

    class FsNotAdminException extends BizException {
        public FsNotAdminException() {
            super("当前用户不是管理员，无法操作");
        }

        public FsNotAdminException(String message) {
            super(message);
        }
    }

    class FsNotRealNameOrgException extends BizException {
        public FsNotRealNameOrgException() {
            super("当前企业未实名");
        }
    }

    class FsRealNameAccountNotSupportException extends BizException {
        public FsRealNameAccountNotSupportException() {
            super("不支持已实名账号");
        }
    }

    class FsUnRealNameAccountNotSupportException extends BizException {
        public FsUnRealNameAccountNotSupportException() {
            super("不支持未实名账号");
        }
    }

    class FsAccountNotSupportAuthCodeException extends BizException {
        public FsAccountNotSupportAuthCodeException() {
            super("账号不支持验证码发送");
        }
    }

    class FsCannotDeleteAdminException extends BizException {
        public FsCannotDeleteAdminException(String orgName) {
            super(String.format("您是%s公司的管理员，暂不支持注销，请联系客服注销", orgName));
        }
    }

    class FsSignNotFinishedException extends BizException {
        public FsSignNotFinishedException() {
            super("未完成文件签署");
        }
    }

    class AuthCodeErrorException extends BizException {
        public AuthCodeErrorException() {
            super("验证码错误");
        }
    }

    class CodeAuthFailedException extends BizException {
        public CodeAuthFailedException() {
            super("验证码验证失败");
        }
    }

    class NoSecretKeyFound extends BizException {
        public NoSecretKeyFound() {
            super("请在应用里点击生成密钥后重新请求");
        }
    }

    class FsHasFileSignException extends BizException {
        public FsHasFileSignException(String orgName) {
            super(String.format("您在%s企业下有正在签署中的文件，暂不支持注销账号，全部参与人完成签署后再注销账号", orgName));
        }
    }

    class FsDeptNameError extends BizException {
        public FsDeptNameError() {
            super(String.format("仅支持：数字、中文、英文大小写、“（）”、“·”、“-”，全角，半角和下划线"));
        }
    }

    class FsDeptNameNotAllowed extends BizException {
        public FsDeptNameNotAllowed() {
            super(String.format("不允许部门名称为所有部门或未分配部门"));
        }
    }

    class TheOrganIsNotRootOrgan extends BizException {
        public TheOrganIsNotRootOrgan(String ouid) {
            super(String.format("所在企业空间非主干分支,ouid:%s", ouid));
        }
    }

    class OrganNameNotBelongsToRootOrgan extends BizException {
        public OrganNameNotBelongsToRootOrgan(String name) {
            super(String.format("所属组织越权:%s", name));
        }
    }

    class DeptOrganNotRootOrgan extends BizException {
        public DeptOrganNotRootOrgan(String ouid) {
            super(String.format("部门所在企业非主干分支,ouid:%s", ouid));
        }
    }

    class FsNotRealLegal extends BizException {
        public FsNotRealLegal(String reason) {
            super(reason);
        }
    }

    class FsLegalNotMatched extends BizException {
        public FsLegalNotMatched(String ouid) {
            super(String.format("存在法人证件号的用户姓名与当前法人姓名不匹配,ouid:%s", ouid));
        }
    }

    class OrgNameNotMatched extends BizException {
        public OrgNameNotMatched(String orgName) {
            super(String.format("已创建的企业名称: %s与输入的企业名称不一致", orgName));
        }
    }

    class BillProductIdMappingEmptyException extends BizException {
        public BillProductIdMappingEmptyException() {
            super(String.format("查询不到对应的E签宝商品id"));
        }
    }

    class AppNotAlowedQueryOrgRealException extends BizException {
        public AppNotAlowedQueryOrgRealException() {
            super(String.format("当前appId 没有权限查询"));
        }
    }

    class QueryOrgRealnameLimitExection extends BizException {
        public QueryOrgRealnameLimitExection(int alowedSize, int querySize) {
            super(String.format("今日可查询企业数还剩%d条，本次查询%d条，超出限额", alowedSize, querySize));
        }
    }

    class QueryOrgRealnameArgsError extends BizException {
        public QueryOrgRealnameArgsError() {
            super("请输入企业名称或统一社会信用代码");
        }
    }

    class OrgGidNameNotMatchError extends BizException {
        public OrgGidNameNotMatchError() {
            super("企业gid和企业名称不相符");
        }
    }

    class TimevaleAccountParseError extends BizException {
        public TimevaleAccountParseError() {
            super("解析天谷账号失败");
        }
    }

    class CredentialsTypeNotSupport extends BizException {
        public CredentialsTypeNotSupport() {
            super("不支持的证件类型");
        }
    }

    class CredentialsLegalIdTypeNotSupport extends BizException {
        public CredentialsLegalIdTypeNotSupport() {
            super("企业法人证件类型不支持");
        }
    }

    class SearchKeywordIsEmpty extends BizException {
        public SearchKeywordIsEmpty() {
            super("搜索关键词不能为空");
        }
    }

    class SearchSizeTooMax extends BizException {
        public SearchSizeTooMax() {
            super("搜索分页数量超过限制");
        }
    }

    class PrincipalExpire extends BizException {
        public PrincipalExpire(String msg) {
            super(msg);
        }
    }

    class RedisParamError extends BizException {
        public RedisParamError(String msg) {
            super(msg);
        }
    }

    class Org4FactValidErrro extends BizException {
        public Org4FactValidErrro() {
            super("企业四要素校验失败");
        }

        public Org4FactValidErrro(String msg) {
            super(String.format("经过比对您不是企业法人，可将小程序推荐给企业法人%s来使用", msg));
        }
    }

    class OrgNotAllowDelete extends BizException {
        public OrgNotAllowDelete() {
            super("该企业不允许删除");
        }
    }

    class UserNotAuthorization extends BizException {
        public UserNotAuthorization() {
            super("用户未授权，无法查询该用户实名信息");
        }
    }

    class AccountNotExists extends BizException {
        public AccountNotExists(String contacts) {
            super(String.format("账号不存在 %s", contacts));
        }
    }

    class OrganNotAuthorization extends BizException {
        public OrganNotAuthorization() {
            super(String.format("当前企业用户未授权，无法管理该企业成员信息"));
        }
    }

    class AuthorizationEnd extends BizException {
        public AuthorizationEnd() {
            super(String.format("当前授权任务已经完成"));
        }
    }

    class RoleNameNotExistInOrg extends BizException {
        public RoleNameNotExistInOrg() {
            super(String.format("某些角色不在所属组织下"));
        }
    }

    class DeptNameNotInOrg extends BizException {
        public DeptNameNotInOrg(String deptName) {
            super(String.format("部门不在所属组织下:%s", deptName));
        }
    }

    class RoleNotExistInOrg extends BizException {
        public RoleNotExistInOrg(String roleId, String roleName) {
            super(String.format("角色不在所属组织下:id:%s, name:%s", roleId, roleName));
        }
    }

    class OutOfLimit extends BizException {
        public OutOfLimit(Integer inviteBatchUploadLimit) {
            super(String.format("超过大小限制:%s", inviteBatchUploadLimit));
        }
    }

    class MembersTooMany extends BizException {
        public MembersTooMany(Integer maxCount) {
            super(String.format("成员数量超过大小限制:%s", maxCount));
        }
    }


    class MemberExistedInOrg extends BizException {
        public MemberExistedInOrg(String orgId, String memberOid) {
            super(String.format("成员:{%s}已加过该企业{%s}", memberOid, orgId));
        }
    }

    class AdminNotAllowDelete extends BizException {
        public AdminNotAllowDelete(String memberOid) {
            super(String.format("管理员，法人或空间管理员不允许删除: %s", memberOid));
        }
    }

    class InviteDataError extends BizException {

        public InviteDataError() {
            super("邀请数据错误");
        }
    }

    class InviteTypeError extends BizException {

        public InviteTypeError() {
            super("邀请类型错误");
        }
    }

    class InvalidInviteContextError extends BizException {

        public InvalidInviteContextError() {
            super("邀请信息无效");
        }
    }

    class InviteStatusError extends BizException {

        public InviteStatusError() {
            super("您已操作完成，请勿重复操作");
        }
    }

    class InviteMemberError extends BizException {

        public InviteMemberError() {
            super("鉴权失败");
        }
    }

    class WillNotSuccess extends BizException {

        public WillNotSuccess() {
            super("意愿认证不通过");
        }
    }


    class MemberNotAdminOrLegal extends BizException {

        public MemberNotAdminOrLegal() {
            super("操作人必须为法人或管理员");
        }
    }

    class ImportantRoleNotGrant extends BizException {

        public ImportantRoleNotGrant() {
            super("法人，管理员或空间管理员不可手动指定");
        }
    }


    class ImportantRoleNotRevoke extends BizException {

        public ImportantRoleNotRevoke() {
            super("法人，管理员或空间管理员不可手动取消");
        }
    }


    class SpaceAdminNotAllowDelete extends BizException {

        public SpaceAdminNotAllowDelete(String oid) {
            super(String.format("空间管理员不允许 ：%s", oid));
        }
    }

    class SpaceCountIsMax extends BizException {

        public SpaceCountIsMax() {
            super(String.format("企业空间数量已达上限"));
        }
    }

    class DeptOrAdminNotEmpty extends BizException {
        public DeptOrAdminNotEmpty() {
            super(String.format("空间管理员或部门不能为空"));
        }
    }

    class AccountNotExist extends BizException {

        public AccountNotExist() {
            super(String.format("账号列表不存在"));
        }
    }

    class SpaceOnlyDeptNotAllowDelete extends BizException {

        public SpaceOnlyDeptNotAllowDelete(String spaceName) {
            super(String.format("改部门为%s空间唯一部门不能删除", spaceName));
        }
    }


    class DeptNotBelongOrg extends BizException {
        public DeptNotBelongOrg(String dept) {
            super(String.format("部门 %s 不在企业内 ", dept));
        }
    }

    class MemberNotDeptMember extends BizException {
        public MemberNotDeptMember(String memberOid) {
            super(String.format("成员 %s 不在业务空间部门内", memberOid));
        }
    }

    class MemberNotInOrgan extends BizException {
        public MemberNotInOrgan(String memberOid) {
            super(String.format("成员 %s 不在企业内", memberOid));
        }
    }

    class NotSpaceAdminShouldNotTransfer extends BizException {
        public NotSpaceAdminShouldNotTransfer() {
            super(String.format("非空间管理员不能操作转授"));
        }
    }

    class BeTransferMemberShouldInSpace extends BizException {
        public BeTransferMemberShouldInSpace() {
            super(String.format("被转授的成员应在业务空间部门下"));
        }
    }

    class ParamEmpty extends BizException {
        public ParamEmpty(String param) {
            super(String.format("参数为空 %s", param));
        }
    }

    class HaveNoMembersToExport extends BaseBizRuntimeException {
        public HaveNoMembersToExport() {
            super(String.format("没有符合筛选条件的数据"));
        }
    }

    class NotBranchInRootOrgan extends BaseBizRuntimeException {
        @JsonCreator
        public NotBranchInRootOrgan(@JsonProperty("branchOrganOid") String organOid, @JsonProperty("rootOrganOid") String rootOrganOid) {
            super(String.format("该企业 %s  不是根企业 %s 的子企业", organOid, rootOrganOid));
        }
    }

    class NoneSsoCustomConfig extends BizException {
        public NoneSsoCustomConfig() {
            super("请先保存SSO配置信息后再启用SSO");
        }
    }

    class NoneRefOrgsOnTheFlowKey extends BizException {
        public NoneRefOrgsOnTheFlowKey() {
            super("连接器找不到关联企业，请联系管理员去SSO配置页面配置域名的关联企业");
        }
    }

    class NoneSsoConnector extends BizException {
        public NoneSsoConnector() {
            super("找不到对应的连接器");
        }
    }

    class DataSyncNotCreateDept extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotCreateDept() {
            super(String.format("开通三方组织架构的企业无法创建部门"));
        }
    }

    class DataSyncNotDeleteDept extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotDeleteDept() {
            super(String.format("开通三方组织架构的企业无法删除部门"));
        }
    }

    class DataSyncNotUpdateDeptName extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotUpdateDeptName() {
            super(String.format("开通三方组织架构的企业无法更新部门名称"));
        }
    }


    class DataSyncNotCreateMember extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotCreateMember() {
            super(String.format("开通三方组织架构的企业无法创建成员"));
        }
    }

    class DataSyncNotDeleteMember extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotDeleteMember(String orgId) {
            super(String.format("开通三方组织架构的企业:%s  无法删除成员", orgId));
        }
    }


    class DataSyncNotUpdateMemberDept extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotUpdateMemberDept() {
            super(String.format("开通三方组织架构的企业无法更新成员部门"));
        }
    }

    class DataSyncNotUpdateMember extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotUpdateMember() {
            super(String.format("开通三方组织架构同步的企业仅允许更新角色"));
        }
    }


    class DataSyncNotUrgInvite extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotUrgInvite() {
            super(String.format("开通三方组织架构的企业无法催办邀请"));
        }
    }


    class DataSyncNotPassInvite extends BaseBizRuntimeException {
        @JsonCreator
        public DataSyncNotPassInvite() {
            super(String.format("开通三方组织架构的企业无法通过邀请"));
        }
    }


    class JoinOrganFail extends BaseBizRuntimeException {
        @JsonCreator
        public JoinOrganFail(String admin) {
            super(String.format("加入失败，该组织管理员开启了三方系统组织成员同步，请联系该组织管理员  %s", admin));
        }
    }


    class AdminAccountError extends BaseBizRuntimeException {
        @JsonCreator
        public AdminAccountError(String admin) {
            super(String.format("管理员账号异常，请管理员%s在当前平台完成认证授权或到e签宝官网进行认证", admin));
        }
    }

    class OrganNotBindTenantKey extends BaseBizRuntimeException {
        @JsonCreator
        public OrganNotBindTenantKey(String name) {
            super(String.format("企业未关联%s,不能操作三方系统组织同步", name));
        }
    }

    class OrganNotSupportOrgSync extends BaseBizRuntimeException {
        @JsonCreator
        public OrganNotSupportOrgSync(String name) {
            super(String.format("%s暂不支持三方系统组织同步", name));
        }
    }

    class OrganNotSupportIntegrationConfig extends BaseBizRuntimeException {
        @JsonCreator
        public OrganNotSupportIntegrationConfig(String name) {
            super(String.format("%s暂不支持集成中心配置管理", name));
        }
    }

    class NoPrivilege extends BizException {
        public NoPrivilege() {
            super("无权限");
        }
    }

    class ChangeOrgNameEqual extends BaseBizRuntimeException {
        @JsonCreator
        public ChangeOrgNameEqual() {
            super(String.format("更改前后名称一致 请检查后在操作"));
        }
    }

    class ChangeOrgNameBizError extends BaseBizRuntimeException {
        @JsonCreator
        public ChangeOrgNameBizError(String s) {
            super(String.format("更改组织名称失败 %s", s));
        }
    }


    class MemberCountOvertop extends BaseBizRuntimeException {
        @JsonCreator
        public MemberCountOvertop() {
            super(String.format("成员数量超出限制"));
        }
    }


    /**
     * 定制错误，和页面有交互约定
     */
    class ChangeOrgNameOrgInfoAuth extends BaseBizRuntimeException {
        @JsonCreator
        public ChangeOrgNameOrgInfoAuth(String s) {
            super(String.format("更改组织名称失败 %s", s));
        }
    }

    /**
     * 参数必填但为空
     */
    class ParamBlankError extends BaseBizRuntimeException {
        @JsonCreator
        public ParamBlankError(String s) {
            super(String.format("%s必填", s));
        }
    }

    /**
     * 参数格式有误
     */
    class ParamFormatError extends BaseBizRuntimeException {
        @JsonCreator
        public ParamFormatError(String s) {
            super(String.format("%s格式有误", s));
        }
    }


    class AuthUpdateNameError extends BaseBizRuntimeException {
        @JsonCreator
        public AuthUpdateNameError() {
            super(String.format("更名失败"));
        }
    }

    /**
     * 参数长度过长
     */
    class ParamLengthTooLong extends BaseBizRuntimeException {
        @JsonCreator
        public ParamLengthTooLong(String s, int length) {
            super(String.format("%s长度过长，不超过%s", s, length));
        }
    }



    class ChangePersonNameBizError extends BaseBizRuntimeException {
        @JsonCreator
        public ChangePersonNameBizError() {
            super(String.format("名字不得为空或者不能与原名字相同"));
        }
    }



    class TimesUp extends BaseBizRuntimeException {
        @JsonCreator
        public TimesUp(String business) {
            super(String.format(business));
        }
    }


    class SingleIpError extends BaseBizRuntimeException {
        @JsonCreator
        public SingleIpError(String startIp,String endIp) {
            super(String.format("ipTag与要求不符 startIp: {},endIp:{}",startIp,endIp));
        }
    }


    class IpCheckError extends BaseBizRuntimeException {
        @JsonCreator
        public IpCheckError(String startIp,String endIp) {
            super(String.format("格式有误 startIp: {},endIp:{}",startIp,endIp));
        }
    }


    class DuplicatedKey extends BaseBizRuntimeException {
        @JsonCreator
        public DuplicatedKey(String key) {
            super(String.format("存在重复的key:{},请重新确认",key));
        }
    }

    class SsoSupportLoginTypeChangeError extends BaseBizRuntimeException {
        @JsonCreator
        public SsoSupportLoginTypeChangeError() {
            super(String.format("请在设置SSO登录主体公司下进行修改"));
        }
    }


    class UserIpNotPermit extends BaseBizRuntimeException {
        @JsonCreator
        public UserIpNotPermit() {
            super(String.format("当前IP地址无权限查看"));
        }
    }


    class EndpointError extends BaseBizRuntimeException {
        @JsonCreator
        public EndpointError() {
            super(String.format("Endpoint不存在"));
        }
    }

    class NotHasThirdPartyUserId extends BaseBizRuntimeException {
        @JsonCreator
        public NotHasThirdPartyUserId() {
            super(String.format("当前账号未绑定三方平台用户id"));
        }
    }

    class QueryThirdUserAndTenantInfoError extends BaseBizRuntimeException {
        @JsonCreator
        public QueryThirdUserAndTenantInfoError() {
            super(String.format("查询三方平台用户和企业信息失败"));
        }
    }

    class QuerySubCorpError extends BaseBizRuntimeException {
        @JsonCreator
        public QuerySubCorpError() {
            super(String.format("查询企业列表失败"));
        }
    }

    class IllegalData extends BaseBizRuntimeException {
        @JsonCreator
        public IllegalData() {
            super(String.format("不合法参数"));
        }
    }

    class MemberCheckDeptError extends BizException {
        public MemberCheckDeptError(String orgId) {
            super(String.format("企业 %s 业务空间管理员信息校验失败", orgId));
        }
    }
}
