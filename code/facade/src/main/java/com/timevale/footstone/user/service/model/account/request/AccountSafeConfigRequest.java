package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: sicheng
 * @since: 2020-06-04 18:54
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AccountSafeConfigRequest extends ToString {

    @ApiModelProperty(value = "0-登录二次验证，1-异地登录提醒", required = true)
    private Integer category;

    @ApiModelProperty(value = "true/false代表阀值开关",required = true)
    private Boolean isOpen;

}



