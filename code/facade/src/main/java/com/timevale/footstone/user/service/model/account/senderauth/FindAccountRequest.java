package com.timevale.footstone.user.service.model.account.senderauth;

import com.timevale.footstone.identity.common.service.model.information.IndividualInformationEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FindAccountRequest {

    @ApiModelProperty(value =  "上下文信息")
    private FindAccountContextInfo contextInfo;

    @ApiModelProperty(value = "个人新身份信息")
    private FindAccountPsnModel indivInfo;
}
