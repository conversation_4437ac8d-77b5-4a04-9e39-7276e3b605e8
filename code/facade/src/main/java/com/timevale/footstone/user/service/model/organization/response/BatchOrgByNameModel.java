package com.timevale.footstone.user.service.model.organization.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class BatchOrgByNameModel {
    @ApiModelProperty(value = "组织名称")
    private String orgName;
    @ApiModelProperty(value = "对应传入的顺序")
    private Integer order;
    @ApiModelProperty(value = "组织实名状态")
    private Boolean realName;
    @ApiModelProperty(value = "组织oid")
    private String ouid;
    @ApiModelProperty(value = "组织guid")
    private String guid;
}
