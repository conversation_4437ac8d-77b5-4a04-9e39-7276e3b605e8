package com.timevale.footstone.user.service.model.third.response;

import com.timevale.easun.service.enums.ThirdPartyBindOrgSceneEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-05-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetThirdPartyBindOrgSceneResponse {
    /**
     * 绑定场景
     *
     * @see ThirdPartyBindOrgSceneEnum#name()
     */
    private String bindScene;

    /**
     * 绑定企业信息
     */
    private List<BindOrg> bindOrgList;

    private BindSceneInfo bindSceneInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BindOrg {
        private String organName;
        private String organOuid;
        private String organGuid;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BindSceneInfo {
        /**
         * 绑定引导语
         */
        private String introduceText;

        /**
         * 确认按钮文案
         */
        private String confirmText;

        /**
         * 取消按钮文案
         */
        private String cancelText;

        /**
         * 前端icon静态资源地址
         */
        private String iconUrl;
    }
}
