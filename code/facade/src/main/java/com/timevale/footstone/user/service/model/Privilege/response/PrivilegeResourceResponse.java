package com.timevale.footstone.user.service.model.Privilege.response;

import com.timevale.footstone.user.service.model.Privilege.mods.BizPrivilegeModel;
import com.timevale.footstone.user.service.model.Privilege.mods.PrivilegeModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17 11:18
 */

@Data
public class PrivilegeResourceResponse extends ToString {

    @ApiModelProperty(value = "数据权限列表")
    private List<PrivilegeModel> privilegeModels;

    @ApiModelProperty(value = "资源名称")
    private String targetClassKeyName;

    @ApiModelProperty(value = "资源key")
    private String targetClassKey;

    @ApiModelProperty(value =  "资源权重")
    private Integer weight;
}
