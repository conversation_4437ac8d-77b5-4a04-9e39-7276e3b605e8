package com.timevale.footstone.user.service.exception;

import com.timevale.footstone.user.service.exception.model.AccountIdModel;

/** <AUTHOR> on 2019-04-24 */
public class OpenApiAccountExistException extends BaseDataBizException {

    private AccountIdModel data;

    public OpenApiAccountExistException(String accountId) {
        super("账号已存在");
        this.data = new AccountIdModel(accountId);
    }

    @Override
    public Object getData() {
        return data;
    }
}
