package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class JoinOrganResponse extends ToString {

    @ApiModelProperty(value = "成员ID")
    private String memberId;

    @ApiModelProperty(value = "组织ID")
    private String organId;
}
