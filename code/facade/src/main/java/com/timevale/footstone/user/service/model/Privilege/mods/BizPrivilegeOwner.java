package com.timevale.footstone.user.service.model.Privilege.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class BizPrivilegeOwner {

    @ApiModelProperty(value = "用户或角色是否拥有该权限")
    private Boolean privilegeOwner;

    @ApiModelProperty(value = "权限操作名称", required = true)
    private String operationPermit;

    @ApiModelProperty(value = "权限所属资源", required = true)
    private String targetClassKey;
}
