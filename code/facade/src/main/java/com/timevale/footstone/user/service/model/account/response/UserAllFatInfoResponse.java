package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.PersonPropCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> on 2019-01-04
 */
@Data
public class UserAllFatInfoResponse {

    @ApiModelProperty("基础信息")
    private UserBaseInfoResponse base;

    @ApiModelProperty("属性信息")
    private PersonPropCollection prop;

    @ApiModelProperty("个人实名信息摘要")
    private UserRealNameBaseInfoResponse baseRealname;

    @ApiModelProperty("登录信息")
    private UserLoginInfosResponse loginInfo;

    @ApiModelProperty("额外信息")
    private ExtraInfo extraInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExtraInfo {
        private String userName;
        private String loginEmail;
        private String loginMobile;
    }
}
