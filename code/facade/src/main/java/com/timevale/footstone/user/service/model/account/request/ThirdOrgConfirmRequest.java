package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.PathVariable;

import javax.validation.constraints.NotBlank;
import java.util.Set;

@Data
public class ThirdOrgConfirmRequest extends ToString {

    @ApiModelProperty(value = "组织id")
    private String organId;

    @ApiModelProperty(value = "三方成员编号")
    private String  thirdUserId;

    @ApiModelProperty(value = "三方成员来源")
    private String thirdSource;

    @ApiModelProperty(value = "确认信息")
    @NotBlank(message = "确认信息不能为空")
    private String confirmInfo;

}
