package com.timevale.footstone.user.service.model.account.request.v2;

import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PsnAuthInfoParam extends PsnAuthBaseParam {

    @ApiModelProperty(" 个人账号OID")
    private String accountId;

    public void valid() {
        if (StringUtils.isBlank(accountId) && StringUtils.isBlank(mobile) && StringUtils.isBlank(email)) {
            throw new ErrorsBase.MissingArguments();
        }
    }

}
