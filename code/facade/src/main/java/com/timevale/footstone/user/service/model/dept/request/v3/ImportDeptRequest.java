package com.timevale.footstone.user.service.model.dept.request.v3;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/4/14 15:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportDeptRequest {

  @ApiModelProperty(value = "关联组织架构名称")
  @ExcelProperty(index = 0)
  private String orgName;

  @ApiModelProperty(value = "部门全路径")
  @ExcelProperty(index = 1)
  private String deptFullName;

  @ApiModelProperty(value = "负责人名字")
  @ExcelProperty(index = 2)
  private String leaderName;

  @ApiModelProperty(value = "负责人账号")
  @ExcelProperty(index = 3)
  private String leaderAccount;

}
