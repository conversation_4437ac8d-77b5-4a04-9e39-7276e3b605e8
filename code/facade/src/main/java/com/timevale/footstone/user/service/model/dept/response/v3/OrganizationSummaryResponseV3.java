package com.timevale.footstone.user.service.model.dept.response.v3;

import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import com.timevale.footstone.user.service.model.member.response.LeaderInfoResponse;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/4/3 15:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganizationSummaryResponseV3 extends ToString {

  @ApiModelProperty(value = "账号OUID")
  private String ouid;

  @ApiModelProperty(value = "账号UUID")
  private String uuid;

  @ApiModelProperty(value = "账号GUID")
  private String guid;

  @ApiModelProperty(value = "账号所属APPID")
  private String appId;

  @ApiModelProperty(value = "组织机构名称")
  private String name;

  @ApiModelProperty(value = "组织机构证件号")
  private String code;

  @ApiModelProperty(value = "组织机构证件类型")
  private String codeType;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "账号类型（PERSON, ORGANIZE）")
  private String type;

  @ApiModelProperty(value = "法人名称")
  private String legalName;

  @ApiModelProperty(value = "法人账号oid")
  private String legalOid;

  @ApiModelProperty(value = "法人手机号")
  // 2024/12/6  脱敏
  @ModelFieldDecoration(DecorationType.MOBILE)
  private String legalMobile;

  @ApiModelProperty(value = "法人邮箱")
  // 2024/12/6  脱敏
  @ModelFieldDecoration(DecorationType.EMAIL)
  private String legalEmail;

  @ApiModelProperty(value = "法人昵称")
  private String legalNickName;

  @ApiModelProperty(value = "是否是主干分支：只有主干分支返回true")
  private Boolean isRootOrgan;

  @ApiModelProperty(value = "成员人数")
  private Integer memberCount;

  @ApiModelProperty(value = "组织来源")
  private String organSource;

  @ApiModelProperty(value = "会员版本")
  private String vipCode;

  @ApiModelProperty(value = "管理员")
  private LeaderInfoResponse admin;

}
