package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.Collections;
import java.util.Map;

/** <AUTHOR> on 2019-12-23 */
public class EloginConfigResponse {

    @ApiModelProperty(value = "免登相关配置")
    private Map<String, String> configs;

    public Map<String, String> getConfigs() {
        return configs == null ? Collections.emptyMap() : configs;
    }

    public void setConfigs(Map<String, String> configs) {
        this.configs = configs;
    }
}
