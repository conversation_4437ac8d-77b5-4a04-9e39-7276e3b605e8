package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by tsign on 2018/12/29.
 */
@Data
@ApiModel(value = "用户已实名认证方式信息")
public class GetRealNameWaysResponse {

    @ApiModelProperty(value = "已实名状态", required = true)
    private boolean status;

    @ApiModelProperty(value = "已实名认证信息详情")
    private List<PIVDetail> data;

    public GetRealNameWaysResponse() {
        this.status = false;
        this.data = new ArrayList<PIVDetail>();
    }

    public void addRealNameWay(int type, String name, String extras) {

        PIVDetail detail = new PIVDetail();
        detail.setType(type);
        detail.setName(name);
        detail.setExtras(extras);

        this.data.add(detail);
    }

    @Data
    @ApiModel
    class PIVDetail extends ToString {

        @ApiModelProperty(value = "实名认证类型: 1-银行四要素;2-人脸识别;3-运营商三要素;4-人工实名")
        private int type;

        @ApiModelProperty(value = "实名认证类型名称: 1-银行四要素;2-人脸识别;3-运营商三要素;4-人工实名")
        private String name;

        @ApiModelProperty(value = "实名附件信息,json字符串: mobile-银行四要素时为实名手机号")
        private String extras;
    }
}
