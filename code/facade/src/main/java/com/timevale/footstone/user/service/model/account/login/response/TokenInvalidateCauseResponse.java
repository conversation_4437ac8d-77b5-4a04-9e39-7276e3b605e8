package com.timevale.footstone.user.service.model.account.login.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/** <AUTHOR> on 2018-12-27 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TokenInvalidateCauseResponse extends ToString {

    @ApiModelProperty(
            value = "当前token状态，ACTIVE-有效，EXPIRE-超时失效，REPEAT_LOGIN-重复登录失效，LOCKED-账号锁定，FREEZED-账号冻结，ACCOUNT_DELETE-账号删除")
    private String status;

    @ApiModelProperty(value = "账号名，目前只会显示手机或邮箱，否则为null")
    private String account;

    @ApiModelProperty(value = "冻结/锁定的时间，只有在status为冻结/锁定时有值")
    private Date lockedTime;

    @ApiModelProperty(value = "冻结/锁定的原因，只有在status为冻结/锁定时有值")
    private String lockedReason;

    @ApiModelProperty(value = "重复登录的时间")
    private Date lastLoginTime;
}
