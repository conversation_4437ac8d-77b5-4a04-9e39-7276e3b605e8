package com.timevale.footstone.user.service.model.account.login.request;

import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * app扫码登录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QRLoginRequest extends ToString {

    @ApiModelProperty(value = "当前用户ouid", required = true)
    private String ouid;

    @ApiModelProperty(value = "二维码uid", required = true)
    private String qrUid;

    @ApiModelProperty(value = "app端用户token")
    private String appToken;

    @ApiModelProperty(value = "登录相关配置")
    private LoginParams loginParams = new LoginParams();
}
