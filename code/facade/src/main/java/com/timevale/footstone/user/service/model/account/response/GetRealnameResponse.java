package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class GetRealnameResponse extends ToString {

    @ApiModelProperty(value = "企业名称")
    private String name;

    @ApiModelProperty(value = "证件类型")
    private String certType;

    @ApiModelProperty(value = "证件号")
    private String certNo;

    @ApiModelProperty(value = "法人姓名")
    private String legalName;

    @ApiModelProperty(value = "法人证件类型")
    private String legalCertType;

    @ApiModelProperty(value = "法人证件号")
    private String legalCertNo;

    @ApiModelProperty(value = "法人手机号")
    private String legalMobile;

    @ApiModelProperty(value = "实名认证类型")
    private String realnameType;

    @ApiModelProperty(value = "对公账户名")
    private String accoutName;

    @ApiModelProperty(value = "对公账户")
    private String accoutNo;

    @ApiModelProperty(value = "对公账户开户总行")
    private String bank;

    @ApiModelProperty(value = "对公账户开户支行")
    private String subbranch;

    @ApiModelProperty(value = "支行所在省份")
    private String province;

    @ApiModelProperty(value = "支行所在城市")
    private String city;

    @ApiModelProperty(value = "实名认证业务ID")
    private String serviceId;
}
