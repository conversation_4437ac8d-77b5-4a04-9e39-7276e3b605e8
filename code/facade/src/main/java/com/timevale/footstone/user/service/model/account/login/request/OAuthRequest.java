package com.timevale.footstone.user.service.model.account.login.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OAuthRequest extends ToString {

    @ApiModelProperty(value = "第三方授权登录方式", allowableValues = "ALIPAY,DING_TALK_EXTERNAL")
    private String state;
}
