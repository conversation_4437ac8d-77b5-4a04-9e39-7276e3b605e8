package com.timevale.footstone.user.service.model.account.request.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ Id: RealNameNotifyRequest.java, v0.1 2021年10月21日 15:24 WangYuWu $
 */
@Data
public class RealNameNotifyBaseRequest {

    /**
     * 实名认证的流程ID
     */
    @ApiModelProperty(value = "个人认证流程id")
    private String      flowId;

    /**
     * e签宝账号OID
     */
    @ApiModelProperty(value = "账户id")
    private String      accountId;

    /**
     * 发起方传入的自己的业务ID
     */
    @ApiModelProperty(value = "业务上下文id")
    private String      contextId;

    /**
     * 认证结果校验码,用于串联e签宝其他业务
     */
    @ApiModelProperty(value = "实名认证结果校验码")
    private String      verifycode;

    /**
     * 认证是否成功. true - 成功; false - 失败
     */
    @ApiModelProperty(value = "实名认证结果")
    private Boolean     success;
}
