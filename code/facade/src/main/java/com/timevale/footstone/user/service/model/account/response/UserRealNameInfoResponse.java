package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserRealNameInfoResponse {

    @ApiModelProperty(value = "实名认证类型")
    private String type;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "证件类型")
    private String certType;

    @ApiModelProperty(value = "证件号")
    private String idno;

    @ApiModelProperty(value = "手机号")
    private String mobile;
}
