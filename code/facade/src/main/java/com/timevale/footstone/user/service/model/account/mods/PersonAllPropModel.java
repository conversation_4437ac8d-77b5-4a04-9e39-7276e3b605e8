package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.account.service.model.service.biz.builder.icuser.BizICUserUpdateInputBuilder;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-08-01 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonAllPropModel extends PersonPropModel {

    @ApiModelProperty(
            "属性-证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String idType;

    @ApiModelProperty("属性-证件号")
    private String idNumber;

    public String getIdType() {
        return StringUtils.isNotBlank(idType) ? idType : BuiltinCredentials.CRED_PSN_CH_IDCARD;
    }

    @Override
    public BizICUserUpdateInputBuilder conv(BizICUserUpdateInputBuilder in) {
        super.conv(in);
        if (StringUtils.isNotBlank(idNumber)) {
            in.buildCredentials().add(getIdType(), getIdNumber());
        }
        return in;
    }
}
