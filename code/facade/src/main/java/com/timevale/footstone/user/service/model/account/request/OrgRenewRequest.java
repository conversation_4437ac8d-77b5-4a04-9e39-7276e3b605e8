package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.builder.icuser.BizICUserUpdateInputBuilder;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;

/** <AUTHOR> on 2019-01-21 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgRenewRequest extends ToString {

    @ApiModelProperty(value = "企业名称")
    private String name;

    @ApiModelProperty(
            "属性-证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String idType;

    @ApiModelProperty("属性-证件号")
    private String idNumber;

    @ApiModelProperty(value = "属性-企业法人名称")
    private String orgLegalName;

    @ApiModelProperty(value = "属性-企业法人证件号")
    private String orgLegalIdNumber;

    @ApiModelProperty(value = "实名证据点")
    private String realnameEvidencePointId;

    public String getIdType() {
        return StringUtils.isNotBlank(idType) ? idType : BuiltinCredentials.CRED_ORG_USCC;
    }

    public BizICUserUpdateInputBuilder conv() {
        BizICUserUpdateInputBuilder builder = BizICUserUpdateInputBuilder.builder();
        builder.buildProperties().add(INFO_NAME, getName());
        if (StringUtils.isNotBlank(idNumber)) {
            builder.buildCredentials().add(getIdType(), idNumber);
        }
        if (StringUtils.isNotBlank(getRealnameEvidencePointId())) {
            builder.buildProperties().add(BuiltinProperty.REALNAME_EVIDENCE_POINT_ID, getRealnameEvidencePointId());
        }
        return builder;
    }
}
