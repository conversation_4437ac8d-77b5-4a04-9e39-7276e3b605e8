package com.timevale.footstone.user.service.model.rules.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量授权返回的流程列表
 * @author: jinhuan
 * @since: 2020-02-12 17:56
 **/
@Data
public class BatchBindRuleFlowResponse extends ToString {

    @ApiModelProperty(value = "流程列表")
    private List<String> flowIds = new ArrayList<>();

}
