package com.timevale.footstone.user.service.model.rules.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2019/12/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetRuleGrantByGrantIdResponse {

  @ApiModelProperty(value = "流程id", required = true)
  private String flowId;

  @ApiModelProperty(value = "授权id")
  private String ruleGrantId;

  @ApiModelProperty(value = "组织id")
  private String orgId;

  @ApiModelProperty(value = "资源id")
  private String resourceId;

  @ApiModelProperty(value = "资源类型")
  private String resourceType;

  @ApiModelProperty(value = "合同模板id")
  private String contractTemplateId;

  @ApiModelProperty(value = "合同模板名称")
  private String contractTemplateName;

  @ApiModelProperty(value = "模板key")
  private String templateKey;

  @ApiModelProperty(value = "模板名称")
  private String templateName;

  @ApiModelProperty(value = "角色类型")
  private String roleKey;

  @ApiModelProperty(value = "角色名称")
  private String roleName;

  @ApiModelProperty(value = "自动落章")
  private Boolean autoFall;

  @ApiModelProperty(value = "落章方式，0-手动落章，1-免意愿且自动落章，2-免意愿且手动落章， 默认手动落章")
  private Integer fallType;

  @ApiModelProperty(value = "授权范围(模板id/ALL/HR/FINANCE)")
  private String scope;

  @ApiModelProperty(value = "授权范围名称")
  private String scopeName;

  @ApiModelProperty(value = "通知配值(默认开启)")
  private Boolean notifySetting;

  @ApiModelProperty(value = "授权人")
  private String granter;

  @ApiModelProperty(value = "被授权对象")
  private String grantedUser;

  @ApiModelProperty(value = "被授权对象名称")
  private String grantedUserName;

  @ApiModelProperty(value = "生效时间")
  private Long effectiveTime;

  @ApiModelProperty(value = "失效时间")
  private Long expireTime;

  @ApiModelProperty(value = "失效描述")
  private String expireDesc;

  @ApiModelProperty(value = "状态")
  private String status;

  @ApiModelProperty(value = "状态描述")
  private String statusDesc;

  private Long createTime;

  @ApiModelProperty(value = "级别")
  private Integer level;

  @ApiModelProperty(value = "企业证件号")
  private String grantedUserCode;

  @ApiModelProperty(value = "授权类型")
  private Integer grantType;

  @ApiModelProperty(value = "授权文件fileKey")
  private String fileKey;

  @ApiModelProperty(value = "授权文件名")
  private String fileName;

  @ApiModelProperty(value = "文件下载地址")
  private String downloadUrl;
}
