package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/14 10:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrganOutput extends ToString {

    /**
     * 注意: 这个字段实际包含了证件号是否存在以及名称是否和证件号匹配的逻辑了
     * 跟命名有差异，但由于前端已经在用了，为了减少风险，这个字段名就不改了
     * 前端使用规则：
     * 当exist为1且ouid不为空时 算做匹配
     * 当exist为0时 如果orgCodeExist为false则认为证件号不存在或未实名
     * 当exist为0时 如果orgCodeExist为ture则认为证件号和姓名不匹配
     */
    @ApiModelProperty("证件号存在且名称一致时返回1")
    @Deprecated
    private Integer exist;

    @ApiModelProperty("企业oid")
    private String ouid;

    @ApiModelProperty("证件号是否存在")
    private boolean orgCodeExist;
}
