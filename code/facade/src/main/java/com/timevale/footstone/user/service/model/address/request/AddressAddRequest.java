/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：app-parent <br>
 * 文件名：AddressAddRequest.java <br>
 * 包：com.timevale.account.webserver.model.request.address <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/2323:20]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.address.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 类名：AddressAddRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/2323:20]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AddressAddRequest extends ToString {

    @ApiModelProperty(value = "收货人")
    @NotBlank(message = "收货人不能为空")
    @Length(max = 50, message = "收货人长度不能超过50")
    private String contact;

    @ApiModelProperty(value = "联系方式")
    @NotBlank(message = "联系方式不能为空")
    @Length(max = 20, message = "联系方式长度不能超过20")
    private String contactNumber;

    @ApiModelProperty(value = "省份代码")
    @NotBlank(message = "省份不能为空")
    private String province;

    @ApiModelProperty(value = "市代码")
    @NotBlank(message = "市不能为空")
    private String city;

    @ApiModelProperty(value = "区代码")
    @NotBlank(message = "区不能为空")
    private String area;

    @ApiModelProperty(value = "收货地址详情")
    @NotBlank(message = "收货地址详情不能为空")
    @Length(max = 100, message = "收货地址详情长度不能超过100")
    private String addressDetail;

    @ApiModelProperty(value = "是否设为默认地址")
    private boolean defaultFlag;
}
