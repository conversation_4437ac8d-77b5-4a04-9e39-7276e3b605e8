package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.OrgModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class ErrorBatchOrgBaseResponse extends BatchOrgBaseResponse {
    @ApiModelProperty(value = "失败原因", required = true)
    private String failedReason;

    public ErrorBatchOrgBaseResponse(String orgId, String creator, OrgModel orgInfo) {
        super(orgId, creator, orgInfo);
    }

    public ErrorBatchOrgBaseResponse(String orgId, String creator, OrgModel orgInfo, String failedReason) {
        super(orgId, creator, orgInfo);
        this.failedReason = failedReason;
    }
}
