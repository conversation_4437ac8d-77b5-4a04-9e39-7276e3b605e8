package com.timevale.footstone.user.service.model.dept.request.v3;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/4/3 21:29
 */
@Data
public class CreateDeptUnitsRequest {

  @ApiModelProperty(value = "分支机构列表", required = true)
  @NotEmpty(message = "分支机构列表不能为空")
  private List<String> branchOrgOids;

  @ApiModelProperty(value = "要添加下属机构的目标部门", required = true)
  @NotEmpty(message = "目标部门id不能为空")
  private String targetDeptId;

}
