package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
@AllArgsConstructor
public class AccountPreDeleteResponse {

    @ApiModelProperty(value = "服务Id")
    private String serviceId;

    @ApiModelProperty(value = "跳转地址")
    private String redirectUrl;
}
