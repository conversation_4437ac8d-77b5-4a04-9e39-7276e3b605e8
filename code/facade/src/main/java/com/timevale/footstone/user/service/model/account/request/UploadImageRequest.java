package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-21
 */
@Data
public class UploadImageRequest extends ToString {

    @ApiModelProperty(value = "文件的唯一标识filekey", required = true)
    @NotBlank(message = "文件不能为空")
    private String fileKey;
}
