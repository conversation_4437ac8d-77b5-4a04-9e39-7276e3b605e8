package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025/4/14 21:08
 */
@ApiModel
@Data
public  class ApplyBizOrgAgentInfo extends ToString {


    @ApiModelProperty(value = "经办人账号ID")
    @NotBlank
    private String agentId;


    @ApiModelProperty(value = "经办人姓名")
    private String agentName;

    @ApiModelProperty(value = "经办人证件号")
    private String agentCertNo;

  /**
   * 经办人证件类型
   *
   * <p>【中国大陆居民身份证】CRED_PSN_CH_IDCARD
   * <p>【香港来往大陆通行证】CRED_PSN_CH_HONGKONG
   * <p>【澳门来往大陆通行证】CRED_PSN_CH_MACAO
   * <p>【台湾来往大陆通行证】CRED_PSN_CH_TWCARD
   * <p>【护照】 CRED_PSN_PASSPORT
   * <p>【其他】 CRED_PSN_UNKNOWN
   *
   * @see com.timevale.account.service.enums.BuiltinCredentialsEnum.code()
   */
  @ApiModelProperty(value = "经办人证件类型")
  private String agentCertType;
}
