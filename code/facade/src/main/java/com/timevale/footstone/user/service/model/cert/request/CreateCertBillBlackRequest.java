package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 类名：CreateCertBillBlackRequest
 * 功能说明：
 *
 * <AUTHOR>
 * @DATE 2023/9/12 13:40
 */
@Data
public class CreateCertBillBlackRequest extends ToString {
    @NotEmpty(message = "证件号码不能为空")
    @ApiModelProperty(required = true, value = "证件号码")
    private String IDCardNum;


}
