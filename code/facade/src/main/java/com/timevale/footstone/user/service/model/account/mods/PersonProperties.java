package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.service.biz.acc.output.BizAccountTOutput;
import com.timevale.account.service.model.service.biz.builder.acc.impl.abs.AbstractBizAccountBuilder;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.account.service.model.constants.BuiltinProperty.*;

/** <AUTHOR> on 2019-01-12 */
@Data
public class PersonProperties implements AccountPropConv {

    @ModelFieldDecoration(DecorationType.PERSON_NAME)
    @ApiModelProperty(value = "个人名称")
    private String name;

    @ApiModelProperty(value = "头像")
    private String head;

    @ModelFieldDecoration(DecorationType.MOBILE)
    @ApiModelProperty(value = "实名手机号")
    private String realnameMobile;

    @ModelFieldDecoration(DecorationType.CARD)
    @ApiModelProperty(value = "银行卡号")
    private String bankNum;

    @ApiModelProperty(value = "签署密码是否存在")
    private Boolean signPwd;

    @ApiModelProperty(value = "实名方式")
    private String realnameContext;

    @ApiModelProperty(value = "商品标签(基础产品库配置属性)")
    private String userConfig;

    @ApiModelProperty(
            value = "账号来源端信息，http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String clentId;

    @ApiModelProperty(value = "登录二次验证是否开启")
    private Boolean loginDoubleCheck;

    @ApiModelProperty(value = "异地登录提醒是否开启")
    private Boolean anotherPlaceWarn;

    @Override
    public void conv(AbstractBizAccountBuilder builder) {
        // 省略签署密码、头像、商品标签
        builder.buildProperties()
                .add(INFO_NAME, getName())
                .add(INFO_PSN_REALNAME_MOBILE, getRealnameMobile())
                .add(INFO_PSN_REALNAME_CONTEXT, getRealnameContext())
                .add(INFO_SRC_CLIENT_ID, getClentId())
                .add(INFO_BANK_NUM, getBankNum());
    }

    @Override
    public List<Property> conv() {
        // 省略签署密码、头像、商品标签、账号来源端信息
        List<Property> list = new ArrayList<>();
        list.add(new Property(INFO_NAME, getName()));
        list.add(new Property(INFO_PSN_REALNAME_MOBILE, getRealnameMobile()));
        list.add(new Property(INFO_BANK_NUM, getBankNum()));
        list.add(new Property(INFO_PSN_REALNAME_CONTEXT, getRealnameContext()));
        return list;
    }

    @Override
    public PersonProperties init(BizAccountTOutput<ContentSecurity> icuser) {
        Map<String, String> propGroup =
                icuser.getProperties().stream()
                        .filter(p -> p.getValue() != null)
                        .collect(
                                Collectors.toMap(
                                        Property::getType, Property::getValue, (k1, k2) -> k1));

        setName(propGroup.get(INFO_NAME));
        setHead(propGroup.get(INFO_HEAD));
        setRealnameMobile(propGroup.get(INFO_PSN_REALNAME_MOBILE));
        setBankNum(propGroup.get(INFO_BANK_NUM));
        setSignPwd(StringUtils.isNotBlank(propGroup.get(INFO_PWD_SIGN)));
        setUserConfig(propGroup.get(INFO_USER_CONFIG));
        setRealnameContext(propGroup.get(INFO_PSN_REALNAME_CONTEXT));
        setClentId(propGroup.get(INFO_SRC_CLIENT_ID));
        setLoginDoubleCheck(StringUtils.isNotBlank(propGroup.get(INFO_LOGIN_DOUBLE_CHECK)));
        setAnotherPlaceWarn(StringUtils.isNotBlank(propGroup.get(INFO_ANOTHER_PLACE_WARN)));
        return this;
    }
}
