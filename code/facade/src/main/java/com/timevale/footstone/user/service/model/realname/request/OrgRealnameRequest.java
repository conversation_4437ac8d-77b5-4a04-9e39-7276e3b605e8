package com.timevale.footstone.user.service.model.realname.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** <AUTHOR> on 2019-01-07 */
@Data
public class OrgRealnameRequest extends AuthConfigParamRequest{

    @ApiModelProperty("企业代理人，默认当前登录账号")
    private String agentAccountId;

    @ApiModelProperty("实名经办人名字")
    private String agentName;

    @ApiModelProperty("实名经办人证件号")
    private String agentIdNo;

    @ApiModelProperty("实名经办人手机号")
    private String agentMobile;

    @ApiModelProperty(value = "实名后跳转地址", required = true)
    private String redirectUrl;

    @ApiModelProperty(value = "实名后通知地址")
    private String notifyUrl;

    @ApiModelProperty(value = "是否显示实名结果页面,默认true")
    private Boolean showResultPage;

    @ApiModelProperty(value = "业务上下文ID")
    private String contextId;

    @ApiModelProperty(value = "业务APP-ID")
    private String bizAppId;

    @ApiModelProperty(value = "业务上下文")
    private Map<String, String> context;

    @ApiModelProperty("是否重复实名")
    private Boolean repeatIdentity;

    @ApiModelProperty("企业信息中允许编辑的属性")
    private List<String> orgEditableInfo;

    @ApiModelProperty("组织机构类型")
    private Integer organizationType;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "证件号")
    private String certNo;

    @ApiModelProperty(value = "证件类型")
    private String certType;

    @ApiModelProperty(value = "法定代表人名字")
    private String legalRepName;

    @ApiModelProperty(value = "法定代表人证件类型")
    private String legalRepCertType;

    @ApiModelProperty(value = "法定代表人证件号码")
    private String legalRepCertNo;

    public Map<String, String> getContext() {
        if (this.context == null) {
            this.context = new HashMap<>();
        }
        return this.context;
    }
}
