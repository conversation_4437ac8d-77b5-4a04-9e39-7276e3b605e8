package com.timevale.footstone.user.service.model.account.response.v3;

import com.timevale.footstone.user.service.enums.CustomBizAuthFlowStatueEnum;
import com.timevale.footstone.user.service.enums.CustomBizSubjectTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务授权响应参数
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RpcQueryBizAuthBaseResponse extends ToString {

    private String appId;
    /**
     * 授权主体类型
     * @see CustomBizSubjectTypeEnum.name()
     * */
    private String bizAuthSubjectType;

    /** 申请业务方ID */
    private String customBizNum;

    /** 申请业务方场景 */
    private String customBizScene;


    /** 授权流程ID */
    private String bizAuthFlowId;

    /**
     *
     * 授权流程状态
     * @see CustomBizAuthFlowStatueEnum.code()
     * */
    private String bizAuthFlowStatus;


    /** 授权完成时间 */
    private long bizAuthFinishTime;

    /** 授权申请时间 */
    private long bizAuthApplyTime;


    @ApiModelProperty(value = "授权认证信息")
    private BizAuthIdentityContext authIdentityContext;


}