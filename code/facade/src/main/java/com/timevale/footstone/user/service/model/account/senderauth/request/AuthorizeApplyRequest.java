package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-03-20 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuthorizeApplyRequest extends ToString {

    @ApiModelProperty(value = "业务类型，与preServiceId二选一。设置第三方登录凭证：SET_THIRDPARTY_IDCARD")
    private String bizType;

    @ApiModelProperty(value = "上次流程事务id，与bizType二选一")
    private String preServiceId;

    @ApiModelProperty(value = "第三方的授权凭证", required = true)
    private String code;

    @ApiModelProperty(value = "第三方类型，微信小程序：WE_CHAT_MINI", required = true)
    private String authorizeType;
}
