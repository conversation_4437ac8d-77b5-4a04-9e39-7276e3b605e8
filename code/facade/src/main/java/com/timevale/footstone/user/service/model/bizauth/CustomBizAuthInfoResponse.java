package com.timevale.footstone.user.service.model.bizauth;

import com.timevale.footstone.user.service.enums.CustomBizAuthFlowStatueEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 企业授权记录
 *
 * <AUTHOR>
 * @since 2023/4/25 10:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomBizAuthInfoResponse extends ToString {

  /**
   * @see  CustomBizAuthFlowStatueEnum.code()
   */
  @ApiModelProperty("当前授权状态:FINISH=已经完成;ING= 进行中;EXPIRED= 过期")
  private String bizAuthFlowStatus;

  /**
   * @see com.timevale.footstone.user.service.enums.CustomBizSubjectTypeEnum
   */
  @ApiModelProperty(required = true, value = "流程主体类型:ORG=企业;PERSON=个人")
  private String bizAuthFlowSubjectType;

  @ApiModelProperty(
      required = true,
      value =
          "流程步骤，bizAuthFlowStatus = ING 才有\n"
              + "\n"
              + "AGENT_APPLY_ROLE=办理人向管理员申请角色\n"
              + "\n"
              + "AGENT_APPLY_LETTER_AUTH=办理人申请授权书\n"
              + "\n"
              + "ADMIN_ACCESS_FORBIDDEN= 非管理员访问\n"
              + "\n"
              + "ADMIN_ACCESS= 管理员访问")
  private String flowStep;

  @ApiModelProperty(required = true, value = "授权主体名称（企业名称）")
  private String authName;

  @ApiModelProperty(required = true, value = "授权发起人姓名")
  private String authorizerName;

  @ApiModelProperty(
      required = true,
      value =
          "补充信息\n"
              + "\n"
              + "adminName=管理员名称 （脱敏）\n"
              + "\n"
              + "adminContact=管理员联系方式 （脱敏）\n"
              + "\n"
              + "applyRole=申请角色 （\n"
              + "\n"
              + "TIANYIN-天印证书业务员\n"
              + "\n"
              + "EQIANDUN-e签盾业务员\n"
              + "\n"
              + "YUNPINGTAI-云平台业务员\n"
              + "\n"
              + "）")
  private Map<String, Object> properties;
}
