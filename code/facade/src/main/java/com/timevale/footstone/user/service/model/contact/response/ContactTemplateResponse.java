/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：ContactTemplateResponse.java <br>
 * 包：com.timevale.footstone.user.service.model.contact.response <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:15]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.contact.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：ContactTemplateResponse.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:15]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class ContactTemplateResponse {

    @ApiModelProperty(value = "模板下载地址")
    private String url;
}
