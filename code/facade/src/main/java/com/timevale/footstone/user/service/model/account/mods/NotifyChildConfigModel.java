package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> on 2019-01-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifyChildConfigModel extends NotifyChildConfigBaseModel{



    @ApiModelProperty(value = "通知场景描述")
    private String desc;


}
