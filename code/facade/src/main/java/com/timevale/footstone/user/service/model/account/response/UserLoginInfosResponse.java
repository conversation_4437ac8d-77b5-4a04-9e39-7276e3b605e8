package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.PersonMultiIdcardCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserLoginInfosResponse {

    @ApiModelProperty("上次登陆时间")
    private Date loginTime;

    @ApiModelProperty("登录凭证")
    private PersonMultiIdcardCollection idcard;

    @ApiModelProperty("是否有密码")
    private Boolean passwordExist;
}
