/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealImageModel.java <br>
 * 包：com.timevale.footstone.user.service.model.seal.model <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/711:27]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：SealImageModel.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/711:27]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealImageModel extends SealCommonModel {

    @ApiModelProperty(value = "印章数据", required = true)
    private String data;

    @ApiModelProperty(value = "印章数据类型，BASE64-base64格式，FILEKEY-文件filekey", required = true)
    private String type;

    @ApiModelProperty(value = "印章是否做透明处理，默认false")
    private boolean transparentFlag = false;
}
