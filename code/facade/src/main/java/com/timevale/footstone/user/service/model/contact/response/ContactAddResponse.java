/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：ContactAddResponse.java <br>
 * 包：com.timevale.footstone.user.service.model.contact.response <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/1923:43]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.contact.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：ContactAddResponse.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/1923:43]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class ContactAddResponse {

    @ApiModelProperty(value = "联系人id")
    private String contactId;
}
