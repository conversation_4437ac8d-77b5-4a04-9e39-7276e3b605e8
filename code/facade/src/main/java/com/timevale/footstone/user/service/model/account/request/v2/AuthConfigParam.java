package com.timevale.footstone.user.service.model.account.request.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ Id: AuthConfigParam.java, v0.1 2021年08月25日 08:09 WangYuWu $
 */
@Data
public class AuthConfigParam {

    @ApiModelProperty("本次认证默认使用的认证类型")
    private String          authType;


    @ApiModelProperty("本次认证可使用的认证类型")
    private List<String>    availableAuthTypes;


    @ApiModelProperty("个人信息中允许编辑的属性")
    private List<String>    indivEditableInfo;


    @ApiModelProperty("企业信息中允许编辑的属性")
    private List<String>    orgEditableInfo;


    @ApiModelProperty("实名完成是否显示结果页,默认显示")
    private Boolean         showResultPage;

    @ApiModelProperty("是否强制实名")
    private Boolean forceRealname;
}
