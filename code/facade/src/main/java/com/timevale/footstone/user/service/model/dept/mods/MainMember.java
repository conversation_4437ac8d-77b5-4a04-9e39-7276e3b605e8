package com.timevale.footstone.user.service.model.dept.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/26
 */
@Data
public class MainMember extends ToString {

    @ApiModelProperty(value = "成员账号id")
    private String memberId;

    @ApiModelProperty(value = "成员名称")
    private String memberName;

    @ApiModelProperty(value = "成员真实姓名")
    private String trueName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;
}
