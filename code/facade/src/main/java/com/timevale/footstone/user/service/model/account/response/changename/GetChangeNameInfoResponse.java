package com.timevale.footstone.user.service.model.account.response.changename;

import com.timevale.footstone.user.service.model.account.mods.changename.ChangeNameUrlContextInfo;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/6/20 11:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetChangeNameInfoResponse extends ToString {
    /**
     * 期望的企业名称
     */
    @ApiModelProperty(value = "期望的企业名称")
    private String expectOrgName;

    @ApiModelProperty(value = "企业证件号")
    private String orgCertNo;

    @ApiModelProperty(value = "实际存在的企业名称")
    private String existOrganName;

    @ApiModelProperty(value = "更名记录状态")
    private String status;

    @ApiModelProperty(value = "更名流程配置信息")
    private ChangeNameUrlContextInfo changeNameUrlContextInfo;

    /**
     * 扩展字段 用于以后透传使用
     */
    @ApiModelProperty(value = "扩展字段 用于以后透传使用")
    private Map<String, String> extendMap;


}
