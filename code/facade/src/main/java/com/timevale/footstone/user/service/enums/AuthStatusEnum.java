package com.timevale.footstone.user.service.enums;

/**
 * <AUTHOR>
 * @since 2020-11-04 14:17
 */
public enum AuthStatusEnum {

    /** 未授权 */
    NOT_AUTHORIZE(1, "未申请授权"),

    /** 授权中 */
    AUTHORIZING(2, "已申请授权,管理员审批中"),

    /** 已授权 */
    AUTHORIZED(3, "已授权");

    private int code;
    private String desc;

    AuthStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
