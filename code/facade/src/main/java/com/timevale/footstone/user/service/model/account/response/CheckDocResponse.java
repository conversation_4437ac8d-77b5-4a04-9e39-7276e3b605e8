package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/25
 */
@Data
public class CheckDocResponse {

    @ApiModelProperty("企业账号id")
    private String orgId;

    @ApiModelProperty("企业名称")
    private String orgName;

    @ApiModelProperty("文件类型 5-待我处理 6-待他人处理")
    private Integer docType;

    @ApiModelProperty("文件个数")
    private Long count;
}
