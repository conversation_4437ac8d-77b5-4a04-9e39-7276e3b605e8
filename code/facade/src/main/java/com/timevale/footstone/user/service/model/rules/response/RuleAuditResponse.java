package com.timevale.footstone.user.service.model.rules.response;

import com.timevale.footstone.user.service.model.rules.model.RuleAuditModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/15 2:53 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleAuditResponse {

    @ApiModelProperty(value = "查询总数")
    private long total;

    @ApiModelProperty(value = "审核列表")
    private List<RuleAuditModel> list;
}
