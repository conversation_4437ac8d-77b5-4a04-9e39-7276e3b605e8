package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户的基本信息
 */
@Data
public class UserSortedInfoResponse {

    @ApiModelProperty(value = "用户oid")
    String ouid;

    @ApiModelProperty(value = "用户gid")
    String guid;

    @ApiModelProperty(value = "用户姓名")
    private String name;

    @ApiModelProperty(value = "用户手机号")
    private String mobile;

    @ApiModelProperty(value = "用户邮箱")
    private String email;

    @ApiModelProperty(value = "实名状态")
    private Boolean realNamed;

}
