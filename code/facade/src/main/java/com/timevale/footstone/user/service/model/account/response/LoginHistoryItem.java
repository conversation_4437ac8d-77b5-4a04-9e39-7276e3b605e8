package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年08月03日 13:56:00
 */
@Data
public class LoginHistoryItem extends ToString {
    @ApiModelProperty(value = "ip", required = true)
    private String ip;

    @ApiModelProperty(value = "登录地", required = true)
    private String addr;

    @ApiModelProperty(value = "登录时间", required = true)
    private Long time;

    @ApiModelProperty(value = "登录类型", required = true)
    private String loginType;

    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @ApiModelProperty(value = "登录结果,0登录失败，1登录成功")
    private Integer result;

    @ApiModelProperty(value = "登录失败原因")
    private String reason;


}
