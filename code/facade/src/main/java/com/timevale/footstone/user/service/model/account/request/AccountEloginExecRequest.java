package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountEloginExecRequest extends ToString {

    @ApiModelProperty(value = "密文", required = true)
    private String encryptContent;

    @ApiModelProperty(value = "登录类型")
    private String type;

    public void valid() {
        AssertSupport.assertNotnull(
                encryptContent, new ErrorsBase.MissingArgumentsWith("encryptContent"));
    }
}
