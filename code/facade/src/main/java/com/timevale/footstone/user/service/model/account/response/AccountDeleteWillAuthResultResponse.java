package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
@AllArgsConstructor
public class AccountDeleteWillAuthResultResponse {


    @ApiModelProperty(value = "意愿结果状态, 0:进行中 1:成功 2:失败")
    private Integer status;

}
