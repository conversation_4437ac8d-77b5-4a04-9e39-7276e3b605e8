package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.footstone.user.service.enums.AccountDeleteStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
@AllArgsConstructor
public class OrganDeleteResultResponse {


    @ApiModelProperty(value = "账号删除状态, PRE:预注销 SUCCESS:成功 FAIL:失败", required = true)
    private String deleteStatus;

}
