package com.timevale.footstone.user.service.model.thirdlogin.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;


@Data
public class ThirdLoginRightsCheckRequest extends ToString {

    /**
     * 三方租户凭证
     */
    private String tenantKey;

    /**
     * 三方平台
     */
    private String platform;

    /**
     * 租户ouid
     */
    private String tenantOuid;


    private boolean conditionAdd;
}
