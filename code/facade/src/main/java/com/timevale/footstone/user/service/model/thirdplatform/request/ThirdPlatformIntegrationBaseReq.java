package com.timevale.footstone.user.service.model.thirdplatform.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ThirdPlatformIntegrationBaseReq extends ToString {

    @ApiModelProperty("企业oid")
    private String ouid;

    @ApiModelProperty("三方平台")
    @NotBlank(message = "三方平台不能为空")
    private String clientId;
}
