package com.timevale.footstone.user.service.model.realname.request;

import lombok.Data;

import java.util.Map;

/** <AUTHOR> on 2019-06-11 */
@Data
public class RealnameCallBackRequest {

    /** 个人实名完成通知的账号id */
    private String accountId;

    /** 企业实名完成通知的企业id */
    private String orgAccountId;

    /** 企业实名完成通知的经办人id */
    private String agentAccountId;

    private String verifycode;

    private String contextId;

    private String serviceId;

    private Boolean status;

    private Map<String, String> context;
}
