package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.framework.tedis.util.TedisUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 使用免登时，用户传的第三方信息
 *
 * <AUTHOR> on 2019-12-12
 */
@Data
@Slf4j
public class EloginInfoModel {

    /** 第三方userId */
    private String userId;

    /** 发起时间，单位s */
    private Long startTime;

    /** 有效时长，单位s */
    private Long timeout;

    /** 发起免登的appId，自己记录 */
    private String appId;

    /** 解决方案流程id，自己记录 */
    private String pid;

    /** 最大有效时长，单位s，自己记录 */
    private Long maxTimeout;

    /** 重定向地址，用于完成免登绑定后，跳回用户页面，自己记录 */
    private String redirectUrl;

    /** 登录类型，由调用方传，后续提供查询接口，仅作记录 */
    private String loginType;

    public void valid() {
        AssertSupport.assertNotnull(getUserId(), new ErrorsBase.MissingArgumentsWith("userId"));

        AssertSupport.assertNotnull(
                getStartTime(), new ErrorsBase.MissingArgumentsWith("startTime"));
        AssertSupport.assertTrue(getStartTime() > 0, new ErrorsBase.InvalidFormat("startTime"));

        AssertSupport.assertNotnull(getTimeout(), new ErrorsBase.MissingArgumentsWith("timeout"));
        AssertSupport.assertTrue(getTimeout() > 0, new ErrorsBase.InvalidFormat("timeout"));
    }

    public String generateAndSave() {
        String id = UUID.randomUUID().toString();
        TedisUtil.set(id, this, getMaxTimeout(), TimeUnit.SECONDS);
        return id;
    }
}
