package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-25
 */
@Data
public class GetByIdcardRequest extends ToString {

    @ApiModelProperty(value = "账号", required = true)
    @NotEmpty
    private String idcard;

    @ApiModelProperty(value = "账号类型[MOBILE, EMAIL]", required = true)
    @NotEmpty
    private String type;
}
