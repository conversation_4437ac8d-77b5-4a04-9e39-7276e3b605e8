package com.timevale.footstone.user.service.model.account.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账号身份信息解绑页面响应参数
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountUnbindUrlResponse extends ToString {

    @ApiModelProperty(value = "账号解绑url长链")
    private String accountUnbindUrl;

    @ApiModelProperty(value = "账号解绑url短链")
    private String accountUnbindShortUrl;
}