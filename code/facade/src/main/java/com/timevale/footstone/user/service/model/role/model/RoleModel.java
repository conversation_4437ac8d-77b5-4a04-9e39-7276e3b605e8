package com.timevale.footstone.user.service.model.role.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class RoleModel extends ToString {

    @ApiModelProperty(value = "角色ID")
    private String id;

    @ApiModelProperty(value = "内置角色KEY")
    private String roleKey;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色拥有者")
    private RoleUser roleOwner;

    @ApiModelProperty(value = "角色描述")
    private String disc;

    @ApiModelProperty(value = "父角色ID")
    private String parentRoleId;

    @ApiModelProperty(value = "角色拥有者")
    private String organId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建来源（1：默认；2：同步自上级）")
    private Integer createSource;

    @ApiModelProperty(value = "角色是否禁用")
    private boolean freeze;


    @ApiModelProperty(value = "角色是否隔离")
    private boolean enableIsolation;

}
