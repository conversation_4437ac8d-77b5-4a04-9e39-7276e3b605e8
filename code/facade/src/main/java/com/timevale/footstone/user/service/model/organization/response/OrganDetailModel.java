package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.footstone.user.service.model.account.mods.OrgPropCollection;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganDetailModel extends ToString {

    @ApiModelProperty(value = "组织oid")
    private String ouid;

    @ApiModelProperty(value = "组织gid")
    private String guid;
    
    @ApiModelProperty(
            value =
                    "实名状态[ACCEPT(获取已实名组织)|INIT(没有进行过实名)|DEPRECATE(重新实名)|COMMIT(实名提交过未完成)|REJECT(实名失败)]")
    private String realNameStatus;

    @ApiModelProperty("属性")
    private OrgPropCollection prop;

    @ApiModelProperty("实名组织激活态")
    private Integer activate;

    @ApiModelProperty("appId")
    private String appId;
}
