package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> on 2019-01-10
 */
@Data
public class IdcardThirdpartyDetail {

    @ApiModelProperty(value = "第三方平台，如ALI_PAY、WE_CHAT、DING_TALK", required = true)
    private String thirdpartyKey;

    @ApiModelProperty(value = "第三方用户id", required = true)
    private String thirdpartyUserId;

    @ApiModelProperty(value = "第三方用户类型，默认值_DEFAULT_USER")
    private String thirdpartyUserType;
}
