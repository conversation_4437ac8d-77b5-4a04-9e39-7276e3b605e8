package com.timevale.footstone.user.service.model.thirdparty.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ThirdPartyDeptBaseResponse extends ToString {

    private String deptId;

    private String deptParentId;

    private String deptName;
}
