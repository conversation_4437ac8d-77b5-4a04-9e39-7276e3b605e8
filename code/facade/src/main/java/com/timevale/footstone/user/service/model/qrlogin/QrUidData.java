package com.timevale.footstone.user.service.model.qrlogin;

import com.timevale.footstone.user.service.model.enums.QrUidStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/12/11
 */
@Data
@Accessors(chain = true)
public class QrUidData implements Serializable {

    private static final long serialVersionUID = 1318719487967213144L;

    private String id;

    private String userId;

    private QrUidStatusEnum status = QrUidStatusEnum.WATING;

    private String owner;

    private String url;

    private Long expired;

    public boolean checkIsValid(){
        return System.currentTimeMillis() < expired;
    }

}
