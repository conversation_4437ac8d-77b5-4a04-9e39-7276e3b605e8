package com.timevale.footstone.user.service.model.third.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取企业和个人在三方平台的信息 响应类
 *
 * <AUTHOR>
 * @since 2025/3/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetTenantAndUserInfoResponse extends ToString {
    /**
     * 三方平台用户id （钉钉平台是unionId）
     */
    private String userId;
    /**
     * 用户在三方平台的昵称（并不一定是真实姓名）
     */
    private String userNickName;
    /**
     * 头像 http链接
     */
    private String userAvatar;
    /**
     * 三方平台企业名称
     */
    private String tenantName;
    /**
     * 是否主管理员
     */
    private Boolean isMasterAdmin;
    /**
     * 是否子管理员
     */
    private Boolean isSubAdmin;
}
