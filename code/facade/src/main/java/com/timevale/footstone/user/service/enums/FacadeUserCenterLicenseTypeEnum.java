/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：cert-parent <br/>
 * 包：com.timevale.cert.common.service.intergration.usercenter <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2018/10/915:06]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.enums;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.openca.common.service.enums.LicenseType;

/**
 * 类名：${FILE_NAME} <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2018/10/915:06]创建类 by flh
 * <AUTHOR>
 */
public enum FacadeUserCenterLicenseTypeEnum {

    CRED_PSN_CH_IDCARD(BuiltinCredentials.CRED_PSN_CH_IDCARD, LicenseType.NEW_IDNO),
    CRED_PSN_CH_TWCARD(BuiltinCredentials.CRED_PSN_CH_TWCARD, LicenseType.NEW_TAIWAN_RESIDENTS_PERMIT),
    CRED_PSN_CH_MACAO(BuiltinCredentials.CRED_PSN_CH_MACAO, LicenseType.NEW_HONGKONG_MACAO_RESIDENTS_PERMIT),
    CRED_PSN_CH_HONGKONG(BuiltinCredentials.CRED_PSN_CH_HONGKONG,LicenseType.NEW_HONGKONG_MACAO_RESIDENTS_PERMIT),
    CRED_PSN_FOREIGN(BuiltinCredentials.CRED_PSN_FOREIGN,LicenseType.NEW_FOREIGNER_PERMANENT_RESIDENCE_PERMIT),
    CRED_PSN_PASSPORT(BuiltinCredentials.CRED_PSN_PASSPORT,LicenseType.NEW_PASSPORT),
    CRED_PSN_CH_SOLDIER_IDCARD(BuiltinCredentials.CRED_PSN_CH_SOLDIER_IDCARD,LicenseType.NEW_SOLDIER_IDNO),
    CRED_PSN_CH_SSCARD(BuiltinCredentials.CRED_PSN_CH_SSCARD,LicenseType.NEW_SOCIAL_SECURITY_CARD),
    CRED_PSN_CH_ARMED_POLICE_IDCARD(BuiltinCredentials.CRED_PSN_CH_ARMED_POLICE_IDCARD,LicenseType.NEW_ARMED_POLICE_ID),
    CRED_PSN_CH_RESIDENCE_BOOKLET(BuiltinCredentials.CRED_PSN_CH_RESIDENCE_BOOKLET,LicenseType.NEW_RESIDENCE_BOOKLET),
    CRED_PSN_CH_TEMPORARY_IDCARD(BuiltinCredentials.CRED_PSN_CH_TEMPORARY_IDCARD,LicenseType.NEW_TEMPORARY_IDNO),
    CRED_PSN_CH_GREEN_CARD(BuiltinCredentials.CRED_PSN_CH_GREEN_CARD,LicenseType.NEW_FOREIGNER_PERMANENT_RESIDENCE_PERMIT),
    CRED_PSN_SHAREHOLDER_CODE(BuiltinCredentials.CRED_PSN_SHAREHOLDER_CODE,LicenseType.SHAREHOLDER_CODE_CERTTIFICATE),
    CRED_PSN_POLICE_ID_CARD(BuiltinCredentials.CRED_PSN_POLICE_ID_CARD,LicenseType.POLICE_ID_CARD),
    CRED_PSN_UNKNOWN(BuiltinCredentials.CRED_PSN_UNKNOWN,LicenseType.NEW_OTHER),
    CRED_ORG_CODE(BuiltinCredentials.CRED_ORG_CODE,LicenseType.NEW_ORGCODE),
    CRED_ORG_USCC(BuiltinCredentials.CRED_ORG_USCC,LicenseType.NEW_UNITEDCODE),
    CRED_ORG_REGCODE(BuiltinCredentials.CRED_ORG_REGCODE,LicenseType.NEW_REGCODE),
    CRED_ORG_BUSINESS_REGISTTATION_CODE(BuiltinCredentials.CRED_ORG_BUSINESS_REGISTTATION_CODE,LicenseType.BUSINESS_REGISTTATION_CERTTIFICATE),
    CRED_ORG_TAX_REGISTTATION_CODE(BuiltinCredentials.CRED_ORG_TAX_REGISTTATION_CODE,LicenseType.TAX_REGISTTATION_CERTTIFICATE),
    CRED_ORG_LEGAL_PERSON_CODE(BuiltinCredentials.CRED_ORG_LEGAL_PERSON_CODE,LicenseType.LEGAL_PERSON_CODE_CERTTIFICATE),
    CRED_ORG_ENT_LEGAL_PERSON_CODE(BuiltinCredentials.CRED_ORG_ENT_LEGAL_PERSON_CODE,LicenseType.ENTERPRISE_LEGAL_PERSON_CERTTIFICATE),
    CRED_ORG_SOCIAL_REG_CODE(BuiltinCredentials.CRED_ORG_SOCIAL_REG_CODE,LicenseType.SOCIAL_ORGANIZATION_REGISTTATION_CERTTIFICATE),
    CRED_ORG_PRIVATE_NON_ENT_REG_CODE(BuiltinCredentials.CRED_ORG_PRIVATE_NON_ENT_REG_CODE,LicenseType.PRIVATE_NON_ENTERPRISE_REGISTTATION_CERTTIFICATE),
    CRED_ORG_FOREIGN_ENT_REG_CODE(BuiltinCredentials.CRED_ORG_FOREIGN_ENT_REG_CODE,LicenseType.REGISTTATION_CERTTIFICATE_OF_RESIDENT_REPRESENTATIVE_OFFICES_OF_FOREIGN_ENTERPRISE),
    CRED_ORG_GOV_APPROVAL(BuiltinCredentials.CRED_ORG_GOV_APPROVAL,LicenseType.THE_GOVERNMENT_APPROVAL),


    ;
    private final LicenseType licenseType;
    private final String credentials;
    FacadeUserCenterLicenseTypeEnum(String credentials, LicenseType licenseType){
        this.credentials = credentials;
        this.licenseType = licenseType;
    }

    public static LicenseType from(String credentials){
        for(FacadeUserCenterLicenseTypeEnum licenseType : values()){
            if(licenseType.getCredentials().equalsIgnoreCase(credentials)){
                return licenseType.getLicenseType();
            }
        }
        return null;
    }

    public static FacadeUserCenterLicenseTypeEnum from(Integer licenseType){
        for (FacadeUserCenterLicenseTypeEnum userType : values()){
            if(userType.getLicenseType().type().equals(licenseType)){
                return userType;
            }
        }
        return null;
    }

    public LicenseType getLicenseType() {
        return licenseType;
    }

    public String getCredentials() {
        return credentials;
    }
}
