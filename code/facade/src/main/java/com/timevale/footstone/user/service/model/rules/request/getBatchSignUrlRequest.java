package com.timevale.footstone.user.service.model.rules.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 获取批量签署地址入参
 * @author: jinhuan
 * @since: 2020-02-12 20:30
 **/
@Data
public class getBatchSignUrlRequest extends ToString {

    @NotEmpty(message = "流程id列表不能为空")
    @ApiModelProperty(value = "流程id列表", required = true)
    List<String> flowIds;

}
