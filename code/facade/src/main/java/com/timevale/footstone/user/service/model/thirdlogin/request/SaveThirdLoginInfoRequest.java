package com.timevale.footstone.user.service.model.thirdlogin.request;

import com.timevale.footstone.user.service.model.thirdlogin.mods.ThirdLoginThirdInfoDTO;
import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-05-09 保存用户三方登录信息请求
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SaveThirdLoginInfoRequest extends ToString {
    /** 三方登陆的三方信息 */
    private ThirdLoginThirdInfoDTO thirdInfo;
}
