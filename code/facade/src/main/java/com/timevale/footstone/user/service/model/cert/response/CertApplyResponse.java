/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyResponse.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.response <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/4/1119:36]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：CertApplyResponse.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/4/1119:36]创建类 by flh
 * <AUTHOR>
 */
@Data
public class CertApplyResponse {

    @ApiModelProperty(value = "证书id")
    private String certId;

    @ApiModelProperty(value = "申领成功的证书通道名称")
    private String caRoute;

    @ApiModelProperty(value = "证书有效期开始时间")
    private long startDate;

    @ApiModelProperty(value = "证书有效期结束时间")
    private long endDate;
}