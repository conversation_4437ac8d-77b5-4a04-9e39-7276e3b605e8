package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: sicheng
 * @since: 2020-06-16 19:32
 **/
@Data
public class SafeWarnsHistoryResponse extends ToString {

    @ApiModelProperty(value = "总数", required = true)
    private Integer total;

    @ApiModelProperty(value = "告警详情", required = true)
    private List<SafeWarnHistoryLoginInfoResponse> loginInfo;
}
