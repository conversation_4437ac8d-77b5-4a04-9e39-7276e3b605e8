package com.timevale.footstone.user.service.model.cert.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

@Data
public class CertUpdateResponse {
    private String certId;
    @ApiModelProperty(value = "签名证书base64")
    private String signCert;

    @ApiModelProperty(value = "加密证书base64")
    private String encCert;
    //    private String privateKey;
    @ApiModelProperty(value = "扩展信息")
    private String ext;
    // private int certInfoId;
    @ApiModelProperty(value = "ca通道")
    public String caRoute;
}
