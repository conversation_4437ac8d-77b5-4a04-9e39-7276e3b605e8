package com.timevale.footstone.user.service.model.account.idcard.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-02-14 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SetEmailRequest extends ToString {

    @ApiModelProperty(value = "邮箱", required = true)
    private String email;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "密码的编码")
    private String encrypter;

    public void valid() {
        AssertSupport.assertNotnull(email, new ErrorsBase.MissingArgumentsWith("email"));
    }
}
