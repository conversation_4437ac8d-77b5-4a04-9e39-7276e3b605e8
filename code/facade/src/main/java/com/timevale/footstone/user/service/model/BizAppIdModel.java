package com.timevale.footstone.user.service.model;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version $ Id: BaseModel.java, v0.1 2021年01月12日 20:55 WangYuWu $
 */
@Data
public class BizAppIdModel {

    /**
     * 业务appId
     */
    @NotBlank
    private String appId;

    /**
     * 动态域名appId 默认取appId
     */
    private String dnsAppId;

    public String adapterDnsAppId() {
        if(StringUtils.isNotBlank(dnsAppId)){
            return dnsAppId;
        }
        return appId;
    }
}
