package com.timevale.footstone.user.service.model.role.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class TransferAdminRequest extends ToString {

    @ApiModelProperty(value = "当前持有管理员的用户ID")
    private String accountId;

    @ApiModelProperty(value = "被转授者的用户ID")
    private String switchedAccountId;
}
