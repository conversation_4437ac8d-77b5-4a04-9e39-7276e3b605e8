package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.service.biz.builder.icuser.BizICUserUpdateInputBuilder;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.model.account.ToUpdateBuilder;
import com.timevale.identity.component.account.mobile.MobileCheckHandler;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


import static com.timevale.account.service.model.constants.BuiltinContacts.EMAIL;
import static com.timevale.account.service.model.constants.BuiltinContacts.MOBILE;
import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_BANK_NUM;

/** <AUTHOR> on 2019-01-20 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonPropModel extends ToString implements ToUpdateBuilder {


    @ApiModelProperty("联系方式，手机号码")
    private String mobile;

    @ApiModelProperty("联系方式，邮箱地址")
    private String email;

    @ApiModelProperty("银行卡号")
    private String cardNo;

    @ApiModelProperty("实名手机号")
    private String realnameMobile;

    @Override
    public BizICUserUpdateInputBuilder conv(BizICUserUpdateInputBuilder in) {
        in.buildProperties().add(INFO_BANK_NUM, getCardNo());
        in.buildContacts().add(MOBILE, getMobile()).add(EMAIL, getEmail());
        return in;
    }

    /**
     * 校验区号是否在允许范围内
     *
     * @param areaCode
     * @param mobile
     */
    public static void checkAreaCodeAllow(String areaCode, String mobile) {
        //. 区号校验为空 直接返回
        if (StringUtils.isBlank(areaCode)) {
            return;
        }
        //. 区号校验是否在允许范围内  不在直接抛异常
        if (!MobileCheckHandler.validateAreaCode(areaCode)) {
            throw new FootstoneUserErrors.InvalidParameterRange("areaCode");
        }
        // 区号不为空的情况下 手机号码必传
        if (StringUtils.isBlank(mobile)) {
            throw new FootstoneUserErrors.MissingArgumentsWith("mobile");
        }
        //. 手机号校验
        if (!MobileCheckHandler.validateInternationalMobile(mobile)) {
            // 数据格式异常: %s
            throw new FootstoneUserErrors.InvalidFormat("mobile");
        }
    }
}
