package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-21 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgModel extends ToString {

    @ApiModelProperty(value = "企业名称", required = true)
    private String name;

    @ApiModelProperty(value = "创建人id",required = true)
    private String creator;

    @ApiModelProperty(value = "属性-企业法人名称")
    private String orgLegalName;

    @ApiModelProperty(value = "属性-企业法人证件号")
    private String orgLegalIdNumber;


    @ApiModelProperty(value = "账号实名方式")
    private String authType;
}
