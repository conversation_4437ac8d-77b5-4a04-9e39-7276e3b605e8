package com.timevale.footstone.user.service.model.thirdparty.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SyncOrgInfoResponse extends ToString {

    @ApiModelProperty("组织基础信息")
    private OrgBaseInfoResponse organizationInfo;

    @ApiModelProperty("三方平台租户信息")
    private ThirdPartyTenantInfoResponse thirdPartyTenantInfo;

    @ApiModelProperty("组织统计信息")
    private ThirdPartyOrgSummaryResponse organizationSummary;


}
