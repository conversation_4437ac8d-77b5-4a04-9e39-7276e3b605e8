package com.timevale.footstone.user.service.api;

import com.timevale.footstone.user.service.ServiceName;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.member.request.MembersExportTaskDownloadRequest;
import com.timevale.footstone.user.service.model.member.response.MembersExportTaskDownloadResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@RestClient(serviceId = ServiceName.name)
public interface RpcCallbackService {

  /**
   * SaaS任务中心 批量导出人员明细任务的 下载按钮 回调此方法
   *
   * @param request
   * @return
   */
  WrapperResponse<MembersExportTaskDownloadResponse> membersExportResult(
      MembersExportTaskDownloadRequest request);
}
