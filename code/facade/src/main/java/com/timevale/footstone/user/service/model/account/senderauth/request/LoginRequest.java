package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LoginRequest extends ServiceIdRequest {

    @ApiModelProperty(value = "登录相关参数配置 默认的配值是 每次登录的是不同端")
    private LoginParams loginParams = new LoginParams();
}
