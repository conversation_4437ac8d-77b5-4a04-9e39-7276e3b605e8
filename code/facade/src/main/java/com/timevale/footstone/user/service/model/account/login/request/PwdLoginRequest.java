package com.timevale.footstone.user.service.model.account.login.request;

import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PwdLoginRequest extends ToString {

    @ApiModelProperty(value = "手机号/邮箱", required = true)
    private String principal;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "密码的编码方式")
    private String encrypter;

    @ApiModelProperty(value = "登录相关配置")
    private LoginParams loginParams = new LoginParams();
}
