package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.account.service.model.service.biz.acc.output.BizAccountTOutput;
import com.timevale.account.service.model.service.biz.builder.acc.impl.abs.AbstractBizAccountBuilder;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.account.service.model.constants.BuiltinProperty.*;

@Data
public class OrgProperties implements AccountPropConv {

    @ModelFieldDecoration(DecorationType.ORGAN_NAME)
    @ApiModelProperty(value = "企业名称")
    private String name;

//    @ModelFieldDecoration(DecorationType.PERSON_NAME)
    @ApiModelProperty("法人姓名")
    private String legalName;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("法人证件号")
    private String legalIdno;

    @ApiModelProperty("法人证件号类型")
    private String legalCertType;

    @ModelFieldDecoration(DecorationType.PERSON_NAME)
    @ApiModelProperty("代理人姓名")
    private String agentName;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("代理人证件号")
    private String agentIdno;

    @ApiModelProperty(value = "实名方式")
    private String realnameContext;

    @ApiModelProperty(value = "商品标签(基础产品库配置属性)")
    private String userConfig;

    @ApiModelProperty(
            value = "账号来源端信息，http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String clentId;

    @Override
    public void conv(AbstractBizAccountBuilder builder) {
        builder.buildProperties()
                .add(INFO_NAME, getName())
                .add(INFO_ORG_LEGAL_NAME, getLegalName())
                .add(INFO_ORG_LEGAL_IDNO, getLegalIdno())
                .add(INFO_ORG_AGENT_NAME, getAgentName())
                .add(INFO_PSN_REALNAME_CONTEXT, getRealnameContext())
                .add(INFO_SRC_CLIENT_ID, getClentId())
                .add(INFO_ORG_AGENT_IDNO, getAgentIdno())
                .add(INFO_ORG_LEGAL_TYPE, getLegalCertType());
    }

    @Override
    public List<Property> conv() {
        List<Property> list = new ArrayList<>();
        list.add(new Property(INFO_NAME, getName()));
        list.add(new Property(INFO_ORG_LEGAL_NAME, getLegalName()));
        if (getLegalIdno()!=null) {
            list.add(new Property(INFO_ORG_LEGAL_IDNO, getLegalIdno()));
        }
        list.add(new Property(INFO_ORG_AGENT_NAME, getAgentName()));
        if (StringUtils.isNoneEmpty(getAgentIdno())) {
            list.add(new Property(INFO_ORG_AGENT_IDNO, getAgentIdno()));
        }
        list.add(new Property(INFO_PSN_REALNAME_CONTEXT, getRealnameContext()));
        if (StringUtils.isNoneEmpty(getLegalCertType())) {
            list.add(new Property(INFO_ORG_LEGAL_TYPE, getLegalCertType()));
        }
        return list;
    }

    @Override
    public OrgProperties init(BizAccountTOutput<ContentSecurity> icuser) {
        Map<String, String> propGroup =
                icuser.getProperties()
                        .stream()
                        .filter(p -> p.getValue() != null)
                        .collect(
                                Collectors.toMap(
                                        Property::getType, Property::getValue, (k1, k2) -> k1));

        setLegalName(propGroup.get(INFO_ORG_LEGAL_NAME));
        setLegalIdno(propGroup.get(INFO_ORG_LEGAL_IDNO));
        setAgentName(propGroup.get(INFO_ORG_AGENT_NAME));
        setAgentIdno(propGroup.get(INFO_ORG_AGENT_IDNO));
        setName(propGroup.get(INFO_NAME));
        setUserConfig(propGroup.get(INFO_USER_CONFIG));
        setRealnameContext(propGroup.get(INFO_PSN_REALNAME_CONTEXT));
        setClentId(propGroup.get(INFO_SRC_CLIENT_ID));
        setLegalCertType(propGroup.get(INFO_ORG_LEGAL_TYPE));
        return this;
    }
}
