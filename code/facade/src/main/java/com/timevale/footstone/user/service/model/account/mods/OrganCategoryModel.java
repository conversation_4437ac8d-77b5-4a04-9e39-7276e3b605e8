package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-11-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrganCategoryModel {

    @ApiModelProperty(value = "业务类型 CONTRACT_FILING-归档合同类型(ALL-归档全部 FINISH-归档已完成)")
    private String category;

    @ApiModelProperty(value = "是否开启")
    private Boolean open;

    @ApiModelProperty(value = "参数值，如果是复杂结构，需要自己解析")
    private String value;
}
