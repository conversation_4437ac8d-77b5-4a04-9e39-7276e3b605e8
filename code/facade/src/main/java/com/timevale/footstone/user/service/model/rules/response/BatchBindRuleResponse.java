package com.timevale.footstone.user.service.model.rules.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年01月16日 15:47:00
 */
@Data
public class BatchBindRuleResponse extends ToString {

    @ApiModelProperty(value = "授权成功数量")
    private int successCount;

    @ApiModelProperty(value = "授权失败数量")
    private int errorCount;

    @ApiModelProperty(value = "授权签署url")
    private String signUrl;
}
