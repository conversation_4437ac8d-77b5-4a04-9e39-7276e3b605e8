package com.timevale.footstone.user.service.model.account.idcard.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024-06-06
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrgThirdPartyBindNoticeRequest extends ToString {
    @ApiModelProperty("三方租户凭证，比如飞书平台的tenantKey")
    @NotBlank(message = "三方租户id不能为空")
    private String tenantKey;
    private String bindOrganOuid;
}
