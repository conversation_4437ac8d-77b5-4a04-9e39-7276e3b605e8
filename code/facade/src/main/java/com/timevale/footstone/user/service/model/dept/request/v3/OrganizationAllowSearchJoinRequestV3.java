package com.timevale.footstone.user.service.model.dept.request.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganizationAllowSearchJoinRequestV3 extends ToString {

  @ApiModelProperty(value = "是否允许搜索组织加入")
  private boolean allowSearchJoin;

}
