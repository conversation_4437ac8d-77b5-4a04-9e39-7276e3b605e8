/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：AddressTagResponse.java <br>
 * 包：com.timevale.footstone.user.model.address.response <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/2414:46]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.address.response;

import com.timevale.footstone.user.service.model.address.model.AddressTagModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类名：AddressTagResponse.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/2414:46]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AddressTagResponse extends ToString {

    @ApiModelProperty(value = "地址标签")
    private List<AddressTagModel> addressTags;
}
