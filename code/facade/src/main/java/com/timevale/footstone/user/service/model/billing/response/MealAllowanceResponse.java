package com.timevale.footstone.user.service.model.billing.response;


import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: ziye
 * @since: 2020-08-25 17:47
 */
@Data
public class MealAllowanceResponse extends ToString {
  @ApiModelProperty(value = "产品名称")
  private String productName;

  @ApiModelProperty("购买量")
  private BigDecimal totalNum;

  @ApiModelProperty(value = "剩余量")
  private BigDecimal margin;

  @ApiModelProperty(value = "计量单位")
  private String units;
}
