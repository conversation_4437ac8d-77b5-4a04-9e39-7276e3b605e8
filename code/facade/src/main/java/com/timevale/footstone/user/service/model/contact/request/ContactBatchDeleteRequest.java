package com.timevale.footstone.user.service.model.contact.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2023/2/22 16:48
 * @Description
 */
@Data
@ApiModel
public class ContactBatchDeleteRequest extends ToString {

  @ApiModelProperty(value = "待删除的联系人ID列表")
  @NotEmpty(message = "待删除联系人不能为空")
  @Size(max = 100, message = "待删除联系人一次不能超过100")
  private List<String> contactIds;

}
