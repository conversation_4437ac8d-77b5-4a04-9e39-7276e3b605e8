package com.timevale.footstone.user.service.model.role.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class UpdateRoleRequest extends ToString {

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色描述")
    private String disc;

    @ApiModelProperty(value = "父角色ID")
    private String parentRoleId;
}
