package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.PersonPropCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class IcUserInfoResponse {
    @ApiModelProperty("基础信息")
    private UserBaseInfoResponse base;

    @ApiModelProperty("属性信息")
    private PersonPropCollection prop;
}
