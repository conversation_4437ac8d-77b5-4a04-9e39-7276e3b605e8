package com.timevale.footstone.user.service.enums;

/**
 * <AUTHOR>
 * @since 2020-11-04 14:17
 */
public enum AdminFlagEnum {

    /** 有管理员 */
    WITH_ADMIN(1, "有管理员"),

    /** 无管理员 */
    WITHOUT_ADMIN(2, "无管理员");

    private int code;
    private String desc;

    AdminFlagEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
