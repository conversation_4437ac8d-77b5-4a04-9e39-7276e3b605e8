package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024/11/14 16:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateNameResponse extends ToString {


    private String serviceId;

    private String authUrl;

    private String updateResult;

}
