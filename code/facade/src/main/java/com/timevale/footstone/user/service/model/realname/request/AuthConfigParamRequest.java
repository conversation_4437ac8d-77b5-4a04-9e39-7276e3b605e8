package com.timevale.footstone.user.service.model.realname.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ Id: AuthConfigParam.java, v0.1 2021年09月07日 17:29 WangYuWu $
 */
@Data
public class AuthConfigParamRequest {

    @ApiModelProperty("本次认证默认使用的认证类型")
    private String          authType;


    @ApiModelProperty("本次认证可使用的认证类型")
    private List<String>    availableAuthTypes;
}
