/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealImageAddRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.seal.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/219:51]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.footstone.user.service.model.seal.model.SealImageModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：SealImageAddRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/219:51]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealImageAddRequest extends SealImageModel {

    @ApiModelProperty(value = "印章类型，3-自定义图片，4-手绘，默认3")
    private Short sealType = 3;

    @ApiModelProperty(value = "是否需要审核，默认false")
    private boolean auditFlag;

    @ApiModelProperty(value = "是否需要返回印章下载地址，默认true")
    private boolean downloadFlag = true;

    @ApiModelProperty(value = "是否需要对图片做单色处理，默认false")
    private boolean handleFlag;

    @ApiModelProperty(value = "印章业务类型，COMMON-无业务类型(其他)，CANCELLATION-作废章, PUBLIC-公章，CONTRACT-合同专用章, FINANCE-财务章, INVOICE-发票专用章")
    private String sealBizType;
}
