package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.footstone.user.service.model.ReqHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人业务授权请求参数
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
public class RpcApplyPersonBizAuthRequest extends ApplyBizAuthBaseRequest implements ReqHandler {

    @ApiModelProperty(value = "认证配置信息")
    private ApplyBizPersonAuthConfig authConfig;


    @ApiModelProperty(value = "个人主体信息")
    private ApplyBizPersonSubject personSubject;

    @Override
    public ReqHandler validate() {
        super.validate();
        return this;
    }

    @Override
    public ReqHandler format() {
        super.format();
        return this;
    }
}