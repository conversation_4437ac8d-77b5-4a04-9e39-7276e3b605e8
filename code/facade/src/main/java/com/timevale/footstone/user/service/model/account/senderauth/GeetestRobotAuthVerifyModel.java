package com.timevale.footstone.user.service.model.account.senderauth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> on 2019-08-15
 */
@Data
public class GeetestRobotAuthVerifyModel {

    @ApiModelProperty(value = "【极验】流水号，一次完整验证流程的唯一标识", required = true)
    private String geetest_challenge;

    @ApiModelProperty(value = "【极验】待校验的核心数据，客户端向极验请求的http://api.geetest.com/ajax.php 接口返回得到", required = true)
    private String geetest_validate;

    @ApiModelProperty(value = "【极验】待校验的核心数据，geetest_validate加上|jordan组成的字符串", required = true)
    private String geetest_seccode;
}
