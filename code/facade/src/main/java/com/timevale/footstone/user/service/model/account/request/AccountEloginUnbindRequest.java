package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountEloginUnbindRequest extends ToString {

    @ApiModelProperty(value = "密文", required = true)
    private String encryptContent;

    public void valid() {
        AssertSupport.assertNotnull(encryptContent, new ErrorsBase.MissingArgumentsWith("encryptContent"));
    }

}
