package com.timevale.footstone.user.service.model.thirdplatform.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

@Data
public class ThirdPlatformAppConfigResponse extends ToString {

    private Long id;

    private String tenantKey;

    private String tenantName;

    private String userName;

    private String notifyUrl;

    private String tenantAppId;

    private String tenantAppSecret;

    private String creator;

    private Date createTime;

    private String modifier;

    private Date modifyTime;
}
