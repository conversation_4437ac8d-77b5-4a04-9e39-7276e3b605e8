package com.timevale.footstone.user.service.model.account.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账号绑定/解绑上下文数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountBindContextResponse extends ToString {

    @ApiModelProperty(value = "登录凭证")
    private String originalIdcard;

    @ApiModelProperty(value = "登录凭证（脱敏）")
    private String decorateIdcard;
}