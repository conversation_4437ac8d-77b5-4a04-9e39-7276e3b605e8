package com.timevale.footstone.user.service.model.account.request;

import com.timevale.easun.service.model.extend.PrefWillTypeEnum;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021年08月11日 15:16:00
 */
@Data
public class PrefWillTypeSetRequest {
    @ApiModelProperty(
            value =
                    "个人意愿偏好，    FACE, //人脸识别"
                            + "    SIGN_PWD, //密码"
                            + "    CERT,     //ukey"
                            + "    CODE_SMS, //短信验证码"
                            + "    FACE_AUDIO_VIDEO_DUAL, //人脸视频"
                            + "    EMAIL // 邮件")
    String prefWillType;

    public void valid(){
        if(StringUtils.isEmpty(prefWillType)){
            return;
        }
        List<String> enumNames =
                Arrays.stream(PrefWillTypeEnum.values())
                        .map(i -> i.name())
                        .collect(Collectors.toList());
        Assert.isTrue(enumNames.contains(prefWillType),"prefWillType 类型错误");
    }
}
