/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyWithoutAccountRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.request <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/1614:39]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 */
@Data
public class OrgStandardCertApplyAuthenticationRequest extends ToString {


    @ApiModelProperty(value = "业务发起来源: app,browser,alipay,dingtalk")
    private String from;

    @ApiModelProperty(value = "回调地址")
    private String redirectUrl;
}