package com.timevale.footstone.user.service.model.account.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 授权认证信息
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizAuthIdentityContext extends ToString {

    /**
     * 认证来源方流程ID
     */
    private String identitySourceId;

    /**
     * 认证来源方类型
     * @see com.timevale.footstone.identity.common.service.model.identification.AuthSourceType.name()
     */
    private String identitySourceType;

    @ApiModelProperty(value = "身份核验ID")
    private String identificationId;
}