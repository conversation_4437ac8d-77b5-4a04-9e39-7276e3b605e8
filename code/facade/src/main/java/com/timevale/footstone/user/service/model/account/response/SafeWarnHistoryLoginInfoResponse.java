package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: sicheng
 * @since: 2020-06-16 19:36
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class SafeWarnHistoryLoginInfoResponse extends ToString {

    @ApiModelProperty(value = "ip", required = true)
    private String ip;

    @ApiModelProperty(value = "登录地", required = true)
    private String addr;

    @ApiModelProperty(value = "登录时间", required = true)
    private Long time;

    @ApiModelProperty(value = "登录类型", required = true)
    private String loginType;

    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @ApiModelProperty(value = "常用登录地true/false", required = true)
    private Boolean commonAddr;
}
