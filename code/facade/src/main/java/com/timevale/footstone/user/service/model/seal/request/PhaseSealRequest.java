package com.timevale.footstone.user.service.model.seal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/23
 */
@Data
public class PhaseSealRequest {

    @ApiModelProperty(value = "印章数据", required = true)
    private String sealData;

    @ApiModelProperty(value = "印章数据类型，BASE64-base64格式，FILEKEY-文件filekey", required = true)
    private String dataType;

    @ApiModelProperty(value = "区块链印章业务类型", required = true)
    private String chainSealBizType;

    @ApiModelProperty(value = "印章宽度")
    private Integer width;

    @ApiModelProperty(value = "印章高度")
    private Integer height;

    @ApiModelProperty(value = "印章别名")
    private String alias;

    @ApiModelProperty(value = "是否默认章")
    private boolean defaultSeal;

    @ApiModelProperty(value = "印章id（公有云创建返回的id）")
    private String sealId;

    @ApiModelProperty(value = "印章是否删除")
    private boolean  deleted;
}
