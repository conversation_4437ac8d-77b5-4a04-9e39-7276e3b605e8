package com.timevale.footstone.user.service.model.role.model;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> on 2019-01-03
 */
public class RoleUser {

    @ApiModelProperty(value = "用户id")
    private String user;

    @ApiModelProperty(value = "用户类型", allowableValues = "ACCOUNT_UID, OUID, MEMBER_ID, ORGAN_ID")
    private String userType;

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }
}
