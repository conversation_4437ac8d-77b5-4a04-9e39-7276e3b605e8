package com.timevale.footstone.user.service.model.rules.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年01月16日 15:38:00
 */
@Data
public class BatchRuleItem extends ToString {

    @ApiModelProperty(value = "资源id", required = true)
    private String ruleGrantedId;

    @ApiModelProperty(value = "失效时间", required = true)
    private Long expireTime;

    @ApiModelProperty(value = "生效时间", required = true)
    private Long effectiveTime;
}
