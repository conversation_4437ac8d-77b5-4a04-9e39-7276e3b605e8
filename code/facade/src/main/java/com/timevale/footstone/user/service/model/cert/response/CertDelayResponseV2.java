package com.timevale.footstone.user.service.model.cert.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class CertDelayResponseV2 {
    private String certId;
    private Date startTime;
    private Date endTime;
    @ApiModelProperty(value = "签名证书base64")
    private String signCert;
    /** 加密证书 */
    @ApiModelProperty(value = "加密证书")
    private String encCert;
    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息")
    private String ext;
}
