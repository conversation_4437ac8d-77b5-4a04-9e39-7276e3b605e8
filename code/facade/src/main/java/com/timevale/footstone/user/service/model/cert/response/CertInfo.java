package com.timevale.footstone.user.service.model.cert.response;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Builder
@Data
public class CertInfo {

    private String certId;
    /**
     * 证书名称
     */
    private String name;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String idNumber;

    private String txHash;

    private String contentHash;

    private Integer status;

    /**
     * 颁发机构
     */
    private String issuer;
    /**
     * 生效日期
     */
    private Date   startTime;
    /**
     * 失效日期
     */
    private Date   endTime;

    private Integer algorithm;

    private String sn;
}
