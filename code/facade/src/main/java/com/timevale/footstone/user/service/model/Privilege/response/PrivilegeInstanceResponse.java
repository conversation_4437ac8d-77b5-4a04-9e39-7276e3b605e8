package com.timevale.footstone.user.service.model.Privilege.response;

import com.timevale.footstone.user.service.model.Privilege.mods.PrivilegeInstanceModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class PrivilegeInstanceResponse extends ToString {

    @ApiModelProperty(value = "数据权限列表")
    private List<PrivilegeInstanceModel> dataPrivilegeModels;
}
