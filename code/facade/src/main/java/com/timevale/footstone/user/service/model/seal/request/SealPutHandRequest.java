/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：app-parent <br/>
 * 文件名：SealPutHandRequest.java <br/>
 * 包：com.timevale.rest.webserver.model.seal <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/1/320:12]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealPutHandRequest extends ToString {

    @ApiModelProperty(value = "手绘印章缓存key")
    private String handKey;

    @ApiModelProperty(value = "手绘图片base64")
    private String sealBase64;

    @ApiModelProperty(value = "文件名")
    private String fileName;
}