package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/23
 */
@Data
public class CheckLegalResponse {

    @ApiModelProperty(value = "企业id")
    private String orgId;

    @ApiModelProperty(value = "法人姓名")
    @ModelFieldDecoration(DecorationType.PERSON_NAME)
    private String legalName;
}
