package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.model.constants.BuiltinContacts;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.helper.acc.BizICUserOutputHelper;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2024/10/30 17:09
 */
@Data
public class PersonGetV1Response  extends  PersonGetResponse{


    @ApiModelProperty(value = "联系人区号 852-香港地区  853-澳门地区 ")
    private String areaCode;

    @ApiModelProperty(value = "实名证据点")
    private String realnameEvidencePointId;
    public PersonGetV1Response(){}

    public PersonGetV1Response(BizAccountRealNameOutput realNameOutput){
        super(realNameOutput);

        BizICUserOutputHelper helper = new BizICUserOutputHelper(realNameOutput.getAccount());
        // 2024/10/30  新增返回区号
        setAreaCode(helper.getContactsValue(BuiltinContacts.MOBILE_AREA));
        setRealnameEvidencePointId(helper.getPropertyValue(BuiltinProperty.REALNAME_EVIDENCE_POINT_ID));
    }

}
