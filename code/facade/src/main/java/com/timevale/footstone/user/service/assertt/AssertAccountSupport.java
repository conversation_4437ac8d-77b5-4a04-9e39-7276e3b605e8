package com.timevale.footstone.user.service.assertt;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/1/3 20:30
 */
public class AssertAccountSupport {

    private static final int PSN_NAME_LEN_MIN = 1;

    public static void assertPsnName(String name){

        if(StringUtils.isNotBlank(name)){
            AssertSupport.assertTrue(name.length() >= PSN_NAME_LEN_MIN, new FootstoneUserErrors.InvalidParameterRange("姓名最小字符数不少于1"));
        }

    }

}
