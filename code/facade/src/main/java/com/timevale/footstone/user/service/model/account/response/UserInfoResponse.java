package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-24
 */
@Data
public class UserInfoResponse {

    @ApiModelProperty(value = "用户ID", required = true)
    private String accountId;

    @ApiModelProperty(value = "用户ID", required = true)
    private String guid;

    @ModelFieldDecoration(DecorationType.CREDENTIALS)
    @ApiModelProperty(value = "实名证件号")
    private String credentical;

    @ApiModelProperty(value = "实名证件类型")
    private String credenticalType;

    @ModelFieldDecoration(DecorationType.EMAIL)
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ModelFieldDecoration(DecorationType.MOBILE)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "实名状态")
    private Boolean realNameStatus;

    @ApiModelProperty(value = "用户头像fileKey")
    private String headImg;

    @ApiModelProperty(value = "用户头像url")
    private String headImgUrl;

    @ModelFieldDecoration(DecorationType.PERSON_NAME)
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "账号注册时间")
    private String registerTime;

    /** 第三方标识 */
    @ApiModelProperty(
            value = "第三方用户标识",
            allowEmptyValue = true,
            allowableValues = "dingb3bf6338af78174135c2f4657eb6378f")
    private String thirdId;
}
