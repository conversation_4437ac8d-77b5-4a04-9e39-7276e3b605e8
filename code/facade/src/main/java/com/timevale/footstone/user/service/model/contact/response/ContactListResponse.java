/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：ContactListResponse.java <br>
 * 包：com.timevale.footstone.user.service.model.contact.response <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:02]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.contact.response;

import com.timevale.footstone.user.service.model.contact.model.ContactModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 类名：ContactListResponse.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:02]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
public class ContactListResponse {

    @ApiModelProperty(value = "查询总数")
    private Long total;

    @ApiModelProperty(value = "联系人列表")
    private List<ContactModel> contactList;
}
