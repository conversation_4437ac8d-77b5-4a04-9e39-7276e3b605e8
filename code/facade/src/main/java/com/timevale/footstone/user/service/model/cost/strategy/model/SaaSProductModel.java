package com.timevale.footstone.user.service.model.cost.strategy.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/14 17:13
 */
@Data
public class SaaSProductModel {

    @ApiModelProperty(value = "saas套餐列表")
    private List<OrgProductModel> combos;

    @ApiModelProperty(value = "saas单项产品列表")
    private List<OrgProductModel> products;
}
