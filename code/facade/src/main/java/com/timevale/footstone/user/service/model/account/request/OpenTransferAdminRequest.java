package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OpenTransferAdminRequest extends ToString {

    @ApiModelProperty(value = "新管理员的用户ID，优先")
    private String accountId;

    @ApiModelProperty(value = "新管理员的第三方ID")
    private String thirdId;

    @ApiModelProperty(value = "新管理员的第三方类型")
    private String thirdType;
}
