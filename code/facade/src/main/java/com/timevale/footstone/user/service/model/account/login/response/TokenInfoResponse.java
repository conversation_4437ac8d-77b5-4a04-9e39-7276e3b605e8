package com.timevale.footstone.user.service.model.account.login.response;

import com.timevale.footstone.user.service.model.account.login.model.TokenModel;
import com.timevale.footstone.user.service.model.account.login.model.TokenProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR> on 2018-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TokenInfoResponse extends TokenModel {

    @ApiModelProperty(value = "登录设备唯一识别号")
    private String equipId;

    @ApiModelProperty(value = "登录账号")
    private String loginUser;

    @ApiModelProperty(value = "登录源唯一识别号")
    private String sourceCode;

    @ApiModelProperty(value = "用户唯一主键")
    private String accountUid;

    @ApiModelProperty(value = "用户唯一主键")
    private String accountId;

    @ApiModelProperty(value = "项目主键[兼容旧项目]")
    private String projectId;

    @ApiModelProperty(value = "项目的key")
    private String productKey;

    @ApiModelProperty(value = "新用户中心登录ID")
    private String productAppId;

    @ApiModelProperty(value = "登录类型")
    private String loginType;

    @ApiModelProperty(value = "token失效时间点")
    private Date expireDate;

    @ApiModelProperty(value = "属性信息")
    private Map<String, TokenProperty> properties;
}
