package com.timevale.footstone.user.service.model.account.idcard.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BindOrgThirdPartyRequest {
    @ApiModelProperty("三方租户凭证，比如飞书平台的tenantKey")
    @NotBlank(message = "三方租户id不能为空")
    private String tenantKey;
    @ApiModelProperty("绑定的企业ouId")
    private String organOuid;
}
