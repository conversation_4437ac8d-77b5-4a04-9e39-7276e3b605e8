package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.footstone.user.service.enums.LoginSourceConstants;
import com.timevale.footstone.user.service.model.account.mods.UserInfoModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class ListOrganAndRoleResponse extends ToString {

    @ApiModelProperty(value = "是否展示个人空间")
    private boolean personSpace = true;

    @ApiModelProperty(value = "登录来源，SSO=sso登录，DEFAULT=默认登录")
    private String loginSource = LoginSourceConstants._DEFAULT;

    @ApiModelProperty(value = "组织以及用户在组织下的角色列表")
    private List<UserInfoModel> bizUserInfoOutputs;

    @ApiModelProperty(value = "用户对应组织总量")
    private Integer total;
}
