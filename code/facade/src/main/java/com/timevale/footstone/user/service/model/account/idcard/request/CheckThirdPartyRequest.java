package com.timevale.footstone.user.service.model.account.idcard.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-02-14 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CheckThirdPartyRequest extends ToString {

    @ApiModelProperty(value = "第三方类型，支付宝：ALI_PAY、微信平台：WE_CHAT、钉钉平台：DING_TALK", required = true)
    private String key;

    @ApiModelProperty(value = "第三方用户id", required = true)
    private String userId;

    @ApiModelProperty(value = "账号类型")
    private String userType;
}
