package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.base.exception.BaseIllegalArgumentException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

@Data
public class PostponeRequest {
    @NotBlank
    @ApiModelProperty(value = "延期时长，暂支支持传值枚举:1D、1Y、2Y、3Y;非必填,默认:1Y")
    private String validDuration;

    public void valid() {
        if (StringUtils.isNotBlank(validDuration)) {
            if (!"1D".equals(validDuration) && !"1Y".equals(validDuration)
                    && !"2Y".equals(validDuration) && !"3Y".equals(validDuration)) {
                throw new BaseIllegalArgumentException("validDuration无效");
            }
        }
    }
}
