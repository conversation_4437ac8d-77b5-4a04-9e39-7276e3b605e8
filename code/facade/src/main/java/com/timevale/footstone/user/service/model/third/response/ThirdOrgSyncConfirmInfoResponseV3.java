package com.timevale.footstone.user.service.model.third.response;

import lombok.Data;
import java.util.Date;
import lombok.EqualsAndHashCode;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdOrgSyncConfirmInfoResponseV3 extends ToString {

  @ApiModelProperty(value = "企业名称")
  private String organName;

  @ApiModelProperty(value = "成员名称")
  private String memberName;

  @ApiModelProperty(value = "是否实名")
  private Boolean isRealName;

}
