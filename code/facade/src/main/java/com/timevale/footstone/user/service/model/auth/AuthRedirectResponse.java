package com.timevale.footstone.user.service.model.auth;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-03-14 01:10
 */
@Data
public class AuthRedirectResponse  extends ToString {

    private String returnUrl;

    public AuthRedirectResponse() {

    }

    public AuthRedirectResponse(String returnUrl) {
        this.returnUrl = returnUrl;
    }
}
