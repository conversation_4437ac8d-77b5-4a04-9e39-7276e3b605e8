package com.timevale.footstone.user.service.model.role.response;

import com.timevale.footstone.user.service.model.role.model.RoleModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@Data
public class ListRolesByNameResponse extends ToString {
    @ApiModelProperty(value = "角色列表", required = true)
    private List<RoleModel> roleModels;
}
