package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-26
 */
@Data
public class AccountModel extends ToString {

    @ApiModelProperty(value = "用户ID accountUid")
    private String accountUid;

    @ApiModelProperty(value = "用户oid")
    private String ouid;

    @ApiModelProperty(value = "用户GUID")
    private String guid;

    @ApiModelProperty(value = "是否实名")
    private Boolean realName;

    @ApiModelProperty(value = "属性实体")
    private Map<String, String> properties;

    @ApiModelProperty(value = "证件号实体")
    private Map<String, String> credentials;

    @ApiModelProperty(value = "联系人实体")
    private Map<String, String> contacts;

    @ApiModelProperty(value = "账号类型 PERSON|ORGANIZE")
    private String accountType;
}
