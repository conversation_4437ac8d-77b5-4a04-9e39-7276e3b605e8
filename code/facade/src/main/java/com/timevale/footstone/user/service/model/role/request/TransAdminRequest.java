package com.timevale.footstone.user.service.model.role.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class TransAdminRequest extends ToString {
    @ApiModelProperty(value = "当前持有管理员的用户ID")
    private String accountId;

    @ApiModelProperty(value = "被转授者的用户ID")
    private String switchedAccountId;
}
