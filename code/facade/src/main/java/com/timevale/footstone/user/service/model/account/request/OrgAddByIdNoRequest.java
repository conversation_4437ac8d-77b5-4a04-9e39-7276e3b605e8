package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.footstone.user.service.model.account.mods.OrgModel;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-21 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgAddByIdNoRequest extends OrgModel {

    @ApiModelProperty(value = "证件号", required = true)
    private String idNumber;

    @ApiModelProperty(value = "证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********", required = true)
    private String idType;


    public String getIdType() {
        return StringUtils.isNotBlank(idType) ? idType : BuiltinCredentials.CRED_ORG_USCC;
    }
}
