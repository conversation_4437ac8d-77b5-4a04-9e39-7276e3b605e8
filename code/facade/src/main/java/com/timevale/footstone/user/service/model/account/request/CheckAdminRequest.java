package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-21
 */
@Data
public class CheckAdminRequest extends ToString {

    @ApiModelProperty(value = "用户ID")
    @NotEmpty(message = "用户ID不能为空")
    private String accountId;

    @ApiModelProperty(value = "企业OID")
    @NotEmpty(message = "企业OID不能为空")
    private String orgId;
}
