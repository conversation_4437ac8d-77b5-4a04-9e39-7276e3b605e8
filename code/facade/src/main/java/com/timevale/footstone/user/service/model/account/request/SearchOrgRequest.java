package com.timevale.footstone.user.service.model.account.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/10 17:24
 */

@Data
public class SearchOrgRequest {
    /**
     * 企业名字
     */
    private String name;

    /**
     * 是否实名
     */
    private String realName;

    /**
     * 偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 模糊搜索
     */
    private Boolean fuzzySearch;

    /**
     * appId
     */
    private String projectId;

    /**
     * 成员oid
     */
    private String memberAccountId;

    /**
     * 企业是否实名组织
     */
    private Boolean organGuidExist;

    /**
     * 成员实名
     */
    private Boolean memberRealname;

    /**
     * 企业激活态
     */
    private Integer activate;
}
