package com.timevale.footstone.user.service.model.Privilege.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
public class CreatePrivilegResponse extends ToString {

    /**
     * 权限类型{@link com.timevale.footstone.user.service.enums.PrivilegeTypeEnum}
     */
    @ApiModelProperty(value = "权限类型 business|data")
    private String privilegeType;
}
