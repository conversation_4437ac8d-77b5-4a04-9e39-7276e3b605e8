package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2019-01-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RealnameServiceIdModel extends ToString {

    @ApiModelProperty(value = "实名流程的id", required = true)
    private String serviceId;
}
