/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：LegalAuthApplyPersonModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.seal.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3020:52]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：LegalAuthApplyPersonModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3020:52]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LegalAuthApplyPersonModel {

    @ApiModelProperty(value = "法人章授权申请人姓名")
    private String accountName;

    @ApiModelProperty(value = "法人章授权申请人登录标识")
    private String idcard;

    @ApiModelProperty(value = "法人章授权申请人账号id")
    private String accountId;

    @ApiModelProperty(value = "法人授权申请人证件号")
    private String idno;
}