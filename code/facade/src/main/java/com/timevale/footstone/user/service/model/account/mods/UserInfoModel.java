package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.footstone.user.service.model.role.model.RoleModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-25
 */
@Data
public class UserInfoModel extends ToString {

    /**
     * 是否为主企业
     */
    @ApiModelProperty(value = "是否为默认组织")
    private Boolean isMain;

    /**
     * 是否三方企业id对应的主企业
     */
    @ApiModelProperty(value = "是否三方企业id对应的主企业")
    private Boolean isTenantKeyOrg;

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "组织accountUid")
    private String orgAccountUid;

    @ApiModelProperty(value = "组织GUID")
    private String orgGuid;

    @ApiModelProperty(value = "账号的guid")
    private String guid;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "角色列表")
    private List<RoleModel> roleDetailList;

    @ApiModelProperty(value = "是否已实名")
    private Boolean realName;

    /**
     * 授权状态 枚举类型 见{@link com.timevale.footstone.user.service.enums.AuthStatusEnum}
     */
    @ApiModelProperty(value = "oem场景使用:企业空间授权状态")
    private int authStatus;

    /**
     * 管理员标识 枚举类型 见{@link com.timevale.footstone.user.service.enums.AdminFlagEnum}
     */
    @ApiModelProperty(value = "oem场景使用:企业是否有管理员标识")
    private int hasAdminFlag;

    @ApiModelProperty(value = "oem场景使用:授权登录页url")
    private String authUrl;

    @ApiModelProperty(value = "实名组织激活态")
    private Integer activate;

    @ApiModelProperty(value = "是否置顶")
    private Integer top;

    @ApiModelProperty(value = "排序权重")
    private Integer weight;
}
