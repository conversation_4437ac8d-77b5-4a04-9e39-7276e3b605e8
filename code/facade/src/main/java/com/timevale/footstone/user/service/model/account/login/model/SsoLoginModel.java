package com.timevale.footstone.user.service.model.account.login.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/29 14:09
 */
@Data
public class SsoLoginModel {


    private Boolean ifExistDomain;

    private String source;

    private String flowKey;
    private String logoutUrl;

    public SsoLoginModel(Boolean ifExistDomain) {
        this.ifExistDomain = ifExistDomain;
    }

    public Boolean existDomain() {
        return ifExistDomain;
    }

    public Boolean existNotDomain() {
        return !existDomain();
    }
}
