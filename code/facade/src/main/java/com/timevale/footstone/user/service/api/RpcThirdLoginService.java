package com.timevale.footstone.user.service.api;

import com.timevale.footstone.user.service.ServiceName;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.thirdlogin.request.*;
import com.timevale.footstone.user.service.model.thirdlogin.response.ThirdLoginRightsCheckResponse;
import com.timevale.footstone.user.service.model.thirdlogin.response.ThirdLoginUrlResponse;
import com.timevale.footstone.user.service.model.thirdlogin.response.GetThirdLoginInfoResponse;
import com.timevale.footstone.user.service.model.thirdlogin.response.SaveThirdLoginInfoResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * <AUTHOR>
 * @since 2024-05-09 标准三方登陆相关
 */
@RestClient(serviceId = ServiceName.name)
public interface RpcThirdLoginService {
    /**
     * 保存用户三方登录信息
     *
     * @param request
     * @return
     */
    WrapperResponse<SaveThirdLoginInfoResponse> saveThirdLoginInfo(
            SaveThirdLoginInfoRequest request);

    /**
     * 获取用户三方登录信息
     *
     * @param request
     * @return
     */
    WrapperResponse<GetThirdLoginInfoResponse> getThirdLoginInfo(GetThirdLoginInfoRequest request);


    /**
     * 根据appId,secret, authCode 获取三方登录信息
     * @param request
     * @return
     */
    WrapperResponse<GetThirdLoginInfoResponse> getThirdLoginInfoByAppIdAndCode(
            ThirdLoginInfoRequestParam request);

    /**
     * 获取三方登录地址
     * @param request
     * @return
     */
    WrapperResponse<ThirdLoginUrlResponse> getThirdLoginUrl(ThirdLoginUrlGetRequest request);

    /**
     * 三方登录 权益校验
     * @param request
     * @return
     */
    WrapperResponse<ThirdLoginRightsCheckResponse> rightsCheck(ThirdLoginRightsCheckRequest request);

}
