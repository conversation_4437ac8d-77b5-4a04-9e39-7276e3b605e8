package com.timevale.footstone.user.service.model.account.request.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ Id: RealNameNotifyOrgRequest.java, v0.1 2021年10月21日 16:49 WangYuWu $
 */
@Data
public class RealNameNotifyOrgRequest extends RealNameNotifyBaseRequest{

    /**
     * 办理人认证流程id，无经办理人认证是为null
     */
    @ApiModelProperty(value = "办理人认证流程id")
    private String      agentFlowId;

    /**
     * 办理人账户id，核身认证时为null
     */
    @ApiModelProperty(value = "办理人账户id")
    private String      agentAccountId;
}
