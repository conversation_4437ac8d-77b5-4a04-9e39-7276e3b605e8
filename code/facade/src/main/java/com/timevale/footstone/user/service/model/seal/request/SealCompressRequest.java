/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealCompressRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.seal.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/222:30]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类名：SealCompressRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/222:30]创建类 by flh
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SealCompressRequest extends ToString {

    @ApiModelProperty(value = "印章数据", required = true)
    private String data;

    @ApiModelProperty(value = "印章数据类型，BASE64-base64格式，FILEKEY-fileKey格式")
    private String dataType;

    @ApiModelProperty(value = "处理后的颜色", required = true)
    private String color;

    @ApiModelProperty(value = "处理阈值", required = true)
    private List<Integer> cvalues;

    @ApiModelProperty(value = "是否返回下载地址，默认false")
    private boolean downloadFlag;
}
