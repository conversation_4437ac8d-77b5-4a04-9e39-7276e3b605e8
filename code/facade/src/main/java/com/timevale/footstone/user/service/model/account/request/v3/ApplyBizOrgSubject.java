package com.timevale.footstone.user.service.model.account.request.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025/4/14 21:08
 */
@ApiModel
@Data
public  class ApplyBizOrgSubject extends ToString {

    @ApiModelProperty(value = "组织账号ID")
    @NotBlank
    private String orgId;


    @ApiModelProperty(value = "组织姓名")
    private String orgName;

    @ApiModelProperty(value = "组织证件号")
    private String orgCertNo;

  /**
   * 组织证件类型
   *
   * <p>【统一社会信用代码】CRED_ORG_USCC
   * <p>【工商注册号】CRED_ORG_REGCODE
   * <p>【其他】CRED_ORG_UNKNOWN
   *
   * @see com.timevale.account.service.enums.BuiltinCredentialsEnum.code()
   */
  @ApiModelProperty(value = "组织证件类型")
  private String orgCertType;

    @ApiModelProperty(value = "法定代表人姓名")
    private String legalRepName;


    @ApiModelProperty(value = "经办人身份信息")
    @NotBlank
    private ApplyBizOrgAgentInfo agentInfo;



}
