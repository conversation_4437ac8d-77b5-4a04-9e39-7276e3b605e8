package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/1/3 20:54
 */
public class PersonAddByIdV2Request extends PersonAddByIdRequest {

  @Override
  public void valid() {
    super.valid();
    if (StringUtils.isNotBlank(this.getIdNumber()) && StringUtils.isBlank(this.getName())) {
      // 有证件号却没有名称的抛出异常提示
      AssertSupport.assertNotnull(this.getName(), new ErrorsBase.MissingArgumentsWith("name"));
    }
  }
}
