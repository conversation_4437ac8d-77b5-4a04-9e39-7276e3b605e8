/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：LegalAuthApplyRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.seal.request <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1214:21]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：LegalAuthApplyRequest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/8/1214:21]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LegalAuthApplyRequest extends ToString {

    @ApiModelProperty(value = "授权文档filekey")
    private String authDocFilekey;

    @ApiModelProperty(value = "身份证正面照filekey")
    private String authIDFrontFilekey;

    @ApiModelProperty(value = "身份证发面照filekey")
    private String authIDReverseFilekey;
}