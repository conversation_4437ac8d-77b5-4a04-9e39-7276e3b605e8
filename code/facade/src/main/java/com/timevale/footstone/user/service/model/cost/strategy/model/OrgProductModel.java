package com.timevale.footstone.user.service.model.cost.strategy.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/14 17:10
 */
@Data
@Builder
public class OrgProductModel {

    @ApiModelProperty(value = "售卖方案id")
    private Long saleSchemaId;

    @ApiModelProperty(value = "售卖方案名称")
    private String saleSchemaName;

    @ApiModelProperty(value = "产品编号")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "当前可用")
    private BigDecimal margin;

    @ApiModelProperty(value = "总量")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "单位")
    private String units;

    @ApiModelProperty(value = "是否不限量")
    private Boolean unlimitNum;

}
