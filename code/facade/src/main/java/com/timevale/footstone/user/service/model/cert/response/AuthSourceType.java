package com.timevale.footstone.user.service.model.cert.response;

/**
 * <AUTHOR>
 * @since 2025/2/17 19:59
 */
public enum AuthSourceType {

    identity("identity",30*24*60*60*1000),
    will("will",30*60*1000);

    private String code;
    private long expired;

    AuthSourceType(String code, long expired) {
        this.code = code;
        this.expired = expired;
    }

    public String getCode() {
        return code;
    }

    public long getExpired() {
        return expired;
    }
}
