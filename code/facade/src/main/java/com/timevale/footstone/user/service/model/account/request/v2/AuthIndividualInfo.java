package com.timevale.footstone.user.service.model.account.request.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ Id: AuthIndividualInfo.java, v0.1 2021年08月25日 08:07 WangYuWu $
 */
@Data
public class AuthIndividualInfo {

    @ApiModelProperty("支付宝ID")
    private String aliPay;


    @ApiModelProperty("钉钉ID")
    private String dingDing;


    @ApiModelProperty("个人姓名")
    private String name;


    @ApiModelProperty("个人证件号")
    private String certNo;


    @ApiModelProperty("个人证件类型")
    private String certType;


    @ApiModelProperty(value = "手机号")
    private String mobileNo;


    @ApiModelProperty("个人银行卡号")
    private String bankCardNo;
}
