package com.timevale.footstone.user.service.model.organization.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:jiany<PERSON>
 * @since 2020-12-15 20:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgOidAndIdCheckResponse {
	@ApiModelProperty(value = "企业oid")
	private String orgOid;
	@ApiModelProperty(value = "当前用户是否已经是管理员")
	private boolean adminNow;
}
