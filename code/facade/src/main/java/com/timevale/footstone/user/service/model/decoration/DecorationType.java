package com.timevale.footstone.user.service.model.decoration;

public interface DecorationType {

    String COMMON = "decoration.common";

    String MOBILE = "decoration.mobile";

    String EMAIL = "decoration.email";

    String CREDENTIALS = "decoration.cred";

    String CARD = "decoration.card";

    String NAME = "decoration.name";

    String PERSON_NAME = "decoration.person.name";

    String PERSON_CRED = "decoration.person.cred";

    //String PERSON_CARD = "decoration.person.cred";

    String ORGAN_NAME = "decoration.organ.name";

    String ORGAN_CRED = "decoration.organ.cred";

    //String ORGAN_CARD = "decoration.person.cred";
}
