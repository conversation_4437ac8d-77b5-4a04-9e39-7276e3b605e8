package com.timevale.footstone.user.service.model.Privilege.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class UpdatePrivilegeModel extends ToString {
    @ApiModelProperty(value = "权限操作标识")
    private String targetClassKey;

    private String targetClassKeyName;

    private String operationPermit;

    private String operationPermitName;
}
