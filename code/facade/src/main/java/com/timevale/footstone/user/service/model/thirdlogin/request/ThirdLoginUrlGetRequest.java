package com.timevale.footstone.user.service.model.thirdlogin.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ThirdLoginUrlGetRequest extends ToString {

    private String sceneType;

    @NotBlank(message = "businessType不能为空")
    private String businessType;

    private ThirdLoginInfoRequest loginInfo;

    private ThirdLoginExtraRequest extra;
}
