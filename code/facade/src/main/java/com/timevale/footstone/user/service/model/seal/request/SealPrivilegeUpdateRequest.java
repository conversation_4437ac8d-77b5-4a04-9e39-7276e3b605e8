/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：SealPrivilegeUpdateRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.seal.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/1/917:55]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.footstone.user.service.model.seal.model.SealRoleModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类名：SealPrivilegeUpdateRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2019/1/917:55]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealPrivilegeUpdateRequest extends ToString {

    @ApiModelProperty(value = "权限操作标识")
    private String targetClassKey;

    @ApiModelProperty(value = "权限分类")
    private String operationPermit;

    @ApiModelProperty(value = "要授权的角色列表")
    private List<SealRoleModel> grantRoleList;

    @ApiModelProperty(value = "要取消授权的角色列表")
    private List<SealRoleModel> revokeRoleList;
}
