package com.timevale.footstone.user.service.model.account.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务授权响应参数
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RpcApplyBizAuthResponse extends ToString {

    @ApiModelProperty(value = "权授URL短链")
    private String bizAuthShortUrl;

    @ApiModelProperty(value = "权授URL长链")
    private String bizAuthUrl;

    @ApiModelProperty(value = "授权ID")
    private String bizAuthFlowId;

    @ApiModelProperty(value = "授权失效时间")
    private long bizAuthExpired;

    @ApiModelProperty(value = "流程模式")
    private String bizAuthFlowMode;

}