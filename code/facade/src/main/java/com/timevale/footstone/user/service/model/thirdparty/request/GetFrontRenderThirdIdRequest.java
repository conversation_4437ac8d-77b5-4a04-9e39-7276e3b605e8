package com.timevale.footstone.user.service.model.thirdparty.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 根据部门id列表和成员id列表，获取需要前端组件渲染的数据 请求类
 *
 * <AUTHOR>
 * @since 2025/5/8 17:41
 **/
@Data
public class GetFrontRenderThirdIdRequest extends ToString {
    /**
     * e签宝部门id列表
     */
    @Size(max = 200, message = "部门一批最多支持200个")
    private List<String> departmentIds;
    /**
     * 是否返回部门路径
     * 默认是false
     */
    private Boolean needDepartmentPath;
    /**
     * e签宝个人账号oid列表
     */
    @Size(max = 200, message = "成员一批最多支持200个")
    private List<String> accountIds;
}
