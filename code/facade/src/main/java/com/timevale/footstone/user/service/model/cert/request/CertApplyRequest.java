/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：CertApplyRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.cert.request <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/6/2715:36]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类名：CertApplyRequest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/6/2715:36]创建类 by flh
 * <AUTHOR>
 */
@Data
public class CertApplyRequest extends ToString {

    @ApiModelProperty(value = "证书有效期，默认一年")
    private String certTime;
}