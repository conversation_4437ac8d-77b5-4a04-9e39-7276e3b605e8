package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> on 2019-01-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifyChildConfigBaseModel {

    @ApiModelProperty(value = "通知场景key：" +
            "COMPACT_DRAFT, " +
            "COMPACT_DRAW, " +
            "COMPACT_FINISH, " +
            "COMPACT_CANCEL, " +
            "COMPACT_REJECT, " +
            "COMPACT_SIGN, " +
            "COMPACT_VALIDITY," +
            "支持扩展")
    private String key;



    @ApiModelProperty(value = "是否开启")
    private Boolean open;

}
