package com.timevale.footstone.user.service.enums;

/**
 * 本地配置语言偏好枚举
 * <AUTHOR>
 * @since 2023/3/7 20:54
 */
public enum LocalConfigLang {

    ZH_CN("简体中文","zh-CN"),
    EN_US("英语(美国)","en-US"),
    ;

    private String title;
    private String code;

    LocalConfigLang(String title, String code) {
        this.title = title;
        this.code = code;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCode() {
        return code;
    }




    public static boolean valid(String code){
        LocalConfigLang lang = getInstance(code);
        if(lang != null){
            return true;
        }
        return false;
    }

    public static boolean validBad(String code){
         return !valid(code);
    }

    public static LocalConfigLang getInstance(String  code){
        for(LocalConfigLang lang : LocalConfigLang.values()){
            if(lang.getCode().equals(code)){
                return lang;
            }
        }

        return null;
    }

    public String getTitle() {
        return title;
    }
}
