package com.timevale.footstone.user.service.model.rules.request;

import com.timevale.footstone.user.service.model.rules.request.bean.UrlBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/5
 */
@Data
@NoArgsConstructor
public class AddRulesRequest extends UrlBaseRequest {

    @ApiModelProperty(value = "资源id", required = true)
    private String resourceId;

    @ApiModelProperty(value = "资源类型", required = true)
    private String resourceType;

    @ApiModelProperty(value = "模板key", required = true)
    private String templateKey;

    @ApiModelProperty(value = "角色类型", required = true)
    private String roleKey;

    @Deprecated
    @ApiModelProperty(value = "自动落章")
    private Boolean autoFall;

    @ApiModelProperty(value = "落章方式，0-手动落章，1-免意愿且自动落章，2-免意愿且手动落章， 默认手动落章")
    private Integer fallType;

    @ApiModelProperty(value = "授权范围列表(模板id/ALL/HR/FINANCE)", required = true)
    private Set<String> scopeList;

    @ApiModelProperty(value = "通知配值(默认开启)", required = false)
    private Boolean notifySetting = true;

    @ApiModelProperty(value = "授权人", required = true)
    private String granter;

    @ApiModelProperty(value = "授权方名称", required = true)
    private String granterName;

    @ApiModelProperty(value = "授权方证件号", required = true)
    private String granterCode;

    @ApiModelProperty(value = "被授权对象", required = false)
    private String grantedUser;

    @ApiModelProperty(value = "被授权对象证件号", required = false)
    private String grantedUserCode;

    @ApiModelProperty(value = "被授权对象", required = false)
    private String grantedUserName;

    @ApiModelProperty(value = "生效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "生效时间不能为空")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "失效时间不能为空")
    private Long expireTime;

    @ApiModelProperty(value = "失效原因", required = true)
    private String expireReason;

    @ApiModelProperty(value = "授权签署回调地址", required = false)
    private String grantRedirectUrl;

    /** @link com.timevale.footstone.user.service.enums.GrantTypeEnum#getType() */
    @ApiModelProperty(value = "授权类型，1-企业内，2-企业间，默认1", required = false)
    private Integer grantType;

    @ApiModelProperty(value = "文件系统fileKey")
    private String fileKey;

    @ApiModelProperty(value = "文件名称")
    private String fileName;
}
