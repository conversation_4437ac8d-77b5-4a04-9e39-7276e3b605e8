package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/11/1
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class Org  extends ToString {

  @ApiModelProperty(value = "企业名称", required = true)
  private String name;

  @ApiModelProperty(value = "创建人id",required = false)
  private String creator;

  @ApiModelProperty(value = "属性-企业法人名称")
  private String orgLegalName;

  @ApiModelProperty(value = "属性-企业法人证件号")
  private String orgLegalIdNumber;

  @ApiModelProperty("属性-企业法人证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
  private String orgLegalIdType;
}