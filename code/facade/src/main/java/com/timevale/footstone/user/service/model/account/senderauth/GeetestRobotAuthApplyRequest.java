package com.timevale.footstone.user.service.model.account.senderauth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** <AUTHOR> on 2019-08-15 */
@Data
public class GeetestRobotAuthApplyRequest {

    @ApiModelProperty(value = "手机号/邮箱等", required = true)
    private String principal;

    @ApiModelProperty(value = "环境信息", required = true)
    private String data;

    @ApiModelProperty(value = "客户端类型：h5/web/native/unknown", required = true)
    private String clientType;

}
