/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：CertApplyWithoutAccountRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.cert.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/7/1614:39]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** */
@Data
public class OrgStandardCertApplyAuthenticationResponse extends ToString {

  @ApiModelProperty(value = "协议是否有效")
  private String authId;

  @ApiModelProperty(value = "申请协议告知内容")
  private String authUrl;


  @ApiModelProperty(value = "业务类型")
  private String authBizType;


  @ApiModelProperty(value = "认证失效时间")
  private long authExpired;

  @ApiModelProperty(value = "认证渠道\n" + "\n" + "identity : 实名\n" + "\n" + "will : 意愿")
  private String authSourceType;
}
