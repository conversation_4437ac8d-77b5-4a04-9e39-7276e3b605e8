package com.timevale.footstone.user.service.model.account.response;


import com.timevale.footstone.user.service.model.account.mods.OrganCategoryModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-11-25
 */
@Data
public class OrganConfigGetResponse {

    @ApiModelProperty("企业配置")
    private List<OrganCategoryModel> configs;
}
