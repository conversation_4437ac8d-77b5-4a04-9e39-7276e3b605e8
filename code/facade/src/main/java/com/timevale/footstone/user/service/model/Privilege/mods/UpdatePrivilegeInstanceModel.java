package com.timevale.footstone.user.service.model.Privilege.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
public class UpdatePrivilegeInstanceModel extends ToString {

    @ApiModelProperty(value = "权限操作标识")
    private String targetClassKey;

    @ApiModelProperty(value = "权限分类")
    private String operationPermit;

    @ApiModelProperty(value = "资源ID")
    private String targetClassId;
}
