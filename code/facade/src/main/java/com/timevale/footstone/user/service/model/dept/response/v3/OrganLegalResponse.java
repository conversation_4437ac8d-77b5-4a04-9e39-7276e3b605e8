package com.timevale.footstone.user.service.model.dept.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

public class OrganLegalResponse extends ToString {


    @ApiModelProperty("法人oid")
    private String ouid;

    @ApiModelProperty("法人accountUid")
    private String accountUid;

    @ApiModelProperty("企业d")
    private String guid;

    @ApiModelProperty("法人证件号")
    private String credentialNo;

    @ApiModelProperty("法人证件类型")
    private String credentialType;

    @ApiModelProperty("法人名字")
    private String legalName;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("是否企业成员")
    private Boolean organMember;


    public String getOuid() {
        return ouid;
    }

    public void setOuid(String ouid) {
        this.ouid = ouid;
    }

    public String getAccountUid() {
        return accountUid;
    }

    public void setAccountUid(String accountUid) {
        this.accountUid = accountUid;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = credentialNo;
    }

    public String getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(String credentialType) {
        this.credentialType = credentialType;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Boolean getOrganMember() {
        return organMember;
    }

    public void setOrganMember(Boolean organMember) {
        this.organMember = organMember;
    }
}
