package com.timevale.footstone.user.service.model.account.request.changename;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @DATE 2024/6/20 11:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChangeNameRecordIdRequest extends ToString {
    
    /**
     * 更名流程ID
     */
    @ApiModelProperty(value = "更名流程ID", required = true)
    private String changeNameRecordId;
}
