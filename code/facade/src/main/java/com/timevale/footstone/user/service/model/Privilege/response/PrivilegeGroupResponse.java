package com.timevale.footstone.user.service.model.Privilege.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17 11:23
 */

@Data
public class PrivilegeGroupResponse extends ToString {


    @ApiModelProperty(value =   "分组列表")
    private List<PrivilegeGroupModel> privilegeGroupModels;
}
