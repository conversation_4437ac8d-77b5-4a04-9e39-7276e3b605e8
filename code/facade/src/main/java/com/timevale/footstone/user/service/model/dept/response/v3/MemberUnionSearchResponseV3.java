package com.timevale.footstone.user.service.model.dept.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MemberUnionSearchResponseV3  extends ToString {

    @ApiModelProperty(value = "分支机构列表")
    private List<DeptBaseResponseV3> unitList;

    @ApiModelProperty(value = "部门列表")
    private List<DeptBaseResponseV3> deptList;

    @ApiModelProperty(value = "成员列表")
    private List<DeptMemberBaseResponseV3> memberList;
}
