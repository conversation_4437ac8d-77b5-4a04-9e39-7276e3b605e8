package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/18 22:55
 */
@Data
public class OrgIdAndNameResponse extends ToString {

  @ApiModelProperty(value = "组织OID")
  private String orgId;

  @ApiModelProperty(value = "组织名称")
  private String orgName;

}
