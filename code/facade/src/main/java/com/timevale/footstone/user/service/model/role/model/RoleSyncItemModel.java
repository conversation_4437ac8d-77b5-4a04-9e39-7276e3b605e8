package com.timevale.footstone.user.service.model.role.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/17 14:58
 */
@Data
public class RoleSyncItemModel extends ToString {

  @ApiModelProperty(value = "角色ID")
  @NotEmpty(message = "角色ID为空")
  private String roleId;

  @ApiModelProperty(value = "角色名称")
  @NotEmpty(message = "角色名称为空")
  private String name;

}
