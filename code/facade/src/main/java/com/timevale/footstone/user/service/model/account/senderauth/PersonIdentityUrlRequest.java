package com.timevale.footstone.user.service.model.account.senderauth;

import com.timevale.footstone.identity.common.service.request.scene.webpage.IndividualIdentityAuthUrlRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PersonIdentityUrlRequest  {


    @ApiModelProperty(value = "人机校验数据")
    private GeetestRobotAuthVerifyModel robotModel;


    @ApiModelProperty(value = "是否进行极验人机校验", example = "false")
    private boolean geetestAuth=true;

    @ApiModelProperty(value = "账号找回请求参数")
    private FindAccountRequest individualIdentityAuthUrlRequest;
}
