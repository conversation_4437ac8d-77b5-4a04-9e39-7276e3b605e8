package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> on 2019-01-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifyConfigModel {

    @ApiModelProperty(value = "业务类型：" +
            "COMPACT_DRAFT, " +
            "COMPACT_DRAW, " +
            "COMPACT_FINISH, " +
            "COMPACT_CANCEL, " +
            "COMPACT_REJECT, " +
            "COMPACT_SIGN, " +
            "COMPACT_VALIDITY," +
            "EMAIL_ALL," +
            "MOBILE_ALL," +
            "支持扩展")
    private String category;

    @ApiModelProperty(value = "天数")
    private Integer day;

    @ApiModelProperty(value = "天数类型 提前-BEFORE 延后-AFTER")
    private String dayMode;

    @ApiModelProperty(value = "触发类型 主动/被动 立即/延后")
    private Set<String> triggerModes;


    @ApiModelProperty(value = "是否开启")
    private Boolean open;

    @ApiModelProperty(value = "通知方式：MOBILE、EMAIL,支持扩展")
    private Set<String> actives = new HashSet<>();


    @ApiModelProperty(value = "业务配置")
    private List<NotifyChildConfigModel> scenes;

    @ApiModelProperty(value = "指定优先通知通道")
    private String appoint;

    public NotifyConfigModel(String category, Integer day, String dayMode, Set<String> triggerModes, Boolean open, Set<String> actives) {
        this.category = category;
        this.day = day;
        this.dayMode = dayMode;
        this.triggerModes = triggerModes;
        this.open = open;
        this.actives = actives;
    }
}
