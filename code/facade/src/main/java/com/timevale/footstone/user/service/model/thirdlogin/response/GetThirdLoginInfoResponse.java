package com.timevale.footstone.user.service.model.thirdlogin.response;

import com.timevale.footstone.user.service.model.thirdlogin.mods.ThirdLoginThirdInfoDTO;
import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-05-09 获取用户三方登录信息响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GetThirdLoginInfoResponse extends ToString {
    /** 三方登陆的三方信息 */
    private ThirdLoginThirdInfoDTO thirdInfo;
}
