package com.timevale.footstone.user.service.model.account.request.v2;

import com.timevale.footstone.user.service.model.enums.OAuthUserModeEnum;
import com.timevale.mandarin.base.util.BooleanUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version $ Id: SilentRegisterRequest.java, v0.1 2021年07月14日 14:04 WangYuWu $
 */
@Data
public class SilentRegisterRequest {

    //授权任务ID
    private String paramsKey;

    /**
     * 授权模式：0-个人；1-企业
     * @see OAuthUserModeEnum
     */
    private Integer mode;



    @ApiModelProperty(value = "是否同意印章授权")
    private boolean agreeSealAuth;

    /**
     * 对接方开发者自定义参数
     * 授权完成后，重定向到redirectUrl时会原样回传给开发者。
     */
    @ApiModelProperty(value = "开发者自定义参数")
    private String state;

    /**
     * 三方appId
     */
    @NotEmpty(message = "appId不能为空")
    @ApiModelProperty(value = "项目ID", required = true)
    private String appId;

    /**
     * 授权范围（比较少，目前可枚举）
     * 用户=get_user_info，
     * 印章=op_seal，
     * 签署=op_sign,
     * 印章签署=use_seal
     */
    @NotEmpty(message = "scope不能为空")
    @ApiModelProperty(value = "授权范围", required = true)
    private String scope;

    /**
     * 授权印章ID
     * 默认是系统默认章
     */
    @ApiModelProperty(value = "授权印章ID")
    private String sealId;

    /**
     * 邮箱地址
     * 或者
     * 手机号码
     */
    @NotEmpty(message = "account不能为空")
    @ApiModelProperty(value = "被授权账号", required = true)
    private String account;

    /**
     * 授权业务类型
     */
    @NotEmpty(message = "bizType不能为空")
    @ApiModelProperty(value = "授权类型")
    private String bizType;

    /**
     * 授权结果页
     */
    private String resultUrl;

    /**
     * 调用方期望的开发者通知地址
     * 授权/认证完成后通知三方后台的https地址
     */
    @ApiModelProperty(value = "开发者通知地址")
    private String notifyUrl;

    /**
     * 调用方期望的重定向地址
     */
    @NotEmpty(message = "redirectUrl不能为空")
    @ApiModelProperty(value = "授权完成后跳转页面")
    private String redirectUrl;

    /**
     * 授权类型
     * 默认值就是code
     */
    @NotEmpty(message = "responseType不能为空")
    @ApiModelProperty(value = "授权类型")
    private String responseType;

    /**
     * 被授权主体信息
     */
    @ApiModelProperty(value = "被授权主体")
    private AuthorizedSubject authSubject;

    /**
     * 个人信息详情
     */
    @ApiModelProperty(value = "个人信息详情")
    private AuthIndividualInfo individualInfo;


    /**
     * 实名认证定制化参数
     */
    @ApiModelProperty(value = "实名认证定制化参数")
    private AuthConfigParam authConfigParam;

    public  boolean isForceRealname(){
        if(authConfigParam!=null && BooleanUtils.isTrue(authConfigParam.getForceRealname())){
            return true;
        }
        return false;
    }
}
