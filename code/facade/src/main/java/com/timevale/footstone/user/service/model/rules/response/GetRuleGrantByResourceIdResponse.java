package com.timevale.footstone.user.service.model.rules.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/12/6
 */
@Data
@NoArgsConstructor
public class GetRuleGrantByResourceIdResponse {

    @ApiModelProperty(value = "授权清单")
    private List<GetRuleGrantByGrantIdResponse> items = Collections.EMPTY_LIST;

}
