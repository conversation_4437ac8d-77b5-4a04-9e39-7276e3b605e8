package com.timevale.footstone.user.service.model;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2018-11-23
 */
public class ApiPageResult<T> {

    @ApiModelProperty("分页数据")
    private List<T> result;

    @ApiModelProperty("总量")
    private Integer total;

    public List<T> getResult() {
        return result;
    }

    public void setResult(List<T> result) {
        this.result = result;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public static <T> ApiPageResult<T> empty(){
        ApiPageResult<T> apiPageResult = new ApiPageResult<>();
        apiPageResult.setResult(new ArrayList<>());
        apiPageResult.setTotal(0);
        return apiPageResult;
    }
}
