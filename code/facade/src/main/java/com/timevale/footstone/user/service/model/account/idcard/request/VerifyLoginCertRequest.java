package com.timevale.footstone.user.service.model.account.idcard.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> on 2018-12-19
 */
public class VerifyLoginCertRequest extends ToString {

    @ApiModelProperty(value = "证书上下文", required = true)
    private String context;

    @ApiModelProperty(value = "签名值", required = true)
    private String signature;

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
