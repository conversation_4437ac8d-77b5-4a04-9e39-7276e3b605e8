/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：ContactListRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.contact.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:06]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.contact.request;

import com.timevale.footstone.user.service.model.ApiPager;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类名：ContactListRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:06]创建类 by flh
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContactListRequest extends ToString {

    @ApiModelProperty("分页参数")
    private ApiPager page;

    @ApiModelProperty("用户名称")
    private String name;

    @ApiModelProperty("用户标识，邮箱或手机号")
    private String uniqueId;

    @ApiModelProperty("所属企业")
    private String affliation;

    @ApiModelProperty("是否需要联系人账号id")
    private Boolean needAccountId;
}
