package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.footstone.user.service.model.account.mods.Org;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/11/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgCreateByThirdPartyUserId extends Org {

    @ApiModelProperty(value = "第三方平台的用户id", required = true)
    private String thirdPartyUserId;

    @ApiModelProperty("第三方平台的用户类型")
    private String thirdPartyUserType;

    @ApiModelProperty(value = "证件号")
    private String idNumber;

    @ApiModelProperty("证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String idType;

    @ApiModelProperty(value = "实名证据点")
    private String realnameEvidencePointId;

    public String getIdType() {
        return StringUtils.isNotBlank(idType) ? idType : BuiltinCredentials.CRED_ORG_USCC;
    }
}
