package com.timevale.footstone.user.service.enums;

import com.timevale.footstone.user.service.exception.FootstoneUserErrors.EndpointError;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors.InvalidParamFormat;
import com.timevale.token.service.enums.EquipEnum;
import com.timevale.token.service.enums.SourceCodeEnum;

import java.util.Arrays;
import java.util.Objects;

public enum EquipMappingEnum {
    PC(EquipEnum.PC.name(), SourceCodeEnum.DEFAULT_BROWERS.name()),
    H5(EquipEnum.MOBILE.name(), SourceCodeEnum.H5.name()),
    ALIPAY(EquipEnum.MOBILE.name(), SourceCodeEnum.ALI_PAY.name()),
    WE_CHART(EquipEnum.MOBILE.name(), SourceCodeEnum.WE_CHART.name()),
    DING_TALK_INNER(EquipEnum.MOBILE.name(), SourceCodeEnum.DING_TALK_INNER.name()),
    DING_TALK_EXTERNAL(EquipEnum.MOBILE.name(), SourceCodeEnum.DING_TALK_EXTERNAL.name()),
    ESIGN_APP(EquipEnum.MOBILE.name(), SourceCodeEnum.ESIGN_APP.name()),
    QR(EquipEnum.PC.name(), SourceCodeEnum.ESIGN_APP.name()),
    SANDBOX(EquipEnum.PC.name(), SourceCodeEnum.DEFAULT_BROWERS.name()),
    FEI_SHU_PC(EquipEnum.PC.name(), SourceCodeEnum.FEI_SHU.name()),
    FEI_SHU_MOBILE(EquipEnum.MOBILE.name(), SourceCodeEnum.FEI_SHU.name()),
    WE_WORK_PC(EquipEnum.PC.name(), SourceCodeEnum.WE_WORK.name()),
    WE_WORK_MOBILE(EquipEnum.MOBILE.name(), SourceCodeEnum.WE_WORK.name()),
    DOUYIN_LOAN(EquipEnum.MOBILE.name(), SourceCodeEnum.DOUYIN_LOAN.name()),
    KINGDEE_K3EE_PC(EquipEnum.MOBILE.name(), SourceCodeEnum.KINGDEE_K3EE.name()),
    /**
     * 企微扫码授权登录
     */
    PC_WE_WORK_SCAN_LOGIN(EquipEnum.PC.name(), SourceCodeEnum.WE_WORK_SCAN_LOGIN.name());

    private String equipId;
    private String sourceCode;

    EquipMappingEnum(String equipId, String sourceCode) {
        this.equipId = equipId;
        this.sourceCode = sourceCode;
    }

    public String getEquipId() {
        return equipId;
    }

    public void setEquipId(String equipId) {
        this.equipId = equipId;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    /**
     * 替换valueOf,手动指定异常
     *
     * @param name
     * @return
     */
    public static EquipMappingEnum fromString(String name) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.name(), name))
                .findFirst()
                .orElseThrow(EndpointError::new);
    }

    /**
     * 字符串转枚举， 不会抛出异常
     *
     * @param name
     * @return
     */
    public static EquipMappingEnum convertByName(String name) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.name(), name))
                .findFirst()
                .orElse(null);
    }

}
