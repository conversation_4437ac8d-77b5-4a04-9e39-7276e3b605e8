package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.model.constants.BuiltinContacts;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccGetBackInfoResponse {
    @ApiModelProperty(value = "登录凭证对应的oid")
    private String oid;

    @ApiModelProperty(value = "用户头像")
    private String head;
    /**
     * @see BuiltinContacts
     */
    @ApiModelProperty(value = "登录凭证类型")
    private String idcardType;

    @ApiModelProperty(value = "登录凭证（脱敏）")
    private String idcard;
}
