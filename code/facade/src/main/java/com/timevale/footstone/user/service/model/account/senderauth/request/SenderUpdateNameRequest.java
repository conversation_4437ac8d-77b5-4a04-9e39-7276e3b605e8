package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2024/11/14 11:00
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class SenderUpdateNameRequest extends ServiceIdModel {


    private String name;




}
