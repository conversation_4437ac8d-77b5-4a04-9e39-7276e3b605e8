package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 类名：GenerateAgreementApplyModel
 * 功能说明：
 *
 * <AUTHOR>
 * @DATE 2023/3/17 13:40
 */
@Data
public class GenerateAgreementTemplateRequest extends ToString {
    /**
     * 用户的oid
     */
    @ApiModelProperty(required = true,value = "用户oid")
    private List<String> oidList;
    /**
     * 对应的appId
     */
    @ApiModelProperty(required = true,value = "应用id")
    private String appId;

    /**
     * 是否在数据库保存协议
     */
    @ApiModelProperty(required = false,value = "是否在数据库保存协议,默认为是")
    private boolean enSaved = true;


}
