package com.timevale.footstone.user.service.model.dept.mods.v3;

import com.timevale.footstone.user.service.model.dept.request.v3.ImportDeptRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/4/14 16:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchCreateDeptsV3FailedModel {


  public static final String ORG_NAME_EMPTY = "组织机构名称不能为空";
  public static final String DEPT_FULL_NAME_EMPTY = "部门名称不能为空";
  public static final String PATH_TOO_LONG = "当前创建的部门深度超过最大级别，无法创建";
  public static final String DEPT_NAME_ERROR = "部门名称只能包含字母、数字、汉字长度不能超过20";
  public static final String DEPT_NAME_NOT_ALLOWED = "不允许部门名称为所有部门或未分配部门";
  public static final String DATA_SYNC_ORG_NOT_ALLOWED = "管理员已开启三方组织同步，主企业不支持导入部门";

  private String orgName;

  private String leaderName;

  private String leaderAccount;

  private String deptFullName;

  private String errorReason;

  public static BatchCreateDeptsV3FailedModel orgNameIsEmpty(ImportDeptRequest deptRequest) {
    return BatchCreateDeptsV3FailedModel.builder().orgName(deptRequest.getOrgName()).leaderName(deptRequest.getLeaderName())
        .leaderAccount(deptRequest.getLeaderAccount()).deptFullName(deptRequest.getDeptFullName()).errorReason(ORG_NAME_EMPTY).build();
  }

  public static BatchCreateDeptsV3FailedModel deptFullNameIsEmpty(ImportDeptRequest deptRequest) {
    return BatchCreateDeptsV3FailedModel.builder().orgName(deptRequest.getOrgName()).leaderName(deptRequest.getLeaderName())
        .leaderAccount(deptRequest.getLeaderAccount()).deptFullName(deptRequest.getDeptFullName()).errorReason(DEPT_FULL_NAME_EMPTY).build();
  }

  public static BatchCreateDeptsV3FailedModel deptDeepTooLong(ImportDeptRequest deptRequest) {
    return BatchCreateDeptsV3FailedModel.builder().orgName(deptRequest.getOrgName()).leaderName(deptRequest.getLeaderName())
        .leaderAccount(deptRequest.getLeaderAccount()).deptFullName(deptRequest.getDeptFullName()).errorReason(PATH_TOO_LONG).build();
  }

  public static BatchCreateDeptsV3FailedModel deptNameFormatError(ImportDeptRequest deptRequest) {
    return BatchCreateDeptsV3FailedModel.builder().orgName(deptRequest.getOrgName()).leaderName(deptRequest.getLeaderName())
        .leaderAccount(deptRequest.getLeaderAccount()).deptFullName(deptRequest.getDeptFullName()).errorReason(DEPT_NAME_ERROR).build();
  }

  public static BatchCreateDeptsV3FailedModel deptNameNotAllowed(ImportDeptRequest deptRequest) {
    return BatchCreateDeptsV3FailedModel.builder().orgName(deptRequest.getOrgName()).leaderName(deptRequest.getLeaderName())
        .leaderAccount(deptRequest.getLeaderAccount()).deptFullName(deptRequest.getDeptFullName()).errorReason(DEPT_NAME_NOT_ALLOWED).build();
  }

  public static BatchCreateDeptsV3FailedModel dataSyncOrgNotImportDept(ImportDeptRequest deptRequest) {
    return BatchCreateDeptsV3FailedModel.builder().orgName(deptRequest.getOrgName()).leaderName(deptRequest.getLeaderName())
            .leaderAccount(deptRequest.getLeaderAccount()).deptFullName(deptRequest.getDeptFullName()).errorReason(DATA_SYNC_ORG_NOT_ALLOWED).build();
  }
}
