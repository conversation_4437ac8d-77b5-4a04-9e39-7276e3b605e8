package com.timevale.footstone.user.service.model.account.response.v3;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账号身份信息绑定/换绑页面响应参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountBindUrlResponse extends ToString {

    @ApiModelProperty(value = "账号绑定长链接，有效期7天")
    private String accountBindUrl;
}