package com.timevale.footstone.user.service.model.account.idcard.response;

import com.timevale.footstone.user.service.model.account.idcard.model.CertInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> on 2018-12-20
 */
@Data
public class ListCertsResponse {

    @ApiModelProperty(value = "证书列表", required = true)
    private List<CertInfoModel> certs;
}
