package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.account.service.model.service.biz.builder.acc.BizICUserCreateInputBuilder;
import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.footstone.user.service.assertt.AssertAccountSupport;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.model.Validater;
import com.timevale.footstone.user.service.model.account.ToCreateBuilder;
import com.timevale.footstone.user.service.model.account.mods.PersonModel;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR> on 2019-01-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonAddByIdRequest extends PersonModel implements Validater , ToCreateBuilder {

  @ApiModelProperty(value = "第三方平台的用户id", required = true)
  private String thirdPartyUserId;

  @ApiModelProperty(value = "第三方平台的用户类型，阿里云开放服务为aliyun")
  private String thirdPartyUserType;

  @ApiModelProperty("属性-证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
  private String idType;

  @ApiModelProperty("属性-证件号")
  private String idNumber;

  public String getIdType() {
    if (StringUtils.isNotBlank(idNumber) && StringUtils.isBlank(idType)) {
      return BuiltinCredentials.CRED_PSN_CH_IDCARD;
    }
    return idType;
  }

  @Override
  public BizICUserCreateInputBuilder conv(BizICUserCreateInputBuilder in) {
    // 2024/10/30  老字段在代码里显示赋值
    return in;
  }

  @Override
  public void valid() {

    AssertSupport.assertNotnull(
        this.getThirdPartyUserId(),
        new FootstoneUserErrors.MissingArgumentsWith("thirdPartyUserId"));

    AssertAccountSupport.assertPsnName(this.getName());
  }
}
