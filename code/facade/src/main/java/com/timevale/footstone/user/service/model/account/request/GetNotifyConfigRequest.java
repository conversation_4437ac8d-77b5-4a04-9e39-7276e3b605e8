package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * <AUTHOR> on 2019-01-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetNotifyConfigRequest extends ToString {

    @ApiModelProperty(value = "业务类型：COMPACT_SIGN, COMPACT_VALIDITY, MOBILE_ALL, EMAIL_ALL")
    private Set<String> category;
}
