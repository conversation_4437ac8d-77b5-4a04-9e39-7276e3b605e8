package com.timevale.footstone.user.service.model.account.response.changename;

import com.timevale.footstone.user.service.model.account.mods.changename.ChangeNameUrlContextInfo;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/6/20 11:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChangeOrgNameByIdResponse extends ToString {
    

    @ApiModelProperty(value = "更名流程配置信息")
    private ChangeNameUrlContextInfo changeNameUrlContextInfo;

    /**
     * 扩展字段 用于以后透传使用
     */
    @ApiModelProperty(value = "扩展字段 用于以后透传使用")
    private Map<String, String> extendMap;
}
