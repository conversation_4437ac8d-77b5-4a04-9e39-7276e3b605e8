/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：ContactImportRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.contact.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:19]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.contact.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 类名：ContactImportRequest.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/200:19]创建类 by flh
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContactImportRequest extends ToString {

    @ApiModelProperty(value = "批量导入文件的fileKey")
    @NotBlank(message = "导入文件不能为空")
    private String fileKey;
}
