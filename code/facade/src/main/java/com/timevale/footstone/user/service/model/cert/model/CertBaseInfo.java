package com.timevale.footstone.user.service.model.cert.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年09月07日 15:40:00
 */
@Data
public class CertBaseInfo {

    @ApiModelProperty(value = "证书id")
    private String certId;

    @JsonIgnore
    @ApiModelProperty(value = "证书名称")
    private String certName;

    @ApiModelProperty(value = "申领成功的证书通道名称")
    private String caRoute;

    @ApiModelProperty(value = "证书序列号")
    private String certSn;

    @ApiModelProperty(value = "证书有效期开始时间")
    private long effectiveFrom;

    @ApiModelProperty(value = "证书有效期结束时间")
    private long effectiveTo;
}
