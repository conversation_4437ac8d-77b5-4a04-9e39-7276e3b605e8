package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2019-01-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RealnameResultModel extends ToString {

    @ApiModelProperty(value = "认证是否通过")
    private Boolean passed;

    @ApiModelProperty(value = "实名认证状态：PROCESSING - 认证中、SUCCESS - 认证成功、FAIL - 认证失败")
    private String status;

    @ApiModelProperty(value = "实名类型，例如银行四要素、芝麻信用刷脸")
    private String type;
}
