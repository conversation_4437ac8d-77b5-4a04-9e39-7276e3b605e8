package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.constants.BuiltinCredentials;
import com.timevale.footstone.user.service.model.account.mods.PersonModel;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-01-19 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonAddByCredRequest extends PersonModel {

    @ApiModelProperty(
            value = "证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String idType;

    @ApiModelProperty(value = "证件号")
    private String idNumber;

    public String getIdType() {
        return StringUtils.isNotBlank(idType) ? idType : BuiltinCredentials.CRED_PSN_CH_IDCARD;
    }
}
