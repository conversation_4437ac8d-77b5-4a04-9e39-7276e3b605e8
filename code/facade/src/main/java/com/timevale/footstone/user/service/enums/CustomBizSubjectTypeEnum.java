package com.timevale.footstone.user.service.enums;

/**
 * <AUTHOR>
 * @since 2025/4/24 11:55
 */
public enum CustomBizSubjectTypeEnum {

    PERSON,
    ORG,
    ;

    CustomBizSubjectTypeEnum() {
    }


    public boolean isPerson(){

        return this == PERSON;
    }

    public static CustomBizSubjectTypeEnum codeOfAndDef(String code) {
        return codeOf(code) == null ? PERSON : codeOf(code);
    }

    public static CustomBizSubjectTypeEnum codeOf(String code) {
        for (CustomBizSubjectTypeEnum value : values())
        {
            if (value.name().equals(code))
            {
                return value;
            }
        }
        return null;
    }
}
