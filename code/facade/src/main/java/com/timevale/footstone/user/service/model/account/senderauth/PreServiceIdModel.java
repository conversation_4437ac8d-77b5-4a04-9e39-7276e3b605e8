package com.timevale.footstone.user.service.model.account.senderauth;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PreServiceIdModel extends ToString {

    @ApiModelProperty(value = "上次事务的id，如没有可不填")
    private String preServiceId;
}
