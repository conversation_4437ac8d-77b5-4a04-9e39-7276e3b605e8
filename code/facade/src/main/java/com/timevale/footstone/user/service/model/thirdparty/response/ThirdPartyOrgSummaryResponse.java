package com.timevale.footstone.user.service.model.thirdparty.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ThirdPartyOrgSummaryResponse extends ToString {

    @ApiModelProperty(value = "部门人数")
    private Integer memberCount;

    @ApiModelProperty(value = "部门数")
    private Integer deptCount;
}
