package com.timevale.footstone.user.service.model.thirdplatform.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

@Data
public class ThirdRelateEnterprisesResponse extends ToString {

    private String tenantKey;

    private String tenantName;

    private String ouid;

    private String name;

    private String creator;

    private Date createTime;

    private String modifier;

    private Date modifyTime;
}
