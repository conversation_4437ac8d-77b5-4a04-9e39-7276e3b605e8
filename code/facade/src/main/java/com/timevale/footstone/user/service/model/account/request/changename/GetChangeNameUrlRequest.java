package com.timevale.footstone.user.service.model.account.request.changename;

import com.timevale.footstone.user.service.model.account.mods.changename.ChangeNameUrlContextInfo;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/6/20 10:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetChangeNameUrlRequest extends ToString {

    @ApiModelProperty(value = "来源bizAppId", required = true)
    private String bizAppId;

    @ApiModelProperty(value = "来源", required = true)
    private String changeNameChannel;
    /**
     * 期望的企业名称
     */
    @ApiModelProperty(value = "期望的企业名称", required = true)
    private String expectOrgName;

    @ApiModelProperty(value = "企业证件号", required = true)
    private String orgCertNo;

    /**
     * 外部来源ID  比如实名流程ID，签署流程ID
     */
    @ApiModelProperty(value = "外部来源id")
    private String outBizId;



    @ApiModelProperty(value = "更名流程配置信息")
    private ChangeNameUrlContextInfo changeNameUrlContextInfo;


    /**
     * 扩展字段 用于以后透传使用
     */
    @ApiModelProperty(value = "扩展字段 用于以后透传使用")
    private Map<String, String> extendMap;


}
