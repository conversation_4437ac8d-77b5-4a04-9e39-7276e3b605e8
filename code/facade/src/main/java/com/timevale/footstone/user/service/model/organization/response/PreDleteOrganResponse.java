package com.timevale.footstone.user.service.model.organization.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/10
 */
@Data
@AllArgsConstructor
public class PreDleteOrganResponse {
    @ApiModelProperty(value = "操作人", required = true)
    private String serviceId;

    @ApiModelProperty(value = "签署地址", required = true)
    private String redirectUrl;

    @ApiModelProperty(value = "签署长链地址")
    private String longUrl;
}
