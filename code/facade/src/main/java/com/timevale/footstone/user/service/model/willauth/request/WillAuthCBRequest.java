package com.timevale.footstone.user.service.model.willauth.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/9
 */
@Data
public class WillAuthCBRequest {

    @ApiModelProperty("标记该通知的业务类型")
    private String action;

    @ApiModelProperty("意愿认证用户ID")
    private String accountId;

    @ApiModelProperty("意愿子任务ID")
    private String willAuthId;

    @ApiModelProperty("是否成功")
    private Boolean success;

    @ApiModelProperty("意愿任务业务ID")
    private String bizId;

    @ApiModelProperty("发送消息时间（毫秒时间）")
    private Date timestamp;


}
