package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: sicheng
 * @since: 2020-06-16 20:40
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AddrWhiteListResponse extends ToString {

    @ApiModelProperty(value = "登录地", required = true)
    private String addr;

    @ApiModelProperty(value = "最近修改时间", required = true)
    private Long modifyTime;
}
