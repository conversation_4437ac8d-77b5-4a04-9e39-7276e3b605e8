package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.account.mods.PersonModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "个人账号批量基础响应")
public class AccountBasePlusResponse<T extends PersonModel> extends AccountBaseResponse {


    @ApiModelProperty(value = "创建时传入的账号信息")
    private T personModel;
    public AccountBasePlusResponse(String accountId) {
        super(accountId);
    }

    public AccountBasePlusResponse(String accountId, T personModel) {
        super(accountId);
        this.personModel = personModel;
    }


}
