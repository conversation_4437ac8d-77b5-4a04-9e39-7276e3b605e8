package com.timevale.footstone.user.service.model.account.mods;

import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> on 2019-01-10
 */
@Data
public class IdcardCredentialsDetail {

    @ApiModelProperty(value = "证件类型，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String type;

    @ModelFieldDecoration(DecorationType.CREDENTIALS)
    @ApiModelProperty(value = "证件号码")
    private String value;
}
