package com.timevale.footstone.user.service.model.account.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 三方组织连接器信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/07/03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ThirdOrgConnectorResponse extends ToString {

    /**
     * 来源
     */
    private String source;

    /**
     * 结果
     */
    private Boolean result;

    /**
     * 同步时间
     */
    private Date syncTime;

}
