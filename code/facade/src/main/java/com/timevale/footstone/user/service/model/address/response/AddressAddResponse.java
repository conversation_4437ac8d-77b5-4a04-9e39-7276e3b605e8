/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：app-parent <br>
 * 文件名：AddressAddResponse.java <br>
 * 包：com.timevale.account.webserver.model.response.address <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2018/12/2323:28]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.address.response;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：AddressAddResponse.java <br>
 * 功能说明： <br>
 * 修改历史： <br>
 * 1.[2018/12/2323:28]创建类 by flh
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AddressAddResponse {

    private String addressId;
}
