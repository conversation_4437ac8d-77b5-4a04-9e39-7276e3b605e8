/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br>
 * 项目名称：footstone-user-parent <br>
 * 文件名：CertApplyWithoutAccountRequest.java <br>
 * 包：com.timevale.footstone.user.service.model.cert.request <br>
 * 描述： <br>
 * 修改历史： <br>
 * 1.[2019/7/1614:39]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.cert.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** */
@Data
public class OrgStandardCertApplyInitResponse extends ToString {

  @ApiModelProperty(value = "协议是否有效")
  private boolean certExist;

  @ApiModelProperty(value = "申请协议告知内容")
  private String certApplyInformContent;

  @ApiModelProperty(
      value =
          "申请协议告知内容类型\n"
              + "\n"
              + "htmlContent=html文本 \n"
              + "htmlUrl=http地址 \n"
              + "pdfUrl=pdf地址 \n"
              + "zeyuanUrl=泽元系统Url.")
  private String certApplyInformContentType;

  @ApiModelProperty(value = "当前企业名称")
  private String orgName;
}
