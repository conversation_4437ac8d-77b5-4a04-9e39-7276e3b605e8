package com.timevale.footstone.user.service.model.third.response;
import lombok.Data;
import java.util.Date;
import lombok.EqualsAndHashCode;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdOrgSyncConnectorInfoResponseV3 extends ToString {

  @ApiModelProperty(value = "来源")
  private String source;

  @ApiModelProperty(value = "是否打开连接器")
  private Boolean isOpen;

  @ApiModelProperty(value = "同步时间")
  private Date syncTime;

  @ApiModelProperty(value = "触发key")
  private String triggerKey;

  @ApiModelProperty(value = "同步模式 COMPATIBLE：兼容模式  MERGE:覆盖同步")
  private String syncMode;

}
