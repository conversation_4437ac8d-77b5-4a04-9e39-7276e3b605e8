/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：UpdateSealNotifyRoleRequest.java <br/>
 * 包：com.timevale.footstone.user.service.model.role.request <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3021:31]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.role.request;

import com.timevale.footstone.user.service.model.role.model.UpdateSealNotifyRoleModel;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类名：UpdateSealNotifyRoleRequest.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3021:31]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UpdateSealNotifyRoleRequest extends ToString {

    @ApiModelProperty(value = "要授权的角色列表")
    private List<UpdateSealNotifyRoleModel> grantRoleList;

    @ApiModelProperty(value = "要取消授权的角色列表")
    private List<UpdateSealNotifyRoleModel> revokeRoleList;

    @ApiModelProperty(value = "要更新授权的印章id")
    private String sealId;
}