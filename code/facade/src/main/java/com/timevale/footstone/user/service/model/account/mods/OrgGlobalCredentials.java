package com.timevale.footstone.user.service.model.account.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2019-02-01 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgGlobalCredentials extends OrgCredentials implements GlobalCredentialGetter {

    @ApiModelProperty(
            value =
                    "全局证件类型，为空或对应证件号不存在时，不设置全局证件信息，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********")
    private String globalIdentity;

    @Override
    public String initGlobalKey() {
        return this.globalIdentity;
    }
}
