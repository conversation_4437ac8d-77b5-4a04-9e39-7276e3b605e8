package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.footstone.user.service.model.account.mods.LoginParams;
import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/** <AUTHOR> on 2019-01-02 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RegisterRequest extends ServiceIdModel {

    @ApiModelProperty(value = "新密码", required = true)
    private String password;

    @ApiModelProperty(value = "密码的编码方式，默认MD5")
    private String encrypter;

    @ApiModelProperty(value = "登录相关参数配置 默认的配值是 每次登录的是不同端")
    private LoginParams loginParams = new LoginParams();

    @ApiModelProperty(
            value = "账号来源参数，详见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=8784839")
    private Map<String, String> source;
}
