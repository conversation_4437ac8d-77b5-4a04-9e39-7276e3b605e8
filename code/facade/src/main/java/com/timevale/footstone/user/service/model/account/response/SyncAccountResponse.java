package com.timevale.footstone.user.service.model.account.response;

import com.timevale.footstone.user.service.model.cert.model.CertBaseInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年09月07日 17:28:00
 */
@Data
public class SyncAccountResponse {
    @ApiModelProperty(value = "账号id")
    private String accountId;

    @ApiModelProperty(value = "证书信息")
    private CertBaseInfo certInfo;
}
