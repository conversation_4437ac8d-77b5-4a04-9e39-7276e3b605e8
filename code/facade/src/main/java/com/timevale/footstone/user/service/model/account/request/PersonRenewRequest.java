package com.timevale.footstone.user.service.model.account.request;

import com.timevale.account.service.model.service.biz.builder.icuser.BizICUserUpdateInputBuilder;
import com.timevale.footstone.user.service.model.account.ToUpdateBuilder;
import com.timevale.footstone.user.service.model.account.mods.PersonAllPropModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.timevale.account.service.model.constants.BuiltinProperty.INFO_NAME;

/** <AUTHOR> on 2019-01-20 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonRenewRequest extends PersonAllPropModel implements ToUpdateBuilder {

    @ApiModelProperty(value = "姓名")
    private String name;

    @Override
    public BizICUserUpdateInputBuilder conv(BizICUserUpdateInputBuilder in) {
        super.conv(in);
        in.buildProperties().add(INFO_NAME, getName());
        return in;
    }
}
