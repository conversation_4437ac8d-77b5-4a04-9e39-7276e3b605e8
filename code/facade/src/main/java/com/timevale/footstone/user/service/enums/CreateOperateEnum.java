package com.timevale.footstone.user.service.enums;

public enum CreateOperateEnum {


    REAL_NAME_AND_JOIN("0","企业已实名，用户已加入企业"),
    REAL_NAME_EXIST_ADMIN_AND_NOT_JOIN("1","企业已实名，用户未加入企业，存在管理员"),
    REAL_NAME_NOT_EXIST_ADMIN_AND_NOT_JOIN("2","企业已实名，用户未加入企业，不存在管理员"),
    NOT_REAL_NAME_AND_JOIN("3","企业未实名，用户加入该企业"),
    CREATE_ORGAN("4","企业不存在，新创建企业"),

    REAL_NAME_EXIST_VIRTUAL_ADMIN_AND_NOT_JOIN("5","企业已实名，用户未加入企业，存在管理员"),
    ;

    CreateOperateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
