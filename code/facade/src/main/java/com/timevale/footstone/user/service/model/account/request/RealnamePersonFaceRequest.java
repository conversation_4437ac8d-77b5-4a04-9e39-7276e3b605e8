package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RealnamePersonFaceRequest extends ToString {

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "身份证号", required = true)
    private String idno;

    @ApiModelProperty("比对原照片fileKey")
    private String photo;

    @ApiModelProperty("比对原照片类型[1:水纹正脸照][2:高清正脸照]")
    private Integer photoType;

    @ApiModelProperty(value = "刷脸类型：腾讯云-1 芝麻信用-2 芝麻信用小程序-3", required = true)
    private Integer faceAuthMode;

    @ApiModelProperty(value = "回调地址", required = true)
    private String callbackUrl;
}
