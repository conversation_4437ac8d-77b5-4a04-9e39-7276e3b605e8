package com.timevale.footstone.user.service.model.account.senderauth.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年05月25日 19:47:00
 */
public class MobileSenderV2Request extends ToString {
    @ApiModelProperty(
            value = "业务类型，首次流程填写",
            allowableValues =
                    "重设密码 - RESET_PWD,"
                            + "注册 - REGISTER, 登录 - LOGIN, 注册并登录 - REGISTER_LOGIN, 原登录凭证验证 - MODIFY_IDCARD_ORIGIN,"
                            + " 新登录凭证验证 - MODIFY_IDCARD_TARGET, 设置登录凭证 - SET_IDCARD, 设置签署密码 - RESET_SIGN_PWD")
    private String bizType;

    @ApiModelProperty(value = "上次流程事务id，非首次流程填写")
    private String preServiceId;
}
