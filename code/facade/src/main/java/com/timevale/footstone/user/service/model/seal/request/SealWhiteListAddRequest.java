package com.timevale.footstone.user.service.model.seal.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SealWhiteListAddRequest {

    @ApiModelProperty(value = "添加原因")
    private Integer reason;
    @ApiModelProperty(value = "企业名称")
    private String orgName;
    @ApiModelProperty(value = "企业oid")
    private String orgGid;
    @ApiModelProperty(value = "创建人")
    private String creator;

    public void valid() {
        AssertSupport.assertTrue(null != reason, new ErrorsBase.MissingArgumentsWith("reason"));
        AssertSupport.assertTrue(StringUtils.isNotBlank(orgName), new ErrorsBase.MissingArgumentsWith("orgName"));
        AssertSupport.assertTrue(StringUtils.isNotBlank(orgGid), new ErrorsBase.MissingArgumentsWith("orgGid"));
    }
}
