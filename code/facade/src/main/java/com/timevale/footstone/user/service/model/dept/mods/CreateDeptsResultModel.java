package com.timevale.footstone.user.service.model.dept.mods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/27
 */
@Data
public class CreateDeptsResultModel {

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "成功条数")
    private Integer success;

    @ApiModelProperty(value = "失败条数")
    private Integer failed;

    @ApiModelProperty(value = "任务状态 R-进行中 F-已完成")
    private String status;

    @ApiModelProperty(value = "失败文件地址")
    private String url;


}
