package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("创建邀请任务参数")
public class CreateInvitationsRequest extends InvitationBaseRequest {
    @ApiModelProperty(value = "被邀请人id", required = true)
    private String inviteeOid;
}
