package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.model.service.biz.rlnm.output.BizAccountRealnameOutput;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserRealNameBaseInfoResponse {

    @ApiModelProperty(value = "ouid")
    private String ouid;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "实名状态")
    private Boolean status;

    @ApiModelProperty(value = "实名时间")
    private Date realnameTime;

    @ApiModelProperty(value = "实名的serviceId")
    private String serviceId;

    @ApiModelProperty(value = "实名证件类型")
    private String type;

    @ModelFieldDecoration(DecorationType.CREDENTIALS)
    @ApiModelProperty(value = "实名证件号")
    private String code;

    public UserRealNameBaseInfoResponse init(
            BizAccountRealnameOutput in, String ouid, String type, String code) {
        setOuid(ouid);
        setGuid(in.getGuid());
        setStatus(RealnameStatus.ACCEPT.equals(in.getStatus()));
        setRealnameTime(in.getModifyTime());
        setServiceId(in.getServiceId());
        setType(type);
        setCode(code);
        return this;
    }
}
