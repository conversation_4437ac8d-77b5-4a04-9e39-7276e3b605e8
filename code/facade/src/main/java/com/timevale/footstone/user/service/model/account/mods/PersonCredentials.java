package com.timevale.footstone.user.service.model.account.mods;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.timevale.account.service.model.service.biz.acc.output.BizAccountTOutput;
import com.timevale.account.service.model.service.biz.builder.acc.impl.abs.AbstractBizAccountBuilder;
import com.timevale.account.service.model.service.mods.ContentSecurity;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.footstone.user.service.exception.FootstoneUserErrors;
import com.timevale.footstone.user.service.model.decoration.DecorationType;
import com.timevale.footstone.user.service.model.decoration.ModelFieldDecoration;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.account.service.model.constants.BuiltinCredentials.*;

/** <AUTHOR> on 2019-01-12 */
@Data
public class PersonCredentials implements AccountPropConv {

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("身份证")
    private String idno;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("台湾来往大陆通行证")
    private String taiwan;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("香港来往大陆通行证")
    private String hongkong;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("澳门来往大陆通行证")
    private String macao;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("外籍证件")
    private String foreign;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("护照")
    private String passport;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("军官证")
    private String soldier;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("社会保障卡")
    private String socialSecurity;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("警官证")
    private String police;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("武装警察身份证件")
    private String armedPolice;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("身份证号")
    private String residenceBooklet;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("临时居民身份证")
    private String temporaryIdno;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("外国人永久居留证")
    private String greenCard;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("股东代码证")
    private String shareholder;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("未知类型")
    private String unknown;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("港澳台居住证")
    private String tWHkMacaoRP;

    @ModelFieldDecoration(DecorationType.PERSON_CRED)
    @ApiModelProperty("港澳台身份证")
    private String hkMacaoIdcard;

    /**
     * 利用反射获取PersonCredentials.class的属性数组
     *
     * @return PersonCredentials类的属性数组
     */
    private Field[] loadPersonCredentialsFields() {
        Field[] fields = PersonCredentials.class.getDeclaredFields();
        Arrays.stream(fields)
                .forEach(
                        f -> {
                            if (!f.isAccessible()) {
                                f.setAccessible(true);
                            }
                        });
        return fields;
    }

    /**
     * 判断PersonCredentials对象的属性是否有值br>
     *
     * <ul>
     * 说明：
     * <li>只要有一个属性有值，返回true
     * <li>所有属性都没有值，返回false
     * </ul>
     *
     * @return 属性是否有值
     */
    public boolean hasValue() {
        for (Field field : loadPersonCredentialsFields()) {
            try {
                String value = (String) field.get(this);
                if(StringUtils.isNotBlank(value)){
                    return true;
                }
            } catch (Exception cause) {
                return false;
            }
        }
        return false;
    }
    public Pair<String,String> getValue(){
        for (Field field : loadPersonCredentialsFields()) {
            try {
                String value = (String) field.get(this);

                if(StringUtils.isNotBlank(value)){
                    ApiModelProperty p=  field.getAnnotation(ApiModelProperty.class);
                    return Pair.of(p.value(),value);
                }
            } catch (Exception cause) {
                continue;
            }
        }
        return null;
    }
    @Override
    public void conv(AbstractBizAccountBuilder builder) {
        builder.buildCredentials().add(CRED_PSN_CH_IDCARD, getIdno());
        builder.buildCredentials().add(CRED_PSN_CH_MACAO, getMacao());
        builder.buildCredentials().add(CRED_PSN_FOREIGN, getForeign());
        builder.buildCredentials().add(CRED_PSN_UNKNOWN, getUnknown());
        builder.buildCredentials().add(CRED_PSN_CH_TWCARD, getTaiwan());
        builder.buildCredentials().add(CRED_PSN_PASSPORT, getPassport());
        builder.buildCredentials().add(CRED_PSN_CH_HONGKONG, getHongkong());
        builder.buildCredentials().add(CRED_PSN_POLICE_ID_CARD, getPolice());
        builder.buildCredentials().add(CRED_PSN_CH_GREEN_CARD, getGreenCard());
        builder.buildCredentials().add(CRED_PSN_CH_SSCARD, getSocialSecurity());
        builder.buildCredentials().add(CRED_PSN_CH_SOLDIER_IDCARD, getSoldier());
        builder.buildCredentials().add(CRED_PSN_SHAREHOLDER_CODE, getShareholder());
        builder.buildCredentials().add(CRED_PSN_CH_TEMPORARY_IDCARD, getTemporaryIdno());
        builder.buildCredentials().add(CRED_PSN_CH_ARMED_POLICE_IDCARD, getArmedPolice());
        builder.buildCredentials().add(CRED_PSN_CH_RESIDENCE_BOOKLET, getResidenceBooklet());
        builder.buildCredentials().add(CRED_PSN_CH_RP_TWHKMACAO, getTWHkMacaoRP());
        builder.buildCredentials().add(CRED_PSN_CH_HONGKONG_MACAO_IDCARD, getHkMacaoIdcard());


    }

    @Override
    public List<Property> conv() {
        if(hasValue()) {
            if(StringUtils.isBlank(getIdno())) {
                // 不支持更新证件类型
                throw new FootstoneUserErrors.CredentialsTypeNotSupport();
            }
            Property property = new Property();
            property.setType(CRED_PSN_CH_IDCARD);
            property.setValue(getIdno());
            return Lists.newArrayList(property);
        }
        return Lists.newArrayList();
    }

    @Override
    public PersonCredentials init(BizAccountTOutput<ContentSecurity> icuser) {
        Map<String, String> propGroup =
                icuser.getCredentials().stream()
                        .filter(p -> p.getValue() != null)
                        .collect(
                                Collectors.toMap(
                                        Property::getType, Property::getValue, (k1, k2) -> k1));

        setIdno(propGroup.get(CRED_PSN_CH_IDCARD));
        setTaiwan(propGroup.get(CRED_PSN_CH_TWCARD));
        setMacao(propGroup.get(CRED_PSN_CH_MACAO));
        setForeign(propGroup.get(CRED_PSN_FOREIGN));
        setUnknown(propGroup.get(CRED_PSN_UNKNOWN));
        setPassport(propGroup.get(CRED_PSN_PASSPORT));
        setHongkong(propGroup.get(CRED_PSN_CH_HONGKONG));
        setPolice(propGroup.get(CRED_PSN_POLICE_ID_CARD));
        setGreenCard(propGroup.get(CRED_PSN_CH_GREEN_CARD));
        setSocialSecurity(propGroup.get(CRED_PSN_CH_SSCARD));
        setSoldier(propGroup.get(CRED_PSN_CH_SOLDIER_IDCARD));
        setShareholder(propGroup.get(CRED_PSN_SHAREHOLDER_CODE));
        setTemporaryIdno(propGroup.get(CRED_PSN_CH_TEMPORARY_IDCARD));
        setArmedPolice(propGroup.get(CRED_PSN_CH_ARMED_POLICE_IDCARD));
        setResidenceBooklet(propGroup.get(CRED_PSN_CH_RESIDENCE_BOOKLET));
        setTWHkMacaoRP(propGroup.get(CRED_PSN_CH_RP_TWHKMACAO));
        setHkMacaoIdcard(propGroup.get(CRED_PSN_CH_HONGKONG_MACAO_IDCARD));

        return this;
    }

    public boolean checkEquals(PersonCredentials o) {
        if (this == o) return true;
        if (o == null) return false;
        return Objects.equal(idno, o.idno)
                && Objects.equal(taiwan, o.taiwan)
                && Objects.equal(hongkong, o.hongkong)
                && Objects.equal(macao, o.macao)
                && Objects.equal(foreign, o.foreign)
                && Objects.equal(passport, o.passport)
                && Objects.equal(soldier, o.soldier)
                && Objects.equal(socialSecurity, o.socialSecurity)
                && Objects.equal(police, o.police)
                && Objects.equal(armedPolice, o.armedPolice)
                && Objects.equal(residenceBooklet, o.residenceBooklet)
                && Objects.equal(temporaryIdno, o.temporaryIdno)
                && Objects.equal(greenCard, o.greenCard)
                && Objects.equal(shareholder, o.shareholder)
                && Objects.equal(unknown, o.unknown)
                && Objects.equal(tWHkMacaoRP,o.tWHkMacaoRP)
                && Objects.equal(hkMacaoIdcard,o.hkMacaoIdcard);
    }
}
