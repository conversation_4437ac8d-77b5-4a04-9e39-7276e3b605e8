package com.timevale.footstone.user.service.model.account.senderauth.response;

import com.timevale.footstone.user.service.model.account.senderauth.ServiceIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> on 2018-12-24 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WillAssembleAuthApplyResponse extends ServiceIdModel {

    @ApiModelProperty(value = "意愿url")
    private String authUrl;

    @ApiModelProperty(value = "意愿业务ID")
    private String bizId;
}
