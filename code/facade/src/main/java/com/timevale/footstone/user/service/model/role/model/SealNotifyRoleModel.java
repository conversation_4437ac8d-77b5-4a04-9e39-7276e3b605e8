/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：SealNotifyRoleModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.role.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3021:27]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.role.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类名：SealNotifyRoleModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/7/3021:27]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealNotifyRoleModel extends RoleModel {

    @ApiModelProperty(value = "是否勾选状态")
    private boolean selectFlag;
}