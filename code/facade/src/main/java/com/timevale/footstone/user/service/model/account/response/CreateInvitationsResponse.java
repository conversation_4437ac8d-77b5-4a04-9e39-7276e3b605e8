package com.timevale.footstone.user.service.model.account.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("创建邀请任务响应")
public class CreateInvitationsResponse {
    @ApiModelProperty(value = "邀请任务id")
    private String invitationId;

    @ApiModelProperty(value = "邀请链接短链")
    private String invitationURL;

    @ApiModelProperty(value = "邀请长连接")
    private String invitationOriginURL;
}
