package com.timevale.footstone.user.service.model.bizauth;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 类名：CustomBizAuthApplyRequest
 * 功能说明：
 *
 * <AUTHOR>
 * @DATE 2023/3/17 13:40
 */
@Data
public class CustomBizAuthApplyRequest extends ToString {

    @ApiModelProperty(required = true,value = "回跳地址")
    private String callbackUrl;


    @ApiModelProperty(value = "登陆态上下文ID")
    private String loginContextId;

  @ApiModelProperty(required = true, value = "流程步骤\n" + "\n"
          + "AGENT_APPLY_LETTER_AUTH=办理人申请授权书")
  private String flowStep;

}
