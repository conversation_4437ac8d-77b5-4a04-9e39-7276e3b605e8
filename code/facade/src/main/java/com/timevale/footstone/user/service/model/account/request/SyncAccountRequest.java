package com.timevale.footstone.user.service.model.account.request;

import com.timevale.esign.platform.toolkit.utils.asrt.AssertSupport;
import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年09月07日 16:59:00
 */
@Data
public class SyncAccountRequest extends ToString {

    @ApiModelProperty(value = "证件号")
    private String certNo;

    @ApiModelProperty("证件类型")
    private String certType;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "数据源")
    private String source;

    public void valid() {
        AssertSupport.assertTrue(StringUtils.isNotBlank(certNo), new ErrorsBase.MissingArgumentsWith("certNo"));
        AssertSupport.assertTrue(StringUtils.isNotBlank(certType), new ErrorsBase.MissingArgumentsWith("certType"));
        AssertSupport.assertTrue(StringUtils.isNotBlank(name), new ErrorsBase.MissingArgumentsWith("name"));
    }
}
