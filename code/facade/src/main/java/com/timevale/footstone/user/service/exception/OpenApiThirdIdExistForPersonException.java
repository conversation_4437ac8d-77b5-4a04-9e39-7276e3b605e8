package com.timevale.footstone.user.service.exception;

import com.timevale.footstone.user.service.exception.model.AccountIdModel;

/**
 * <AUTHOR> on 2019-08-01
 */
public class OpenApiThirdIdExistForPersonException extends BaseDataBizException {

    private AccountIdModel data;

    public OpenApiThirdIdExistForPersonException(String personId) {
        super("thirdPartyUserId已创建个人账号，无法重复创建");
        this.data = new AccountIdModel(personId);
    }

    @Override
    public AccountIdModel getData() {
        return data;
    }
}
