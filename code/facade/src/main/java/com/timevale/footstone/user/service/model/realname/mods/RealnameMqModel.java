package com.timevale.footstone.user.service.model.realname.mods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR> on 2019-06-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealnameMqModel {

    private String ouid;

    private String guid;

    private String agentOuid;

    private String agentGuid;

    private Map<String,String> context;

}
