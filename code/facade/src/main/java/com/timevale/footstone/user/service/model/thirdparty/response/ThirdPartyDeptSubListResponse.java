package com.timevale.footstone.user.service.model.thirdparty.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ThirdPartyDeptSubListResponse extends ToString {

    private List<ThirdPartyDeptBaseResponse> thirdPartyDeptList;


}
