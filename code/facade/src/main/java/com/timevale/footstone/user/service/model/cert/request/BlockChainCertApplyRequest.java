package com.timevale.footstone.user.service.model.cert.request;

import com.timevale.footstone.user.service.enums.FacadeUserCenterLicenseTypeEnum;
import com.timevale.mandarin.base.exception.BaseIllegalArgumentException;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

@Data
public class BlockChainCertApplyRequest extends ToString {

    @NotBlank(message = "name不能为空")
    // @Length(min = 2, max = 25, message = "name长度为2-25")
    private String name;
    @NotBlank(message = "idType不能为空")
    private String idType;
    @NotBlank(message = "idNumber不能为空")
    private String idNumber;
    private String validDuration;

    @ApiModelProperty(value = "是否使用区块链")
    private boolean useBlockChain;

    @ApiModelProperty(value = "场景描述")
    private String sceneDescription;

    public void valid() {
        if (name.length() < 2 || name.length() > 30) {
            throw new BaseIllegalArgumentException("name长度为2-30");
        }
        if (useBlockChain) {
            if (StringUtils.isBlank(sceneDescription)) {
                throw new BaseIllegalArgumentException("sceneDescription不能为空");
            }
            if (sceneDescription.length() < 5 || sceneDescription.length() > 1024) {
                throw new BaseIllegalArgumentException("sceneDescription长度为5-1024");
            }
        }
        if ("".equals(validDuration)) {
            throw new BaseIllegalArgumentException("validDuration无效");
        } else if (StringUtils.isNotBlank(validDuration)) {
            if (!"1D".equals(validDuration) && !"1Y".equals(validDuration)) {
                throw new BaseIllegalArgumentException("validDuration无效");
            }
        }

        if (FacadeUserCenterLicenseTypeEnum.from(idType) == null) {
            throw new BaseIllegalArgumentException("idType无效");
        }
    }
}
