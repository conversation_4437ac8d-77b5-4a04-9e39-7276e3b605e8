package com.timevale.footstone.user.service.model.account.request.v2;

import com.timevale.esign.platform.toolkit.utils.exceptions.ErrorsBase;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgAuthInfoBaseParam extends ToString {

    @ApiModelProperty("企业名称")
    protected String name;

    public void valid() {
        if (StringUtils.isBlank(name)) {
            throw new ErrorsBase.MissingArguments();
        }
    }
}
