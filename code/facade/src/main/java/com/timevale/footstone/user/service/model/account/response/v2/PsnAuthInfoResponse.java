package com.timevale.footstone.user.service.model.account.response.v2;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PsnAuthInfoResponse extends PsnAuthBaseResponse {


    @ApiModelProperty("个人证件号")
    private String certNo;

    @ApiModelProperty("个人名字")
    private String name;

    @ApiModelProperty("证件类型")
    private String certType;

    @ApiModelProperty("证件号")
    private String bankCardNo;

    @ApiModelProperty("实名手机号")
    private String realNameMobile;

    @ApiModelProperty("实名流程ID")
    private String psnSmFlowId;

}
