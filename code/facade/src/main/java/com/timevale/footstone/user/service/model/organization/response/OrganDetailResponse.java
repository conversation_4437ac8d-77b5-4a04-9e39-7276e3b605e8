package com.timevale.footstone.user.service.model.organization.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018-12-19
 */
public class OrganDetailResponse extends ToString {

    @ApiModelProperty(value = "实名证件号")
    private String credentical;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "实名状态")
    private Boolean realNameStatus;

    @ApiModelProperty(value = "企业头像url")
    private String headImg;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "账号注册时间")
    private String registerTime;
}
