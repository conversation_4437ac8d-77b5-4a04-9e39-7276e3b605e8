package com.timevale.footstone.user.service.api;

import com.timevale.footstone.user.service.ServiceName;
import com.timevale.footstone.user.service.model.WrapperResponse;
import com.timevale.footstone.user.service.model.auth.AuthRedirectModel;
import com.timevale.footstone.user.service.model.auth.AuthRedirectResponse;
import com.timevale.mandarin.common.annotation.RestClient;

@RestClient(serviceId = ServiceName.name)
public interface AuthIdentityService {

    WrapperResponse<AuthRedirectResponse> authRedirect(AuthRedirectModel model);

}
