package com.timevale.footstone.user.service.model.account.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: <PERSON> @Description: 创建个人账号参数 @Date: Created in 2018/4/23 @Modified By:
 */
@Data
@ApiModel(value = "用户个人账号创建")
public class PersonAccountCreateRequest extends ToString {

    /**
     * 第三方用户标识
     */
    @ApiModelProperty(
            value = "第三方用户标识",
            allowEmptyValue = true,
            allowableValues = "dingb3bf6338af78174135c2f4657eb6378f")
    private String thirdId;

    /**
     * 第三方用户标识
     */
    @NotBlank(message = "缺少参数name")
    @ApiModelProperty(value = "用户名", required = true, allowableValues = "张三")
    private String name;

    /**
     * 第三方用户标识
     */
    @NotBlank(message = "缺少参数idNo")
    @ApiModelProperty(value = "证件号", required = true, allowableValues = "110101199003079171")
    private String idNo;

    /**
     * 证件类型 枚举类型 见{@link com.timevale.esign.compontent.common.base.constants.CertTypeEnum}
     */
    @NotNull(message = "缺少参数idType")
    @ApiModelProperty(value = "证件类型", required = true, allowableValues = "19")
    private Integer idType;

    /**
     * mobile
     */
    @ApiModelProperty(
            value = "手机号码",
            required = false,
            allowEmptyValue = true,
            allowableValues = "18758908728")
    private String mobile;
    /**
     * email
     */
    @ApiModelProperty(
            value = "邮箱地址",
            allowEmptyValue = true,
            required = false,
            allowableValues = "<EMAIL>")
    private String email;
}
