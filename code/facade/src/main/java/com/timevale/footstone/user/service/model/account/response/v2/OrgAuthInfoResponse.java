package com.timevale.footstone.user.service.model.account.response.v2;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgAuthInfoResponse extends OrgAuthBaseResponse {

    @ApiModelProperty(" 法定代表人")
    private String legalRepName;

    @ApiModelProperty(" 企业证件号")
    private String certNo;

    @ApiModelProperty(" 企业证件类型")
    private String certType;

    @ApiModelProperty(" 法人证件类型")
    private String legalRepCertNo;

    @ApiModelProperty(" 法人证件类型")
    private String legalRepCertType;

    @ApiModelProperty("实名流程ID")
    private String orgSmFlowId;
}
