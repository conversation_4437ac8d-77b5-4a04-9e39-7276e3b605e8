package com.timevale.footstone.user.service.model.account.response;

import com.timevale.account.service.enums.AccountType;
import com.timevale.account.service.model.service.mods.accbase.AccountBaseDetail;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserBaseInfoResponse {

    @ApiModelProperty(value = "账号类型")
    String type;

    String ouid;

    String uuid;

    String guid;

    String accountUid;

    @ApiModelProperty(value = "账号创建时间")
    Date createTime;

    @ApiModelProperty(value = "app id")
    String appId;



    public UserBaseInfoResponse init(AccountBaseDetail base, ICUserId id) {
        AccountType inType = base.getType();
        setType(inType == null ? null : inType.name());
        setOuid(id.getOuid());
        setUuid(id.getUuid());
        setGuid(id.getGuid());
        setAccountUid(base.getAccountUid());
        setCreateTime(base.getCreateTime());
        setAppId(base.getProjectApp().getProjectId());
        return this;
    }
}
