/**
 * 杭州天谷信息科技有限公司源代码，版权归杭州天谷信息科技有限公司所有 <br/>
 * 项目名称：footstone-user-parent <br/>
 * 文件名：SealWithRoleModel.java <br/>
 * 包：com.timevale.footstone.user.service.model.seal.model <br/>
 * 描述： <br/>
 * 修改历史： <br/>
 * 1.[2019/1/1517:08]创建文件 by flh
 *
 * @auther flh
 */
package com.timevale.footstone.user.service.model.seal.model;

import com.timevale.footstone.user.service.model.role.model.RoleModel;
import com.timevale.footstone.user.service.model.role.model.RoleUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类名：SealWithRoleModel.java <br/>
 * 功能说明： <br/>
 * 修改历史： <br/>
 * 1.[2019/1/1517:08]创建类 by flh
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SealWithRoleModel extends SealModel {

    @ApiModelProperty(value = "被授权的角色列表")
    private List<RoleModel> roles;
}