<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.timevale</groupId>
        <artifactId>mandarin-bom</artifactId>
        <version>2.11.0</version>
        <relativePath/>
    </parent>

    <groupId>com.timevale.footstone.user</groupId>
    <artifactId>footstone-user-facade</artifactId>
    <version>1.5.6-SNAPSHOT</version>

    <name>footstone-user/facade</name>
    <packaging>jar</packaging>

    <properties>
        <lombok.version>1.16.20</lombok.version>
        <swagger2.version>2.9.2</swagger2.version>
        <footstone-base-model.version>1.0.8-SNAPSHOT</footstone-base-model.version>
        <authcoce.version>2.0.9-SNAPSHOT</authcoce.version>
        <account.easun.version>3.4.1-SNAPSHOT</account.easun.version>
        <account.organize.version>2.6.7-SNAPSHOT</account.organize.version>
        <cert.facade.version>2.4.80-SNAPSHOT</cert.facade.version>
        <easyexcel.version>3.3.2</easyexcel.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>mandarin-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.account</groupId>
            <artifactId>account-facade</artifactId>
            <version>2.2.26-SNAPSHOT</version>
        </dependency>

        <!--认证常用常量和枚举-->
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>open-identity-constant</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger2.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.footstone-base</groupId>
            <artifactId>footstone-base-model</artifactId>
            <version>${footstone-base-model.version}</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.authcode</groupId>
            <artifactId>authcode-facade</artifactId>
            <version>${authcoce.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>timevale-platform.toolkit.facade-utils</artifactId>
                    <groupId>timevale-platform.toolkit</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.timevale.easun</groupId>
            <artifactId>easun-facade</artifactId>
            <version>${account.easun.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.cert</groupId>
                    <artifactId>cert-common-service-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.timevale.account</groupId>
                    <artifactId>account-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.timevale.account</groupId>
                    <artifactId>account-organization-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 之前organ 都是通过easun 直接引用 -->
        <dependency>
            <groupId>com.timevale.account</groupId>
            <artifactId>account-organization-facade</artifactId>
            <version>${account.organize.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.open.platform.service</groupId>
                    <artifactId>open.platform.service-facade</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>account-facade</artifactId>
                    <groupId>com.timevale.account</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.timevale.account</groupId>
                    <artifactId>account-organization-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.timevale.cert</groupId>
            <artifactId>cert-facade</artifactId>
            <version>${cert.facade.version}</version>
        </dependency>

        <!--新开放平台-->
        <dependency>
            <groupId>com.timevale.open.platform.service</groupId>
            <artifactId>open.platform.service-facade</artifactId>
            <version>release-********</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.spring.boot</groupId>
                    <artifactId>esign-mq-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.notice</groupId>
            <artifactId>notice-facade</artifactId>
            <version>2.0.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.token</groupId>
            <artifactId>token-facade</artifactId>
            <version>2.3.1-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.timevale.footstone</groupId>
            <artifactId>footstone-identity-common-service-facade</artifactId>
            <version>2.7.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.willauth</groupId>
                    <artifactId>willauth-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.timevale.footstone-base</groupId>
                    <artifactId>footstone-base-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.timevale</groupId>
                    <artifactId>open-identity-account</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>open-identity-account</artifactId>
            <version>0.0.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>


</project>
