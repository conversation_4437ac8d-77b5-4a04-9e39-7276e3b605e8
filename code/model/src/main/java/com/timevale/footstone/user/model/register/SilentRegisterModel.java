package com.timevale.footstone.user.model.register;

import com.timevale.account.service.enums.AccountType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @version $ Id: SilentRegisterModel.java, v0.1 2021年07月16日 10:47 WangYuWu $
 */
@Data
public class SilentRegisterModel {

    //授权任务ID
    private String          paramsKey;
    private String          code;

    private String          state;

    /**
     * 授权模式
     */
    private AccountType     mode;

    /**
     * 企业账号OID
     */
    private String          orgOid;

    /**
     * 操作人账号OID
     */
    private String          agentOid;

    /**
     * 三方appId
     */
    private String          appId;

    /**
     * 授权范围（比较少，目前可枚举）
     * 用户=get_user_info，
     * 印章=op_seal，
     * 签署=op_sign
     */
    private String          scope;

    /**
     * 邮箱地址
     * 或者
     * 手机号码
     */
    private String          account;

    /**
     * 授权业务类型
     */
    private String          bizType;

    /**
     * 调用方期望的开发者通知地址
     * 授权/认证完成后通知三方后台的https地址
     */
    private String          notifyUrl;

    /**
     * 调用方期望的重定向地址
     */
    private String          redirectUrl;

    /**
     * 授权类型
     * 默认值就是code
     */
    private String          responseType;

    /**
     * 实名认证Id
     */
    private String          flowId;

    /**
     * 意愿认证Id
     */
    private String          willId;

    /**
     * 意愿认证类型
     */
    private String          willType;

    private String signFlowId;

    private boolean ifRealName;

    /**
     * 静默授权结果页面(审批、成功)
     */
    private String          silentAuthResultUrl;


}

