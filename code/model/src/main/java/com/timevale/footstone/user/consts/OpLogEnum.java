package com.timevale.footstone.user.consts;

import lombok.Getter;

import static com.timevale.footstone.user.consts.OpLogConsts.FIRST_MODULE_PERSONAL_CENTER;
import static com.timevale.footstone.user.consts.OpLogConsts.USER_ORGANIZATIONAL_CHANGE;
import static com.timevale.footstone.user.consts.OpLogConsts.USER_SSO;

/**
 * 日志类型常量，需要产品申请
 */
public enum OpLogEnum {
    /**
     * 账户名修改
     */
    UPDATE_NAME_INFO(FIRST_MODULE_PERSONAL_CENTER, "姓名变更", "信息变更", "修改", "账号名称"),
    /**
     * 修改手机号
     */
    UPDATE_MOBILE(FIRST_MODULE_PERSONAL_CENTER, "手机号变更", "信息变更", "修改", "手机号"),
    /**
     * 修改邮箱
     */
    UPDATE_EMAIL(FIRST_MODULE_PERSONAL_CENTER, "邮箱变更", "信息变更", "修改", "邮箱"),
    /**
     * 修改密码
     */
    UPDATE_PASSWORD(FIRST_MODULE_PERSONAL_CENTER, "密码变更", "信息变更", "修改", "密码"),
    /**
     * 微信绑定
     */
    WECHAT_BIND(FIRST_MODULE_PERSONAL_CENTER, "三方账号绑定-微信", "账号变更", "绑定", "微信"),
    /**
     * 微信解绑
     */
    WECHAT_UN_BIND(FIRST_MODULE_PERSONAL_CENTER, "三方账号解绑-微信", "账号变更", "解绑", "微信"),
    /**
     * 钉钉绑定
     */
    DING_BIND(FIRST_MODULE_PERSONAL_CENTER, "三方账号绑定-钉钉", "账号变更", "绑定", "钉钉"),
    /**
     * 钉钉解绑
     */
    DING_UN_BIND(FIRST_MODULE_PERSONAL_CENTER, "三方账号解绑-钉钉", "账号变更", "解绑", "钉钉"),
    /**
     * 支付宝绑定
     */
    ALIPAY_BIND(FIRST_MODULE_PERSONAL_CENTER, "三方账号绑定-支付宝", "账号变更", "绑定", "支付宝"),
    /**
     * 支付宝解绑
     */
    ALIPAY_UN_BIND(FIRST_MODULE_PERSONAL_CENTER, "三方账号解绑-支付宝", "账号变更", "解绑", "支付宝"),
    /**
     * 创建个人账号
     */
    CREATE_PERSON(FIRST_MODULE_PERSONAL_CENTER, "账号注册", "账号变更", "注册", "个人账号"),
    /**
     * 创建企业
     */
    CREATE_ORG(FIRST_MODULE_PERSONAL_CENTER, "创建企业", "创建企业", "新增", "企业账号"),

    /**
     * 编辑部门
     */
    EDIT_DEPT(USER_ORGANIZATIONAL_CHANGE, "部门信息变更", "组织变更", "修改", "组织变更"),

    /**
     * 添加部门负责人
     */
    ADD_DEPT_LEADER(USER_ORGANIZATIONAL_CHANGE, "添加部门负责人", "组织变更", "修改", "组织变更"),

    /**
     * 修改部门负责人
     */
    CHANGE_DEPT_LEADER(USER_ORGANIZATIONAL_CHANGE, "修改部门负责人", "组织变更", "修改", "组织变更"),

    /**
     * 创建SSO连接器
     */
    CREATE_SSO_CONNECTOR(USER_SSO, "创建SSO连接器", "单点登录", "新增", ""),

    /**
     * 启用sso连接器
     */
    START_SSO_CONNECTOR(USER_SSO, "启用SSO连接器", "单点登录", "启用", ""),


    /**
     * 停用sso连接器
     */
    STOP_SSO_CONNECTOR(USER_SSO, "停用SSO连接器", "单点登录", "停用", ""),


    /**
     * 修改SSO企业域名
     */
    CHANGE_SSO_DOMAIN(USER_SSO, "修改SSO企业域名", "单点登录", "修改", ""),


    /**
     *关联SSO企业域名
     */
    RELATE_SSO_DOMAIN(USER_SSO, "关联SSO企业域名", "单点登录", "绑定", ""),


    /**
     * 修改企业可见限制
     */
    CHANGE_ORGAN_VISIBLE(USER_SSO, "修改企业可见限制", "单点登录", "修改", ""),

    /**
     * 创建企业业务空间
     */
    CREATE_SPACE(USER_ORGANIZATIONAL_CHANGE, "创建业务空间", "组织变更", "修改", ""),

    /**
     * 修改企业业务空间
     */
    CHANGE_SPACE(USER_ORGANIZATIONAL_CHANGE, "修改业务空间", "组织变更", "修改", ""),

    /**
     * 删除企业业务空间
     */
    CLOSE_SPACE(USER_ORGANIZATIONAL_CHANGE, "关闭业务空间", "组织变更", "修改", ""),

    /**
     * 成员注销账号
     */
    MEMBER_DELETE_ACCOUNT(USER_ORGANIZATIONAL_CHANGE, "企业成员账号注销", "组织变更", "修改", ""),

    /**
     * 批量导出成员
     */
    BATCH_EXPORT_MEMBER(USER_ORGANIZATIONAL_CHANGE, "批量导出人员", "组织变更", "导出", ""),




    /**
     * 成员加入企业
     */
    CREATE_MEMBER(USER_ORGANIZATIONAL_CHANGE, "成员加入企业", "组织变更", "修改", ""),
    ;

    /**
     * 一级模块
     */
    @Getter
    private String firstModule;
    /**
     * 事件
     */
    @Getter
    private String event;

    /**
     * 二级模块
     */
    @Getter
    private String secondaryModule;
    /**
     * 操作类型
     */
    @Getter
    private String type;
    /**
     * 资源名称
     */
    @Getter
    private String sourceName;

    OpLogEnum(String firstModule, String event, String secondaryModule, String type, String sourceName) {
        this.firstModule = firstModule;
        this.event = event;
        this.secondaryModule = secondaryModule;
        this.type = type;
        this.sourceName = sourceName;
    }
}
