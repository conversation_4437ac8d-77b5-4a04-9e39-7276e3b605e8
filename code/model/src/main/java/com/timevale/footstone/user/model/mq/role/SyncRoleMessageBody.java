package com.timevale.footstone.user.model.mq.role;

import com.timevale.footstone.user.consts.RoleOperateEnum;
import lombok.Builder;
import lombok.Data;
import org.apache.curator.framework.api.transaction.OperationType;

import java.util.List;


/**
 * <AUTHOR>
 * @version $ Id: SyncRoleMessageBody.java, v0.1 2021年01月12日 20:40 WangYuWu $
 */
@Data
@Builder
public class SyncRoleMessageBody {

    /**
     * 传说中的oid
     */
    private String ouId;

    /**
     * 传说中的gid
     */
    private String guId;

    /**
     * 角色ID
     */
    private List<String> roleIds;


    /**
     * 操作人oid
     */
    private String operator;

    /**
     * 操作类型
     */
    private RoleOperateEnum operationType;
}
