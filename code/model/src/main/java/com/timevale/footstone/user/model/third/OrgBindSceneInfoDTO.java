package com.timevale.footstone.user.model.third;

import com.timevale.easun.service.enums.ThirdPartyBindOrgSceneEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-05-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgBindSceneInfoDTO {
    /**
     * 绑定场景
     *
     * @see ThirdPartyBindOrgSceneEnum#name()
     */
    private String bindScene;

    /**
     * 绑定引导语
     */
    private String introduceText;

    /**
     * 确认按钮文案
     */
    private String confirmText;

    /**
     * 取消按钮文案
     */
    private String cancelText;

    /**
     * 前端icon url
     */
    private String iconUrl;
}
