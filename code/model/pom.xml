<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.timevale.footstone.user</groupId>
		<artifactId>footstone-user-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>footstone-user-model</artifactId>
	<name>footstone-user/model</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>footstone-user-dal</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>footstone-user-facade</artifactId>
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<groupId>com.timevale.footstone</groupId>-->
<!--					<artifactId>footstone-identity-common-service-facade</artifactId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
		</dependency>
	</dependencies>
</project>
